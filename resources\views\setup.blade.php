@extends('layouts.app')

@section('content')
<div class="w-[1200px] mx-auto py-6">
    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
        <p class="font-bold">Error</p>
        <p>{{ session('error') }}</p>
    </div>
    @endif

    @if(session('message'))
    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-6" role="alert">
        <p>{{ session('message') }}</p>
    </div>
    @endif

    @if(isset($needsBusinessConnection) && $needsBusinessConnection)
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 class="text-md md:text-2xl font-bold text-gray-900 mb-4">Connect Google Business Account</h2>
        <p class="mb-6 text-gray-600">To use the review management features, you need to connect your Google Business account.</p>
        <p class="mb-6 text-gray-600">You're currently authenticated with Google for basic access, but you need to grant additional permissions for Google Business functionality.</p>

        @if(isset($message))
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6" role="alert">
            <p>{{ $message }}</p>
        </div>
        @endif
        <div class="flex items-center justify-end">
            <a href="{{ route('auth.google.business') }}" class="bg-blue-600 mx-auto hover:bg-blue-700 text-white text-sm md:text-lg font-bold py-2 px-4 md:py-3 md:px-6 rounded">
                Connect Google Business Account
            </a>
        </div>
    </div>
    @endif

    @if(isset($account))
    <a href="?account={{ urlencode($account->name) }}"
        class="block p-4 border rounded-lg hover:bg-gray-50 {{$account->name}}">
        <h3 class="font-medium text-gray-900"><?php echo htmlspecialchars($account->accountName); ?></h3>
        <p class="text-sm text-gray-500">Type: <?php echo htmlspecialchars($account->type); ?></p>
        <p class="text-sm text-gray-500">ID: <?php echo htmlspecialchars(basename($account->name)); ?></p>
    </a>
    @endif

    @if(isset($response))
    <div class="bg-white shadow-md rounded-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Business Details</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

            @foreach($response['locations'] as $location)
            @php
            // Search for a match in $checkLocation where location_name matches location['name']
            $matched = collect($checkLocation)->firstWhere('location_name', $location['name']);
            @endphp

            <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-all duration-300 ease-in-out flex flex-col gap-3 text-left">

                @if(isset($matched['user']) && isset($matched['user']['name']))
                <div class="col-span-1 md:col-span-2 lg:col-span-3 bg-blue-50 border-l-4 border-blue-500 px-3 py-2 rounded shadow-sm">
                    <p class="font-medium">
                        <span class="font-semibold text-blue-700">{{ $matched['user']['name'] }}</span> is owner of this business.
                    </p>
                </div>
                @endif

                <!-- Title & Button -->
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">{{ $location['title'] }}</h3>
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Details</span>
                </div>

                <!-- Address -->
                @if (isset($location['storefrontAddress']))
                <div class="flex items-start mt-2">
                    <svg class="w-5 h-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <p class="text-gray-600 text-sm">
                        {{ implode(', ', $location['storefrontAddress']['addressLines'] ?? []) }}
                        {{ isset($location['storefrontAddress']['locality']) ? ', ' . $location['storefrontAddress']['locality'] : '' }}
                        {{ isset($location['storefrontAddress']['administrativeArea']) ? ', ' . $location['storefrontAddress']['administrativeArea'] : '' }}
                    </p>
                </div>
                @endif

                <!-- Phone -->
                @if (isset($location['phoneNumbers']['primaryPhone']))
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-gray-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <p class="text-gray-600 text-sm">{{ $location['phoneNumbers']['primaryPhone'] }}</p>
                </div>
                @endif

                <!-- ID -->
                <div class="flex items-center mt-auto pt-2 border-t border-gray-100">
                    <svg class="w-5 h-5 text-gray-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"></path>
                    </svg>
                    <p class="text-gray-500 text-xs font-medium">ID: {{ basename($location['name']) }}</p>
                </div>

                @if(!isset($matched['user']['name']))
                <div class="mt-4">
                    <a href="{{ route('google.reviews', ['location' => $location['name']]) }}"
                        class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:border-blue-700 focus:ring focus:ring-blue-200 active:bg-blue-600 transition ease-in-out duration-150">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                        Continue
                    </a>
                </div>
                @endif
            </div>
            @endforeach

        </div>
    </div>
    @endif
</div>
@endsection