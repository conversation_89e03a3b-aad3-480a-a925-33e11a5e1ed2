<?php $__env->startSection('content'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

<div class="min-h-screen bg-gray-100 w-[1200px] mx-auto">
    <!-- Header Section -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-4 lg:px-6 py-4 md:py-6 bg-white">
            <div class="text-center">
                <h1 class="text-2xl md:text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
                <p class="text-sm md:text-lg text-gray-600">
                    Select the perfect plan for your business and apply coupon codes for instant savings
                </p>
            </div>
        </div>
    </div>

        <!-- Error Messages -->
        <?php if($errors->any()): ?>
            <div class="mb-6">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                There were some errors with your submission
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Success Messages -->
        <?php if(session('success')): ?>
            <div class="mb-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">
                                <?php echo e(session('success')); ?>

                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    <!-- Main Content -->
    <div class="flex flex-col md:flex-row min-h-screen">
        <!-- Left Side - Plan Selection (White Theme) -->
        <div class="md:w-2/2 bg-white">
            <div class="p-6 lg:p-8">
                <!-- Billing Cycle and Plan Info -->
                <div class="mb-8">
                    <div class="flex flex-col flex-wrap sm:flex-row sm:items-center sm:justify-between mb-6 gap-2 sm:space-y-0">
                        <div>
                            <h2 class="text-md md:text-2xl font-bold text-gray-900">
                                <?php if($isUpgrade): ?>
                                    Upgrade Your Plan
                                <?php else: ?>
                                    Choose Your Plan
                                <?php endif; ?>
                            </h2>
                            <?php if($isUpgrade && $currentSubscription): ?>
                                <p class="text-sm text-gray-600 mt-1">
                                    Currently on: <span class="font-medium"><?php echo e($currentSubscription->plan->name); ?></span>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="flex flex-wrap gap-3 sm:gap-4">
                            <!-- Billing Cycle Toggle -->
                            <div class="flex items-center bg-gray-100 rounded-lg gap-2 p-1">
                                <button id="billing-monthly" class="billing-btn px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-white text-gray-900 shadow-sm">
                                    Monthly
                                </button>
                                <button id="billing-annual" class="billing-btn px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900">
                                    Annual <span class="text-xs text-green-600 font-semibold ml-1">Save up to 20%</span>
                                </button>
                            </div>
                            <!-- Currency Display (No Toggle) -->
                            <div class="flex items-center bg-gray-100 rounded-lg gap-2 p-1 px-4 py-2">
                                <i class="fas fa-<?php echo e($currency === 'INR' ? 'rupee-sign' : 'dollar-sign'); ?> mr-1 text-gray-600"></i>
                                <span class="text-sm font-medium text-gray-900"><?php echo e($currency); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Plans Grid -->
                <div class="grid grid-cols-1 xl:grid-cols-1 gap-4" id="plans-container">
                    <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="w-full plan-card border-2 border-gray-200 rounded-xl p-6 cursor-pointer transition-all duration-300 hover:border-gray-900 hover:shadow-lg <?php echo e($selectedPlanId == $plan->id ? 'border-gray-900 bg-gray-50' : ''); ?>"
                         data-plan-id="<?php echo e($plan->id); ?>"
                         data-plan-name="<?php echo e($plan->name); ?>"
                         data-plan-price="<?php echo e($plan->price); ?>"
                         data-plan-annual-price="<?php echo e($plan->annual_price ?? ($plan->price * 12)); ?>"
                         data-plan-annual-discount="<?php echo e($plan->annual_discount_percentage); ?>"
                         data-plan-tax-percentage="<?php echo e($plan->tax_percentage ?? 0); ?>"
                         data-plan-currency="<?php echo e($plan->currency); ?>"
                         data-plan-symbol="<?php echo e($plan->currency_symbol); ?>"
                         data-plan-duration="<?php echo e($plan->duration_days); ?>"
                         data-plan-description="<?php echo e($plan->short_description); ?>"
                         data-plan-features="<?php echo e(json_encode($plan->features)); ?>">

                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($plan->name); ?></h3>
                                <p class="text-gray-600 text-sm mb-3"><?php echo e($plan->short_description); ?></p>

                                <!-- Pricing Display -->
                                <div class="pricing-container">
                                    <!-- Monthly Pricing -->
                                    <div class="monthly-pricing flex items-baseline">
                                        <span class="text-3xl font-bold text-gray-900">
                                            <?php if($plan->price == 0): ?>
                                                Free
                                            <?php else: ?>
                                                <?php echo e($plan->currency_symbol); ?><?php echo e(number_format($plan->price, $plan->currency === 'USD' ? 2 : 0)); ?>

                                            <?php endif; ?>                                            
                                        </span>
                                        <?php if($plan->price > 0): ?>
                                            <span class="text-gray-500 ml-2">/ month</span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Annual Pricing -->
                                    <div class="annual-pricing hidden">
                                        <?php
                                            $regularAnnual = $plan->price * 12;
                                            $discountedAnnual = $plan->annual_price ?? ($regularAnnual - ($regularAnnual * ($plan->annual_discount_percentage ?? 0) / 100));
                                            $savings = $regularAnnual - $discountedAnnual;
                                        ?>
                                        <div class="flex items-baseline space-x-2">
                                            <?php if($plan->annual_price == 0): ?>
                                                <span class="text-3xl font-bold text-gray-900">Free</span>
                                            <?php else: ?>                                            
                                                <?php if($plan->annual_discount_percentage > 0): ?>
                                                    <span class="text-lg text-gray-400 line-through"><?php echo e($plan->currency_symbol); ?><?php echo e(number_format($regularAnnual, $plan->currency === 'USD' ? 2 : 0)); ?></span>
                                                <?php endif; ?>
                                                <span class="text-3xl font-bold text-gray-900"><?php echo e($plan->currency_symbol); ?><?php echo e(number_format($discountedAnnual, $plan->currency === 'USD' ? 2 : 0)); ?></span>
                                                <span class="text-gray-500">/ year</span>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($savings > 0 && $plan->annual_price > 0): ?>
                                            <div class="text-sm text-green-600 font-medium mt-1">
                                                Save <?php echo e($plan->currency_symbol); ?><?php echo e(number_format($savings, $plan->currency === 'USD' ? 2 : 0)); ?> (<?php echo e($plan->annual_discount_percentage); ?>% off)
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="plan-radio ml-4">
                                <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center transition-all duration-200 <?php echo e($selectedPlanId == $plan->id ? 'border-gray-900 bg-gray-900' : ''); ?>">
                                    <?php if($selectedPlanId == $plan->id): ?>
                                    <div class="w-2 h-2 bg-white rounded-full"></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <?php if(json_decode($plan->long_description)): ?>
                        <div class="space-y-2 mb-4">
                            <?php $__currentLoopData = json_decode($plan->long_description); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-3 text-xs"></i>
                                <span><?php echo e($feature); ?></span>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                            <!-- <?php if(count(json_decode($plan->long_description)) > 4): ?>
                            <button type="button"
                                    class="view-all-features-btn text-sm text-blue-600 hover:text-blue-800 font-medium ml-6 transition-colors duration-200"
                                    data-plan-id="<?php echo e($plan->id); ?>">
                                <i class="fas fa-eye mr-1"></i>
                                View all <?php echo e(count(json_decode($plan->long_description))); ?> features
                            </button>
                            <?php endif; ?> -->
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Right Side - Payment Process (Black Theme) -->
        <div class="md:w-2/2 bg-gray-900 text-white ">
            <div class="p-6 lg:p-8 h-full">
                <div id="checkout-section" class="sticky top-[100px] <?php echo e(!$selectedPlanId ? 'hidden' : ''); ?>">
                    <h2 class="text-2xl font-bold text-white mb-8">Complete Your Purchase</h2>

                    <!-- Selected Plan Display -->
                    <div id="selected-plan-info" class="mb-8">
                        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-white">Selected Plan</h3>
                                <span class="text-sm text-gray-400" id="selected-currency"><?php echo e($currency); ?></span>
                            </div>
                            <h4 class="text-xl font-bold text-white mb-2" id="selected-plan-name">
                                <?php if($selectedPlanId): ?>
                                    <?php echo e($plans->where('id', $selectedPlanId)->first()->name ?? ''); ?>

                                <?php endif; ?>
                            </h4>
                            <p class="text-gray-300 text-sm mb-4" id="selected-plan-description">
                                <?php if($selectedPlanId): ?>
                                    <?php echo e($plans->where('id', $selectedPlanId)->first()->short_description ?? ''); ?>

                                <?php endif; ?>
                            </p>
                            <div class="text-2xl font-bold text-white" id="selected-plan-price">
                                <?php if($selectedPlanId): ?>
                                    <?php echo e($plans->where('id', $selectedPlanId)->first()->currency_symbol ?? '₹'); ?><?php echo e(number_format($plans->where('id', $selectedPlanId)->first()->price ?? 0, $plans->where('id', $selectedPlanId)->first()->currency === 'USD' ? 2 : 0)); ?>

                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Coupon Section -->
                    <div class="mb-8">
                        <label class="block text-sm font-medium text-gray-300 mb-3">
                            <i class="fas fa-tag mr-2"></i>Have a coupon code?
                        </label>
                        <div class="flex space-x-3">
                            <input type="text"
                                   id="coupon-code"
                                   placeholder="Enter coupon code"
                                   class="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-white focus:border-white text-white placeholder-gray-400 uppercase transition-all duration-200">
                            <button type="button"
                                    id="apply-coupon-btn"
                                    class="px-6 py-3 bg-white text-gray-900 rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-indigo-600 hover:text-white">
                                Apply
                            </button>
                        </div>

                        <!-- Coupon Messages -->
                        <div id="coupon-message" class="mt-3 text-sm hidden"></div>

                        <!-- Applied Coupon Display -->
                        <div id="applied-coupon" class="mt-4 hidden">
                            <div class="bg-green-900 border border-green-700 rounded-lg p-4">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-green-300 font-medium" id="applied-coupon-code"></span>
                                        <p class="text-green-400 text-sm" id="applied-coupon-description"></p>
                                    </div>
                                    <button type="button" id="remove-coupon-btn" class="text-green-300 hover:text-green-100 transition-colors">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Price Breakdown -->
                    <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
                        <h3 class="text-lg font-semibold text-white mb-4">Order Summary</h3>
                        <div class="space-y-3">
                            <!-- Billing Cycle Display -->
                            <div class="flex justify-between text-gray-300 text-sm">
                                <span>Billing Cycle:</span>
                                <span id="billing-cycle-display" class="font-medium">Monthly</span>
                            </div>

                            <div class="flex justify-between text-gray-300">
                                <span>Subtotal:</span>
                                <span id="subtotal-amount" class="font-medium">
                                    <?php if($selectedPlanId): ?>
                                        <?php echo e($plans->where('id', $selectedPlanId)->first()->currency_symbol ?? '₹'); ?><?php echo e(number_format($plans->where('id', $selectedPlanId)->first()->price ?? 0, $plans->where('id', $selectedPlanId)->first()->currency === 'USD' ? 2 : 0)); ?>

                                    <?php else: ?>
                                        ₹0.00
                                    <?php endif; ?>
                                </span>
                            </div>

                            <div class="flex justify-between text-green-400 hidden" id="discount-row">
                                <span>Discount:</span>
                                <span id="discount-amount" class="font-medium">-₹0.00</span>
                            </div>

                            <!-- Tax Row -->
                            <div class="flex justify-between text-gray-300 hidden" id="tax-row">
                                <span>Tax (<span id="tax-percentage">0</span>%):</span>
                                <span id="tax-amount" class="font-medium">₹0.00</span>
                            </div>

                            <div class="border-t border-gray-600 pt-3">
                                <div class="flex justify-between text-xl font-bold text-white">
                                    <span>Total:</span>
                                    <span id="total-amount">
                                        <?php if($selectedPlanId): ?>
                                            <?php echo e($plans->where('id', $selectedPlanId)->first()->currency_symbol ?? '₹'); ?><?php echo e(number_format($plans->where('id', $selectedPlanId)->first()->price ?? 0, $plans->where('id', $selectedPlanId)->first()->currency === 'USD' ? 2 : 0)); ?>

                                        <?php else: ?>
                                            ₹0.00
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Form -->
                    <form id="checkout-form" action="<?php echo e(route('checkout.process')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="plan_id" id="form-plan-id" value="<?php echo e($selectedPlanId); ?>">
                        <input type="hidden" name="coupon_code" id="form-coupon-code" value="">
                        <input type="hidden" name="billing_cycle" id="form-billing-cycle" value="monthly">

                        <button type="submit"
                                id="checkout-btn"
                                class="w-full py-4 px-6 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-all duration-200 font-bold text-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:bg-indigo-600 hover:text-white"
                                <?php echo e(!$selectedPlanId ? 'disabled' : ''); ?>>
                            <span id="checkout-btn-text">
                                <?php if($selectedPlanId): ?>
                                    <i class="fas fa-credit-card mr-2"></i>Proceed to Payment
                                <?php else: ?>
                                    Select a Plan
                                <?php endif; ?>
                            </span>
                        </button>
                    </form>

                    <!-- Security Badge -->
                    <div class="mt-6 text-center">
                        <div class="flex flex-col lg:flex-row items-center justify-center text-sm gap-3 text-gray-400">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt mr-2 text-green-400"></i>
                                <span>256-bit SSL encryption</span>                                
                            </div>
                            <span class="mx-2 hidden lg:block">•</span>
                            <div class="flex items-center">
                                <i class="fas fa-lock mr-2"></i>
                                <span>Secure payment by Stripe</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div id="empty-state" class="<?php echo e($selectedPlanId ? 'hidden' : ''); ?> flex flex-col items-center justify-center h-full text-center">
                    <div class="w-24 h-24 bg-gray-800 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-arrow-left text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">Select a Plan</h3>
                    <p class="text-gray-400 text-lg">Choose a plan from the left to continue with checkout</p>
                </div>
            </div>
        </div>
    </div>

<!-- Feature Modal -->
<div id="feature-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4 backdrop-blur-sm">
    <div class="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 class="text-xl font-bold text-gray-900" id="modal-plan-name">Plan Features</h3>
            <button type="button" id="close-modal" class="text-gray-400 hover:text-gray-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[70vh]">
            <div id="modal-features-list" class="space-y-4">
                <!-- Features will be populated here -->
            </div>
        </div>
        <div class="p-6 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-sm text-gray-600">Starting at</div>
                    <div class="text-2xl font-bold text-gray-900" id="modal-plan-price">₹0</div>
                </div>
                <button type="button" id="modal-select-plan" class="px-6 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium">
                    Select This Plan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">Processing...</span>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let selectedPlan = null;
    let appliedCoupon = null;
    let currentCurrency = '<?php echo e($currency); ?>';
    let currentBillingCycle = 'monthly';
    let currentPricing = {
        original: 0,
        discount: 0,
        tax: 0,
        final: 0,
        currency: '₹'
    };

    // Currency is now auto-detected based on IP location
    // No manual currency switching allowed

    // Billing cycle switching functionality
    document.querySelectorAll('.billing-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const newBillingCycle = this.id === 'billing-monthly' ? 'monthly' : 'annual';
            if (newBillingCycle !== currentBillingCycle) {
                switchBillingCycle(newBillingCycle);
            }
        });
    });

    // Currency switching removed - now auto-detected based on IP location

    function switchBillingCycle(billingCycle) {
        currentBillingCycle = billingCycle;

        // Update billing cycle buttons
        document.querySelectorAll('.billing-btn').forEach(btn => {
            btn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
            btn.classList.add('text-gray-600', 'hover:text-gray-900');
        });

        const activeBtn = document.getElementById(`billing-${billingCycle}`);
        activeBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
        activeBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');

        // Update pricing display for all plans
        document.querySelectorAll('.plan-card').forEach(card => {
            const monthlyPricing = card.querySelector('.monthly-pricing');
            const annualPricing = card.querySelector('.annual-pricing');

            if (billingCycle === 'annual') {
                monthlyPricing.classList.add('hidden');
                annualPricing.classList.remove('hidden');
            } else {
                monthlyPricing.classList.remove('hidden');
                annualPricing.classList.add('hidden');
            }
        });

        // Update form billing cycle
        document.getElementById('form-billing-cycle').value = billingCycle;
        document.getElementById('billing-cycle-display').textContent = billingCycle === 'annual' ? 'Annual' : 'Monthly';

        // Update pricing if plan is selected
        if (selectedPlan) {
            updatePricing();
        }
    }

    function clearSelection() {
        selectedPlan = null;
        appliedCoupon = null;

        // Clear visual selection
        document.querySelectorAll('.plan-card').forEach(card => {
            card.classList.remove('border-gray-900', 'bg-gray-50');
            card.classList.add('border-gray-200');
            const radio = card.querySelector('.plan-radio > div');
            radio.classList.remove('border-gray-900', 'bg-gray-900');
            radio.classList.add('border-gray-300');
            radio.innerHTML = '';
        });

        updateUI();
    }

    // Initialize with selected plan if any
    const selectedPlanId = '<?php echo e($selectedPlanId); ?>';
    if (selectedPlanId) {
        const planCards = document.querySelectorAll('.plan-card');
        planCards.forEach(card => {
            if (card.dataset.planId === selectedPlanId && card.dataset.planCurrency === currentCurrency) {
                selectPlan(card);
            }
        });
    }

    // Plan selection handling
    document.querySelectorAll('.plan-card').forEach(card => {
        card.addEventListener('click', function() {
            selectPlan(this);
        });
    });

    function selectPlan(card) {
        // Remove selection from all visible cards
        document.querySelectorAll('.plan-card').forEach(c => {
            c.classList.remove('border-gray-900', 'bg-gray-50');
            c.classList.add('border-gray-200');
            const radio = c.querySelector('.plan-radio > div');
            radio.classList.remove('border-gray-900', 'bg-gray-900');
            radio.classList.add('border-gray-300');
            radio.innerHTML = '';
        });

        // Select current card
        card.classList.remove('border-gray-200');
        card.classList.add('border-gray-900', 'bg-gray-50');
        const radio = card.querySelector('.plan-radio > div');
        radio.classList.remove('border-gray-300');
        radio.classList.add('border-gray-900', 'bg-gray-900');
        radio.innerHTML = '<div class="w-2 h-2 bg-white rounded-full"></div>';

        // Update selected plan
        selectedPlan = {
            id: card.dataset.planId,
            name: card.dataset.planName,
            description: card.dataset.planDescription,
            price: parseFloat(card.dataset.planPrice),
            annualPrice: parseFloat(card.dataset.planAnnualPrice),
            annualDiscount: parseInt(card.dataset.planAnnualDiscount) || 0,
            taxPercentage: parseFloat(card.dataset.planTaxPercentage) || 0,
            currency: card.dataset.planCurrency,
            symbol: card.dataset.planSymbol,
            duration: card.dataset.planDuration,
            features: JSON.parse(card.dataset.planFeatures || '[]')
        };

        // Clear applied coupon when plan changes
        if (appliedCoupon) {
            removeCoupon();
        }

        updateUI();
        updatePricing();
    }

    // Coupon handling
    document.getElementById('apply-coupon-btn').addEventListener('click', applyCoupon);
    document.getElementById('coupon-code').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            applyCoupon();
        }
    });

    document.getElementById('remove-coupon-btn').addEventListener('click', removeCoupon);

    // Feature modal functionality
    document.querySelectorAll('.view-all-features-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const planId = this.dataset.planId;
            const planCard = document.querySelector(`[data-plan-id="${planId}"]`);
            showFeatureModal(planCard);
        });
    });

    document.getElementById('close-modal').addEventListener('click', hideFeatureModal);
    document.getElementById('feature-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideFeatureModal();
        }
    });

    document.getElementById('modal-select-plan').addEventListener('click', function() {
        const planId = this.dataset.planId;
        const planCard = document.querySelector(`[data-plan-id="${planId}"]`);
        if (planCard) {
            selectPlan(planCard);
            hideFeatureModal();
        }
    });

    function applyCoupon() {
        if (!selectedPlan) {
            showCouponMessage('Please select a plan first.', 'error');
            return;
        }

        const couponCode = document.getElementById('coupon-code').value.trim().toUpperCase();
        if (!couponCode) {
            showCouponMessage('Please enter a coupon code.', 'error');
            return;
        }

        const applyBtn = document.getElementById('apply-coupon-btn');
        const originalText = applyBtn.textContent;
        applyBtn.disabled = true;
        applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Applying...';

        fetch('<?php echo e(route("checkout.validate-coupon")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({
                coupon_code: couponCode,
                plan_id: selectedPlan.id,
                amount: currentBillingCycle === 'annual' ? selectedPlan.annualPrice : selectedPlan.price,
                billing_cycle: currentBillingCycle
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                appliedCoupon = data.coupon;
                currentPricing = {
                    original: data.pricing.original_amount,
                    discount: data.pricing.discount_amount,
                    tax: data.pricing.tax_amount || 0,
                    final: data.pricing.final_amount,
                    currency: data.pricing.currency_symbol
                };

                showAppliedCoupon(data.coupon);
                updatePricing();
                showCouponMessage(data.message, 'success');
                document.getElementById('coupon-code').value = '';

                // Add success animation
                applyBtn.classList.add('bg-green-600');
                setTimeout(() => {
                    applyBtn.classList.remove('bg-green-600');
                }, 1000);
            } else {
                showCouponMessage(data.message, 'error');
                // Add error animation
                document.getElementById('coupon-code').classList.add('border-red-500');
                setTimeout(() => {
                    document.getElementById('coupon-code').classList.remove('border-red-500');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showCouponMessage('Failed to apply coupon. Please try again.', 'error');
        })
        .finally(() => {
            applyBtn.disabled = false;
            applyBtn.textContent = originalText;
        });
    }

    function removeCoupon() {
        appliedCoupon = null;
        const basePrice = currentBillingCycle === 'annual' ? selectedPlan.annualPrice : selectedPlan.price;
        currentPricing = {
            original: basePrice,
            discount: 0,
            tax: 0,
            final: basePrice,
            currency: selectedPlan.symbol
        };

        document.getElementById('applied-coupon').classList.add('hidden');
        document.getElementById('coupon-code').value = '';
        document.getElementById('form-coupon-code').value = '';
        updatePricing();
        hideCouponMessage();
    }

    function showAppliedCoupon(coupon) {
        document.getElementById('applied-coupon-code').textContent = coupon.code;
        document.getElementById('applied-coupon-description').textContent = coupon.name || 'Coupon applied';
        document.getElementById('applied-coupon').classList.remove('hidden');
        document.getElementById('form-coupon-code').value = coupon.code;
    }

    function showCouponMessage(message, type) {
        const messageEl = document.getElementById('coupon-message');
        messageEl.textContent = message;
        messageEl.className = `mt-3 text-sm px-3 py-2 rounded-lg ${type === 'error' ? 'text-red-300 bg-red-900 border border-red-700' : 'text-green-300 bg-green-900 border border-green-700'}`;
        messageEl.classList.remove('hidden');

        setTimeout(() => {
            hideCouponMessage();
        }, 5000);
    }

    function hideCouponMessage() {
        document.getElementById('coupon-message').classList.add('hidden');
    }

    function showFeatureModal(planCard) {
        const planName = planCard.dataset.planName;
        const planPrice = planCard.dataset.planPrice;
        const planSymbol = planCard.dataset.planSymbol;
        const planFeatures = JSON.parse(planCard.dataset.planFeatures || '[]');
        const planId = planCard.dataset.planId;

        document.getElementById('modal-plan-name').textContent = `${planName} - All Features`;
        document.getElementById('modal-plan-price').textContent = `${planSymbol}${parseFloat(planPrice).toLocaleString()}`;
        document.getElementById('modal-select-plan').dataset.planId = planId;

        const featuresList = document.getElementById('modal-features-list');
        featuresList.innerHTML = '';

        planFeatures.forEach(feature => {
            const featureDiv = document.createElement('div');
            featureDiv.className = 'flex items-start space-x-3 p-3 bg-gray-50 rounded-lg';
            featureDiv.innerHTML = `
                <div class="flex-shrink-0 mt-1">
                    <i class="fas fa-check text-green-500"></i>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-gray-900">${feature.title}</div>
                    ${feature.description ? `<div class="text-sm text-gray-600 mt-1">${feature.description}</div>` : ''}
                </div>
            `;
            featuresList.appendChild(featureDiv);
        });

        document.getElementById('feature-modal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function hideFeatureModal() {
        document.getElementById('feature-modal').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    function updateUI() {
        const checkoutSection = document.getElementById('checkout-section');
        const emptyState = document.getElementById('empty-state');
        const checkoutBtn = document.getElementById('checkout-btn');
        const checkoutBtnText = document.getElementById('checkout-btn-text');

        if (selectedPlan) {
            document.getElementById('selected-plan-name').textContent = selectedPlan.name;
            document.getElementById('selected-plan-description').textContent = selectedPlan.description;
            document.getElementById('selected-plan-price').textContent =
                `${selectedPlan.symbol}${selectedPlan.price.toLocaleString(undefined, {
                    minimumFractionDigits: selectedPlan.currency === 'USD' ? 2 : 0,
                    maximumFractionDigits: selectedPlan.currency === 'USD' ? 2 : 0
                })}`;
            document.getElementById('form-plan-id').value = selectedPlan.id;

            checkoutSection.classList.remove('hidden');
            emptyState.classList.add('hidden');
            checkoutBtn.disabled = false;
            checkoutBtnText.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Proceed to Payment';
        } else {
            checkoutSection.classList.add('hidden');
            emptyState.classList.remove('hidden');
            checkoutBtn.disabled = true;
            checkoutBtnText.textContent = 'Select a Plan';
        }
    }

    function updatePricing() {
        if (!selectedPlan) return;

        // Calculate base price based on billing cycle
        let basePrice = currentBillingCycle === 'annual' ? selectedPlan.annualPrice : selectedPlan.price;

        const pricing = appliedCoupon ? currentPricing : {
            original: basePrice,
            discount: 0,
            tax: 0,
            final: basePrice,
            currency: selectedPlan.symbol
        };

        // Calculate tax on the amount after discount
        const afterDiscount = pricing.original - pricing.discount;
        const taxAmount = (afterDiscount * selectedPlan.taxPercentage) / 100;
        pricing.tax = taxAmount;
        pricing.final = afterDiscount + taxAmount;

        const decimals = selectedPlan.currency === 'USD' ? 2 : 0;

        document.getElementById('subtotal-amount').textContent =
            `${pricing.currency}${pricing.original.toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            })}`;

        if (pricing.discount > 0) {
            document.getElementById('discount-amount').textContent =
                `-${pricing.currency}${pricing.discount.toLocaleString(undefined, {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                })}`;
            document.getElementById('discount-row').classList.remove('hidden');
        } else {
            document.getElementById('discount-row').classList.add('hidden');
        }

        // Show tax if applicable
        if (selectedPlan.taxPercentage > 0 && pricing.tax > 0) {
            document.getElementById('tax-percentage').textContent = selectedPlan.taxPercentage;
            document.getElementById('tax-amount').textContent =
                `${pricing.currency}${pricing.tax.toLocaleString(undefined, {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                })}`;
            document.getElementById('tax-row').classList.remove('hidden');
        } else {
            document.getElementById('tax-row').classList.add('hidden');
        }

        document.getElementById('total-amount').textContent =
            `${pricing.currency}${pricing.final.toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            })}`;

        // Update checkout button text for free plans
        const checkoutBtnText = document.getElementById('checkout-btn-text');
        if (pricing.final === 0) {
            checkoutBtnText.innerHTML = '<i class="fas fa-gift mr-2"></i>Activate Free Plan';
        } else {
            checkoutBtnText.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Proceed to Payment';
        }

        // Update current pricing for coupon calculations
        currentPricing = pricing;
    }

    // Form submission
    document.getElementById('checkout-form').addEventListener('submit', function(e) {
        if (!selectedPlan) {
            e.preventDefault();
            showCouponMessage('Please select a plan first.', 'error');
            return;
        }

        const submitBtn = document.getElementById('checkout-btn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

        document.getElementById('loading-overlay').classList.remove('hidden');
    });

    // Mobile enhancements
    function addMobileEnhancements() {
        // Add touch feedback for plan cards
        document.querySelectorAll('.plan-card').forEach(card => {
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            card.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Smooth scrolling for mobile
        if (window.innerWidth < 1024) {
            document.querySelectorAll('.plan-card').forEach(card => {
                card.addEventListener('click', function() {
                    setTimeout(() => {
                        document.querySelector('.lg\\:w-1\\/2.bg-gray-900').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }, 300);
                });
            });
        }
    }

    // Initialize mobile enhancements
    addMobileEnhancements();

    // Handle window resize
    window.addEventListener('resize', function() {
        addMobileEnhancements();
    });

    // Add CSS animations and mobile styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .plan-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .plan-card:hover {
            transform: translateY(-2px);
        }

        @media (max-width: 1023px) {
            .lg\\:w-1\\/2 {
                min-height: 100vh;
            }

            #feature-modal .bg-white {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
            }

            .billing-btn, .currency-btn {
                font-size: 0.875rem;
                padding: 0.5rem 0.75rem;
            }
        }

        @media (max-width: 640px) {
            .plan-card {
                padding: 1rem;
            }

            .text-3xl {
                font-size: 1.875rem;
            }

            #feature-modal .bg-white {
                margin: 0.5rem;
                max-height: calc(100vh - 1rem);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/checkout/index.blade.php ENDPATH**/ ?>