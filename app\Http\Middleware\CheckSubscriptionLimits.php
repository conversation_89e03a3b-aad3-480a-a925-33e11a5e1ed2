<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\SubscriptionService;
use App\Services\SubscriptionUsageTracker;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionLimits
{
    protected $subscriptionService;
    protected $usageTracker;

    public function __construct(SubscriptionService $subscriptionService, SubscriptionUsageTracker $usageTracker)
    {
        $this->subscriptionService = $subscriptionService;
        $this->usageTracker = $usageTracker;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ?string $action = null): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // If no specific action is provided, just continue
        if (!$action) {
            return $next($request);
        }

        // Extract parameters based on action type
        switch ($action) {
            case 'connect_business':
                // Check if user can connect more businesses
                $validation = $this->subscriptionService->canConnectBusiness($user->id);
                break;

            case 'invite_team_member':
                // Get business ID from request
                $businessId = $request->route('business_id') ?? $request->input('business_id') ?? 0;
                $validation = $this->subscriptionService->canInviteTeamMember($user->id, $businessId);
                break;

            case 'send_reply':
                // Check if user can send more replies
                $validation = $this->subscriptionService->canSendReply($user->id);
                break;

            case 'data_export':
                $validation = $this->subscriptionService->hasFeatureAccess($user->id, 'data_export');
                break;

            case 'scheduled_auto_replies':
                $validation = $this->subscriptionService->hasFeatureAccess($user->id, 'scheduled_auto_replies');
                break;

            case 'settings_access':
                $validation = $this->subscriptionService->hasFeatureAccess($user->id, 'settings_access');
                break;

            case 'beta_features':
                $validation = $this->subscriptionService->hasFeatureAccess($user->id, 'beta_features');
                break;

            case 'api_basic':
                $validation = $this->subscriptionService->hasApiAccess($user->id, 'basic');
                break;

            case 'api_advanced':
                $validation = $this->subscriptionService->hasApiAccess($user->id, 'advanced');
                break;

            default:
                // Unknown action, allow by default
                return $next($request);
        }

        // If validation failed, redirect with error
        if (!$validation['allowed']) {
            // Get real-time usage data for upgrade suggestions
            $usageData = $this->usageTracker->getRealTimeUsage($user->id);

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Subscription limit exceeded',
                    'message' => $validation['message'],
                    'upgrade_required' => true,
                    'usage_data' => $usageData,
                    'current_count' => $validation['current_count'] ?? null,
                    'limit' => $validation['limit'] ?? null,
                    'action_type' => $action
                ], 403);
            }

            return redirect()->back()
                ->with('error', $validation['message'])
                ->with('upgrade_required', true)
                ->with('usage_data', $usageData)
                ->with('action_type', $action);
        }

        return $next($request);
    }
}
