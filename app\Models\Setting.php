<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $table = 'settings';

    protected $fillable = [
        'business_id',
        'ai_provider',
        'model_version',
        'response_style',
        'response_length',
        'custom_signoff_text',
        'custom_instruction',
        'response_language',
        'auto_check_reviews',
        'check_interval_minutes',
        'auto_reply',
        'auto_reply_settings',
        'review_length_filter',
        'last_review_fetched_at',
        'reply_timing',
        'timezone',
        'from_auto_reply_date',
    ];

    public function business()
    {
        return $this->belongsTo(Business::class, 'business_id', 'id');
    }

    public function businessAccount()
    {
        return $this->belongsTo(BusinessAccount::class, 'business_id');
    }
}
