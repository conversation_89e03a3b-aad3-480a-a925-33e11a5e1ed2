<?php $__env->startSection('content'); ?>
<?php $locationId = explode("/", $business['location_name'])[1]; ?>
<div class="max-w-[1200px] mx-auto px-4 md:px-6 py-6 md:py-8 bg-white">
    <div class="flex justify-between items-start gap-2 border-b border-gray-200 mb-4 pb-4">
        <!-- Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <button id="activityTabBtn" class="tab-btn active px-3 py-2 text-sm font-medium border-b-2 border-indigo-600 text-indigo-600 hidden" data-tab="activityTab">
                <i class="fas fa-star mr-1"></i> Reviews
            </button>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Business Activity Dashboard</h1>
                <p class="text-gray-600">Monitor your business activities and subscription usage</p>
            </div>
        </div>
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 flex items-center">
                <a href="<?php echo e(route('business.dashboard')); ?>" class="flex items-center px-2 py-1 md:px-4 md:py-2 bg-white-600 text-indigo-600 border ring-2 ring-indigo-600 rounded-md text-sm font-medium hover:text-white hover:bg-indigo-600">
                    <i class="fas fa-arrow-left mr-2"></i> Back <span class="hidden md:block ml-1">to Business</span>
                </a>
            </div>
        </div>
    </div>
    <!-- Filters -->
    <div class="flex justify-between items-center flex-wrap gap-3 mb-4 md:mt-0">
        <div class="flex items-center flex-wrap gap-3">
            <!-- Business Filter -->
            <select id="businessFilter" class="text-sm px-2 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Businesses</option>
                <?php $__currentLoopData = $businesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($business->id); ?>" <?php echo e($selectedBusiness && $selectedBusiness->id == $business->id ? 'selected' : ''); ?>>
                    <?php echo e($business->title); ?>

                </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
    
            <!-- Date Range Filter -->
            <select id="dateRangeFilter" class="text-sm px-2 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="7_days" <?php echo e($dateRange == '7_days' ? 'selected' : ''); ?>>Last 7 Days</option>
                <option value="30_days" <?php echo e($dateRange == '30_days' ? 'selected' : ''); ?>>Last 30 Days</option>
                <option value="90_days" <?php echo e($dateRange == '90_days' ? 'selected' : ''); ?>>Last 90 Days</option>
                <option value="1_year" <?php echo e($dateRange == '1_year' ? 'selected' : ''); ?>>Last Year</option>
                <option value="all_time" <?php echo e($dateRange == 'all_time' ? 'selected' : ''); ?>>All Time</option>
            </select>
        </div>

        <!-- Export Button -->
        <button id="exportBtn" class="text-sm px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <i class="fas fa-download mr-2"></i>Export CSV
        </button>
    </div>

    <!-- Usage Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Business Connections -->
        <div class="bg-white rounded-lg hover:shadow-md p-6 border border-[#efefef]">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Business Connections</h3>
                <i class="fas fa-building text-blue-500 text-xl"></i>
            </div>
            <div class="flex items-end justify-between">
                <div>
                    <p class="text-3xl font-bold text-gray-900"><?php echo e($usageStats['business_connections']['current']); ?></p>
                    <p class="text-sm text-gray-600">of <?php echo e($usageStats['business_connections']['limit']); ?> used</p>
                </div>
                <div class="w-16 h-16">
                    <div class="relative w-full h-full">
                        <div class="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                        <div class="absolute inset-0 rounded-full border-4 border-blue-500"
                            style="clip-path: polygon(50% 50%, 50% 0%, <?php echo e(50 + ($usageStats['business_connections']['percentage'] * 0.5)); ?>% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%)"></div>
                    </div>
                </div>
            </div>
            <?php if($usageStats['business_connections']['percentage'] > 80): ?>
            <div class="mt-3 p-2 bg-yellow-100 border border-yellow-300 rounded text-sm text-yellow-800">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                Approaching limit - consider upgrading
            </div>
            <?php endif; ?>
        </div>

        <!-- Monthly Replies -->
        <div class="bg-white rounded-lg hover:shadow-md p-6 border border-[#efefef]">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Monthly Replies</h3>
                <i class="fas fa-reply text-green-500 text-xl"></i>
            </div>
            <div class="flex items-end justify-between">
                <div>
                    <p class="text-3xl font-bold text-gray-900"><?php echo e($usageStats['monthly_replies']['current']); ?></p>
                    <p class="text-sm text-gray-600">
                        <?php if($usageStats['monthly_replies']['unlimited']): ?>
                        Unlimited plan
                        <?php else: ?>
                        of <?php echo e($usageStats['monthly_replies']['limit']); ?> used
                        <?php endif; ?>
                    </p>
                </div>
                <?php if(!$usageStats['monthly_replies']['unlimited']): ?>
                <div class="w-16 h-16">
                    <div class="relative w-full h-full">
                        <div class="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                        <div class="absolute inset-0 rounded-full border-4 border-green-500"
                            style="clip-path: polygon(50% 50%, 50% 0%, <?php echo e(50 + ($usageStats['monthly_replies']['percentage'] * 0.5)); ?>% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%)"></div>
                    </div>
                </div>
                <?php else: ?>
                <div class="text-green-500">
                    <i class="fas fa-infinity text-2xl"></i>
                </div>
                <?php endif; ?>
            </div>
            <?php if(!$usageStats['monthly_replies']['unlimited'] && $usageStats['monthly_replies']['percentage'] > 80): ?>
            <div class="mt-3 p-2 bg-yellow-100 border border-yellow-300 rounded text-sm text-yellow-800">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                Approaching limit - consider upgrading
            </div>
            <?php endif; ?>
        </div>

        <!-- Total Activities -->
        <div class="bg-white rounded-lg hover:shadow-md p-6 border border-[#efefef]">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Total Activities</h3>
                <i class="fas fa-chart-line text-purple-500 text-xl"></i>
            </div>
            <div>
                <p class="text-3xl font-bold text-gray-900"><?php echo e($stats['total_activities']); ?></p>
                <p class="text-sm text-gray-600">in selected period</p>
            </div>
        </div>
    </div>

    <!-- Activity Breakdown Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Activity Types -->
        <div class="bg-white rounded-lg hover:shadow-md p-6 border border-[#efefef]">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity Types</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $stats['activity_breakdown']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600"><?php echo e(ucfirst(str_replace('_', ' ', $type))); ?></span>
                    <div class="flex items-center">
                        <div class="w-24 h-2 bg-gray-200 rounded-full mr-3">
                            <div class="h-2 bg-blue-500 rounded-full" style="width: <?php echo e($stats['total_activities'] > 0 ? ($count / $stats['total_activities']) * 100 : 0); ?>%"></div>
                        </div>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($count); ?></span>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Performer Breakdown -->
        <div class="bg-white rounded-lg hover:shadow-md p-6 border border-[#efefef]">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Performed By</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $stats['performer_breakdown']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $performer => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600"><?php echo e(ucfirst($performer)); ?></span>
                    <div class="flex items-center">
                        <div class="w-24 h-2 bg-gray-200 rounded-full mr-3">
                            <div class="h-2 bg-green-500 rounded-full" style="width: <?php echo e($stats['total_activities'] > 0 ? ($count / $stats['total_activities']) * 100 : 0); ?>%"></div>
                        </div>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($count); ?></span>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Team Performance -->
        <div class="bg-white rounded-lg hover:shadow-md p-6 border border-[#efefef]">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Team Performance</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Total Members</span>
                    <span class="text-sm font-medium text-gray-900"><?php echo e($teamAnalytics['summary']['total_members']); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Active Members</span>
                    <span class="text-sm font-medium text-gray-900"><?php echo e($teamAnalytics['summary']['active_members']); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Avg Activities/Member</span>
                    <span class="text-sm font-medium text-gray-900"><?php echo e($teamAnalytics['summary']['avg_activities_per_member']); ?></span>
                </div>
                <?php if($teamAnalytics['summary']['most_active_member']): ?>
                <div class="mt-3 p-2 bg-blue-50 rounded">
                    <div class="text-xs text-blue-600 font-medium">Most Active</div>
                    <div class="text-sm text-blue-900"><?php echo e($teamAnalytics['summary']['most_active_member']['name']); ?></div>
                    <div class="text-xs text-blue-600"><?php echo e($teamAnalytics['summary']['most_active_member']['activity_count']); ?> activities</div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Team Analytics Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Team Members -->
        <div class="bg-white rounded-lg hover:shadow-md border border-[#efefef] overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Team Members</h3>
                <?php if($teamValidation['can_invite']): ?>
                <span class="text-sm text-green-600"><?php echo e($teamValidation['remaining']); ?> slots remaining</span>
                <?php else: ?>
                <span class="text-sm text-red-600">Limit reached (<?php echo e($teamValidation['current_count']); ?>/<?php echo e($teamValidation['limit']); ?>)</span>
                <?php endif; ?>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php $__currentLoopData = $teamAnalytics['team_members']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center flex-wrap justify-between gap-2 p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 flex-shrink-0 bg-<?php echo e($member['is_owner'] ? 'blue' : 'gray'); ?>-500 rounded-full flex items-center justify-center text-white font-medium">
                                <?php echo e(strtoupper(substr($member['name'], 0, 1))); ?>

                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900 flex items-center gap-1">
                                    <?php echo e($member['name']); ?>

                                    <?php if($member['is_owner']): ?>
                                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">Owner</span>
                                    <?php else: ?>
                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded"><?php echo e(ucfirst($member['role'])); ?></span>
                                    <?php endif; ?>
                                </div>
                                <div class="text-xs text-gray-500 break-all"><?php echo e($member['email']); ?></div>
                                <?php if($member['last_activity']): ?>
                                <div class="text-xs text-gray-400">Last active: <?php echo e(\Carbon\Carbon::parse($member['last_activity'])->diffForHumans()); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="text-right w-full sm:w-auto text-right">
                            <div class="flex items-center gap-1.5 justify-end">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($member['activity_count']); ?></div>
                                <div class="text-xs text-gray-500">activities</div>
                                <div class="mt-0">
                                    <span class="px-2 py-1 text-xs rounded-full <?php echo e($member['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo e(ucfirst($member['status'])); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Team Member Validation Alert -->
                <?php if($teamValidation['upgrade_required']): ?>
                <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                        <div>
                            <div class="text-sm font-medium text-red-800">Team Member Limit Reached</div>
                            <div class="text-xs text-red-600"><?php echo e($teamValidation['message']); ?></div>
                        </div>
                    </div>
                </div>
                <?php elseif($teamValidation['percentage'] >= 80): ?>
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-yellow-500 mr-2"></i>
                        <div>
                            <div class="text-sm font-medium text-yellow-800">Approaching Limit</div>
                            <div class="text-xs text-yellow-600"><?php echo e($teamValidation['message']); ?></div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Team Activity Timeline -->
        <div class="bg-white rounded-lg hover:shadow-md border border-[#efefef] overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Recent Team Activities</h3>
            </div>
            <div class="p-6">
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    <?php $__currentLoopData = $teamAnalytics['activity_timeline']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm text-gray-900"><?php echo e($activity['description']); ?></div>
                            <div class="text-xs text-gray-500">
                                by <?php echo e($activity['performed_by']); ?> • <?php echo e(\Carbon\Carbon::parse($activity['date'])->diffForHumans()); ?>

                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white rounded-lg hover:shadow-md border border-[#efefef] overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo e($activity->created_at->format('M d, Y H:i')); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo e($activity->business?->title ?? 'N/A'); ?>

                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div>
                                <p class="font-medium"><?php echo e($activity->getActivityTypeDisplayAttribute()); ?></p>
                                <p class="text-gray-500 text-xs"><?php echo e($activity->activity_description); ?></p>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo e($activity->getPerformedByDisplayNameAttribute()); ?>

                            <span class="text-xs text-gray-500">(<?php echo e($activity->getPerformedByTypeDisplayAttribute()); ?>)</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?php echo e($activity->status === 'success' ? 'bg-green-100 text-green-800' : 
                                       ($activity->status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800')); ?>">
                                <?php echo e(ucfirst($activity->status)); ?>

                            </span>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            No activities found for the selected period.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($activities->hasPages()): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <?php echo e($activities->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const businessFilter = document.getElementById('businessFilter');
        const dateRangeFilter = document.getElementById('dateRangeFilter');
        const exportBtn = document.getElementById('exportBtn');

        // Handle filter changes
        function updateFilters() {
            const params = new URLSearchParams(window.location.search);

            if (businessFilter.value) {
                params.set('business_id', businessFilter.value);
            } else {
                params.delete('business_id');
            }

            if (dateRangeFilter.value) {
                params.set('date_range', dateRangeFilter.value);
            } else {
                params.delete('date_range');
            }

            window.location.search = params.toString();
        }

        businessFilter.addEventListener('change', updateFilters);
        dateRangeFilter.addEventListener('change', updateFilters);

        // Handle export
        exportBtn.addEventListener('click', function() {
            const params = new URLSearchParams(window.location.search);
            params.set('format', 'csv');

            const exportUrl = '<?php echo e(route("activity.export")); ?>?' + params.toString();
            window.open(exportUrl, '_blank');
        });
    });
</script>


<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/activity-dashboard.blade.php ENDPATH**/ ?>