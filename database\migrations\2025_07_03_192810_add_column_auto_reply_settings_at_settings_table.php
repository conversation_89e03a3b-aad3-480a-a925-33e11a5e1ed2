<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn(['auto_reply_rating_min', 'auto_reply_rating_max', 'template_selection', 'reply_tone', 'sentiment_filter']);
            $table->json('auto_reply_settings')->nullable()->after('auto_reply');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->tinyInteger('auto_reply_rating_min')->unsigned()->nullable()->after('auto_reply');
            $table->tinyInteger('auto_reply_rating_max')->unsigned()->nullable()->after('auto_reply_rating_min');
            $table->string('template_selection', 255)->default('AUTO_SELECT');
            $table->string('reply_tone', 50)->default('PROFESSIONAL');
            $table->string('sentiment_filter', 100)->default('ANY');
            $table->dropColumn('auto_reply_settings');
        });
    }
};
