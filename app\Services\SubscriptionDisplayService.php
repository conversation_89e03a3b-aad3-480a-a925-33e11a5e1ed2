<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use App\Models\Business;
use App\Models\TeamMember;
use App\Models\ReplyLog;
use Carbon\Carbon;

class SubscriptionDisplayService
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Get formatted subscription details for display
     */
    public function getSubscriptionDisplay(int $userId): array
    {
        $subscription = $this->subscriptionService->getActiveSubscription($userId);
        
        if (!$subscription) {
            return $this->getNoSubscriptionDisplay();
        }

        $plan = $subscription->plan;
        // Use subscription features if available, fallback to plan features
        $featuresData = $subscription->features ?? $plan->features;
        $features = $this->formatFeaturesForDisplay($featuresData);
        $usage = $this->getCurrentUsage($userId, $subscription);

        return [
            'subscription' => [
                'id' => $subscription->id,
                'plan_name' => $plan->name,
                'plan_description' => $plan->short_description,
                'status' => $subscription->status,
                'start_date' => $subscription->start_date->format('M d, Y'),
                'expiry_date' => $subscription->expiry_date->format('M d, Y'),
                'days_remaining' => Carbon::parse($subscription->expiry_date)->diffInDays(now()),
                'is_expired' => Carbon::parse($subscription->expiry_date)->isPast(),
                'currency' => $plan->currency,
                'currency_symbol' => $plan->currency_symbol,
                'price' => $plan->price,
                'annual_price' => $plan->annual_price,
                'annual_discount' => $plan->annual_discount_percentage
            ],
            'features' => $features,
            'usage' => $usage,
            'limits' => $this->getLimitsDisplay($plan),
            'upgrade_suggestions' => $this->getUpgradeSuggestions($userId, $plan)
        ];
    }

    /**
     * Format plan features for customer display
     */
    private function formatFeaturesForDisplay(array $features): array
    {
        $formatted = [];
        
        foreach ($features as $feature) {
            $formatted[] = [
                'title' => $feature['title'],
                'value' => $this->formatFeatureValue($feature['key'], $feature['value']),
                'key' => $feature['key'],
                'raw_value' => $feature['value'],
                'is_boolean' => is_bool($feature['value']),
                'is_enabled' => $this->isFeatureEnabled($feature['value'])
            ];
        }

        return $formatted;
    }

    /**
     * Format feature values for display
     */
    private function formatFeatureValue(string $key, $value): string
    {
        switch ($key) {
            case 'business_connections_limit':
                return $value . ' business' . ($value > 1 ? 'es' : '');
                
            case 'team_members_limit':
                return $value . ' team member' . ($value > 1 ? 's' : '');
                
            case 'monthly_reply_limit':
                return $value === -1 ? 'Unlimited' : number_format($value) . ' per month';
                
            case 'support_level':
                return $this->formatSupportLevel($value);
                
            case 'api_access_level':
                return $this->formatApiLevel($value);
                
            case 'analytics_level':
                return ucfirst($value) . ' Analytics';
                
            case 'ai_customization_level':
                return $value === 'custom_training' ? 'Custom AI Training' : 'Default AI';
                
            case 'template_access':
                return ucfirst($value) . ' Templates';
                
            default:
                if (is_bool($value)) {
                    return $value ? 'Yes' : 'No';
                }
                return (string) $value;
        }
    }

    /**
     * Get current usage statistics
     */
    private function getCurrentUsage(int $userId, Subscription $subscription): array
    {
        $businessCount = Business::where('user_id', $userId)->count();
        $replyCount = ReplyLog::getMonthlyReplyCount($userId, $subscription->id);
        
        // Get team member count across all businesses
        $teamMemberCount = TeamMember::whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('status', 'active')->count() + 1; // +1 for owner

        return [
            'businesses' => [
                'current' => $businessCount,
                'limit' => $subscription->plan->getBusinessConnectionsLimit(),
                'percentage' => $this->calculateUsagePercentage($businessCount, $subscription->plan->getBusinessConnectionsLimit())
            ],
            'team_members' => [
                'current' => $teamMemberCount,
                'limit' => $subscription->plan->getTeamMembersLimit(),
                'percentage' => $this->calculateUsagePercentage($teamMemberCount, $subscription->plan->getTeamMembersLimit())
            ],
            'replies' => [
                'current' => $replyCount,
                'limit' => $subscription->plan->getMonthlyReplyLimit(),
                'remaining' => $subscription->plan->hasUnlimitedReplies() ? -1 : max(0, $subscription->plan->getMonthlyReplyLimit() - $replyCount),
                'percentage' => $subscription->plan->hasUnlimitedReplies() ? 0 : $this->calculateUsagePercentage($replyCount, $subscription->plan->getMonthlyReplyLimit()),
                'is_unlimited' => $subscription->plan->hasUnlimitedReplies()
            ]
        ];
    }

    /**
     * Get plan limits for display
     */
    private function getLimitsDisplay($plan): array
    {
        return [
            'businesses' => $plan->getBusinessConnectionsLimit(),
            'team_members' => $plan->getTeamMembersLimit(),
            'monthly_replies' => $plan->hasUnlimitedReplies() ? 'Unlimited' : number_format($plan->getMonthlyReplyLimit()),
            'has_data_export' => $plan->hasFeature('data_export_enabled'),
            'has_scheduled_replies' => $plan->hasFeature('scheduled_auto_replies_enabled'),
            'api_access' => $plan->getFeatureValue('api_access_level'),
            'analytics_level' => $plan->getFeatureValue('analytics_level'),
            'has_settings_access' => $plan->hasFeature('settings_access_enabled'),
            'has_beta_features' => $plan->hasFeature('beta_features_enabled')
        ];
    }

    /**
     * Get upgrade suggestions based on usage
     */
    private function getUpgradeSuggestions(int $userId, $plan): array
    {
        $suggestions = [];
        $usage = $this->getCurrentUsage($userId, Subscription::where('user_id', $userId)->where('status', 'ACTIVE')->first());

        // Business limit suggestions
        if ($usage['businesses']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'business_limit',
                'message' => 'You\'re approaching your business connection limit. Consider upgrading for more connections.',
                'urgency' => $usage['businesses']['percentage'] > 95 ? 'high' : 'medium'
            ];
        }

        // Reply limit suggestions
        if (!$usage['replies']['is_unlimited'] && $usage['replies']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'reply_limit',
                'message' => 'You\'re approaching your monthly reply limit. Upgrade for more replies or unlimited access.',
                'urgency' => $usage['replies']['percentage'] > 95 ? 'high' : 'medium'
            ];
        }

        // Feature suggestions
        if (!$plan->hasFeature('data_export_enabled')) {
            $suggestions[] = [
                'type' => 'feature',
                'message' => 'Upgrade to export your review data and analytics.',
                'urgency' => 'low'
            ];
        }

        return $suggestions;
    }

    /**
     * Helper methods
     */
    private function calculateUsagePercentage(int $current, int $limit): float
    {
        if ($limit <= 0) return 0;
        return min(100, ($current / $limit) * 100);
    }

    private function isFeatureEnabled($value): bool
    {
        if (is_bool($value)) return $value;
        if (is_string($value)) return !in_array(strtolower($value), ['no', 'none', 'false', '0']);
        if (is_numeric($value)) return $value > 0;
        return false;
    }

    private function formatSupportLevel(string $level): string
    {
        switch ($level) {
            case 'email': return 'Email Support';
            case 'email_phone': return 'Email + Phone Support';
            case 'dedicated_manager': return 'Dedicated Account Manager';
            default: return 'Email Support';
        }
    }

    private function formatApiLevel(string $level): string
    {
        switch ($level) {
            case 'none': return 'No API Access';
            case 'basic': return 'Basic API Access';
            case 'advanced': return 'Advanced API Access';
            default: return 'No API Access';
        }
    }

    private function getNoSubscriptionDisplay(): array
    {
        return [
            'subscription' => null,
            'features' => [],
            'usage' => [
                'businesses' => ['current' => 0, 'limit' => 0, 'percentage' => 0],
                'team_members' => ['current' => 0, 'limit' => 0, 'percentage' => 0],
                'replies' => ['current' => 0, 'limit' => 0, 'remaining' => 0, 'percentage' => 0, 'is_unlimited' => false]
            ],
            'limits' => [],
            'upgrade_suggestions' => [
                [
                    'type' => 'no_subscription',
                    'message' => 'Subscribe to a plan to start managing your business reviews.',
                    'urgency' => 'high'
                ]
            ]
        ];
    }
}
