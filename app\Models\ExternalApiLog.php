<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExternalApiLog extends Model
{
    use HasFactory;

    protected $table = 'external_api_logs';

    protected $fillable = [
        'log_type',
        'endpoint',
        'request_payload',
        'response_payload',
        'status_code',
        'status_message',
        'duration_ms',
        'business_id',
        'user_id',
    ];

    protected $casts = [
        'request_payload' => 'array',
        'response_payload' => 'array',
    ];

    // Optionally, add relationships to Business and User
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
