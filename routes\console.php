<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule::command('google:tokens:refresh')->everyMinute();
//Schedule::command('google:refresh-tokens')->everyMinute();
Schedule::command('google:tokens:refresh')->everyThirtyMinutes();

// Run the new fetch-google-reviews command daily at midnight
// This will dispatch jobs to fetch latest reviews for all businesses
Schedule::command('fetch-google-reviews')->everyMinute();
