<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Business;
use App\Models\TeamMember;
use App\Models\ReplyLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class BillingService
{
    protected $subscriptionService;
    protected $subscriptionDisplayService;

    public function __construct(SubscriptionService $subscriptionService, SubscriptionDisplayService $subscriptionDisplayService)
    {
        $this->subscriptionService = $subscriptionService;
        $this->subscriptionDisplayService = $subscriptionDisplayService;
    }

    /**
     * Get comprehensive billing details for a user
     */
    public function getBillingDetails(int $userId): array
    {
        return [
            'active_subscription' => $this->getActiveSubscriptionDetails($userId),
            'past_subscriptions' => $this->getPastSubscriptions($userId),
            'payment_history' => $this->getPaymentHistory($userId),
            'current_usage' => $this->getCurrentUsageDetails($userId),
            'billing_summary' => $this->getBillingSummary($userId)
        ];
    }

    /**
     * Get active subscription details
     */
    public function getActiveSubscriptionDetails(int $userId): ?array
    {
        $subscription = $this->subscriptionService->getActiveSubscription($userId);
        
        if (!$subscription) {
            return null;
        }

        $plan = $subscription->plan;
        $payment = $subscription->payment;

        return [
            'id' => $subscription->id,
            'plan_name' => $plan->name,
            'plan_description' => $plan->short_description,
            'status' => $subscription->status,
            'start_date' => $subscription->start_date->format('M d, Y'),
            'expiry_date' => $subscription->expiry_date->format('M d, Y'),
            'days_remaining' => Carbon::parse($subscription->expiry_date)->diffInDays(now()),
            'is_expired' => Carbon::parse($subscription->expiry_date)->isPast(),
            'currency' => $plan->currency,
            'currency_symbol' => $plan->currency_symbol,
            'price' => $plan->price,
            'annual_price' => $plan->annual_price,
            'annual_discount' => $plan->annual_discount_percentage,
            'duration_days' => $plan->duration_days,
            'features' => $this->formatSubscriptionFeatures($subscription->features ?? $plan->features),
            'payment_info' => $payment ? [
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'payment_date' => $payment->payment_date->format('M d, Y'),
                'payment_method' => $payment->payment_method,
                'gateway' => $payment->gateway_name,
                'transaction_id' => $payment->transaction_id
            ] : null
        ];
    }

    /**
     * Get past subscriptions
     */
    public function getPastSubscriptions(int $userId): array
    {
        $subscriptions = Subscription::with(['plan', 'payment'])
            ->where('user_id', $userId)
            ->where(function($query) {
                $query->where('status', '!=', 'ACTIVE')
                      ->orWhere('expiry_date', '<', now());
            })
            ->orderBy('created_at', 'desc')
            ->get();

        return $subscriptions->map(function($subscription) {
            $plan = $subscription->plan;
            $payment = $subscription->payment;

            return [
                'id' => $subscription->id,
                'plan_name' => $plan->name,
                'status' => $subscription->status,
                'start_date' => $subscription->start_date->format('M d, Y'),
                'expiry_date' => $subscription->expiry_date->format('M d, Y'),
                'duration' => $subscription->start_date->diffInDays($subscription->expiry_date) . ' days',
                'currency_symbol' => $plan->currency_symbol,
                'price' => $plan->price,
                'payment_info' => $payment ? [
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date->format('M d, Y'),
                    'payment_method' => $payment->payment_method,
                    'gateway' => $payment->gateway_name
                ] : null
            ];
        })->toArray();
    }

    /**
     * Get payment history
     */
    public function getPaymentHistory(int $userId): array
    {
        $payments = Payment::with(['plan'])
            ->where('user_id', $userId)
            ->orderBy('payment_date', 'desc')
            ->get();

        return $payments->map(function($payment) {
            return [
                'id' => $payment->id,
                'plan_name' => $payment->plan ? $payment->plan->name : 'N/A',
                'amount' => $payment->amount,
                'original_amount' => $payment->original_amount,
                'discount_amount' => $payment->discount_amount,
                'tax_amount' => $payment->tax_amount,
                'currency' => $payment->currency,
                'payment_status' => $payment->payment_status,
                'payment_method' => $payment->payment_method,
                'gateway_name' => $payment->gateway_name,
                'transaction_id' => $payment->transaction_id,
                'order_id' => $payment->order_id,
                'coupon_code' => $payment->coupon_code,
                'billing_cycle' => $payment->billing_cycle,
                'payment_date' => $payment->payment_date->format('M d, Y H:i'),
                'refund_id' => $payment->refund_id,
                'refund_date' => $payment->refund_date ? $payment->refund_date->format('M d, Y H:i') : null,
                'is_refunded' => $payment->payment_status === 'REFUNDED'
            ];
        })->toArray();
    }

    /**
     * Get current usage details
     */
    public function getCurrentUsageDetails(int $userId): array
    {
        $subscription = $this->subscriptionService->getActiveSubscription($userId);
        
        if (!$subscription) {
            return [
                'has_subscription' => false,
                'message' => 'No active subscription found'
            ];
        }

        $businessCount = Business::where('user_id', $userId)->count();
        $replyCount = ReplyLog::getMonthlyReplyCount($userId, $subscription->id);
        
        // Get team member count across all businesses
        $teamMemberCount = TeamMember::whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('status', 'active')->count() + 1; // +1 for owner

        $plan = $subscription->plan;

        return [
            'has_subscription' => true,
            'subscription_id' => $subscription->id,
            'plan_name' => $plan->name,
            'businesses' => [
                'current' => $businessCount,
                'limit' => $plan->getBusinessConnectionsLimit(),
                'percentage' => $this->calculateUsagePercentage($businessCount, $plan->getBusinessConnectionsLimit()),
                'remaining' => max(0, $plan->getBusinessConnectionsLimit() - $businessCount)
            ],
            'team_members' => [
                'current' => $teamMemberCount,
                'limit' => $plan->getTeamMembersLimit(),
                'percentage' => $this->calculateUsagePercentage($teamMemberCount, $plan->getTeamMembersLimit()),
                'remaining' => max(0, $plan->getTeamMembersLimit() - $teamMemberCount)
            ],
            'replies' => [
                'current' => $replyCount,
                'limit' => $plan->getMonthlyReplyLimit(),
                'remaining' => $plan->hasUnlimitedReplies() ? -1 : max(0, $plan->getMonthlyReplyLimit() - $replyCount),
                'percentage' => $plan->hasUnlimitedReplies() ? 0 : $this->calculateUsagePercentage($replyCount, $plan->getMonthlyReplyLimit()),
                'is_unlimited' => $plan->hasUnlimitedReplies(),
                'reset_date' => now()->endOfMonth()->format('M d, Y')
            ]
        ];
    }

    /**
     * Get billing summary
     */
    public function getBillingSummary(int $userId): array
    {
        $totalSpent = Payment::where('user_id', $userId)
            ->where('payment_status', 'SUCCESS')
            ->sum('amount');

        $totalRefunded = Payment::where('user_id', $userId)
            ->where('payment_status', 'REFUNDED')
            ->sum('amount');

        $subscriptionCount = Subscription::where('user_id', $userId)->count();
        $activeSubscription = $this->subscriptionService->getActiveSubscription($userId);

        return [
            'total_spent' => $totalSpent,
            'total_refunded' => $totalRefunded,
            'net_spent' => $totalSpent - $totalRefunded,
            'subscription_count' => $subscriptionCount,
            'has_active_subscription' => $activeSubscription !== null,
            'currency' => $activeSubscription ? $activeSubscription->plan->currency : 'INR',
            'currency_symbol' => $activeSubscription ? $activeSubscription->plan->currency_symbol : '₹'
        ];
    }

    /**
     * Format subscription features for display
     */
    private function formatSubscriptionFeatures(array $features): array
    {
        $formatted = [];
        
        foreach ($features as $feature) {
            $formatted[] = [
                'title' => $feature['title'],
                'value' => $this->formatFeatureValue($feature['key'], $feature['value']),
                'key' => $feature['key'],
                'raw_value' => $feature['value'],
                'is_enabled' => $this->isFeatureEnabled($feature['value'])
            ];
        }

        return $formatted;
    }

    /**
     * Format feature values for display
     */
    private function formatFeatureValue(string $key, $value): string
    {
        switch ($key) {
            case 'business_connections_limit':
                return $value . ' business' . ($value > 1 ? 'es' : '');
                
            case 'team_members_limit':
                return $value . ' team member' . ($value > 1 ? 's' : '');
                
            case 'monthly_reply_limit':
                return $value === -1 ? 'Unlimited' : number_format(intval($value)) . ' per month';
                
            default:
                if (is_bool($value)) {
                    return $value ? 'Yes' : 'No';
                }
                return (string) $value;
        }
    }

    /**
     * Check if feature is enabled
     */
    private function isFeatureEnabled($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }
        
        if (is_numeric($value)) {
            return $value > 0;
        }
        
        return !empty($value);
    }

    /**
     * Calculate usage percentage
     */
    private function calculateUsagePercentage(int $current, int $limit): int
    {
        if ($limit <= 0) {
            return 0;
        }
        
        return min(100, round(($current / $limit) * 100));
    }
}
