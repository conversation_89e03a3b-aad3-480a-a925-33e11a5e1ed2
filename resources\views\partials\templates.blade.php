<div class="flex justify-end mb-4">
    <button id="addTemplateBtn" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
        <i class="fas fa-plus mr-2"></i> Add New Template
    </button>
</div>

<!--
Example of manual binding for template access validation:

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Manual binding example for template access
    if (typeof window.bindUpgradeValidation === 'function') {
        window.bindUpgradeValidation('addTemplateBtn', 'template_access', {}, function() {
            // Original functionality - called only if validation passes
            if (typeof showAddTemplateModal === 'function') {
                showAddTemplateModal();
            }
        });
    }
});
</script>
-->

<div class="bg-white rounded-xl shadow-sm p-4 md:p-6">
    <div class="flex justify-between items-center mb-4 flex-wrap">
        <h3 class="text-lg font-semibold">Response Templates</h3>
        <div class="text-sm text-gray-500">
            Templates are automatically selected based on review rating, sentiment, and length
        </div>
    </div>

    <div class="mb-4">
        <p class="text-sm text-gray-600">
            Create and manage templates for responding to different types of reviews.
            You can use variables like <code>"businessName"</code>, <code>"businessContact""</code>,
            <code>"reviewerFirstName"</code>, and <code>"reviewerLastName"</code> that will be
            automatically replaced with actual values.
        </p>
    </div>
    @foreach ($templates as $template)
    <div class="bg-white rounded-lg shadow-sm p-4 mb-4 border border-gray-200 hover:border-indigo-300 transition-colors">
        <div class="flex items-start mb-2 gap-4">
            <h3 class="font-medium text-gray-900 flex-shrink-0">{{ $template->title }}</h3>
            @if($template->title !== 'Default Template')
            <div class="flex gap-2 justify-between w-full">
                <button class="edit-template-btn text-indigo-600 hover:text-indigo-800 editTemplateBtn" data-index="{{ $template->id }}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-template-btn text-red-600 hover:text-red-800 deleteTemplateBtn" data-index="{{ $template->id }}">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
            @endif
        </div>
        <p class="text-sm text-gray-600 mb-2">{{ $template->description }}</p>
        <div class="text-xs text-gray-500 mb-2">
            @if($template->title !== 'Default Template')
            <div class="flex flex-wrap gap-2 mb-1">
                <span class="inline-block bg-blue-100 text-blue-800 rounded px-2 py-1">
                    <i class="fas fa-star mr-1"></i> {{ $template->ratings }}
                </span>
                <span class="inline-block bg-purple-100 text-purple-800 rounded px-2 py-1">
                    <i class="fas fa-comment mr-1"></i> {{ $template->sentiment }}
                </span>
                <span class="inline-block bg-green-100 text-green-800 rounded px-2 py-1">
                    <i class="fas fa-text-height mr-1"></i> {{ $template->length }}
                </span>
            </div>
            @endif
        </div>
        <div class="bg-gray-50 p-2 rounded text-sm text-gray-700 mb-2 whitespace-pre-line">
            {{ $template->template_text }}
        </div>
    </div>
    @endforeach

</div>