<!-- Upgrade Subscription Modal -->
<div id="upgradeSubscriptionModal" class="transition-opacity duration-300 ease-out opacity-100 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden ">
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm px-4 md:px-4">
    <div class="absolute md:relative top-0 bg-white md:rounded-2xl shadow-lg w-full md:w-auto h-full md:h-auto md:min-h-[400px] md:max-h-[90vh] flex flex-col overflow-hidden">
      <!-- Close button -->
      <!-- <button id="closeUpgradeModal" class="absolute top-4 right-4 z-10 h-8 w-8 rounded-full bg-white/90 hover:bg-white flex items-center justify-center">
        ✕
      </button> -->
     

      <!-- Header -->
      <div class="bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 p-6 text-white relative flex items-center">
        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>        
        <div class="relative z-10 flex flex-col items-center justify-start gap-3">
          <h2 class="text-xl text-white">Upgrade Your Plan</h2>
          <p class="text-blue-100" id="upgradeModalSubtitle">Unlock more features and capabilities with our higher tier plans</p>
        </div>
        <button id="maybeLaterBtn" class="p-2 w-[24px] h-[24px] border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 absolute right-4 z-10 flex items-center justify-center">
              <i class="fa-solid fa-xmark"></i>
          </button>
      </div>
      
      <!-- Upgrading Overlay -->
      <div id="upgradeModalLoading" class="md:absolute md:top-0 inset-0 bg-white bg-opacity-90 flex items-center justify-center h-full z-20 hidden">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4 mt-10"></div>
          <p class="text-gray-600">Loading upgrade options...</p>
        </div>
      </div>

      <!-- Content -->
      <div id="upgradeModalContent" class="flex-1 overflow-y-auto p-4 overflow-auto">
        <!-- Content will be loaded via AJAX -->
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p class="text-gray-600">Loading upgrade options...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Upgrade Subscription Modal functionality
class UpgradeSubscriptionModal {
  constructor() {
    this.modal = document.getElementById('upgradeSubscriptionModal');
    this.loadingOverlay = document.getElementById('upgradeModalLoading');
    this.contentContainer = document.getElementById('upgradeModalContent');
    this.subtitle = document.getElementById('upgradeModalSubtitle');
    this.isDataLoaded = false;

    this.initEventListeners();
  }

  initEventListeners() {
    // Close modal events
    document.getElementById('maybeLaterBtn')?.addEventListener('click', () => this.hide());

    // Close on outside click
    this.modal?.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.hide();
        
      }
    });

    // Escape key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
        this.hide();
      }
    });

    // Delegate click events for dynamically loaded content
    this.contentContainer?.addEventListener('click', (e) => {
      if (e.target.id === 'maybeLaterBtn') {
        this.hide();
      }
      if (e.target.classList.contains('upgrade-plan-btn')) {
        const planId = e.target.getAttribute('data-plan-id');
        this.selectPlan(planId);
      }
    });
  }

  // Close all other modals before showing upgrade modal
  closeOtherModals() {
    // Close common modals
    const modalsToClose = [
      'inviteTeamMemberModal',
      'templateModal',
      'editTemplateModal',
      'connectBusinessModal',
      'billingInfoModal',
      'changeRoleModal'
    ];

    modalsToClose.forEach(modalId => {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
        modal.classList.add('hidden');
      }
    });

    // Close any modal with common modal classes
    document.querySelectorAll('.modal, [id*="modal"], [id*="Modal"]').forEach(modal => {
      if (modal.id !== 'upgradeSubscriptionModal') {
        modal.style.display = 'none';
        modal.classList.add('hidden');
      }
    });
  }

  async show(limitType = null, usageData = null) {
    // Close other modals first
    this.closeOtherModals();

    // Show modal immediately
    this.modal.classList.remove('hidden');

    // Load content via AJAX
    try {
      this.loadingOverlay.classList.remove('hidden');
      this.loadingOverlay.style.display = 'flex';
      await this.loadContent();
    } catch (error) {
      console.error('Error loading upgrade modal content:', error);
      this.contentContainer.innerHTML = `
        <div class="text-center py-8">
          <p class="text-red-600 mb-4">Failed to load upgrade options</p>
          <button onclick="upgradeModalInstance.hide()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md">Close</button>
        </div>
      `;
    }
  }

  hide() {
    this.modal.classList.add('hidden');
  }

  async loadContent() {
    try {      
      const response = await fetch('/subscription/upgrade-modal-content', {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'text/html'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch upgrade modal content');
      }

      const html = await response.text();
      this.contentContainer.innerHTML = html;
      this.loadingOverlay.classList.add('hidden');
      this.loadingOverlay.style.display = 'none';
      this.contentContainer.style.display = 'block';

      // Initialize slider after content is loaded
      this.initializeSlider();
      
    } catch (error) {
      console.error('Error loading upgrade modal content:', error);
      throw error;
    }
  }

  selectPlan(planId) {
    // Redirect to checkout with selected plan
    window.location.href = `/checkout?plan=${planId}&upgrade=true`;
  }

  initializeSlider() {
    // Small delay to ensure DOM elements are rendered
    setTimeout(() => {
      const sliderElement = document.getElementById('planSlider');
      console.log('Initializing slider, found element:', sliderElement);

      if (sliderElement) {
        if (window.planSlider) {
          // Destroy existing slider
          window.planSlider = null;
        }
        window.planSlider = new PlanSlider();
        console.log('Slider initialized successfully');
      } else {
        console.log('Slider element not found');
      }
    }, 100);
  }

}

// Mobile plan slider functionality
class PlanSlider {
  constructor() {
    console.log('PlanSlider constructor called');
    this.currentSlide = 0;
    this.totalSlides = document.querySelectorAll('.plan-indicator').length;
    this.slider = document.getElementById('planSlider');
    this.indicators = document.querySelectorAll('.plan-indicator');
    this.prevBtn = document.getElementById('prevSlide');
    this.nextBtn = document.getElementById('nextSlide');
    this.slideNumber = document.getElementById('currentSlideNumber');

    console.log('Slider elements found:', {
      slider: this.slider,
      totalSlides: this.totalSlides,
      prevBtn: this.prevBtn,
      nextBtn: this.nextBtn,
      indicators: this.indicators.length
    });

    this.startX = 0;
    this.currentX = 0;
    this.isDragging = false;

    this.init();
  }

  init() {
    if (!this.slider) {
      console.log('Slider element not found, aborting initialization');
      return;
    }

    console.log('Initializing slider event listeners');

    // Add event listeners
    if (this.prevBtn) {
      this.prevBtn.addEventListener('click', () => {
        console.log('Previous button clicked');
        this.previousSlide();
      });
      console.log('Previous button listener added');
    }

    if (this.nextBtn) {
      this.nextBtn.addEventListener('click', () => {
        console.log('Next button clicked');
        this.nextSlide();
      });
      console.log('Next button listener added');
    }

    // Indicator clicks
    this.indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => this.goToSlide(index));
    });

    // Touch events for swipe
    this.slider.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
    this.slider.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: true });
    this.slider.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

    // Mouse events for drag (desktop testing)
    this.slider.addEventListener('mousedown', (e) => this.handleMouseDown(e));
    this.slider.addEventListener('mousemove', (e) => this.handleMouseMove(e));
    this.slider.addEventListener('mouseup', (e) => this.handleMouseUp(e));
    this.slider.addEventListener('mouseleave', (e) => this.handleMouseUp(e));

    // Keyboard navigation
    document.addEventListener('keydown', (e) => this.handleKeyDown(e));

    this.updateSlider();
  }

  handleTouchStart(e) {
    this.startX = e.touches[0].clientX;
    this.isDragging = true;
  }

  handleTouchMove(e) {
    if (!this.isDragging) return;
    this.currentX = e.touches[0].clientX;
  }

  handleTouchEnd(e) {
    if (!this.isDragging) return;

    const diffX = this.startX - this.currentX;
    const threshold = 50; // Minimum swipe distance

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0) {
        this.nextSlide();
      } else {
        this.previousSlide();
      }
    }

    this.isDragging = false;
  }

  handleMouseDown(e) {
    this.startX = e.clientX;
    this.isDragging = true;
    this.slider.style.cursor = 'grabbing';
  }

  handleMouseMove(e) {
    if (!this.isDragging) return;
    this.currentX = e.clientX;
  }

  handleMouseUp(e) {
    if (!this.isDragging) return;

    const diffX = this.startX - this.currentX;
    const threshold = 50;

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0) {
        this.nextSlide();
      } else {
        this.previousSlide();
      }
    }

    this.isDragging = false;
    this.slider.style.cursor = 'grab';
  }

  handleKeyDown(e) {
    if (e.key === 'ArrowLeft') {
      this.previousSlide();
    } else if (e.key === 'ArrowRight') {
      this.nextSlide();
    }
  }

  goToSlide(index) {
    if (index >= 0 && index < this.totalSlides) {
      this.currentSlide = index;
      this.updateSlider();
    }
  }

  nextSlide() {
    if (this.currentSlide < this.totalSlides - 1) {
      this.currentSlide++;
      this.updateSlider();
    }
  }

  previousSlide() {
    if (this.currentSlide > 0) {
      this.currentSlide--;
      this.updateSlider();
    }
  }

  updateSlider() {
    // Update slider position
    const translateX = -this.currentSlide * 100;
    this.slider.style.transform = `translateX(${translateX}%)`;

    // Update indicators
    this.indicators.forEach((indicator, index) => {
      if (index === this.currentSlide) {
        indicator.classList.add('bg-indigo-600', 'scale-110');
        indicator.classList.remove('bg-gray-300');
      } else {
        indicator.classList.remove('bg-indigo-600', 'scale-110');
        indicator.classList.add('bg-gray-300');
      }
    });

    // Update slide number
    if (this.slideNumber) {
      this.slideNumber.textContent = this.currentSlide + 1;
    }

    // Update navigation buttons
    if (this.prevBtn) {
      this.prevBtn.style.opacity = this.currentSlide === 0 ? '0.5' : '1';
    }
    if (this.nextBtn) {
      this.nextBtn.style.opacity = this.currentSlide === this.totalSlides - 1 ? '0.5' : '1';
    }
  }
}

// Global upgrade modal instance
window.upgradeModalInstance = null;

// Initialize the upgrade modal when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  window.upgradeModalInstance = new UpgradeSubscriptionModal();
});

// Global function to show upgrade modal (can be called from anywhere)
window.showUpgradeModal = function(limitType = null, usageData = null) {
  if (window.upgradeModalInstance) {
    window.upgradeModalInstance.show(limitType, usageData);
  } else {
    // Fallback if modal not initialized
    console.warn('Upgrade modal not initialized, creating new instance');
    window.upgradeModalInstance = new UpgradeSubscriptionModal();
    setTimeout(() => {
      window.upgradeModalInstance.show(limitType, usageData);
    }, 100);
  }
};

// Global function to validate subscription limits and show upgrade modal if needed
window.validateSubscriptionAndUpgrade = async function(limitType, params = {}) {
  try {
    const response = await fetch('/api/subscription-usage/validate-limit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
      },
      body: JSON.stringify({
        limit_type: limitType,
        ...params
      })
    });

    const data = await response.json();

    if (!data.success) {
      console.error('Failed to validate subscription limit:', data.message);
      return { allowed: true, error: data.message }; // Allow action if validation fails
    }

    if (!data.allowed) {
      // Show upgrade modal
      window.showUpgradeModal();
      return { allowed: false, message: data.message };
    }

    return { allowed: true };

  } catch (error) {
    console.error('Error validating subscription limit:', error);
    return { allowed: true, error: error.message }; // Allow action if validation fails
  }
};

// Helper function to bind upgrade validation to any element
window.bindUpgradeValidation = function(elementId, limitType, params = {}, originalCallback = null) {
  const element = document.getElementById(elementId);
  if (!element) {
    console.warn(`Element with ID '${elementId}' not found`);
    return;
  }

  element.addEventListener('click', async function(e) {
    e.preventDefault();

    const validation = await window.validateSubscriptionAndUpgrade(limitType, params);

    if (validation.allowed && originalCallback) {
      // If validation passes and there's an original callback, execute it
      originalCallback.call(this, e);
    }
  });
};
</script>
<?php /**PATH C:\wamp64\www\reviewbiz\resources\views/components/upgrade-subscription-modal.blade.php ENDPATH**/ ?>