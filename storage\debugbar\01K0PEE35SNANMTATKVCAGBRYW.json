{"__meta": {"id": "01K0PEE35SNANMTATKVCAGBRYW", "datetime": "2025-07-21 12:13:16", "utime": **********.347628, "method": "POST", "uri": "/businesses/1/reviews/desc", "ip": "127.0.0.1"}, "messages": {"count": 5, "messages": [{"message": "[12:13:16] LOG.debug: Business context determined {\n    \"is_team_member\": false,\n    \"user_id\": 1,\n    \"auth_user_id\": 1\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.222919, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:16] LOG.debug: Business query executed {\n    \"criteria\": {\n        \"user_id\": 1,\n        \"id\": \"1\"\n    },\n    \"relations\": [],\n    \"found\": true,\n    \"business_id\": 1\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.234895, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:16] LOG.debug: Business account retrieved {\n    \"user_id\": 1,\n    \"found\": true,\n    \"account_id\": \"110486499747300774507\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.251068, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:16] LOG.debug: Location ID extracted {\n    \"location_name\": \"locations\\/13522179532217756997\",\n    \"location_id\": \"13522179532217756997\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.251767, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:16] LOG.info: Filter Reviews Request {\n    \"business_id\": \"1\",\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\",\n    \"page\": 1,\n    \"per_page\": 10,\n    \"filters\": {\n        \"date_range\": \"all\",\n        \"ratings\": null,\n        \"type\": \"all\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.252593, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.458349, "end": **********.347665, "duration": 0.****************, "duration_str": "889ms", "measures": [{"label": "Booting", "start": **********.458349, "relative_start": 0, "end": **********.880682, "relative_end": **********.880682, "duration": 0.****************, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.880705, "relative_start": 0.*****************, "end": **********.347669, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.922705, "relative_start": 0.****************, "end": **********.929144, "relative_end": **********.929144, "duration": 0.0064389705657958984, "duration_str": "6.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.342058, "relative_start": 0.****************, "end": **********.342826, "relative_end": **********.342826, "duration": 0.0007679462432861328, "duration_str": "768μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.023360000000000006, "accumulated_duration_str": "23.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt' limit 1", "type": "query", "params": [], "bindings": ["R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.975267, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "review_master", "explain": null, "start_percent": 0, "width_percent": 4.409}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.009963, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 4.409, "width_percent": 7.449}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` = 1 and `business_accounts`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.0282261, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CheckGoogleTokenExpiration.php:18", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FMiddleware%2FCheckGoogleTokenExpiration.php&line=18", "ajax": false, "filename": "CheckGoogleTokenExpiration.php", "line": "18"}, "connection": "review_master", "explain": null, "start_percent": 11.858, "width_percent": 5.009}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************', '[]', '{\\\"azp\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"aud\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"sub\\\":\\\"110486499747300774507\\\",\\\"scope\\\":\\\"https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/business.manage https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.email https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.profile openid\\\",\\\"exp\\\":\\\"1753101336\\\",\\\"expires_in\\\":\\\"1335\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_verified\\\":\\\"true\\\",\\\"access_type\\\":\\\"offline\\\"}', 200, 'success', 165, null, 1, '2025-07-21 12:13:16', '2025-07-21 12:13:16')", "type": "query", "params": [], "bindings": ["google_api", "https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************", "[]", "{\"azp\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"aud\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"sub\":\"110486499747300774507\",\"scope\":\"https:\\/\\/www.googleapis.com\\/auth\\/business.manage https:\\/\\/www.googleapis.com\\/auth\\/userinfo.email https:\\/\\/www.googleapis.com\\/auth\\/userinfo.profile openid\",\"exp\":\"1753101336\",\"expires_in\":\"1335\",\"email\":\"<EMAIL>\",\"email_verified\":\"true\",\"access_type\":\"offline\"}", 200, "success", 165, null, 1, "2025-07-21 12:13:16", "2025-07-21 12:13:16"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 99}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 236}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 24}], "start": **********.2070148, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 16.866, "width_percent": 5.522}, {"sql": "select * from `businesses` where `user_id` = 1 and `id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 103}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1399}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.225015, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:103", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=103", "ajax": false, "filename": "BusinessController.php", "line": "103"}, "connection": "review_master", "explain": null, "start_percent": 22.389, "width_percent": 16.481}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1403}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.236401, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:52", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=52", "ajax": false, "filename": "BusinessController.php", "line": "52"}, "connection": "review_master", "explain": null, "start_percent": 38.87, "width_percent": 4.195}, {"sql": "select * from `google_reviews` where `parent_id` is null order by `created_at_google` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1484}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.255351, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1484", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1484}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1484", "ajax": false, "filename": "BusinessController.php", "line": "1484"}, "connection": "review_master", "explain": null, "start_percent": 43.065, "width_percent": 9.889}, {"sql": "select * from `google_reviews` where `parent_id` is null and exists (select * from `google_reviews` as `laravel_reserved_0` where `google_reviews`.`id` = `laravel_reserved_0`.`parent_id` and `parent_id` is not null) order by (\nSELECT MAX(updated_at_google)\nFROM google_reviews AS replies\nWHERE replies.parent_id = google_reviews.id\n) DESC limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1497}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2652018, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1497", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1497}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1497", "ajax": false, "filename": "BusinessController.php", "line": "1497"}, "connection": "review_master", "explain": null, "start_percent": 52.954, "width_percent": 18.279}, {"sql": "select * from `google_reviews` where `parent_id` is not null and `google_reviews`.`parent_id` in (6) order by `updated_at_google` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1497}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.275299, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1497", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1497}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1497", "ajax": false, "filename": "BusinessController.php", "line": "1497"}, "connection": "review_master", "explain": null, "start_percent": 71.233, "width_percent": 4.195}, {"sql": "select count(*) as aggregate from `google_reviews` where `parent_id` is null and `location_id` = '13522179532217756997' and `account_id` = '110486499747300774507'", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1503}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2825298, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1503", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1503}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1503", "ajax": false, "filename": "BusinessController.php", "line": "1503"}, "connection": "review_master", "explain": null, "start_percent": 75.428, "width_percent": 4.024}, {"sql": "select * from `google_reviews` where `parent_id` is null and `location_id` = '13522179532217756997' and `account_id` = '110486499747300774507' order by `created_at_google` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1503}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.289432, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1503", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1503}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1503", "ajax": false, "filename": "BusinessController.php", "line": "1503"}, "connection": "review_master", "explain": null, "start_percent": 79.452, "width_percent": 5.437}, {"sql": "select * from `google_reviews` where `parent_id` is not null and `google_reviews`.`parent_id` in (1, 2, 6, 8, 9, 11, 13, 14, 15, 16) order by `updated_at_google` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1503}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.297487, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1503", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1503}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1503", "ajax": false, "filename": "BusinessController.php", "line": "1503"}, "connection": "review_master", "explain": null, "start_percent": 84.889, "width_percent": 5.394}, {"sql": "select * from `templates` where `business_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1512}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.316705, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1512", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1512}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1512", "ajax": false, "filename": "BusinessController.php", "line": "1512"}, "connection": "review_master", "explain": null, "start_percent": 90.283, "width_percent": 9.717}]}, "models": {"data": {"App\\Models\\GoogleReview": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=1", "ajax": false, "filename": "GoogleReview.php", "line": "?"}}, "App\\Models\\Template": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}, "App\\Models\\BusinessAccount": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusinessAccount.php&line=1", "ajax": false, "filename": "BusinessAccount.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Business": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusiness.php&line=1", "ajax": false, "filename": "Business.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/businesses/1/reviews/desc", "action_name": null, "controller_action": "App\\Http\\Controllers\\BusinessController@filterReviews", "uri": "POST businesses/{business}/reviews/{order?}", "controller": "App\\Http\\Controllers\\BusinessController@filterReviews<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1386\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1386\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BusinessController.php:1386-1524</a>", "middleware": "web, auth", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f718a41-13ca-439e-9c5f-c96231c634e1\" target=\"_blank\">View in Telescope</a>", "duration": "898ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1271561513 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1271561513\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-113066354 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>date_range</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  \"<span class=sf-dump-key>ratings</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  \"<span class=sf-dump-key>per_page</span>\" => <span class=sf-dump-num>10</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113066354\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-613225271 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/business-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1694 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im1ySEFHV2hQVDlGcjRqUSs4bHlUclE9PSIsInZhbHVlIjoibDlUcW14ZXYzYU04Sk5hNkhNc3FsTjNvbmVvdzI3ZndKb1RmQzl5dHhjUnB2N1N2ek5PZDhVNUtsVmFSR3NsS3VPcTAwYlhIRlc1YkhIVlllUUE3K1VlSEZEWGd1RG9oU2c2Q0xpcS9kZE5ScHpaZlpaM3FMVjI0dlFKQWRWTTMzdEliSHhVaXB2L0p2OTNLWVNodEpRN1hiNmowQ0RQNkloTExmdlAvM1poS051Qm1oTVAvSDJpcFI2LzJ6eWMxWEpVQU1uTlgyVFJ4eWw4OWpROURaMkx3MmN2TE5id0RRZi9oYnJOZ2hOZz0iLCJtYWMiOiIzNGFiOWI5ZjdiYzFjZWJmZjZlMDI2NzFjY2FhYjhlZDIzOTQyNjNjZjAzNWU0YjBlY2VkYjVhZDkxMmFjOTMxIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InhsS1ErNFB2VC9UOVE1R1ovemFtVmc9PSIsInZhbHVlIjoieStNUXZDUDNZU3RqRHdvKzFUT25jSHg4dUVCSjFwUmllU25qNXFiV0F1OW84OTBCNU9CdTE2MTVMNUFvVHB2TVlTS2NSQjJMTkduZE0wU3I0cTJ4Tmp0UisxU1VqNlRqR0F2cnJEM3ZNZGQzSTFUMEtaUWFqUDNMQkNqTkE5MWpoRUN1YkhGOEpDWmthUTJ4RWlVeUl3PT0iLCJtYWMiOiIzZTI3OTliODc5NGUyZTBhMzcwYzdmZjA1MjM1MDVjY2RhNjcyNzMyZTk2NjRjYTczYjE4YmZhMGMwNTQ3YzNlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1udzh0aUV4b1dHdmozMHVOdXpJa0E9PSIsInZhbHVlIjoiMGc0WkNmSG9xaERObGRWZEdDczdaSnJZeEprWUgwQ04ycU0rcjdtQ29USzd2alVtUWdFbHE5UDRYNXJZV2Z5WEl0QW93YUkrRGYrMUgvWU1kSXhxS2JoRFpIRXpKNFlMT0FXbStzRVNYOW1pYytLY1l1eUpRbnRDMytaMEFxa3EiLCJtYWMiOiI2YWYyOTk1MzBjZGRhZDJhZDU4NWE4MmIwYjAxNWYxY2FlYzQ5NjIzNThlMDYwNGJmN2QzMWExN2EzYzBiZGY1IiwidGFnIjoiIn0%3D; reviewmasterai_session=eyJpdiI6IlVET1UzWXRjUHFuSmNISEFZZC81TEE9PSIsInZhbHVlIjoiaVlTVnRESndIa3VUL0NTbjZ1bFBZYXlPS1IyY3JFM1BSRll6TXhvRC9DVW5HNDArbmlEV20xa0hYRDFrdnJMTmtwejk1bldOTHREN0lXOVJuYUQ2aWxRQ0NyZndYQzVIN0ErN1JuSlpKQUM0K2R5R1I0Rm5UaG5SdUs2QlREUzAiLCJtYWMiOiI5YjZiMTAzNzQ1MzE2ZmU3ZmRjMzZjZGIwODdjYzg0MDExZmYxYjhjNTJmZDlkZjAwZTE0NDk4YTMyNTJiOTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613225271\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-797421000 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|pmk0ZPcwNvdcyZh8txRDWRjQhRBItD6bQKyiIIwPOGZVYyePFjEhKDNxjvoa|$2y$12$quO4cLolCFDH2wXsSke/u.BarK4ZwLV6ACtlaLyMBFl5.eec7Yrdi</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|d4gsFumgVybRUrjg0Rh5kBgW54R5vfpkeMJcrWrT7FkGNX8F4D9Tf62Ue7fx|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  \"<span class=sf-dump-key>reviewmasterai_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797421000\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1555951372 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 12:13:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555951372\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-576865369 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/business-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>business_google_token</span>\" => \"<span class=sf-dump-str title=\"224 characters\">********************************************************************************************************************************************************************************************************************************</span>\"\n  \"<span class=sf-dump-key>business_google_refresh_token</span>\" => \"<span class=sf-dump-str title=\"103 characters\">1//0gTNtsu4DcUdMCgYIARAAGBASNwF-L9IrdXV9dXsoYipZErrwYsSEb4XPiKiODRngXtCKIqm0BDgTYJem9CDOhq2TaCAHNMGEzQM</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576865369\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/businesses/1/reviews/desc", "controller_action": "App\\Http\\Controllers\\BusinessController@filterReviews"}, "badge": null}}