<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_activity_logs', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            
            // Core identifiers
            $table->unsignedBigInteger('user_id'); // User who owns the business
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('subscription_id')->nullable();
            
            // Activity details
            $table->string('activity_type', 100); // connect_business, disconnect_business, send_reply, etc.
            $table->string('activity_category', 50); // business_management, review_management, api_usage, etc.
            $table->text('activity_description');
            
            // Actor information (who performed the activity)
            $table->enum('performed_by_type', ['owner', 'member', 'system'])->default('owner');
            $table->unsignedBigInteger('performed_by_user_id')->nullable(); // Actual user who performed action
            $table->string('performed_by_name', 255)->nullable(); // Name for display
            
            // Counting and limits
            $table->boolean('counts_toward_limit')->default(true); // Whether this activity counts toward subscription limits
            $table->integer('activity_count')->default(1); // Number of activities (usually 1, but can be batch)
            
            // Context and metadata
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent', 500)->nullable();
            $table->json('metadata')->nullable(); // Additional context data
            
            // Status and results
            $table->enum('status', ['success', 'failed', 'pending'])->default('success');
            $table->text('error_message')->nullable();
            
            // Timestamps
            $table->timestamps();
            
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            
            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('set null');
            $table->foreign('performed_by_user_id')->references('id')->on('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['user_id', 'created_at']);
            $table->index(['business_id', 'created_at']);
            $table->index(['subscription_id', 'created_at']);
            $table->index(['activity_type', 'created_at']);
            $table->index(['activity_category', 'created_at']);
            $table->index(['performed_by_type', 'created_at']);
            $table->index(['counts_toward_limit', 'created_at']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_activity_logs');
    }
};
