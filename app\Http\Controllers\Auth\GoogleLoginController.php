<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessAccount;
use App\Models\GoogleReview;
use App\Models\User;
use App\Services\ReviewService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Laravel\Socialite\Facades\Socialite;
use App\Helpers\ReviewHelper;
use App\Models\ReplyLog;
use App\Models\TeamMember;
use App\Services\ExternalApiLogger;
use App\Services\GoogleBusinessService;
use Illuminate\Support\Facades\Artisan;

class GoogleLoginController extends Controller
{

    protected $googleBusinessService;

    public function __construct(GoogleBusinessService $googleBusinessService)
    {
        $this->googleBusinessService = $googleBusinessService;
    }
    /**
     * Exchange authorization code for access and refresh tokens
     * 
     * @param string $code The authorization code from Google
     * @return array|null Token data or null on failure
     */
    protected function exchangeCodeForTokens(string $code): ?array
    {
        try {
            $response = Http::asForm()->post('https://www.googleapis.com/oauth2/v4/token', [
                'client_id' => config('services.google.client_id'),
                'client_secret' => config('services.google.client_secret'),
                'redirect_uri' => config('services.google.redirect_business'),
                'grant_type' => 'authorization_code',
                'code' => $code
            ]);

            if ($response->successful() && isset($response['access_token'])) {
                return $response->json();
            }

            Log::error('Failed to exchange code for tokens', [
                'status' => $response->status(),
                'response' => $response->json() ?? 'null'
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while exchanging code for tokens', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Get user info from Google API using access token
     * 
     * @param string $accessToken The access token
     * @return array User info data
     */
    protected function getUserInfo(string $accessToken): array
    {
        try {
            $response = Http::withToken($accessToken)
                ->get('https://www.googleapis.com/oauth2/v3/userinfo');

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Failed to get user info', [
                'status' => $response->status(),
                'response' => $response->json() ?? 'null'
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while getting user info', [
                'message' => $e->getMessage()
            ]);

            return [];
        }
    }
    public $token = null;

    // Redirect to Google login
    /**
     * Redirect to Google for user authentication (email and profile scopes only)
     */
    public function redirectToGoogle()
    {
        // Only request basic profile scopes for user authentication
        // Using standard approach with just the basic scopes needed for authentication
        return Socialite::driver('google')
            ->scopes(['https://www.googleapis.com/auth/userinfo.email', 'https://www.googleapis.com/auth/userinfo.profile'])
            ->with([
                'access_type' => 'offline',
                'prompt' => 'consent'
            ])
            ->redirect();
    }

    /**
     * Redirect to Google for business management access
     */
    public function redirectToGoogleBusiness()
    {
        // This configuration needs to be defined in config/services.php for the Google driver
        // with the additional business.manage scope
        $additionalScope = 'https://www.googleapis.com/auth/business.manage';
        $clientId = config('services.google.client_id');
        $clientSecret = config('services.google.client_secret');
        $redirectUrl = config('services.google.redirect_business');

        // Create a custom configuration for the business scope
        $parameters = [
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'redirect' => $redirectUrl,
            'additional_scope' => $additionalScope,
        ];

        // Create a temporarily configured instance of Socialite with the business scope
        return Socialite::buildProvider(
            \Laravel\Socialite\Two\GoogleProvider::class,
            $parameters
        )
            ->scopes(['email', 'profile', $additionalScope])
            ->with(['access_type' => 'offline', 'prompt' => 'consent'])
            ->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            $user = User::where('email', $googleUser->email)->whereNull('google_id')->first();
            if ($user) {
                $user->update([
                    'google_id' => $googleUser->id,
                    'name' => $googleUser->name,
                    'image' => $googleUser->avatar ?? null,
                    'access_token' => $googleUser->token,
                    'refresh_token' => $googleUser->refreshToken,
                ]);
            } else {
                $user = User::firstOrCreate(
                    ['google_id' => $googleUser->id],
                    [
                        'name' => $googleUser->name,
                        'email' => $googleUser->email,
                        'image' => $googleUser->avatar ?? null,
                        'access_token' => $googleUser->token,
                        'refresh_token' => $googleUser->refreshToken,
                    ]
                );
            }

            if (!$user) {
                Log::error('User creation failed', ['googleUser' => $googleUser]);
                return redirect()->route('login')->with('error', 'User creation failed.');
            }

            Auth::login($user, true);
            Session::put('google_user', $googleUser);

            // Store authentication tokens in session
            Session::put('google_auth_token', $googleUser->token);
            Session::put('google_auth_refresh_token', $googleUser->refreshToken);

            if (config("google.debug_mode")) {
                Log::info('Google auth user data', [
                    'access_token' => $googleUser->token,
                    'refresh_token' => $googleUser->refreshToken,
                ]);
            }

            // Check if user already has business connected
            $businessAccount = BusinessAccount::where('user_id', $user->id)->first();
            if ($businessAccount) {
                // Check if business tokens are valid or need refresh
                $tokenValid = true;

                // Validate token by checking if refresh token exists
                if (empty($businessAccount->business_refresh_token)) {
                    $tokenValid = false;
                    Log::warning('Business refresh token is missing', [
                        'user_id' => $user->id,
                        'business_account_id' => $businessAccount->id
                    ]);
                }

                // If token is invalid, try to refresh it
                if (!$tokenValid) {
                    Log::info('Attempting to refresh business token', [
                        'user_id' => $user->id,
                        'business_account_id' => $businessAccount->id
                    ]);

                    $refreshed = \App\Helpers\GoogleTokenRefreshHelper::refreshSingleBusinessToken($businessAccount);

                    if (!$refreshed) {
                        Log::error('Failed to refresh business token', [
                            'user_id' => $user->id,
                            'business_account_id' => $businessAccount->id
                        ]);

                        // If refresh failed, redirect to reconnect business account
                        return redirect()->route('business.setup')
                            ->with('error', 'Your Google Business account connection has expired. Please reconnect your account.');
                    }

                    // Reload the business account to get the updated tokens
                    $businessAccount = BusinessAccount::find($businessAccount->id);
                    Log::info('Business token refreshed successfully', [
                        'user_id' => $user->id,
                        'business_account_id' => $businessAccount->id
                    ]);
                }

                // Store business tokens in session
                Session::put('business_google_token', $businessAccount->business_google_token);
                Session::put('business_google_refresh_token', $businessAccount->business_refresh_token);

                return redirect()->route("business.dashboard");
            }

            // User is authenticated but has no business connected and no subscription taken
            return redirect()->route("business.subscriptions");
        } catch (\Exception $e) {
            Log::error('Google login error', ['message' => $e->getMessage()]);
            return redirect()->route('login');
        }
    }

    /**
     * Handle the Google business callback (for business management access)
     */
    public function handleGoogleBusinessCallback(Request $request)
    {
        try {
            // Check if user is authenticated            
            $user = Auth::user();
            if (!$user) {
                Log::error('User not authenticated for business connection');
                return redirect()->route('login')->with('error', 'You must be logged in to connect your business account.');
            }

            // Get the authorization code from the callback
            $code = $request->query('code');
            if (!$code) {
                Log::error('No authorization code provided in callback');
                return redirect()->route('business.setup')->with('error', 'Failed to connect Google Business account. No authorization code provided.');
            }

            // Exchange code for tokens using our helper
            $tokenData = $this->exchangeCodeForTokens($code);
            if (!$tokenData || !isset($tokenData['access_token'])) {
                Log::error('Failed to exchange authorization code for tokens', ['tokenData' => $tokenData ?? 'null']);
                return redirect()->route('business.setup')->with('error', 'Failed to connect Google Business account. Invalid response from Google.');
            }

            // Retrieve user info to get Google ID
            $userInfo = $this->getUserInfo($tokenData['access_token']);

            // Store business tokens (these take precedence for API calls)
            $accessToken = $tokenData['access_token'];
            $refreshToken = $tokenData['refresh_token'] ?? null;

            Session::put('business_google_token', $accessToken);
            Session::put('business_google_refresh_token', $refreshToken);

            // Store business Google ID if available
            $businessGoogleId = $userInfo['sub'] ?? $user->google_id;
            Session::put('business_google_business_id', $businessGoogleId);

            if (config("google.debug_mode")) {
                Log::info('Google business user data', [
                    'access_token' => $accessToken,
                    'refresh_token' => $refreshToken,
                ]);
            }

            // Fetch business locations from Google
            $url = str_replace('{accountId}', $businessGoogleId, config('google.google_location_api')) . '?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng';
            $response = google_api_request($url, $accessToken);
            $checkLocation = ReviewHelper::checkBusinessLocation($response);
            // Save business account info to database
            BusinessAccount::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'business_email' => $userInfo['email'] ?? $user->email,
                    'business_google_id' => $businessGoogleId,
                    'business_google_token' => $accessToken,
                    'business_refresh_token' => $refreshToken
                ]
            );

            // Check if we have any business locations
            if (empty($response['locations']) || !is_array($response['locations'])) {
                return redirect()->route('business.setup')->with('warning', 'No business locations found. Please set up your business on Google My Business first.');
            }
            return redirect()->route('business.setup');
        } catch (\Exception $e) {
            Log::error('Google business connection error', ['message' => $e->getMessage()]);
            return redirect()->route('business.setup')->with('error', 'Failed to connect Google Business account: ' . $e->getMessage());
        }
    }

    public function makeRequest($url, $accessToken, $method = 'GET', $data = null)
    {
        $start = microtime(true);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);

        if ($method === 'POST' || $method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } else if ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        ExternalApiLogger::logGoogleApi(
            $url,
            $data,
            $response,
            $httpCode,
            $error ?: 'success',
            (int)((microtime(true) - $start) * 1000)
        );

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        if ($httpCode == 401) {
            $newAccessToken = $this->googleBusinessService->refreshAccessToken();
            if ($newAccessToken) {
                return $this->makeRequest($url, $newAccessToken, $method, $data);
            } else {
                throw new Exception("Unable to refresh access token");
            }
        }

        if ($error) {
            throw new Exception("cURL Error: $error");
        }

        $responseData = json_decode($response, true);

        if ($httpCode >= 400) {
            $errorMessage = isset($responseData['error']['message'])
                ? $responseData['error']['message']
                : "API Error: HTTP Code $httpCode";
            throw new Exception($errorMessage);
        }

        // Process review photos if they exist
        if (isset($responseData['reviews'])) {
            foreach ($responseData['reviews'] as &$review) {
                // Check if there are photos in the review
                if (isset($review['photos']) && !empty($review['photos'])) {
                    // Process each photo in the review
                    foreach ($review['photos'] as &$photo) {
                        // If the photo contains a URL, we can use it directly
                        if (isset($photo['photoUri'])) {
                            $photo['processedUri'] = $photo['photoUri'];
                        }
                    }
                }

                // For Google Business API v4, photos might be in a different location
                // Check for mediaItems or other possible locations
                if (isset($review['mediaItems']) && !empty($review['mediaItems'])) {
                    $review['photos'] = $review['photos'] ?? [];
                    foreach ($review['mediaItems'] as $mediaItem) {
                        if (isset($mediaItem['photoUri']) || isset($mediaItem['uri'])) {
                            $photoUri = isset($mediaItem['photoUri']) ? $mediaItem['photoUri'] : $mediaItem['uri'];
                            $review['photos'][] = [
                                'photoUri' => $photoUri,
                                'processedUri' => $photoUri
                            ];
                        }
                    }
                }

                // Some versions of the API might include reviewMedia
                if (isset($review['reviewMedia']) && !empty($review['reviewMedia'])) {
                    $review['photos'] = $review['photos'] ?? [];
                    foreach ($review['reviewMedia'] as $media) {
                        if (isset($media['uri'])) {
                            $review['photos'][] = [
                                'photoUri' => $media['uri'],
                                'processedUri' => $media['uri']
                            ];
                        }
                    }
                }
            }

            // Debug output for the first review with photos
            if (config("google.debug_mode")) {
                foreach ($responseData['reviews'] as $review) {
                    if (isset($review['photos']) && !empty($review['photos'])) {
                        error_log("Found review with photos: " . json_encode(array_slice($review['photos'], 0, 2)));
                        break;
                    }
                }
            }
        }

        return $responseData;
    }

    public function showReviews(Request $request)
    {

        $user = Auth::user();
        $hasBusiness = Business::where('user_id', $user->id)->exists();
        $hasBusinessAccount = BusinessAccount::where('user_id', $user->id)->exists();

        $getLocation = Business::where('user_id', $user->id)->pluck('location_name')->first();
        $getBusinessId = BusinessAccount::where('user_id', $user->id)->pluck('business_google_id')->first();

        if (!$hasBusinessAccount) {
            $hasReviews = GoogleReview::where(['location_id' => explode("/", $getLocation)[1], 'account_id' => $getBusinessId])->exists();
        }


        // If tokens are expired but user already has business and reviews data
        if (($this->isTokenExpired($user) || $this->isBusinessTokenExpired($user))
            && $hasBusiness && $hasBusinessAccount
        ) {

            // Revoke business token
            if ($user->business_token) {
                $user->business_token = null;
                $user->business_token_expires_at = null;
                $user->save();
            }

            // Redirect to dashboard instead of setup page
            return redirect()->route('business.dashboard')->with('warning', 'Your Google tokens have expired. Please reconnect your Google account.');
        }


        $location = $request->query('location');
        $token = Session::get('business_google_token');
        $selectedLocation = Session::get('selected_location');
        if ($selectedLocation) {
            $locationId = explode("/", $selectedLocation)[1];
        } else {
            $locationId = null;
        }

        $userData = User::find(Auth::user()->id);

        if (!$location) {
            return redirect()->route('business.setup')->with('error', 'No location specified.');
        }

        // Store the selected location in the session
        Session::put('selected_location', $location);

        $reviewsResponse = $this->getReviews($location, $token);
        foreach ($reviewsResponse['reviews'] as $review) {
            $reviewId = $review['reviewId'] ?? null;

            if ($reviewId && GoogleReview::where(['review_id' => $reviewId, 'location_id' => $locationId])->exists()) {
                continue;
            }

            // Extract photo URLs if available
            $photoUrls = [];
            if (!empty($review['photos']) && is_array($review['photos'])) {
                foreach ($review['photos'] as $photo) {
                    if (!empty($photo['photoUri'])) {
                        $photoUrls[] = $photo['photoUri'];
                    }
                }
            }

            $mainReview = GoogleReview::create([
                'review_id'         => $reviewId,
                'location_id'       => $this->extractLocationId($review['name']),
                'account_id'        => $this->extractAccountId($review['name']),
                'parent_id'         => null,
                'reviewer_name'     => $review['reviewer']['displayName'] ?? null,
                'reviewer_photo'    => $review['reviewer']['profilePhotoUrl'] ?? null,
                'star_rating'       => $review['starRating'] ?? null,
                'comment'           => $review['comment'] ?? null,
                'review_photos'     => !empty($photoUrls) ? $photoUrls : null,
                'created_at_google' => isset($review['createTime']) ? Carbon::parse($review['createTime'])->toDateTimeString() : null,
                'updated_at_google' => isset($review['updateTime']) ? Carbon::parse($review['updateTime'])->toDateTimeString() : null,
            ]);

            if (!empty($review['reviewReply']['comment'])) {
                $existingReply = GoogleReview::where('parent_id', $mainReview->id)
                    ->where('comment', $review['reviewReply']['comment'])
                    ->first();

                if (!$existingReply) {
                    GoogleReview::create([
                        'review_id'         => null,
                        'location_id'       => $this->extractLocationId($review['name']),
                        'account_id'        => $this->extractAccountId($review['name']),
                        'parent_id'         => $mainReview->id,
                        'reviewer_name'     => 'Admin',
                        'reviewer_photo'    => null,
                        'star_rating'       => null,
                        'comment'           => $review['reviewReply']['comment'],
                        'created_at_google' => null,
                        'updated_at_google' => isset($review['reviewReply']['updateTime']) ? Carbon::parse($review['reviewReply']['updateTime'])->toDateTimeString() : null,
                    ]);
                }
            }
        }

        if (!$userData->hasActiveSubscription()) {
            return redirect()->route('business.subscriptions');
        }
        return redirect()->route('business.dashboard');
    }

    private function isTokenExpired($user)
    {
        return !$user->access_token;
    }

    private function isBusinessTokenExpired($user)
    {
        $business = \App\Models\Business::where('user_id', $user->id)->first();
        return !$business || !$business->business_google_token;
    }

    public function getReviews($locationName, $token)
    {
        // Extract the location ID from the full location name
        $locationId = basename($locationName);

        // Try the exact format from specs
        try {
            // Get accounts first
            $accounts = $this->googleBusinessService->getAccounts($token);
            if (!empty($accounts['accounts'])) {
                $accountId = basename($accounts['accounts'][0]['name']);

                // Use GoogleBusinessService to get reviews
                return $this->googleBusinessService->getReviews($locationId, $accountId, $token);
            } else {
                throw new \Exception("No accounts found");
            }
        } catch (\Exception $e) {
            if (config("google.debug_mode")) {
                error_log("Primary API format failed: " . $e->getMessage());
            }

            // Try alternative formats as fallback
            try {
                // Use the fallback URL without additional parameters
                $url = config('google.google_mybusiness_api_v4') . '/' . $locationName . '/reviews';

                return $this->googleBusinessService->makeRequest($url, $token, 'GET');
            } catch (\Exception $e2) {
                // If all attempts fail, throw the original exception
                throw $e;
            }
        }
    }

    public function getAccounts()
    {
        $token = Session::get('business_google_token');
        return $this->googleBusinessService->getAccounts($token);
    }

    private function extractLocationId($name)
    {
        preg_match('/locations\/([^\/]+)/', $name, $matches);
        return $matches[1] ?? null;
    }

    private function extractAccountId($name)
    {
        preg_match('/accounts\/([^\/]+)/', $name, $matches);
        return $matches[1] ?? null;
    }


    public function replyToReview(Request $request)
    {
        try {
            $reviewId = $request->input('reviewId');
            $locationName = $request->input('locationName');
            $comment = $request->input('comment');
            $replyTemplateId = $request->input('reply_template_id');
            $token = Session::get('business_google_token');
            if (empty($reviewId) || empty($locationName) || empty($comment)) {
                return response()->json(['error' => 'Missing required fields'], 400);
            }

            $accounts = $this->getAccounts();

            $googleReviewId = GoogleReview::where('review_id', $reviewId)->pluck('id')->first();
            $existingReply = GoogleReview::where('parent_id', $googleReviewId)->first();

            if ($existingReply) {
                return response()->json(['error' => 'A reply for this review already exists.'], 409);
            }

            $replayData = [
                'location_id' => explode('/', $locationName)[1],
                'account_id' => explode('/', $accounts['accounts'][0]['name'])[1],
                'comment' => $comment,
                'parent_id' => $googleReviewId,
                'reply_by' => Auth::user()->id,
                'reply_template_id' => $replyTemplateId,
            ];

            // Check if a reply already exists for this parent_id and comment

            if (!empty($accounts['accounts'])) {
                $accountId = basename($accounts['accounts'][0]['name']);
                $locationId = basename($locationName);
                //Bhavik we tremendously appreciate your efforts and review, Thank you.
                // Use GoogleBusinessService to reply to review

                $response = $this->googleBusinessService->replyToReview($locationId, $accountId, $reviewId, $comment, $token);
                ReviewService::generateReplay($replayData);
                $user = User::select('id')->where('id', Auth::user()->id)->with('activeSubscription', 'getBusinessId')->first();
                ReplyLog::logReply(Auth::user()->id, $user['activeSubscription']['id'], $user['getBusinessId']['id'], $replayData);

                return response()->json(['success' => true, 'message' => 'Reply posted successfully', 'data' => $response], 200);
            } else {
                throw new Exception("No accounts found");
            }
        } catch (Exception $e) {
            if (config("google.debug_mode")) {
                error_log("Primary API failed for reply: " . $e->getMessage());
            }
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function fetchLatestReviews(Request $request)
    {
        $location = $request->input('location');
        $businessId = $request->input('businessId');
        $token = Session::get('business_google_token');
        $isJsonRequest = $request->wantsJson() || $request->ajax();

        if (!$location) {
            return $isJsonRequest
                ? response()->json(['success' => false, 'message' => 'No location specified.'], 400)
                : redirect()->back()->with('error', 'No location specified.');
        }

        
        // Dispatch the job to fetch reviews in the background
        \App\Jobs\FetchNewReviews::dispatch($location, $businessId, $token);

        return response()->json([
            'success' => true,
            'status' => true,
            'message' => 'Review fetch command has been triggered.'
        ]);
    }

    public function updateReply(Request $request)
    {
        try {
            $replyId = $request->input('replyId');
            $reviewId = $request->input('reviewId');
            $comment = $request->input('comment');
            $replyTemplateId = $request->input('reply_template_id');

            $token = Session::get('business_google_token');

            if (empty($replyId) || empty($comment)) {
                return response()->json(['success' => false, 'message' => 'Missing required fields'], 400);
            }

            // Get the reply from database
            $reply = GoogleReview::find($replyId);
            if (!$reply) {
                return response()->json(['success' => false, 'message' => 'Reply not found'], 404);
            }

            // Get the parent review
            $review = GoogleReview::find($reply->parent_id);
            if (!$review) {
                return response()->json(['success' => false, 'message' => 'Parent review not found'], 404);
            }

            $accounts = $this->getAccounts();

            if (!empty($accounts['accounts'])) {
                $accountId = basename($accounts['accounts'][0]['name']);
                $locationId = $reply->location_id;

                // Use the exact format from the specs
                $reviewName = "accounts/{$accountId}/locations/{$locationId}/reviews/{$review->review_id}";
                $url = config("google.google_mybusiness_api_v4") . '/' . $reviewName . '/reply';

                if (config("google.debug_mode")) {
                    error_log("Trying update reply URL: " . $url);
                }

                // Update in Google API using GoogleBusinessService
                $response = $this->googleBusinessService->replyToReview($locationId, $accountId, $review->review_id, $comment, $token);

                // Update in database
                $reply->comment = $comment;
                $reply->reply_edited_by = Auth::user()->id;
                $reply->reply_template_id = $replyTemplateId;
                $reply->updated_at_google = now();
                $reply->save();

                $user = User::select('id')->where('id', Auth::user()->id)->with('activeSubscription', 'getBusinessId')->first();
                ReplyLog::logReply(Auth::user()->id, $user['activeSubscription']['id'], $user['getBusinessId']['id'], []);

                return response()->json(['success' => true, 'message' => 'Reply updated successfully', 'data' => $response], 200);
            } else {
                throw new Exception("No accounts found");
            }
        } catch (Exception $e) {
            if (config("google.debug_mode")) {
                error_log("Failed to update reply: " . $e->getMessage());
            }
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function deleteReply(Request $request)
    {
        try {
            $replyId = $request->input('replyId');
            $reviewId = $request->input('reviewId');
            $token = Session::get('business_google_token');
            if (empty($replyId)) {
                return response()->json(['success' => false, 'message' => 'Missing required fields'], 400);
            }

            // Get the reply from database
            $reply = GoogleReview::find($replyId);
            if (!$reply) {
                return response()->json(['success' => false, 'message' => 'Reply not found'], 404);
            }

            // Get the parent review
            $review = GoogleReview::find($reply->parent_id);
            if (!$review) {
                return response()->json(['success' => false, 'message' => 'Parent review not found'], 404);
            }

            $accounts = $this->getAccounts();

            if (!empty($accounts['accounts'])) {
                $accountId = basename($accounts['accounts'][0]['name']);
                $locationId = $reply->location_id;

                // Use the exact format from the Google Business API specification
                $reviewName = "accounts/{$accountId}/locations/{$locationId}/reviews/{$review->review_id}";
                $url = config("google.google_mybusiness_api_v4") . '/' . $reviewName . '/reply';
                if (config("google.debug_mode")) {
                    error_log("Trying delete reply URL: " . $url);
                }

                // Delete in Google API with proper error handling
                try {
                    $this->googleBusinessService->deleteReviewReply($locationId, $accountId, $review->review_id, $token);

                    // Only delete from database if the API call was successful
                    $reply->delete();

                    return response()->json(['success' => true, 'message' => 'Reply deleted successfully'], 200);
                } catch (Exception $apiError) {
                    if (config("google.debug_mode")) {
                        error_log("Google API error: " . $apiError->getMessage());
                    }
                    return response()->json(['success' => false, 'message' => 'Failed to delete reply on Google: ' . $apiError->getMessage()], 500);
                }
            } else {
                throw new Exception("No accounts found");
            }
        } catch (Exception $e) {
            if (config("google.debug_mode")) {
                error_log("Failed to delete reply: " . $e->getMessage());
            }
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
