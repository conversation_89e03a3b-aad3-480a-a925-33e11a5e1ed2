<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        Admin::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('ReviewMaster@123'),
            'role' => 'super_admin',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create Regular Admin
        Admin::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('ReviewMaster@123'),
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => now(),
            'created_by' => 1, // Created by Super Admin
        ]);

        // Create Moderator
        Admin::create([
            'name' => 'Moderator User',
            'email' => '<EMAIL>',
            'password' => Hash::make('ReviewMaster@123'),
            'role' => 'moderator',
            'is_active' => true,
            'email_verified_at' => now(),
            'created_by' => 1, // Created by Super Admin
        ]);
    }
}
