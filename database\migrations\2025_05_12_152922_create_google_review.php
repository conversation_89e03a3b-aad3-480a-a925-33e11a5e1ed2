<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_reviews', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('review_id')->nullable();
            $table->string('location_id')->nullable();
            $table->string('account_id')->nullable();

            $table->foreignId('parent_id')->nullable()->constrained('google_reviews')->onDelete('cascade');

            $table->string('reviewer_name')->nullable();
            $table->text('reviewer_photo')->nullable();

            $table->enum('star_rating', ['ONE', 'TWO', 'THREE', 'FOUR', 'FIVE'])->nullable();
            $table->text('comment')->nullable();
            $table->json('review_photos')->nullable();
            $table->string('reviewType')->nullable();
            $table->decimal('averageRating', 10, 2)->nullable();
            $table->text('reply_comment')->nullable();
            $table->integer('reply_by')->nullable();
            $table->integer('reply_edited_by')->nullable()->default(null);
            $table->integer('reply_template_id')->nullable();

            $table->timestamp('created_at_google')->nullable();
            $table->timestamp('updated_at_google')->nullable();
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('google_review');
    }
};
