{"__meta": {"id": "01K0RFPF0GQ91AC3AQNM1HK50G", "datetime": "2025-07-22 07:13:48", "utime": **********.048817, "method": "GET", "uri": "/admin/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753168426.672273, "end": **********.048833, "duration": 1.3765599727630615, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1753168426.672273, "relative_start": 0, "end": **********.136536, "relative_end": **********.136536, "duration": 0.****************, "duration_str": "464ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.136568, "relative_start": 0.****************, "end": **********.048836, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "912ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.184734, "relative_start": 0.****************, "end": **********.193825, "relative_end": **********.193825, "duration": 0.009090900421142578, "duration_str": "9.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.623013, "relative_start": 0.****************, "end": **********.04649, "relative_end": **********.04649, "duration": 0.****************, "duration_str": "423ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.analytics.index", "start": **********.629142, "relative_start": 0.****************, "end": **********.629142, "relative_end": **********.629142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.app", "start": **********.042065, "relative_start": 1.****************, "end": **********.042065, "relative_end": **********.042065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 40318448, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "admin.analytics.index", "param_count": null, "params": [], "start": **********.62906, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/admin/analytics/index.blade.phpadmin.analytics.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fadmin%2Fanalytics%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin.layouts.app", "param_count": null, "params": [], "start": **********.04191, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 27, "nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03327000000000001, "accumulated_duration_str": "33.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'pLcSbpYCo5akCOQgsmjlPfAQstrJbcACbezixrlq' limit 1", "type": "query", "params": [], "bindings": ["pLcSbpYCo5akCOQgsmjlPfAQstrJbcACbezixrlq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.2444968, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "review_master", "explain": null, "start_percent": 0, "width_percent": 2.855}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 14}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.282588, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 2.855, "width_percent": 3.907}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` = 1 and `business_accounts`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.298721, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "CheckGoogleTokenExpiration.php:18", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FMiddleware%2FCheckGoogleTokenExpiration.php&line=18", "ajax": false, "filename": "CheckGoogleTokenExpiration.php", "line": "18"}, "connection": "review_master", "explain": null, "start_percent": 6.763, "width_percent": 7.815}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************', '[]', '{\\\"azp\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"aud\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"sub\\\":\\\"110486499747300774507\\\",\\\"scope\\\":\\\"https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/business.manage https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.email https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.profile openid\\\",\\\"exp\\\":\\\"1753171505\\\",\\\"expires_in\\\":\\\"3073\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_verified\\\":\\\"true\\\",\\\"access_type\\\":\\\"offline\\\"}', 200, 'success', 135, null, 1, '2025-07-22 07:13:47', '2025-07-22 07:13:47')", "type": "query", "params": [], "bindings": ["google_api", "https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************", "[]", "{\"azp\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"aud\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"sub\":\"110486499747300774507\",\"scope\":\"https:\\/\\/www.googleapis.com\\/auth\\/business.manage https:\\/\\/www.googleapis.com\\/auth\\/userinfo.email https:\\/\\/www.googleapis.com\\/auth\\/userinfo.profile openid\",\"exp\":\"1753171505\",\"expires_in\":\"3073\",\"email\":\"<EMAIL>\",\"email_verified\":\"true\",\"access_type\":\"offline\"}", 200, "success", 135, null, 1, "2025-07-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 99}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 236}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 24}], "start": **********.456394, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 14.578, "width_percent": 12.684}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin.auth", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\AdminAuth.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.47771, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 27.262, "width_percent": 3.306}, {"sql": "select * from `payments` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47' and `payment_status` = 'SUCCESS'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47", "SUCCESS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 446}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 400}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.486085, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:446", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 446}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=446", "ajax": false, "filename": "DashboardController.php", "line": "446"}, "connection": "review_master", "explain": null, "start_percent": 30.568, "width_percent": 3.577}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_at` between '2025-07-22 07:13:47' and '2025-06-22 07:13:47' and `payment_status` = 'SUCCESS'", "type": "query", "params": [], "bindings": ["2025-07-22 07:13:47", "2025-06-22 07:13:47", "SUCCESS"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 455}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 400}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.497697, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:455", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 455}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=455", "ajax": false, "filename": "DashboardController.php", "line": "455"}, "connection": "review_master", "explain": null, "start_percent": 34.145, "width_percent": 1.924}, {"sql": "select count(*) as aggregate from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 488}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 401}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.502464, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:488", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 488}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=488", "ajax": false, "filename": "DashboardController.php", "line": "488"}, "connection": "review_master", "explain": null, "start_percent": 36.069, "width_percent": 4.178}, {"sql": "select count(*) as aggregate from `users` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 489}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 401}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.506911, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:489", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 489}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=489", "ajax": false, "filename": "DashboardController.php", "line": "489"}, "connection": "review_master", "explain": null, "start_percent": 40.246, "width_percent": 1.653}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `subscriptions` where `users`.`id` = `subscriptions`.`user_id` and `status` = 'active' and `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47')", "type": "query", "params": [], "bindings": ["active", "2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 502}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 401}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.513788, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:502", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 502}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=502", "ajax": false, "filename": "DashboardController.php", "line": "502"}, "connection": "review_master", "explain": null, "start_percent": 41.9, "width_percent": 4.809}, {"sql": "select * from `subscriptions` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 538}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 402}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.519879, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:538", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=538", "ajax": false, "filename": "DashboardController.php", "line": "538"}, "connection": "review_master", "explain": null, "start_percent": 46.709, "width_percent": 2.555}, {"sql": "select * from `subscriptions` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 548}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 402}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.52487, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:548", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 548}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=548", "ajax": false, "filename": "DashboardController.php", "line": "548"}, "connection": "review_master", "explain": null, "start_percent": 49.264, "width_percent": 3.817}, {"sql": "select * from `plans` where `plans`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 548}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 402}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5315251, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:548", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 548}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=548", "ajax": false, "filename": "DashboardController.php", "line": "548"}, "connection": "review_master", "explain": null, "start_percent": 53.081, "width_percent": 1.894}, {"sql": "select count(*) as aggregate from `businesses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 567}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5394802, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:567", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 567}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=567", "ajax": false, "filename": "DashboardController.php", "line": "567"}, "connection": "review_master", "explain": null, "start_percent": 54.974, "width_percent": 7.695}, {"sql": "select count(*) as aggregate from `businesses` where exists (select * from `users` where `businesses`.`user_id` = `users`.`id` and exists (select * from `subscriptions` where `users`.`id` = `subscriptions`.`user_id` and `status` = 'active'))", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 570}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 403}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.546833, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:570", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 570}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=570", "ajax": false, "filename": "DashboardController.php", "line": "570"}, "connection": "review_master", "explain": null, "start_percent": 62.669, "width_percent": 2.405}, {"sql": "select count(*) as aggregate from `google_reviews`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 573}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.553889, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:573", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 573}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=573", "ajax": false, "filename": "DashboardController.php", "line": "573"}, "connection": "review_master", "explain": null, "start_percent": 65.074, "width_percent": 4.028}, {"sql": "select avg(`star_rating`) as aggregate from `google_reviews`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 574}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.558831, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:574", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 574}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=574", "ajax": false, "filename": "DashboardController.php", "line": "574"}, "connection": "review_master", "explain": null, "start_percent": 69.101, "width_percent": 3.126}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 595}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.563291, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:595", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 595}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=595", "ajax": false, "filename": "DashboardController.php", "line": "595"}, "connection": "review_master", "explain": null, "start_percent": 72.227, "width_percent": 1.683}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_at` between '2025-07-22 07:13:47' and '2025-06-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-07-22 07:13:47", "2025-06-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 596}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.568716, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:596", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 596}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=596", "ajax": false, "filename": "DashboardController.php", "line": "596"}, "connection": "review_master", "explain": null, "start_percent": 73.91, "width_percent": 2.645}, {"sql": "select count(*) as aggregate from `users` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 601}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5729861, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:601", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 601}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=601", "ajax": false, "filename": "DashboardController.php", "line": "601"}, "connection": "review_master", "explain": null, "start_percent": 76.555, "width_percent": 1.563}, {"sql": "select count(*) as aggregate from `users` where `created_at` between '2025-07-22 07:13:47' and '2025-06-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-07-22 07:13:47", "2025-06-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 602}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5778918, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:602", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 602}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=602", "ajax": false, "filename": "DashboardController.php", "line": "602"}, "connection": "review_master", "explain": null, "start_percent": 78.118, "width_percent": 1.773}, {"sql": "select count(*) as aggregate from `subscriptions` where `created_at` between '2025-06-22 07:13:47' and '2025-07-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47", "2025-07-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 607}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5835102, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:607", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 607}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=607", "ajax": false, "filename": "DashboardController.php", "line": "607"}, "connection": "review_master", "explain": null, "start_percent": 79.892, "width_percent": 2.525}, {"sql": "select count(*) as aggregate from `subscriptions` where `created_at` between '2025-07-22 07:13:47' and '2025-06-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-07-22 07:13:47", "2025-06-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 608}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.588055, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:608", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 608}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=608", "ajax": false, "filename": "DashboardController.php", "line": "608"}, "connection": "review_master", "explain": null, "start_percent": 82.417, "width_percent": 1.593}, {"sql": "select count(*) as aggregate from `businesses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 613}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5928528, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:613", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 613}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=613", "ajax": false, "filename": "DashboardController.php", "line": "613"}, "connection": "review_master", "explain": null, "start_percent": 84.01, "width_percent": 10.67}, {"sql": "select count(*) as aggregate from `businesses` where `created_at` < '2025-06-22 07:13:47'", "type": "query", "params": [], "bindings": ["2025-06-22 07:13:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 614}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 404}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.5995698, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:614", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 614}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=614", "ajax": false, "filename": "DashboardController.php", "line": "614"}, "connection": "review_master", "explain": null, "start_percent": 94.68, "width_percent": 2.344}, {"sql": "select distinct `currency` from `payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 363}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 407}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.60364, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:363", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 363}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=363", "ajax": false, "filename": "DashboardController.php", "line": "363"}, "connection": "review_master", "explain": null, "start_percent": 97.024, "width_percent": 1.653}, {"sql": "select `id`, `name`, `currency` from `plans`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 364}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 407}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.610151, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:364", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 364}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=364", "ajax": false, "filename": "DashboardController.php", "line": "364"}, "connection": "review_master", "explain": null, "start_percent": 98.677, "width_percent": 1.323}]}, "models": {"data": {"App\\Models\\Plan": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusinessAccount.php&line=1", "ajax": false, "filename": "BusinessAccount.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Payment": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@analytics", "uri": "GET admin/analytics", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@analytics<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=389\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=389\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/DashboardController.php:389-416</a>", "middleware": "web, admin.auth, admin.permission:analytics.view", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f732223-7bef-4463-8a58-bd3c06429023\" target=\"_blank\">View in Telescope</a>", "duration": "1.38s", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2084372835 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2084372835\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1764405385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1764405385\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-733869412 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/logs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1694 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im1ySEFHV2hQVDlGcjRqUSs4bHlUclE9PSIsInZhbHVlIjoibDlUcW14ZXYzYU04Sk5hNkhNc3FsTjNvbmVvdzI3ZndKb1RmQzl5dHhjUnB2N1N2ek5PZDhVNUtsVmFSR3NsS3VPcTAwYlhIRlc1YkhIVlllUUE3K1VlSEZEWGd1RG9oU2c2Q0xpcS9kZE5ScHpaZlpaM3FMVjI0dlFKQWRWTTMzdEliSHhVaXB2L0p2OTNLWVNodEpRN1hiNmowQ0RQNkloTExmdlAvM1poS051Qm1oTVAvSDJpcFI2LzJ6eWMxWEpVQU1uTlgyVFJ4eWw4OWpROURaMkx3MmN2TE5id0RRZi9oYnJOZ2hOZz0iLCJtYWMiOiIzNGFiOWI5ZjdiYzFjZWJmZjZlMDI2NzFjY2FhYjhlZDIzOTQyNjNjZjAzNWU0YjBlY2VkYjVhZDkxMmFjOTMxIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InhsS1ErNFB2VC9UOVE1R1ovemFtVmc9PSIsInZhbHVlIjoieStNUXZDUDNZU3RqRHdvKzFUT25jSHg4dUVCSjFwUmllU25qNXFiV0F1OW84OTBCNU9CdTE2MTVMNUFvVHB2TVlTS2NSQjJMTkduZE0wU3I0cTJ4Tmp0UisxU1VqNlRqR0F2cnJEM3ZNZGQzSTFUMEtaUWFqUDNMQkNqTkE5MWpoRUN1YkhGOEpDWmthUTJ4RWlVeUl3PT0iLCJtYWMiOiIzZTI3OTliODc5NGUyZTBhMzcwYzdmZjA1MjM1MDVjY2RhNjcyNzMyZTk2NjRjYTczYjE4YmZhMGMwNTQ3YzNlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlIyKzZ2dDUrUUEyR1diZCt2bjZ6RVE9PSIsInZhbHVlIjoiZUw3TjNTZHlTRmNNcnAvWGVOeTRISnJXZ3ZnSE1aNU5nRGdJcXpVV29jdVR4MHBMM1pZU1lieFVZRTNPYjBRM0QzMVpEY1YxS1hocW5QUmRGZkN1MmZldXJNVUN1OGlCc1RaNzVOYk9oRTVwZ2UxK09sWnFDdGkvMFZua1hQZ0giLCJtYWMiOiIzZjNlMzNmZThiZTlmMTNiMDJkNTdhYWNhZGZjN2U0NTQ1Mjg4OWU1ZGMyMDI1NzQ5NGE0MDMxYjYxN2IwMzVmIiwidGFnIjoiIn0%3D; reviewmasterai_session=eyJpdiI6IkZmcUxuK3Q1WDR5eGt6TGhoTXcwM2c9PSIsInZhbHVlIjoiMVAxQ09UT2ExY1YwWnU0R0VKT3daNXp6cDdjWFZoa3lrcXZEK3d1cDV3dUpGczEyMUowNmlpMWtEYWp5YnBVZHNMakhadWZ0K3JMTkFlblBBSHFGd2RiV3VXbG9rZTNrdFpYNm1ROVJhYjlldlVBWFhrNVErR202dEJETmhGTjYiLCJtYWMiOiIxMjM2N2RiZmFlZDJkNjA5ZDhkOGQ2NjU2NjE1MGRkMTg5YWE4NGJhZDA1YTMyYWY0NGI3MDUyMzliMDEyZTViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733869412\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1689151133 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|pmk0ZPcwNvdcyZh8txRDWRjQhRBItD6bQKyiIIwPOGZVYyePFjEhKDNxjvoa|$2y$12$quO4cLolCFDH2wXsSke/u.BarK4ZwLV6ACtlaLyMBFl5.eec7Yrdi</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|d4gsFumgVybRUrjg0Rh5kBgW54R5vfpkeMJcrWrT7FkGNX8F4D9Tf62Ue7fx|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wCg4junLV3gcbb8K67WFruGhKD5epVrHXOom5dSS</span>\"\n  \"<span class=sf-dump-key>reviewmasterai_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pLcSbpYCo5akCOQgsmjlPfAQstrJbcACbezixrlq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689151133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-486734088 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 07:13:47 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486734088\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1293509411 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wCg4junLV3gcbb8K67WFruGhKD5epVrHXOom5dSS</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/logs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293509411\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@analytics"}, "badge": null}}