<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Plan;
use App\Models\Coupon;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\CouponUsageLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class CheckoutTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $plan;
    protected $coupon;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        $this->plan = Plan::create([
            'name' => 'Test Plan',
            'short_description' => 'Test plan for checkout',
            'long_description' => 'Test plan description',
            'currency' => 'INR',
            'currency_symbol' => '₹',
            'price' => 999.00,
            'duration_days' => 30,
            'features' => json_encode(['Feature 1', 'Feature 2']),
            'is_active' => true,
        ]);

        $this->coupon = Coupon::create([
            'coupon_code' => 'TEST20',
            'name' => 'Test Coupon',
            'description' => 'Test coupon for 20% off',
            'type' => 'percentage',
            'value' => 20.00,
            'usage_limit' => 100,
            'usage_limit_per_user' => 1,
            'is_active' => true,
            'starts_at' => Carbon::now(),
            'expires_at' => Carbon::now()->addDays(30),
        ]);
    }

    /** @test */
    public function user_can_view_checkout_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('checkout.index'));

        $response->assertStatus(200);
        $response->assertViewIs('checkout.index');
        $response->assertViewHas('plans');
    }

    /** @test */
    public function user_can_validate_valid_coupon()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('checkout.validate-coupon'), [
                'coupon_code' => 'TEST20',
                'plan_id' => $this->plan->id,
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'valid' => true,
            'coupon' => [
                'code' => 'TEST20',
                'type' => 'percentage',
                'value' => 20.00,
            ],
        ]);
    }

    /** @test */
    public function user_cannot_validate_invalid_coupon()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('checkout.validate-coupon'), [
                'coupon_code' => 'INVALID',
                'plan_id' => $this->plan->id,
            ]);

        $response->assertStatus(422);
        $response->assertJson([
            'valid' => false,
            'message' => 'Invalid coupon code.',
        ]);
    }

    /** @test */
    public function user_cannot_validate_expired_coupon()
    {
        $expiredCoupon = Coupon::create([
            'coupon_code' => 'EXPIRED',
            'name' => 'Expired Coupon',
            'type' => 'percentage',
            'value' => 10.00,
            'is_active' => true,
            'starts_at' => Carbon::now()->subDays(30),
            'expires_at' => Carbon::now()->subDays(1),
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('checkout.validate-coupon'), [
                'coupon_code' => 'EXPIRED',
                'plan_id' => $this->plan->id,
            ]);

        $response->assertStatus(422);
        $response->assertJson([
            'valid' => false,
            'message' => 'This coupon has expired.',
        ]);
    }

    /** @test */
    public function user_can_checkout_with_free_coupon()
    {
        $freeCoupon = Coupon::create([
            'coupon_code' => 'FREE100',
            'name' => 'Free Trial',
            'type' => 'free_trial',
            'value' => 100.00,
            'is_active' => true,
            'starts_at' => Carbon::now(),
            'expires_at' => Carbon::now()->addDays(30),
        ]);

        $response = $this->actingAs($this->user)
            ->post(route('checkout.process'), [
                'plan_id' => $this->plan->id,
                'gateway' => 'STRIPE',
                'coupon_code' => 'FREE100',
            ]);

        $response->assertRedirect(route('payment.success'));
        
        // Check that payment was created
        $this->assertDatabaseHas('payments', [
            'user_id' => $this->user->id,
            'amount' => 0,
            'payment_status' => 'SUCCESS',
            'coupon_code' => 'FREE100',
        ]);

        // Check that subscription was created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => 'ACTIVE',
        ]);

        // Check that coupon usage was logged
        $this->assertDatabaseHas('coupon_usage_logs', [
            'coupon_id' => $freeCoupon->id,
            'user_id' => $this->user->id,
            'status' => 'used',
        ]);
    }

    /** @test */
    public function user_can_checkout_with_percentage_coupon()
    {
        $response = $this->actingAs($this->user)
            ->post(route('checkout.process'), [
                'plan_id' => $this->plan->id,
                'gateway' => 'STRIPE',
                'coupon_code' => 'TEST20',
            ]);

        // Should redirect to Stripe (external payment)
        $response->assertStatus(302);
        
        // Check that payment was created with correct discount
        $payment = Payment::where('user_id', $this->user->id)->first();
        $this->assertNotNull($payment);
        $this->assertEquals(999.00, $payment->original_amount);
        $this->assertEquals(199.80, $payment->discount_amount); // 20% of 999
        $this->assertEquals(799.20, $payment->amount); // 999 - 199.80
        $this->assertEquals('TEST20', $payment->coupon_code);
    }

    /** @test */
    public function checkout_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->post(route('checkout.process'), [
                // Missing plan_id and gateway
            ]);

        $response->assertSessionHasErrors(['plan_id', 'gateway']);
    }

    /** @test */
    public function checkout_prevents_duplicate_coupon_usage()
    {
        // First usage
        $this->coupon->apply($this->user->id, $this->plan->price, $this->plan->id);

        // Second usage attempt
        $response = $this->actingAs($this->user)
            ->post(route('checkout.process'), [
                'plan_id' => $this->plan->id,
                'gateway' => 'STRIPE',
                'coupon_code' => 'TEST20',
            ]);

        $response->assertSessionHasErrors(['coupon_code']);
    }

    /** @test */
    public function coupon_usage_is_tracked_correctly()
    {
        $initialUsageCount = $this->coupon->usage_count;

        $this->actingAs($this->user)
            ->post(route('checkout.process'), [
                'plan_id' => $this->plan->id,
                'gateway' => 'STRIPE',
                'coupon_code' => 'TEST20',
            ]);

        $this->coupon->refresh();
        $this->assertEquals($initialUsageCount + 1, $this->coupon->usage_count);

        // Check usage log was created
        $this->assertDatabaseHas('coupon_usage_logs', [
            'coupon_id' => $this->coupon->id,
            'user_id' => $this->user->id,
            'coupon_code' => 'TEST20',
            'original_amount' => 999.00,
            'discount_amount' => 199.80,
            'final_amount' => 799.20,
        ]);
    }

    /** @test */
    public function checkout_handles_invalid_plan()
    {
        $response = $this->actingAs($this->user)
            ->post(route('checkout.process'), [
                'plan_id' => 99999, // Non-existent plan
                'gateway' => 'STRIPE',
            ]);

        $response->assertSessionHasErrors(['plan_id']);
    }
}
