@extends('admin.layouts.app')

@section('title', 'Coupon Details - ' . $coupon->coupon_code)

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $coupon->coupon_code }}</h1>
            <p class="text-gray-600 mt-1">{{ $coupon->name }}</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-0">
            <a href="{{ route('admin.coupons.edit', $coupon) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-edit mr-2"></i>Edit Coupon
            </a>
            <a href="{{ route('admin.coupons.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Coupons
            </a>
        </div>
    </div>

    <!-- Coupon Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Coupon Details Card -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Coupon Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Coupon Code</label>
                        <p class="text-lg font-mono font-bold text-gray-900">{{ $coupon->coupon_code }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Display Name</label>
                        <p class="text-lg text-gray-900">{{ $coupon->name }}</p>
                    </div>
                    @if($coupon->description)
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-500">Description</label>
                        <p class="text-gray-900">{{ $coupon->description }}</p>
                    </div>
                    @endif
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Discount Type</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium 
                            {{ $coupon->type === 'percentage' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                            {{ $coupon->discount_display }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Status</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium 
                            {{ $coupon->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $coupon->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    @if($coupon->minimum_amount)
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Minimum Order Amount</label>
                        <p class="text-gray-900">₹{{ number_format($coupon->minimum_amount, 2) }}</p>
                    </div>
                    @endif
                    @if($coupon->maximum_discount)
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Maximum Discount</label>
                        <p class="text-gray-900">₹{{ number_format($coupon->maximum_discount, 2) }}</p>
                    </div>
                    @endif
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Usage Limit</label>
                        <p class="text-gray-900">
                            {{ $coupon->usage_limit ? number_format($coupon->usage_limit) : 'Unlimited' }}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Per User Limit</label>
                        <p class="text-gray-900">
                            {{ $coupon->usage_limit_per_user ? number_format($coupon->usage_limit_per_user) : 'Unlimited' }}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Valid From</label>
                        <p class="text-gray-900">
                            {{ $coupon->starts_at ? $coupon->starts_at->format('M d, Y H:i') : 'Immediately' }}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Valid Until</label>
                        <p class="text-gray-900 {{ $coupon->expires_at && $coupon->expires_at->isPast() ? 'text-red-600' : '' }}">
                            {{ $coupon->expires_at ? $coupon->expires_at->format('M d, Y H:i') : 'No expiry' }}
                        </p>
                    </div>
                </div>

                @if($coupon->applicable_plans && count($coupon->applicable_plans) > 0)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Applicable Plans</label>
                    <div class="flex flex-wrap gap-2">
                        @foreach($coupon->applicable_plans as $planId)
                            @php
                                $plan = \App\Models\Plan::find($planId);
                            @endphp
                            @if($plan)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ $plan->name }}
                                </span>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Usage Statistics Card -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Usage</span>
                        <span class="text-lg font-medium text-gray-900">{{ number_format($analytics['total_usage']) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Unique Users</span>
                        <span class="text-lg font-medium text-gray-900">{{ number_format($analytics['unique_users']) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Discount Given</span>
                        <span class="text-lg font-medium text-gray-900">₹{{ number_format($analytics['total_discount_given'], 2) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Revenue Impact</span>
                        <span class="text-lg font-medium text-gray-900">₹{{ number_format($analytics['total_revenue_impact'], 2) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Average Discount</span>
                        <span class="text-lg font-medium text-gray-900">₹{{ number_format($analytics['average_discount'], 2) }}</span>
                    </div>
                </div>

                @if($analytics['usage_by_status']->isNotEmpty())
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Usage by Status</h4>
                    <div class="space-y-2">
                        @foreach($analytics['usage_by_status'] as $status => $count)
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 capitalize">{{ $status }}</span>
                                <span class="text-sm font-medium text-gray-900">{{ number_format($count) }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Monthly Usage Chart -->
    @if($analytics['monthly_usage']->isNotEmpty())
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Usage Trend</h3>
            <div class="h-64">
                <canvas id="monthlyUsageChart"></canvas>
            </div>
        </div>
    </div>
    @endif

    <!-- Recent Usage -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Usage</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Original Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($analytics['recent_usage'] as $usage)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $usage->user->name ?? 'N/A' }}</div>
                                <div class="text-sm text-gray-500">{{ $usage->user->email ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ₹{{ number_format($usage->original_amount, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                -₹{{ number_format($usage->discount_amount, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ₹{{ number_format($usage->final_amount, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $usage->status === 'used' ? 'bg-green-100 text-green-800' : 
                                       ($usage->status === 'applied' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800') }}">
                                    {{ ucfirst($usage->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $usage->created_at->format('M d, Y H:i') }}
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-chart-bar text-4xl text-gray-300 mb-4"></i>
                                <p class="text-lg font-medium">No usage data</p>
                                <p class="text-sm">This coupon hasn't been used yet</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@if($analytics['monthly_usage']->isNotEmpty())
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Usage Chart
const monthlyUsageCtx = document.getElementById('monthlyUsageChart').getContext('2d');
const monthlyUsageChart = new Chart(monthlyUsageCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($analytics['monthly_usage']->map(function($item) { return $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT); })) !!},
        datasets: [{
            label: 'Usage Count',
            data: {!! json_encode($analytics['monthly_usage']->pluck('usage_count')) !!},
            borderColor: 'rgb(99, 102, 241)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.1,
            yAxisID: 'y'
        }, {
            label: 'Total Discount (₹)',
            data: {!! json_encode($analytics['monthly_usage']->pluck('total_discount')) !!},
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Month'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Usage Count'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Total Discount (₹)'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
</script>
@endpush
@endif
