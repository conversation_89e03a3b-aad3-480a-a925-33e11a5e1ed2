<?php

namespace App\Http\Controllers;

use App\Console\Commands\FetchGoogleReviews;
use App\Helpers\ReviewHelper;
use App\Jobs\FetchGoogleReviewsJob;
use App\Http\Controllers\Auth\GoogleLoginController;
use App\Models\AssociateBusiness;
use App\Models\Business;
use App\Models\BusinessAccount;
use App\Models\GoogleReview;
use App\Models\Permission;
use App\Models\Plan;
use App\Models\Setting;
use App\Models\TeamMember;
use App\Models\Template;
use App\Models\User;
use App\Models\UserBusinessPreference;
use App\Services\GoogleBusinessService;
use App\Services\SettingService;
use App\Services\TemplateService;
use App\Traits\LogsBusinessActivity;
use App\Traits\ValidatesSubscriptionUsage;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class BusinessController extends Controller
{
    use LogsBusinessActivity, ValidatesSubscriptionUsage;

    protected $googleBusinessService;

    public function __construct(GoogleBusinessService $googleBusinessService)
    {
        $this->googleBusinessService = $googleBusinessService;
    }

    /**
     * Get business account for user optimized
     */
    private function getBusinessAccountForUser($userId = null)
    {
        $userId = $userId ?: Auth::id();
        $businessAccount = BusinessAccount::where('user_id', $userId)->first();

        Log::debug('Business account retrieved', [
            'user_id' => $userId,
            'found' => !is_null($businessAccount),
            'account_id' => $businessAccount?->business_google_id
        ]);

        return $businessAccount;
    }

    /**
     * Extract location ID from location name with validation
     */
    private function extractLocationId($locationName)
    {
        if (empty($locationName)) {
            Log::warning('Empty location name provided for ID extraction');
            return null;
        }

        $parts = explode("/", $locationName);
        if (count($parts) < 2) {
            Log::warning('Invalid location name format', ['location_name' => $locationName]);
            return null;
        }

        $locationId = $parts[1];
        Log::debug('Location ID extracted', [
            'location_name' => $locationName,
            'location_id' => $locationId
        ]);

        return $locationId;
    }

    /**
     * Get business with enhanced debugging
     */
    private function getBusinessWithDebug($criteria, $relations = [])
    {
        $query = Business::query();

        if (!empty($relations)) {
            $query->with($relations);
        }

        foreach ($criteria as $key => $value) {
            $query->where($key, $value);
        }

        $business = $query->first();

        Log::debug('Business query executed', [
            'criteria' => $criteria,
            'relations' => $relations,
            'found' => !is_null($business),
            'business_id' => $business?->id
        ]);

        return $business;
    }

    /**
     * Get current business context (team member vs owner)
     */
    private function getCurrentBusinessContext()
    {
        $isTeamMember = Session::get('is_team_member', false);
        $userId = $isTeamMember ? Session::get('business_owner_id') : Auth::id();

        Log::debug('Business context determined', [
            'is_team_member' => $isTeamMember,
            'user_id' => $userId,
            'auth_user_id' => Auth::id()
        ]);

        return [
            'is_team_member' => $isTeamMember,
            'user_id' => $userId,
            'auth_user_id' => Auth::id()
        ];
    }

    /**
     * Get star rating map for conversion
     */
    private function getStarRatingMap()
    {
        return [
            'ONE' => 1,
            'TWO' => 2,
            'THREE' => 3,
            'FOUR' => 4,
            'FIVE' => 5,
        ];
    }

    /**
     * Convert star rating to numeric value
     */
    private function convertStarRatingToNumeric($starRating)
    {
        $starMap = $this->getStarRatingMap();
        $ratingWord = strtoupper(trim($starRating));
        return $starMap[$ratingWord] ?? 0;
    }

    /**
     * Get cached analytics data with debugging
     */
    private function getCachedAnalyticsData($locationId, $accountId, $timeRange, $cacheMinutes = 15)
    {
        $cacheKey = "analytics_{$locationId}_{$accountId}_{$timeRange}";

        return Cache::remember($cacheKey, $cacheMinutes * 60, function () use ($locationId, $accountId, $timeRange) {
            Log::debug('Generating analytics data', [
                'location_id' => $locationId,
                'account_id' => $accountId,
                'time_range' => $timeRange
            ]);

            $sentimentTrend = $this->getSentimentTrendData($locationId, $timeRange);
            $reviewActivity = $this->getReviewActivityData($locationId, $timeRange);

            return [
                'sentiment_trend' => $sentimentTrend,
                'review_activity' => $reviewActivity,
                'generated_at' => now()->toISOString()
            ];
        });
    }

    /**
     * Get team member data with caching and debugging
     */
    private function getTeamMemberData($userId, $useCache = true)
    {
        $cacheKey = "team_member_data_{$userId}";

        if (!$useCache) {
            Cache::forget($cacheKey);
        }

        return Cache::remember($cacheKey, 300, function () use ($userId) {
            $checkTeamMember = TeamMember::where('user_id', $userId)
                ->with(['business', 'invitedBy'])
                ->exists();

            $teamMembers = null;
            if ($checkTeamMember) {
                $teamMembers = TeamMember::where('user_id', $userId)
                    ->with(['business', 'invitedBy', 'permissions'])
                    ->get();

                // Update pending statuses
                foreach ($teamMembers as $member) {
                    if ($member->status === 'pending') {
                        $member->status = 'active';
                        $member->save();
                    }
                }
            }

            Log::debug('Team member data retrieved', [
                'user_id' => $userId,
                'is_team_member' => $checkTeamMember,
                'team_count' => $teamMembers ? $teamMembers->count() : 0
            ]);

            return [
                'is_team_member' => $checkTeamMember,
                'team_members' => $teamMembers
            ];
        });
    }

    /**
     * Get user permissions with caching
     */
    private function getUserPermissions($userId, $businessId = null)
    {
        $businessId = $businessId ?: Session::get('businessId');

        if (!$businessId) {
            return ['all'];
        }

        $cacheKey = "user_permissions_{$userId}_{$businessId}";

        return Cache::remember($cacheKey, 300, function () use ($userId, $businessId) {
            $getTeamMemberId = TeamMember::where(['business_id' => $businessId, 'user_id' => $userId])->pluck('id');

            if ($getTeamMemberId->isEmpty()) {
                return ['all'];
            }

            $grantedPermissions = Permission::where('team_member_id', $getTeamMemberId)->get();
            $permissions = $grantedPermissions->where('is_granted', true)->pluck('permission_name')->toArray();

            Log::debug('User permissions retrieved', [
                'user_id' => $userId,
                'business_id' => $businessId,
                'permissions' => $permissions
            ]);

            return $permissions;
        });
    }

    /**
     * Format review data for API response with caching for user/template lookups
     */
    private function formatReviewForResponse($review, $latestReview = null, $latestReplyReview = null)
    {
        // Convert star rating using helper method
        $rating = $this->convertStarRatingToNumeric($review->star_rating);

        // Format created_at_google to human-readable format
        $createdAt = Carbon::parse($review->created_at_google)->diffForHumans(null, true, false, 2) . ' ago';

        $isLatestReview = $latestReview && $review->id === $latestReview->id &&
            Carbon::parse($review->created_at_google)->diffInMinutes(now()) <= 15;
        $isLatestReply = $latestReplyReview && $review->id === $latestReplyReview->id &&
            Carbon::parse(optional($review->replies->first())->updated_at_google)->diffInMinutes(now()) <= 15;

        // Format replies with caching
        $replies = $review->replies->map(function ($reply) {
            return $this->formatReplyForResponse($reply);
        });

        return [
            'id' => $review->id,
            'review_id' => $review->review_id,
            'reviewer_name' => $review->reviewer_name,
            'reviewer_photo' => $review->reviewer_photo,
            'star_rating' => $review->star_rating,
            'star_rating_numeric' => $rating,
            'comment' => $review->comment,
            'created_at' => $createdAt,
            'created_at_google' => $review->created_at_google,
            'replies' => $replies,
            'is_latest_review' => $isLatestReview,
            'is_latest_reply' => $isLatestReply
        ];
    }

    /**
     * Format reply data for API response with cached user/template lookups
     */
    private function formatReplyForResponse($reply)
    {
        // Cache user names to avoid repeated queries
        $replyByName = $reply->reply_by ? $this->getCachedUserName($reply->reply_by) : null;

        // Cache template titles to avoid repeated queries
        $templateTitle = $reply->reply_template_id ? $this->getCachedTemplateTitle($reply->reply_template_id) : null;

        $updatedAt = Carbon::parse($reply->updated_at_google)->diffForHumans(null, true, false, 2) . ' ago';

        return [
            'id' => $reply->id,
            'reviewer_name' => $reply->reviewer_name,
            'comment' => $reply->comment,
            'reply_by' => $reply->reply_by,
            'reply_by_name' => $replyByName,
            'reply_edited_by' => $reply->reply_edited_by,
            'reply_template_id' => $reply->reply_template_id,
            'replied_template' => $templateTitle,
            'updated_at' => $updatedAt,
            'created_at' => $updatedAt
        ];
    }

    /**
     * Get cached user name
     */
    private function getCachedUserName($userId)
    {
        $cacheKey = "user_name_{$userId}";

        return Cache::remember($cacheKey, 3600, function () use ($userId) { // 1 hour cache
            return User::where('id', $userId)->value('name');
        });
    }

    /**
     * Get cached template title
     */
    private function getCachedTemplateTitle($templateId)
    {
        $cacheKey = "template_title_{$templateId}";

        return Cache::remember($cacheKey, 3600, function () use ($templateId) { // 1 hour cache
            return Template::where('id', $templateId)->value('title');
        });
    }

    /**
     * Apply date range filter to query with validation
     */
    private function applyDateRangeFilter($query, $dateRange)
    {
        if (!$dateRange) {
            return $query;
        }

        $now = Carbon::now();
        switch ($dateRange) {
            case 'all':
                // No filter needed
                break;
            case 'last_week':
                $query->where('created_at_google', '>=', $now->subWeek());
                Log::debug('Date range filter applied', ['range' => 'last_week']);
                break;
            case 'last_month':
                $query->where('created_at_google', '>=', $now->subMonth());
                Log::debug('Date range filter applied', ['range' => 'last_month']);
                break;
            case 'last_3_months':
                $query->where('created_at_google', '>=', $now->subMonths(3));
                Log::debug('Date range filter applied', ['range' => 'last_3_months']);
                break;
            default:
                Log::warning('Unknown date range filter', ['range' => $dateRange]);
                break;
        }

        return $query;
    }

    /**
     * Apply rating filter to query with validation
     */
    private function applyRatingFilter($query, $rating, $ratings = null)
    {
        if ($rating) {
            $query->where('star_rating', $rating);
            Log::debug('Rating filter applied', ['rating' => $rating]);
        } elseif ($ratings && is_array($ratings)) {
            $query->whereIn('star_rating', $ratings);
            Log::debug('Multiple ratings filter applied', ['ratings' => $ratings]);
        }

        return $query;
    }

    /**
     * Get validated date range based on time range parameter
     */
    private function getValidatedDateRange($timeRange)
    {
        $endDate = Carbon::now()->endOfMonth();

        switch ($timeRange) {
            case 'all':
                $startDate = Carbon::now()->subYears(10)->startOfYear();
                break;
            case '30days':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '3months':
                $startDate = Carbon::now()->subMonths(3)->startOfMonth();
                break;
            case '6months':
                $startDate = Carbon::now()->subMonths(6)->startOfMonth();
                break;
            case 'year':
            default:
                $startDate = Carbon::now()->subYear()->startOfMonth();
                break;
        }

        Log::debug('Date range validated', [
            'time_range' => $timeRange,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d')
        ]);

        return [
            'start' => $startDate,
            'end' => $endDate,
            'range' => $timeRange
        ];
    }
    public function businessDashboard(Request $request)
    {
        $userData = Auth::user();
        $user = User::with(['teamMemberships', 'businessAccounts'])->find($userData->id);
        $perPage = $request->input('per_page', 10);

        // Check for Google Business token (preferred)
        $accessToken = Session::get('business_google_token');
        $refreshToken = Session::get('business_google_refresh_token');

        $checkTeamMember = TeamMember::where('user_id', $userData->id)
            ->with(['business', 'invitedBy'])
            ->exists();

        $teamMembers = null;
        if ($checkTeamMember) {
            $teamMembers = TeamMember::where('user_id', $userData->id)
                ->with(['business', 'invitedBy', 'permissions'])
                ->get();
            foreach ($teamMembers as $member) {
                if ($member->status === 'pending') {
                    $member->status = 'active';
                    $member->save();
                }
            }
        }

        $business = Business::where(['user_id' => $user->id, 'status' => 'active'])->with('setting')->first();
        if ($business) {
            $selectedLocation = $business->location_name ?? null;
        } else {
            $selectedLocation = Session::get('selected_location');
        }

        $permissions = ['all'];
        if (Session::has('businessId') === true) {
            $getTeamMemberId = TeamMember::where(['business_id' => Session::get('businessId'), 'user_id' => $user->id])->pluck('id');
            if (count($getTeamMemberId) > 0) {
                $grantedPermissions = Permission::where('team_member_id', $getTeamMemberId)->get();
                $permissions = $grantedPermissions->where('is_granted', true)->pluck('permission_name')->toArray();
            }
        }
        $subscriptions = $user->subscriptions()->where('status', 'ACTIVE')->get();

        $showCouponPopup = false;
        if ($user && !$user->first_login_popup_shown) {
            $showCouponPopup = true;
            $user->first_login_popup_shown = true;
            $user->save();
        }

        try {
            if (Session::has('business_id')) {
                $businessId = Session::get('business_id');
            } else {
                $businessAccount = BusinessAccount::where('user_id', $user->id)->first();
                $businessId = $businessAccount->business_google_id;
            }

            //Fetch reviews if dashboard reload after 10 min
            $reviewsCacheKey = "fetched_reviews_{$businessId}_{$selectedLocation}";
            if (!Cache::has($reviewsCacheKey)) {
                if (!empty($businessId) && !empty($selectedLocation)) {
                    //FetchGoogleReviewsJob::dispatchSync($selectedLocation, $businessId);
                    $reviewsData = $this->googleBusinessService->getReviews($selectedLocation, $businessId, $accessToken);
                    ReviewHelper::storeReviews($businessId, $selectedLocation, $reviewsData);
                    Cache::put($reviewsCacheKey, true, 600);
                }
            }

            $response = [];

            if (Session::get('is_team_member') != true) {
                $response = $this->googleBusinessService->getLocations($businessId, $accessToken, 'name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng');
                ReviewHelper::insertBusiness($response, $selectedLocation, $user);
            }
            $checkLocation = $this->checkLocation();
            if (Session::has('business_id')) {
                $teamMemberBusinessIds = [];
                if ($teamMembers && $teamMembers->count() > 0) {
                    $teamMemberBusinessIds = $teamMembers->pluck('business_id')->toArray();
                }
                $associateUser = User::where('google_id', Session::get('business_id'))->first();
                if (!$associateUser) {
                    $associateUser = $user;
                }
                $associateBusinesses = AssociateBusiness::where('user_id', $associateUser->id)->get();
                $ownedBusinesses = Business::where('user_id', $associateUser->id)->get();

                $teamBusinesses = Business::whereIn('id', $teamMemberBusinessIds)
                    ->with('user')
                    ->get();

                $connectedBusinesses = $ownedBusinesses->merge($teamBusinesses)->unique('id');

                if (Session::has('is_team_member')) {
                    $preferenceBusiness = $this->insertUserBusinessPreference($user->id, 'team_member', null);

                    $business = Business::where(['id' => $preferenceBusiness->business_id])
                        ->with('setting')
                        ->first();

                    $getBusinessId = TeamMember::where(['user_id' => Auth::user()->id, 'status' => 'active'])->pluck('business_id')->toArray();
                    $connectedBusinesses = Business::whereIn('id', $getBusinessId)
                        ->with('setting')
                        ->get();
                } else {
                    $preferenceBusiness = $this->insertUserBusinessPreference($user->id, null, Session::get('selected_location'));
                    $business = Business::where(['id' => $preferenceBusiness->business_id])
                        ->with('setting')
                        ->first();
                }

                $businessId = $associateUser->google_id ?? null;
            } else {
                $this->allBusiness();

                $associateBusinesses = AssociateBusiness::where('user_id', $user->id)->get();
                $connectedBusinesses = Business::where('user_id', $user->id)->get();

                if (Session::get('selected_location')) {
                    $preferenceBusiness = $this->insertUserBusinessPreference($user->id, null, Session::get('selected_location'));
                    $business = Business::where(['id' => $preferenceBusiness->business_id])
                        ->with('setting')
                        ->first();
                } else {
                    $preferenceBusiness = Business::where(['user_id' => $user->id])
                        ->with('setting')
                        ->first();
                    $business = Business::where(['id' => $preferenceBusiness->id])
                        ->with('setting')
                        ->first();
                }
            }

            $templates = $this->getTemplates($business->id);

            $business_location_name = explode("/", $business->location_name)[1];

            $selectedLocationName = $business->location_name;
            if (Session::has('is_team_member')) {
                $teamData = TeamMember::where(['user_id' => $user->id, 'business_id' => Session::get('businessId')])->with('invitedByBusinessAccount')->first();
                $totalReviews = GoogleReview::totalReviews($business_location_name, $teamData->invitedByBusinessAccount->business_google_id);
            } else {
                $totalReviews = GoogleReview::totalReviews($business_location_name, $businessId);
            }
            $order = $request->input('order', 'desc');
            // Pass the current order to the view`
            $currentOrder = $order;

            // Get rating statistics
            $ratingStats = GoogleReview::getRatingStats($business_location_name, $businessId);

            // Get response metrics
            $responseMetrics = GoogleReview::getResponseMetrics($business_location_name, $businessId);

            // Extract values for the view with null coalescing operators for safety
            $totalRatings = $ratingStats['totalRatings'] ?? 0;
            $avgRating = $ratingStats['avgRating'] ?? 0;
            $ratingCounts = $ratingStats['ratingCounts'] ?? ['FIVE' => 0, 'FOUR' => 0, 'THREE' => 0, 'TWO' => 0, 'ONE' => 0];
            $responseRate = $responseMetrics['responseRate'] ?? 0;
            $avgResponseTime = $responseMetrics['avgResponseTime'] ?? 0;
            $reviewsWithReplies = $responseMetrics['reviewsWithReplies'] ?? 0;

            $timeRange = $request->input('timeRange', 'all');
            $sentimentTrend = $this->getSentimentTrendData($business_location_name, $timeRange);
            $reviewActivity = $this->getReviewActivityData($business_location_name, $timeRange);

            // Create a map of connected business IDs for easy lookup
            $connectedBusinessIds = $connectedBusinesses->pluck('location_name')->toArray();
            $templatesListing = Template::where('business_id', $business->id)->get()->groupBy('ratings');
            return view('business-dashboard', compact(
                'response',
                'user',
                'business',
                'templates',
                'subscriptions',
                'showCouponPopup',
                'selectedLocationName',
                'businessId',
                'sentimentTrend',
                'reviewActivity',
                'perPage',
                'timeRange',
                'totalReviews',
                'responseRate',
                'avgResponseTime',
                'avgRating',
                'totalRatings',
                'ratingCounts',
                'associateBusinesses',
                'connectedBusinesses',
                'connectedBusinessIds',
                'teamMembers',
                'permissions',
                'checkLocation',
                'checkTeamMember',
                'templatesListing'
            ));
        } catch (\Exception $e) {
            // API request failed - token may be invalid
            Log::error('Failed to fetch Google Business data:', ['error' => $e->getMessage(), 'line' => $e->getLine()]);
            $message = 'Failed to fetch your Google Business data. Please reconnect your account.';
            return redirect()->route('business.setup')->with('error', $message);
        }
    }

    public function businessSetup()
    {
        $user = Auth::user();
        $selectedLocation = Session::get('selected_location');

        // Check if user is a team member that can bypass business setup
        $isTeamMember = TeamMember::where('user_id', $user->id)->exists();
        if ($isTeamMember) {
            $teamMember = TeamMember::where('user_id', $user->id)
                ->with(['business', 'invitedBy'])
                ->first();

            if ($teamMember && $teamMember->invitedBy && $teamMember->business) {
                $googleTokens = getGoogleTokens($teamMember->invitedBy->id);

                if ($googleTokens && isset($googleTokens->business_google_token)) {
                    // Use the business owner's tokens
                    Session::put('business_id', $teamMember->invitedBy->google_id);
                    Session::put('businessId', $teamMember->business->id);
                    Session::put('selected_location', $teamMember->business->location_name);
                    Session::put('business_google_token', $googleTokens->business_google_token);
                    Session::put('business_google_refresh_token', $googleTokens->business_refresh_token);
                    Session::put('is_team_member', true);
                    Session::put('business_owner_id', $teamMember->invitedBy->id);

                    // Redirect to dashboard as team member has access through owner
                    return redirect()->route('business.dashboard');
                }
            }
        }

        if ($selectedLocation) {
            $LocationId = explode("/", $selectedLocation)[1];
            $reviews = GoogleReview::with('replies')->where('location_id', $LocationId)->get();
        } else {
            $reviews = [];
        }

        // Check if the user has connected their business account
        $businessToken = null;
        $refreshToken = null;
        $needsBusinessConnection = false;
        $response = null;

        if (Session::has('business_google_token')) {
            $businessToken = Session::get('business_google_token');
        } else {
            $businessAccountId = BusinessAccount::where('user_id', $user->id)->first();
            if ($businessAccountId && $businessAccountId->business_google_token != null) {
                $businessToken = $businessAccountId->business_google_token;
                $refreshToken = $businessAccountId->business_google_refresh_token;
                Session::put('business_google_token', $businessToken);
                Session::put('business_google_refresh_token', $refreshToken);
            }
        }

        if (!$businessToken) {
            // No business token found - user needs to connect their business account
            $needsBusinessConnection = true;
            $message = 'You need to connect your Google Business account to access business features.';
            return view('setup', compact('user', 'reviews', 'needsBusinessConnection', 'message'));
        }
        $isTokenValid = $this->googleBusinessService->isTokenValid($businessToken);
        $refreshToken = $this->googleBusinessService->refreshAccessToken();
        $businessAccountId = BusinessAccount::where('business_google_token', $refreshToken)->pluck('business_google_id')->first();
        $location = Business::where('user_id', $user->id)->pluck('location_name')->first();
        $locationExists = Business::where('user_id', $user->id)->exists();

        if ($locationExists) {
            $locationId = explode('/', $location)[1];
            $checkReviews = GoogleReview::where(['location_id' => $locationId, 'account_id' => $businessAccountId])->exists();
            if ($refreshToken && $checkReviews) {
                return redirect()->route('business.dashboard');
            }
        }

        try {
            if ($refreshToken) {
                $businessAccountData = $this->getBusinessAccount($refreshToken);
                $accountId = Session::get('google_business_id', $businessAccountData->business_google_id);
                $response = $this->googleBusinessService->getLocations($accountId, $refreshToken, 'name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng');
            } else {
                $accountId = Session::get('google_business_id', $user->google_id);
                $response = $this->googleBusinessService->getLocations($accountId, $refreshToken, 'name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng');
            }

            $checkLocation = ReviewHelper::checkBusinessLocation($response);
            $this->insertAllBusiness($response);
            // Check if response has locations
            if (!isset($response['locations']) && !is_array($response['locations'])) {
                // No locations found
                $message = 'No business locations found for your Google Business account.';
                return view('setup', compact('user', 'reviews', 'needsBusinessConnection', 'message'));
            }
        } catch (\Exception $e) {
            // API request failed - token may be invalid
            $message = 'Failed to fetch your Google Business data. Please reconnect your account.';
            $needsBusinessConnection = true;
            return view('setup', compact('user', 'reviews', 'needsBusinessConnection', 'message', 'e'));
        }
        return view('setup', compact('response', 'reviews', 'user', 'needsBusinessConnection', 'checkLocation'));
    }


    public function allBusiness()
    {
        $user = Auth::user();
        $refreshToken = $this->googleBusinessService->refreshAccessToken();
        $accountId = Session::get('google_business_id', $user->google_id);

        $response = $this->googleBusinessService->getLocations($accountId, $refreshToken, 'name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng');

        $data = $this->insertAllBusiness($response);
        return $data;
    }

    public function subscriptions()
    {
        $user = Auth::user();
        $userData = User::find($user->id);
        $businesses = Business::where('user_id', $user->id)->get();

        $checkMember = TeamMember::where('user_id', $user->id)->exists();

        if ($checkMember) {
            $getAssociateUser = TeamMember::where('user_id', $user->id)->get();
            foreach ($getAssociateUser as $associateUser) {
                return redirect()->route('business.dashboard');
                //$businesses = Business::where('user_id', $associateUser->invitedBy->id)->get();
            }
        }

        $subscriptions = $userData->subscriptions()->where('status', 'ACTIVE')->get();
        $plans = Plan::first();
        $message = "You are not subscribed to any plan. Please subscribe to a plan to access the features.";
        return view('subscription', compact('businesses', 'subscriptions', 'plans', 'message', 'user'));
    }

    public function getAnalyticsData(Request $request)
    {
        $user = Auth::user();
        $businessId = $request->input('business_id');
        $timeRange = $request->input('timeRange', 'year');

        // Find the business
        $business = Business::where('id', $businessId)->first();
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        // Get the location ID
        $businessAccountId = BusinessAccount::where('user_id', $user->id)->pluck('business_google_id')->first();
        $business_location_name = explode("/", $business->location_name)[1];

        // Set date range based on selected filter
        $endDate = Carbon::now();
        switch ($timeRange) {
            case 'all':
                $startDate = Carbon::now()->subYear(2000);
                break;
            case '30days':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '3months':
                $startDate = Carbon::now()->subMonths(3);
                break;
            case '6months':
                $startDate = Carbon::now()->subMonths(6);
                break;
            case 'year':
                $startDate = Carbon::now()->subYear()->startOfMonth();
                break;
            default:
                $startDate = Carbon::now()->subDays(7);
                break;
        }

        // Get reviews within the time range
        $reviews = GoogleReview::where([
            'location_id' => $business_location_name,
            'account_id' => $businessAccountId
        ])
            ->whereNull('parent_id')
            ->whereNotNull('star_rating')
            ->whereBetween('updated_at_google', [$startDate, $endDate])
            ->get();

        // Calculate average rating
        $totalRatings = $reviews->count();
        $sumRatings = 0;
        $ratingCounts = [
            'FIVE' => 0,
            'FOUR' => 0,
            'THREE' => 0,
            'TWO' => 0,
            'ONE' => 0
        ];

        foreach ($reviews as $review) {
            $rating = strtoupper($review->star_rating);
            if (isset($ratingCounts[$rating])) {
                $ratingCounts[$rating]++;
                $sumRatings += GoogleReview::getNumericRating($rating);
            }
        }

        $avgRating = $totalRatings > 0 ? number_format($sumRatings / $totalRatings, 1) : 0;

        // Calculate response metrics
        $reviewsWithReplies = 0;
        $totalResponseTime = 0;
        $responseCount = 0;

        foreach ($reviews as $review) {
            $reply = $review->replies()->first();
            if ($reply) {
                $reviewsWithReplies++;
                if ($review->created_at_google && $reply->updated_at_google) {
                    // Convert string dates to Carbon instances
                    $reviewDate = is_string($review->created_at_google)
                        ? Carbon::parse($review->created_at_google)
                        : $review->created_at_google;

                    $replyDate = is_string($reply->updated_at_google)
                        ? Carbon::parse($reply->updated_at_google)
                        : $reply->updated_at_google;

                    $responseTime = $reviewDate->diffInHours($replyDate);
                    $totalResponseTime += $responseTime;
                    $responseCount++;
                }
            }
        }

        $responseRate = $totalRatings > 0 ? round(($reviewsWithReplies / $totalRatings) * 100) : 0;
        $avgResponseTime = $responseCount > 0 ? round($totalResponseTime / $responseCount) : 0;

        return response()->json([
            'avgRating' => $avgRating,
            'totalReviews' => $totalRatings,
            'responseRate' => $responseRate,
            'avgResponseTime' => $avgResponseTime,
            'timeRange' => $timeRange
        ]);
    }

    public function getBusinessAccount($token)
    {
        $token = trim($token); // Trim just in case
        Log::info('Searching for business account with token: ' . $token);

        $businessAccount = BusinessAccount::where('business_google_token', $token)->first();
        if (!$businessAccount) {
            Log::warning('No business account found for token: ' . $token);
        }

        Log::info('Business account data: ' . $businessAccount);
        return $businessAccount;
    }


    // In BusinessController.php
    public function generateAutoReplies(Request $request)
    {
        // Validate subscription usage before sending replies
        $validation = $this->validateReplySendUsage();
        if (!$validation['allowed']) {
            if ($request->expectsJson()) {
                return $this->handleSubscriptionValidationResponse($validation, 'send_reply');
            } else {
                return $this->handleSubscriptionValidationRedirect($validation, 'send_reply');
            }
        }

        $businessId = $request->input('businessId');
        $location = $request->input('location');
        $reviewIds = $request->input('reviewIds');


        $reviewIds = explode(',', $reviewIds);
        $business = Business::where('location_name', 'locations/' . $location)->first();

        // Get business settings
        $settings = Setting::where('business_id', $business->id)->first();

        // Get pending reviews without replies
        $pendingReviews = GoogleReview::where('location_id', $location)
            ->whereIn('id', $reviewIds)
            ->whereNull('parent_id')
            ->whereDoesntHave('replies')
            ->with(['replies'])
            ->get();
        $allReply = [];
        foreach ($pendingReviews as $index => $review) {
            // Generate reply using OpenAI based on settings
            $reply = $this->generateReply($review, $settings);
            $allReply[$index]['reply'] = $reply;
            $allReply[$index]['review'] = $review;
            $allReply[$index]['review_id'] = $review['id'];
        }

        if (!$allReply) {
            return response()->json(['message' => 'No generatedreplies found', 'success' => false]);
        } else {
            return response()->json(['message' => 'Multiple Generated Replies', 'data' => $allReply, 'success' => true]);
        }

        // Send replies to each review
        // return redirect()->back()->with('success', 'Auto replies sent successfully');
    }

    public function sendAutoReplies(Request $request)
    {
        try {
            $data = $request->all();

            // Validate the request
            if (!isset($data['replies']) || !is_array($data['replies'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No replies data provided'
                ], 400);
            }

            $replyReviewData = $data['replies'];
            $successCount = 0;
            $failedReplies = [];

            foreach ($replyReviewData as $index => $replyReview) {
                try {
                    // Validate individual reply data
                    if (!isset($replyReview['review']) || !isset($replyReview['reply'])) {
                        $failedReplies[] = "Reply " . ($index + 1) . ": Invalid data structure";
                        continue;
                    }

                    $this->sendGoogleReply($replyReview['review'], $replyReview['reply']);
                    $successCount++;
                } catch (\Exception $e) {
                    Log::error('Failed to send individual reply', [
                        'review_id' => $replyReview['review']['id'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                    $failedReplies[] = "Reply to " . ($replyReview['review']['reviewer_name'] ?? 'unknown reviewer') . ": " . $e->getMessage();
                }
            }

            // Return JSON response based on results
            if ($successCount > 0 && empty($failedReplies)) {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully sent {$successCount} auto replies",
                    'sent_count' => $successCount
                ]);
            } elseif ($successCount > 0 && !empty($failedReplies)) {
                return response()->json([
                    'success' => true,
                    'message' => "Sent {$successCount} replies with some failures",
                    'sent_count' => $successCount,
                    'failed_count' => count($failedReplies),
                    'failures' => $failedReplies
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send any replies',
                    'failures' => $failedReplies
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Error in sendAutoReplies', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending replies: ' . $e->getMessage()
            ], 500);
        }
    }

    private function generateReply($review, $settings)
    {
        $prompt = $this->generatePrompt($review, $settings);

        try {
            $response = Http::withToken(env('OPEN_AI_KEY'))->post('https://api.openai.com/v1/chat/completions', [
                'model' => $settings->model_version,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a professional business owner.'],
                    ['role' => 'user', 'content' => $prompt . " Also include " . $review->reviewer_name . " reviewers first name properly and remove Regards with 'Your Name' and 'Your Regards' variable"],
                ],
                'max_tokens' => 150,
                'temperature' => 0.7,
            ]);

            // Log the API call
            \App\Services\ExternalApiLogger::logAi(
                $prompt,
                $response->json(),
                $response->status(),
                $response->json()['choices'][0]['message']['content'],
                Session::get('business_id') // business_id if available
            );

            if ($response->successful()) {
                $content = $response->json()['choices'][0]['message']['content'];
                Log::info('Generated Reply:', ['reply' => $content]);
                return $content;
            } else {
                Log::error('OpenAI API Error:', ['status' => $response->status(), 'error' => $response->json()]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('OpenAI API Error:', ['message' => $e->getMessage()]);
            return null;
        }
    }

    private function generatePrompt($review, $settings)
    {
        $template = $settings->template_selection;
        $rating = $review->star_rating;


        // You can create different templates based on rating and selection
        $prompt = "You are a professional business owner. Generate a reply to this review:\n\n";
        $prompt .= "Review: " . $review->comment . "\n";
        $prompt .= "Rating: " . $rating . " stars\n";

        if ($rating <= 2) {
            $prompt .= "This is a negative review. Respond professionally and apologize if needed.\n";
        } elseif ($rating <= 3) {
            $prompt .= "This is a neutral review. Respond with gratitude and address any concerns.\n";
        } else {
            $prompt .= "This is a positive review. Respond with gratitude and appreciation.\n";
        }

        $prompt .= "\nTemplate Selection: " . $template;

        return $prompt;
    }

    private function sendGoogleReply($review, $reply)
    {
        $locationId = $review['location_id'];
        $businessId = $review['account_id'];
        $token = Session::get('business_google_token');

        try {
            // Use GoogleBusinessService to send the reply
            $response = $this->googleBusinessService->replyToReview($locationId, $businessId, $review['review_id'], $reply, $token);

            // Save reply to database
            GoogleReview::create([
                'review_id' => null,
                'location_id' => $locationId,
                'account_id' => $businessId,
                'parent_id' => $review['id'],
                'reviewer_name' => 'Admin',
                'reviewer_photo' => null,
                'star_rating' => null,
                'comment' => $reply,
                'reply_by' => Auth::user()->id,
                'created_at_google' => now(),
                'updated_at_google' => now(),
            ]);

            // Log reply sent activity
            $business = Business::where('location_name', 'accounts/' . $businessId . '/locations/' . $locationId)->first();
            if ($business) {
                $this->logReplySentActivity(
                    $business->id,
                    $business->user_id,
                    $this->createActivityMetadata([
                        'review_id' => $review['review_id'],
                        'reply_type' => 'auto',
                        'reply_length' => strlen($reply),
                        'review_rating' => $review['star_rating']
                    ]),
                    false // Don't show toast for auto replies
                );
            }
        } catch (\Exception $e) {
            Log::error('Failed to send Google reply:', [
                'review_id' => $review->review_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function getSentimentTrendData($locationId, $timeRange = 'year')
    {
        // Set date range based on selected filter
        $endDate = Carbon::now()->endOfMonth(); // Ensure we include the entire current month
        switch ($timeRange) {
            case 'all':
                $startDate = Carbon::now()->subYears(10)->startOfYear(); // Go back 10 years to include all data
                $interval = '1 year';
                $format = 'Y';
                break;
            case '30days':
                $startDate = Carbon::now()->subDays(30);
                $interval = '1 day';
                $format = 'M d';
                break;
            case '3months':
                $startDate = Carbon::now()->subMonths(3)->startOfMonth(); // Start from beginning of month
                $interval = '1 month';
                $format = 'M Y';
                break;
            case '6months':
                $startDate = Carbon::now()->subMonths(6)->startOfMonth(); // Start from beginning of month
                $interval = '1 month';
                $format = 'M Y';
                break;
            case 'year':
            default:
                $startDate = Carbon::now()->subYear()->startOfMonth(); // Start from beginning of month
                $interval = '1 month';
                $format = 'M Y';
                break;
        }

        // Query for reviews within the selected time range
        $reviews = GoogleReview::where('location_id', $locationId)
            ->whereNull('parent_id')
            ->whereBetween('updated_at_google', [$startDate, $endDate])
            ->orderBy('updated_at_google')
            ->get();

        // Group by appropriate interval
        $groupedReviews = $reviews->groupBy(function ($review) use ($format) {
            if ($format === 'Y') {
                return Carbon::parse($review->updated_at_google)->format('Y');
            } else {
                return Carbon::parse($review->updated_at_google)->format($format === 'M Y' ? 'Y-m' : 'Y-m-d');
            }
        });

        $labels = [];
        $positiveData = [];
        $neutralData = [];
        $negativeData = [];

        // Create range of dates
        $period = CarbonPeriod::create($startDate, $interval, $endDate);

        foreach ($period as $date) {
            $dateKey = $format === 'M Y' ? $date->format('Y-m') : $date->format('Y-m-d');
            $dateLabel = $date->format($format);

            $labels[] = $dateLabel;

            $periodReviews = $groupedReviews->get($dateKey, collect());
            $totalPeriodReviews = $periodReviews->count();

            if ($totalPeriodReviews > 0) {
                // Calculate sentiment based on star rating
                $positiveCount = $periodReviews->whereIn('star_rating', ['FOUR', 'FIVE'])->count();
                $neutralCount = $periodReviews->where('star_rating', 'THREE')->count();
                $negativeCount = $periodReviews->whereIn('star_rating', ['ONE', 'TWO'])->count();

                $positiveData[] = round(($positiveCount / $totalPeriodReviews) * 100);
                $neutralData[] = round(($neutralCount / $totalPeriodReviews) * 100);
                $negativeData[] = round(($negativeCount / $totalPeriodReviews) * 100);
            } else {
                $positiveData[] = 0;
                $neutralData[] = 0;
                $negativeData[] = 0;
            }
        }

        return [
            'labels' => $labels,
            'positive' => $positiveData,
            'neutral' => $neutralData,
            'negative' => $negativeData
        ];
    }

    private function getReviewActivityData($locationId, $timeRange = 'year')
    {
        // Set date range based on selected filter
        $endDate = Carbon::now()->endOfMonth(); // Ensure we include the entire current month
        switch ($timeRange) {
            case 'all':
                $startDate = Carbon::now()->subYears(10)->startOfYear(); // Go back 10 years to include all data
                $interval = '1 year';
                $format = 'Y';
                break;
            case '30days':
                $startDate = Carbon::now()->subDays(30);
                $interval = '1 day';
                $format = 'M d';
                break;
            case '3months':
                $startDate = Carbon::now()->subMonths(3)->startOfMonth(); // Start from beginning of month
                $interval = '1 month';
                $format = 'M Y';
                break;
            case '6months':
                $startDate = Carbon::now()->subMonths(6)->startOfMonth(); // Start from beginning of month
                $interval = '1 month';
                $format = 'M Y';
                break;
            case 'year':
            default:
                $startDate = Carbon::now()->subYear()->startOfMonth(); // Start from beginning of month
                $interval = '1 month';
                $format = 'M Y';
                break;
        }

        // Query for reviews within the selected time range
        $reviews = GoogleReview::where('location_id', $locationId)
            ->whereNull('parent_id')
            ->whereBetween('updated_at_google', [$startDate, $endDate])
            ->orderBy('updated_at_google')
            ->get();

        // Query for responses
        $responses = GoogleReview::where('location_id', $locationId)
            ->whereNotNull('parent_id')
            ->whereBetween('updated_at_google', [$startDate, $endDate])
            ->orderBy('updated_at_google')
            ->get();

        // Group by appropriate interval
        $groupFormat = $format === 'Y' ? 'Y' : ($format === 'M Y' ? 'Y-m' : 'Y-m-d');

        $reviewsByPeriod = $reviews->groupBy(function ($review) use ($groupFormat) {
            return Carbon::parse($review->updated_at_google)->format($groupFormat);
        });

        $responsesByPeriod = $responses->groupBy(function ($response) use ($groupFormat) {
            return Carbon::parse($response->updated_at_google)->format($groupFormat);
        });

        $labels = [];
        $reviewData = [];
        $responseData = [];

        // Create range of dates
        $period = CarbonPeriod::create($startDate, $interval, $endDate);

        foreach ($period as $date) {
            $dateKey = $format === 'M Y' ? $date->format('Y-m') : $date->format('Y-m-d');
            $dateLabel = $date->format($format);

            $labels[] = $dateLabel;
            $reviewData[] = $reviewsByPeriod->get($dateKey, collect())->count();
            $responseData[] = $responsesByPeriod->get($dateKey, collect())->count();
        }

        return [
            'labels' => $labels,
            'reviews' => $reviewData,
            'responses' => $responseData
        ];
    }

    public function paginateReviews(Request $request)
    {
        try {
            $perPage = max(1, (int) $request->input('per_page', 10));
            $businessId = $request->input('business_id');
            $accountId = $request->input('account_id');

            Log::info('Paginate Reviews Request', [
                'perPage' => $perPage,
                'businessId' => $businessId,
                'accountId' => $accountId
            ]);

            // Get business using helper method
            $business = $this->getBusinessWithDebug(['id' => $businessId]);

            if (!$business) {
                Log::warning('Business not found for pagination', ['business_id' => $businessId]);
                return response()->json(['error' => 'Business not found'], 404);
            }

            // Extract location ID using helper method
            $locationId = $this->extractLocationId($business->location_name);

            // Get templates
            $templates = $this->getTemplates($businessId);

            // Get reviews with pagination
            $reviews = GoogleReview::where([
                'location_id' => $locationId,
                'account_id' => $accountId
            ])->whereNull('parent_id')->with('replies')->paginate($perPage);

            // Transform reviews data using helper method
            $reviewsData = $reviews->map(function ($review) {
                return $this->formatReviewForResponse($review);
            });

            Log::debug('Reviews paginated successfully', [
                'business_id' => $businessId,
                'location_id' => $locationId,
                'total_reviews' => $reviews->total(),
                'current_page' => $reviews->currentPage()
            ]);

            return response()->json([
                'reviews' => $reviewsData,
                'current' => $reviews->count(),
                'total' => $reviews->total(),
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'per_page' => $reviews->perPage(),
                'templates' => $templates,
                'location_name' => $locationId
            ]);
        } catch (\Exception $e) {
            Log::error('Error in paginateReviews', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'business_id' => $request->input('business_id'),
                'account_id' => $request->input('account_id')
            ]);

            return response()->json([
                'error' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get templates for a business
     */
    private function getTemplates($businessId)
    {
        return Template::where('business_id', $businessId)->get();
    }

    public function filterReviews(Request $request, $businessId, $order = null)
    {
        // Get business context using helper method
        $context = $this->getCurrentBusinessContext();

        // Get business and business account using helper methods
        if ($context['is_team_member']) {
            $business = $this->getBusinessWithDebug([
                'user_id' => Session::get('business_owner_id'),
                'id' => Session::get('businessId')
            ]);
            $businessAccount = $this->getBusinessAccountForUser(Session::get('business_owner_id'));
        } else {
            $business = $this->getBusinessWithDebug([
                'user_id' => Auth::user()->id,
                'id' => $businessId
            ]);
            $businessAccount = $this->getBusinessAccountForUser(Auth::user()->id);
        }

        if (!$business || !$businessAccount) {
            Log::warning('Business or account not found for filtering', [
                'business_id' => $businessId,
                'is_team_member' => $context['is_team_member']
            ]);
            return response()->json(['error' => 'Business or account not found'], 404);
        }

        // Extract location ID using helper method
        $locationId = $this->extractLocationId($business->location_name);
        $accountId = $businessAccount->business_google_id;
        $businessLocation = $business->location_name;

        $page = max(1, (int) $request->input('page', 1));
        $perPage = max(1, (int) $request->input('per_page', 10));

        Log::info('Filter Reviews Request', [
            'business_id' => $businessId,
            'location_id' => $locationId,
            'account_id' => $accountId,
            'page' => $page,
            'per_page' => $perPage,
            'filters' => $request->only(['date_range', 'rating', 'ratings', 'type'])
        ]);

        $query = GoogleReview::whereNull('parent_id');

        // Apply date range filter using helper method
        $query = $this->applyDateRangeFilter($query, $request->date_range);

        // Apply rating filter using helper method
        $query = $this->applyRatingFilter($query, $request->rating, $request->ratings);

        // Apply location filter
        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        // Apply account filter
        if ($accountId) {
            $query->where('account_id', $accountId);
        }

        // Apply type filter
        if ($request->type) {
            switch ($request->type) {
                case 'replied':
                    // Reviews that have at least one reply
                    $query->whereHas('replies');
                    break;
                case 'not_replied':
                    // Reviews that have no replies
                    $query->whereDoesntHave('replies');
                    break;
                case 'with_photos':
                    // Reviews with a non-null reviewer_photo
                    $query->whereNotNull('reviewer_photo');
                    break;
            }
        }

        // Order by creation date (newest first)
        if ($order === 'asc' || $order === 'desc') {
            $query->orderBy('created_at_google', $order);
        } elseif ($order === 'asc-reply' || $order === 'desc-reply') {
            $replyOrder = explode('-', $order)[0];
            $query->orderByRaw("COALESCE((
                SELECT MAX(updated_at_google)
                FROM google_reviews AS replies
                WHERE replies.parent_id = google_reviews.id
            ), google_reviews.created_at_google) $replyOrder");
        } else {
            // Default fallback order
            $query->orderBy('created_at_google', 'desc');
        }

        $latestReview = GoogleReview::whereNull('parent_id')
            ->orderBy('created_at_google', 'desc')
            ->first();

        // Fetch latest reply (based on MAX updated_at_google of any reply)
        $latestReplyReview = GoogleReview::whereNull('parent_id')
            ->whereHas('replies')
            ->with(['replies' => function ($q) {
                $q->orderBy('updated_at_google', 'desc');
            }])
            ->orderByRaw("(
            SELECT MAX(updated_at_google)
            FROM google_reviews AS replies
            WHERE replies.parent_id = google_reviews.id
        ) DESC")
            ->first();


        // Retrieve parent reviews with their replies
        $reviews = $query->with(['replies' => function ($query) {
            $query->orderBy('updated_at_google', 'desc');
        }])->paginate($perPage, ['*'], 'page', $page);


        // Transform reviews data using helper method
        $reviewsData = $reviews->map(function ($review) use ($latestReview, $latestReplyReview) {
            return $this->formatReviewForResponse($review, $latestReview, $latestReplyReview);
        });

        // Get cached templates
        $templates = Template::where('business_id', $businessId)->get();

        // Return pagination metadata along with the reviews
        return response()->json([
            'reviews' => $reviewsData,
            'templates' => $templates,
            'locationName' => $businessLocation,
            'current_page' => $reviews->currentPage(),
            'last_page' => $reviews->lastPage(),
            'total' => $reviews->total(),
            'per_page' => $reviews->perPage()
        ]);
    }

    public function getChartData(Request $request, $id)
    {
        $chartType = $request->input('chartType');
        $timeRange = $request->input('timeRange', 'year');

        // Get business using helper method
        $business = $this->getBusinessWithDebug(['id' => $id]);

        if (!$business) {
            Log::warning('Business not found for chart data', ['business_id' => $id]);
            return response()->json(['error' => 'Business not found'], 404);
        }

        // Extract location ID using helper method
        $locationId = $this->extractLocationId($business->location_name);

        // Get business account using helper method
        $businessAccount = $this->getBusinessAccountForUser(Auth::id());
        $accountId = $businessAccount ? $businessAccount->business_google_id : null;

        if (!$accountId) {
            Log::warning('Business account not found for chart data', ['user_id' => Auth::id()]);
            return response()->json(['error' => 'Business account not found'], 404);
        }

        // Get date range using helper method
        $dateRange = $this->getValidatedDateRange($timeRange);

        Log::debug('Chart data request', [
            'business_id' => $id,
            'location_id' => $locationId,
            'chart_type' => $chartType,
            'time_range' => $timeRange,
            'date_range' => $dateRange
        ]);

        // Get cached chart data based on chart type
        if ($chartType === 'sentiment') {
            $chartData = $this->getSentimentTrendData($locationId, $timeRange);
        } else {
            $chartData = $this->getReviewActivityData($locationId, $timeRange);
        }

        // Calculate summary metrics for the selected time period
        $summaryMetrics = $this->calculateSummaryMetrics($locationId, $accountId, $dateRange['start'], $dateRange['end']);

        // Add summary metrics to the response data
        $data = array_merge($chartData, ['summaryMetrics' => $summaryMetrics]);

        Log::debug('Chart data generated successfully', [
            'business_id' => $id,
            'chart_type' => $chartType,
            'has_summary_metrics' => isset($data['summaryMetrics'])
        ]);

        return response()->json($data);
    }

    /**
     * Calculate summary metrics for a given time period with caching
     */
    private function calculateSummaryMetrics($locationId, $accountId, $startDate, $endDate)
    {
        $cacheKey = "summary_metrics_{$locationId}_{$accountId}_{$startDate->format('Ymd')}_{$endDate->format('Ymd')}";

        return Cache::remember($cacheKey, 900, function () use ($locationId, $accountId, $startDate, $endDate) { // 15 minutes cache
            Log::debug('Calculating summary metrics', [
                'location_id' => $locationId,
                'account_id' => $accountId,
                'date_range' => $startDate->format('Y-m-d') . ' to ' . $endDate->format('Y-m-d')
            ]);

            // Get reviews within the selected time range
            $reviews = GoogleReview::where('location_id', $locationId)
                ->where('account_id', $accountId) // Use the account_id parameter
                ->whereNull('parent_id')
                ->whereBetween('updated_at_google', [$startDate, $endDate])
                ->get();

            $responses = GoogleReview::where('location_id', $locationId)
                ->where('account_id', $accountId)
                ->whereNotNull('parent_id')
                ->whereBetween('updated_at_google', [$startDate, $endDate])
                ->get();

            // Calculate total reviews
            $totalReviews = $reviews->count();

            // Calculate overall rating using helper method
            $overallRating = 0;
            if ($totalReviews > 0) {
                $totalStars = 0;
                foreach ($reviews as $review) {
                    $totalStars += $this->convertStarRatingToNumeric($review->star_rating);
                }
                $overallRating = $totalStars / $totalReviews;
            }

            // Calculate response metrics
            $reviewIds = $responses->pluck('parent_id')->unique();
            $reviewsWithReplies = $reviewIds->count();
            $responseTimes = [];

            // Calculate response time for each review
            foreach ($reviewIds as $reviewId) {
                $review = $reviews->firstWhere('id', $reviewId);
                $reply = $responses->firstWhere('parent_id', $reviewId);

                if ($review && $reply) {
                    $reviewDate = Carbon::parse($review->updated_at_google);
                    $replyDate = Carbon::parse($reply->updated_at_google);
                    $responseTimeHours = $reviewDate->diffInHours($replyDate);
                    $responseTimes[] = $responseTimeHours;
                }
            }

            // Calculate average response time in hours
            $avgResponseTime = count($responseTimes) > 0
                ? array_sum($responseTimes) / count($responseTimes)
                : 0;

            // Calculate response rate
            $responseRate = $totalReviews > 0
                ? round(($reviewsWithReplies / $totalReviews) * 100)
                : 0;

            $result = [
                'overallRating' => round($overallRating, 1),
                'totalReviews' => $totalReviews,
                'responseRate' => $responseRate,
                'averageResponseTime' => round($avgResponseTime, 1),
                'reviewsWithReplies' => $reviewsWithReplies
            ];

            Log::debug('Summary metrics calculated', $result);
            return $result;
        });
    }

    public function fetchNextReviews(Request $request)
    {
        $jobResponse = FetchGoogleReviewsJob::dispatchSync($request->location, $request->businessId);

        //if ($request->ajax() || $request->wantsJson()) {
        // Just return the JSON response directly from the job
        // The job already formats the response as expected
        if ($jobResponse instanceof \Illuminate\Http\JsonResponse) {
            return $jobResponse;
        }

        // Fallback in case the job doesn't return a proper response
        return response()->json([
            'success' => true,
            'message' => 'Review sync completed.',
            'status' => 200
        ]);
        //}

        // For non-AJAX requests, extract message from job response if possible
        if ($jobResponse instanceof \Illuminate\Http\JsonResponse) {
            $responseData = json_decode($jobResponse->getContent(), true);
            $message = $responseData['message'] ?? 'Review sync completed.';
            return redirect()->back()->with('success', $message);
        }

        return redirect()->back()->with('success', 'Review sync initiated and completed.');
    }

    public function insertAllBusiness($response)
    {
        $userId = Auth::id();
        $existingBusiness = AssociateBusiness::where('user_id', Auth::id())->pluck('location_name')->toArray();
        foreach ($response['locations'] as $location) {
            $locationName = $location['name'] ?? null;
            if (!$locationName || !$userId) {
                continue;
            }
            if (!in_array($locationName, $existingBusiness)) {
                AssociateBusiness::create([
                    'location_name'         => $locationName,
                    'title'                 => $location['title'] ?? null,
                    'primary_phone'         => $location['phoneNumbers']['primaryPhone'] ?? null,
                    'additional_phones'     => $location['phoneNumbers']['additionalPhones'] ?? [],
                    'region_code'           => $location['storefrontAddress']['regionCode'] ?? null,
                    'language_code'         => $location['storefrontAddress']['languageCode'] ?? null,
                    'postal_code'           => $location['storefrontAddress']['postalCode'] ?? null,
                    'administrative_area'   => $location['storefrontAddress']['administrativeArea'] ?? null,
                    'locality'              => $location['storefrontAddress']['locality'] ?? null,
                    'address_lines'         => $location['storefrontAddress']['addressLines'] ?? [],
                    'website'               => $location['websiteUri'] ?? null,
                    'latitude'              => $location['latlng']['latitude'] ?? null,
                    'longitude'             => $location['latlng']['longitude'] ?? null,
                    'profile_description'   => $location['profile']['description'] ?? null,
                    'user_id'               => $userId
                ]);
            }
        }
    }

    /**
     * Connect a business from associate businesses to user's businesses
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function connectBusiness(\Illuminate\Http\Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $user = Auth::user();

            // Check subscription limits for business connections with real-time validation
            $validation = $this->validateBusinessConnectionUsage();

            if (!$validation['allowed']) {
                return response()->json([
                    'success' => false,
                    'message' => $validation['message'],
                    'upgrade_required' => true,
                    'current_count' => $validation['current_count'],
                    'limit' => $validation['limit'],
                    'metadata' => $validation['metadata'] ?? []
                ], 403);
            }

            // Validate request
            $request->validate([
                'location_name' => 'required|string',
                'title' => 'required|string',
            ]);

            // Check if business already exists
            $existingBusiness = Business::where('location_name', $request->location_name)
                ->where('user_id', $user->id)
                ->first();

            if ($existingBusiness) {
                return response()->json([
                    'success' => false,
                    'message' => 'Business already connected',
                    'business' => $existingBusiness
                ]);
            }

            $getAssociateBusiness = AssociateBusiness::where('location_name', $request->location_name)
                ->where('user_id', $user->id)
                ->first();

            if (!$getAssociateBusiness) {
                return response()->json([
                    'success' => false,
                    'message' => 'Business not found'
                ]);
            }

            // Setup business and account
            [$business, $businessAccount] = $this->setupBusinessAndAccount($user, $getAssociateBusiness);

            // Import Google reviews for this business
            $this->importGoogleReviews($getAssociateBusiness->location_name, $businessAccount);

            // Log business connection activity
            $this->logBusinessConnectionActivity(
                $business->id,
                $user->id,
                $this->createActivityMetadata([
                    'location_name' => $request->location_name,
                    'business_title' => $request->title,
                    'connection_method' => 'manual'
                ]),
                false // Don't show toast for successful connections
            );

            return response()->json([
                'success' => true,
                'message' => 'Business connected successfully',
                'business' => $business
            ]);
        } catch (\Exception $e) {
            Log::error('Error connecting business', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Error connecting business: ' . $e->getMessage()
            ], 500);
        }
    }


    /**
     * Setup business and associated account
     *
     * @param \App\Models\User $user
     * @param \App\Models\AssociateBusiness $associateBusiness
     * @return array{\App\Models\Business, \App\Models\BusinessAccount}
     * @throws \Exception
     */
    private function setupBusinessAndAccount(\App\Models\User $user, \App\Models\AssociateBusiness $associateBusiness): array
    {
        try {
            // Create or get business
            $business = Business::firstOrCreate(
                [
                    'location_name' => $associateBusiness->location_name,
                    'user_id' => $user->id
                ],
                [
                    'title' => $associateBusiness->title,
                    'primary_phone' => $associateBusiness->primary_phone,
                    'additional_phones' => $associateBusiness->additional_phones,
                    'region_code' => $associateBusiness->region_code,
                    'language_code' => $associateBusiness->language_code,
                    'postal_code' => $associateBusiness->postal_code,
                    'administrative_area' => $associateBusiness->administrative_area,
                    'locality' => $associateBusiness->locality,
                    'address_lines' => $associateBusiness->address_lines,
                    'website' => $associateBusiness->website,
                    'latitude' => $associateBusiness->latitude,
                    'longitude' => $associateBusiness->longitude,
                    'profile_description' => $associateBusiness->profile_description,
                    'user_id' => $user->id
                ]
            );

            // Get or create business account
            $businessAccount = BusinessAccount::where('user_id', $user->id)
                ->first();

            if (!$businessAccount) {
                $businessAccount = BusinessAccount::create([
                    'user_id' => $user->id,
                    'business_email' => $user->email,
                    'business_google_id' => Session::get('business_google_business_id'),
                    'business_google_token' => Session::get('business_google_token'),
                    'business_refresh_token' => Session::get('business_google_refresh_token'),
                    'token_expires_at' => now()->addHours(1)
                ]);
            }

            // Create default templates if needed
            $templateCount = Template::where('business_id', $business->id)->count();
            if ($business->wasRecentlyCreated || $templateCount === 0) {
                TemplateService::createDefaultTemplatesForBusiness($business->id);
            }

            // Create default settings for this business
            SettingService::createDefaultSettingsForBusiness($business->id);

            return [$business, $businessAccount];
        } catch (\Exception $e) {
            Log::error('Error setting up business and account', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'business_location' => $associateBusiness->location_name
            ]);
            throw $e;
        }
    }

    /**
     * Import Google reviews for a business location
     *
     * @param string $locationName
     * @param \App\Models\BusinessAccount $businessAccount
     * @return void
     */
    private function importGoogleReviews(string $locationName, BusinessAccount $businessAccount): void
    {
        try {
            // Extract location ID from the location name
            $locationId = basename($locationName);
            $accountId = $businessAccount->business_google_id;
            $token = $businessAccount->business_google_token;

            if (!$token || !$locationId || !$accountId) {
                Log::warning('Missing required data for importing reviews', [
                    'locationId' => $locationId,
                    'accountId' => $accountId,
                    'hasToken' => !empty($token)
                ]);
                return;
            }

            // Use GoogleLoginController to fetch reviews
            $googleLoginController = new GoogleLoginController($this->googleBusinessService);
            $reviewsData = $googleLoginController->getReviews($locationName, $token);

            if (!isset($reviewsData['reviews']) || !is_array($reviewsData['reviews'])) {
                Log::info('No reviews found for import', [
                    'locationName' => $locationName
                ]);
                return;
            }

            ReviewHelper::storeReviews($accountId, $locationId, $reviewsData);

            Log::info('Successfully imported reviews', [
                'locationName' => $locationName,
                'reviewCount' => count($reviewsData['reviews'])
            ]);

            // Log reviews fetch activity
            $business = Business::where('location_name', $locationName)->first();
            if ($business) {
                $reviewCount = count($reviewsData['reviews']);
                $this->logReviewsFetchActivity(
                    $business->id,
                    $business->user_id,
                    $reviewCount,
                    $this->createActivityMetadata([
                        'location_name' => $locationName,
                        'fetch_method' => 'import'
                    ]),
                    false // Don't show toast for review fetches
                );
            }
        } catch (\Exception $e) {
            Log::error('Error importing Google reviews', [
                'message' => $e->getMessage(),
                'locationName' => $locationName
            ]);
        }
    }


    /**
     * Remove a business from user's businesses
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeBusiness(\Illuminate\Http\Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $user = Auth::user();

            // Validate request
            $request->validate([
                'location_name' => 'required|string',
            ]);

            // Find the business
            $business = Business::where('location_name', $request->location_name)
                ->where('user_id', $user->id)
                ->first();

            if (!$business) {
                return response()->json([
                    'success' => false,
                    'message' => 'Business not found'
                ]);
            }

            $businessId = $business->id;
            $locationId = explode('/', $business->location_name)[1];
            $accountId = Session::get('business_google_business_id');

            // Log business disconnection activity before deletion
            $this->logBusinessDisconnectionActivity(
                $businessId,
                $user->id,
                $this->createActivityMetadata([
                    'location_name' => $request->location_name,
                    'business_title' => $business->title,
                    'removal_method' => 'manual'
                ]),
                false // Don't show toast for successful removals
            );

            // Delete related records
            Template::where('business_id', $businessId)->delete();
            Setting::where('business_id', $businessId)->delete();
            GoogleReview::where(['location_id' => $locationId, 'account_id' => $accountId])->delete();

            // Delete the business
            $business->delete();

            return response()->json([
                'success' => true,
                'message' => 'Business removed successfully',
                'businessId' => $businessId,
                'refresh' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove business: ' . $e->getMessage()
            ]);
        }
    }

    public function changeStatus(Request $request)
    {
        $business = Business::find($request->id);

        if (!$business) {
            return response()->json([
                'success' => false,
                'message' => 'Business not found'
            ]);
        }

        $currentUser = Auth::user();
        $isTeamMember = TeamMember::where('business_id', $business->id)
            ->where('user_id', $currentUser->id)
            ->exists();

        UserBusinessPreference::where('user_id', $currentUser->id)
            ->update(['is_active' => false]);

        UserBusinessPreference::updateOrCreate(
            ['user_id' => $currentUser->id, 'business_id' => $business->id],
            ['is_active' => true]
        );

        $tokenUserId = $isTeamMember ? $business->user_id : $currentUser->id;
        $googleTokens = getGoogleTokens($tokenUserId);
        if (!$googleTokens || !isset($googleTokens->business_google_token) || !isset($googleTokens->business_refresh_token)) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to retrieve business tokens'
            ]);
        }

        Session::put('business_id', $currentUser->google_id);
        Session::put('businessId', $business->id);
        Session::put('selected_location', $business->location_name);
        Session::put('business_google_token', $googleTokens->business_google_token);
        Session::put('business_google_refresh_token', $googleTokens->business_refresh_token);
        //dd(Session::get('selected_location'));
        if ($isTeamMember) {
            Session::put('is_team_member', true);
            Session::put('business_owner_id', $business->user_id);
        } else {
            Session::forget('is_team_member');
            Session::forget('business_owner_id');
        }

        TeamMember::where('invited_by', $business->user_id)
            ->where('user_id', Auth::id())
            ->update(['business_status' => 'inactive']);

        TeamMember::where('business_id', $business->id)
            ->where('invited_by', $business->user_id)
            ->where('user_id', $currentUser->id)
            ->update(['business_status' => 'active']);

        return response()->json([
            'success' => true,
            'message' => 'Business activated for user successfully'
        ]);
    }

    public function countReviews($locationId, $rating)
    {
        $count = GoogleReview::where('location_id', $locationId)
            ->where('star_rating', $rating)
            ->count();

        return $count;
    }

    public function insertUserBusinessPreference($userId, $type = null, $locationName = null)
    {
        if ($type === 'team_member') {
            $teamMember = TeamMember::where(['user_id' => $userId, 'business_status' => 'active'])
                ->with('business')
                ->first();

            $business = $teamMember?->business;
        } else {
            $business = Business::where(['user_id' => $userId, 'location_name' => $locationName])->first();
        }
        //dd($business);
        if (!$business) {
            return null;
        }

        $existing = UserBusinessPreference::where('user_id', $userId)
            ->where('business_id', $business->id)
            ->first();

        if ($existing) {
            return $existing;
        }

        $preference = UserBusinessPreference::create([
            'user_id' => $userId,
            'business_id' => $business->id,
            'is_active' => 1
        ]);

        return $preference;
    }

    public function checkLocation()
    {
        $business = Business::where('user_id', '!=', Auth::user()->id)->with('user')->get();
        return $business;
    }
    public function getPendingReviews($accountId, $businessId, $locationId, Request $request = null)
    {
        $request = $request ?: request();
        $searchName = $request->input('reviewer_name');
        $settingValue = $request->input('setting_value');

        // Debug logging
        \Illuminate\Support\Facades\Log::info('getPendingReviews called', [
            'settingValue' => $settingValue,
            'accountId' => $accountId,
            'businessId' => $businessId,
            'locationId' => $locationId
        ]);
        // Get all pending reviews (reviews without replies)
        $reviews = GoogleReview::select(['id', 'parent_id', 'comment', 'reviewer_name', 'star_rating', 'created_at_google'])
            ->where('location_id', $locationId)
            ->where('account_id', $accountId)
            ->whereNull('parent_id')
            ->whereDoesntHave('selectedReply')
            ->when($searchName, function ($query) use ($searchName) {
                $query->where('reviewer_name', 'LIKE', "%{$searchName}%");
            })
            ->orderByDesc('created_at_google')
            ->get();

        // Apply filters based on the setting value
        if ($settingValue == '0') {
            // Business Dashboard: Return ALL pending reviews without any filtering
            $filteredReviews = $reviews;
        } else {
            // Settings Page: Apply filters based on auto_reply_settings
            $setting = Setting::where('business_id', $businessId)
                ->select('auto_reply_settings', 'from_auto_reply_date')
                ->first();



            if (!$setting) {
                // If no settings found, return all reviews
                $filteredReviews = $reviews;
            } else {
                // Parse auto reply settings
                $settings = json_decode($setting->auto_reply_settings, true) ?? [];

                if (empty($settings)) {
                    // If no settings configured, return all reviews
                    $filteredReviews = $reviews;
                } else {
                    // Get allowed ratings (where status is 'on')
                    $allowedRatings = collect($settings)
                        ->filter(fn($item) => isset($item['status']) && $item['status'] === 'on')
                        ->pluck('reply_rating')
                        ->map(fn($r) => (string) $r)
                        ->toArray();

                    // If no ratings are allowed, return all reviews (avoid empty filter)
                    if (empty($allowedRatings)) {
                        $filteredReviews = $reviews;
                    } else {
                        $ratingMap = [
                            'ONE'   => '1',
                            'TWO'   => '2',
                            'THREE' => '3',
                            'FOUR'  => '4',
                            'FIVE'  => '5',
                        ];

                        $fromAutoReplyDate = $setting->from_auto_reply_date;

                        // Filter reviews based on allowed ratings and date
                        $filteredReviews = $reviews->filter(function ($review) use ($allowedRatings, $ratingMap, $fromAutoReplyDate) {
                            $rating = strtoupper($review->star_rating);

                            // Convert rating text to number if needed
                            if (isset($ratingMap[$rating])) {
                                $rating = $ratingMap[$rating];
                            }

                            // Check if rating is allowed
                            $ratingAllowed = in_array($rating, $allowedRatings);

                            // Check if review is after the from_auto_reply_date
                            $dateAllowed = true;
                            if ($fromAutoReplyDate) {
                                $dateAllowed = $review->created_at_google >= $fromAutoReplyDate;
                            }

                            return $ratingAllowed && $dateAllowed;
                        });
                    }
                }
            }
        }

        // Apply additional search filter if provided (for both cases)
        if (!empty($searchName)) {
            $filteredReviews = $filteredReviews->filter(function ($review) use ($searchName) {
                return $review->reviewer_name && stripos($review->reviewer_name, $searchName) !== false;
            });
        }

        $result = $filteredReviews->values();

        // Debug logging
        \Illuminate\Support\Facades\Log::info('getPendingReviews result', [
            'settingValue' => $settingValue,
            'totalReviews' => $reviews->count(),
            'filteredCount' => $result->count(),
            'sampleReviewIds' => $result->take(3)->pluck('id')->toArray()
        ]);

        return $result;
    }
    /**
     * Get auto-reply settings for a business
     *
     * @param int $id Business ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAutoReplySettings($id)
    {
        try {
            // Get business with settings
            $business = $this->getBusinessWithDebug(['id' => $id], ['setting']);

            if (!$business) {
                Log::warning('Business not found for auto-reply settings', ['business_id' => $id]);
                return response()->json([
                    'autoReplyEnabled' => false,
                    'hasValidSettings' => false,
                    'message' => 'Business not found'
                ], 404);
            }

            // Check if business has settings
            if (!$business->setting) {
                Log::warning('Business settings not found', ['business_id' => $id]);
                return response()->json([
                    'autoReplyEnabled' => false,
                    'hasValidSettings' => false,
                    'message' => 'Business settings not found'
                ]);
            }

            $settings = $business->setting;
            $autoReply = $settings->auto_reply == 1;

            // Parse auto-reply settings JSON
            $autoReplySettings = json_decode($settings->auto_reply_settings ?? '[]', true);

            // Check if any template status is "on"
            $hasValidSettings = collect($autoReplySettings)->contains(function ($item) {
                return isset($item['status']) && $item['status'] === 'on';
            });

            Log::debug('Auto-reply settings retrieved', [
                'business_id' => $id,
                'auto_reply_enabled' => $autoReply,
                'has_valid_settings' => $hasValidSettings,
                'settings_count' => count($autoReplySettings)
            ]);

            return response()->json([
                'autoReplyEnabled' => $autoReply && $hasValidSettings,
                'hasValidSettings' => $hasValidSettings,
                'autoReply' => $autoReply,
                'settingsDetails' => $autoReplySettings
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching auto-reply settings', [
                'business_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'autoReplyEnabled' => false,
                'hasValidSettings' => false,
                'message' => 'Error fetching settings: ' . $e->getMessage()
            ], 500);
        }
    }
}
