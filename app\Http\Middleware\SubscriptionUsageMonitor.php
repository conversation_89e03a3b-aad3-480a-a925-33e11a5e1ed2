<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use App\Services\SubscriptionUsageTracker;
use App\Services\SubscriptionService;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionUsageMonitor
{
    protected $usageTracker;
    protected $subscriptionService;

    public function __construct(SubscriptionUsageTracker $usageTracker, SubscriptionService $subscriptionService)
    {
        $this->usageTracker = $usageTracker;
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        
        // Get real-time usage data
        $usageData = $this->usageTracker->getRealTimeUsage($user->id);
        
        // Share usage data with all views
        View::share('subscriptionUsage', $usageData);
        
        // Check for critical alerts that need immediate attention
        $criticalAlerts = $this->getCriticalAlerts($usageData);
        if (!empty($criticalAlerts)) {
            View::share('criticalSubscriptionAlerts', $criticalAlerts);
        }
        
        // Add usage summary to request for controllers
        $request->attributes->set('subscription_usage', $usageData);
        
        return $next($request);
    }

    /**
     * Get critical alerts that need immediate user attention
     */
    private function getCriticalAlerts(array $usageData): array
    {
        $criticalAlerts = [];
        
        foreach ($usageData['upgrade_alerts'] as $alert) {
            if ($alert['urgency'] === 'high') {
                $criticalAlerts[] = $alert;
            }
        }
        
        return $criticalAlerts;
    }
}
