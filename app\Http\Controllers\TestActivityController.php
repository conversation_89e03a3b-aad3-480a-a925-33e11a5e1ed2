<?php

namespace App\Http\Controllers;

use App\Services\BusinessActivityLogger;
use App\Models\Business;
use App\Models\BusinessActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TestActivityController extends Controller
{
    /**
     * Test the activity logging system
     */
    public function testActivityLogging(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }
        
        // Get first business for testing
        $business = Business::where('user_id', $user->id)->first();
        
        if (!$business) {
            return response()->json(['error' => 'No business found for user'], 404);
        }
        
        try {
            // Test different types of activity logging
            $results = [];
            
            // Test 1: Business connection activity
            $result1 = BusinessActivityLogger::logBusinessConnection(
                $business->id,
                $user->id,
                ['test' => 'connection_test', 'timestamp' => now()]
            );
            $results['business_connection'] = $result1 ? 'SUCCESS' : 'FAILED';
            
            // Test 2: Reply sent activity
            $result2 = BusinessActivityLogger::logReplySent(
                $business->id,
                $user->id,
                ['test' => 'reply_test', 'review_id' => 'test_review_123']
            );
            $results['reply_sent'] = $result2 ? 'SUCCESS' : 'FAILED';
            
            // Test 3: Reviews fetch activity
            $result3 = BusinessActivityLogger::logReviewsFetch(
                $business->id,
                $user->id,
                5, // 5 reviews fetched
                ['test' => 'fetch_test', 'source' => 'google_api']
            );
            $results['reviews_fetch'] = $result3 ? 'SUCCESS' : 'FAILED';
            
            // Test 4: Template activity
            $result4 = BusinessActivityLogger::logTemplateActivity(
                'create',
                $business->id,
                $user->id,
                ['test' => 'template_test', 'template_name' => 'Test Template']
            );
            $results['template_activity'] = $result4 ? 'SUCCESS' : 'FAILED';
            
            // Test 5: System activity
            $result5 = BusinessActivityLogger::logSystemActivity(
                $business->id,
                $user->id,
                'auto_reply_sent',
                'Automated reply sent to customer review',
                ['test' => 'system_test', 'automation_type' => 'auto_reply']
            );
            $results['system_activity'] = $result5 ? 'SUCCESS' : 'FAILED';
            
            // Get recent activity logs for verification
            $recentActivities = BusinessActivityLog::forUser($user->id)
                                                  ->forBusiness($business->id)
                                                  ->orderBy('created_at', 'desc')
                                                  ->limit(10)
                                                  ->get();
            
            return response()->json([
                'success' => true,
                'message' => 'Activity logging test completed',
                'results' => $results,
                'business' => [
                    'id' => $business->id,
                    'title' => $business->title,
                    'location_name' => $business->location_name
                ],
                'recent_activities' => $recentActivities->map(function($activity) {
                    return [
                        'id' => $activity->id,
                        'activity_type' => $activity->activity_type,
                        'activity_description' => $activity->activity_description,
                        'performed_by_type' => $activity->performed_by_type,
                        'status' => $activity->status,
                        'created_at' => $activity->created_at->format('Y-m-d H:i:s')
                    ];
                })
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Activity logging test failed',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
    
    /**
     * Get activity statistics for testing
     */
    public function getActivityStats(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }
        
        try {
            // Get activity counts by type
            $activityCounts = BusinessActivityLog::forUser($user->id)
                                                ->selectRaw('activity_type, COUNT(*) as count')
                                                ->groupBy('activity_type')
                                                ->pluck('count', 'activity_type')
                                                ->toArray();
            
            // Get activity counts by category
            $categoryCounts = BusinessActivityLog::forUser($user->id)
                                                ->selectRaw('activity_category, COUNT(*) as count')
                                                ->groupBy('activity_category')
                                                ->pluck('count', 'activity_category')
                                                ->toArray();
            
            // Get activity counts by performer type
            $performerCounts = BusinessActivityLog::forUser($user->id)
                                                 ->selectRaw('performed_by_type, COUNT(*) as count')
                                                 ->groupBy('performed_by_type')
                                                 ->pluck('count', 'performed_by_type')
                                                 ->toArray();
            
            // Get total activities
            $totalActivities = BusinessActivityLog::forUser($user->id)->count();
            
            // Get activities that count toward limits
            $limitCountingActivities = BusinessActivityLog::forUser($user->id)
                                                         ->countsTowardLimit()
                                                         ->count();
            
            return response()->json([
                'success' => true,
                'stats' => [
                    'total_activities' => $totalActivities,
                    'limit_counting_activities' => $limitCountingActivities,
                    'activity_counts' => $activityCounts,
                    'category_counts' => $categoryCounts,
                    'performer_counts' => $performerCounts
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get activity stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Clear test activity logs
     */
    public function clearTestActivities(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }
        
        try {
            // Delete activities with test metadata
            $deleted = BusinessActivityLog::forUser($user->id)
                                         ->whereJsonContains('metadata->test', 'connection_test')
                                         ->orWhereJsonContains('metadata->test', 'reply_test')
                                         ->orWhereJsonContains('metadata->test', 'fetch_test')
                                         ->orWhereJsonContains('metadata->test', 'template_test')
                                         ->orWhereJsonContains('metadata->test', 'system_test')
                                         ->delete();
            
            return response()->json([
                'success' => true,
                'message' => "Cleared {$deleted} test activity logs"
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to clear test activities',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
