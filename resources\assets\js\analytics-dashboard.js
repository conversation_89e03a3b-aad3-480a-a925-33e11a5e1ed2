import mockAPI from './mock-api.js';
/**
 * ReviewMaster AI - Analytics Dashboard Component
 * Handles the analytics visualization functionality
 */

/**
 * Initialize the analytics dashboard component
 */
function initializeAnalyticsDashboard() {
  console.log('Initializing Analytics Dashboard');
  
  // Load Chart.js from local file
  loadChartJsLibrary();
  
  // Check permissions for analytics access
  checkAnalyticsPermissions();
}

/**
 * Load Chart.js library from local file
 */
function loadChartJsLibrary() {
  if (typeof Chart !== 'undefined') {
    console.log('Chart.js already loaded');
    return;
  }
  
  console.log('Loading Chart.js from local file');
  
  // For Chrome extensions, we need to use a local file instead of CDN
  // This is a placeholder - we'll need to download Chart.js and include it in our project
  const script = document.createElement('script');
  script.src = 'assets/js/chart.min.js';
  script.onload = () => {
    console.log('Chart.js loaded successfully');
  };
  script.onerror = () => {
    console.error('Failed to load Chart.js from local file');
  };
  
  document.head.appendChild(script);
}

/**
 * Load analytics for a specific business
 */
async function loadAnalyticsForBusiness(businessId) {
  // Check if user has permission to view analytics
  if (typeof permissionsHandler !== 'undefined' && !permissionsHandler.hasPermission('view_analytics')) {
    showAnalyticsError('You do not have permission to view analytics');
    return;
  }
  console.log(`Loading analytics for business: ${businessId}`);
  
  try {
    // Show loading state
    showAnalyticsLoading();
    
    // Fetch analytics data
    const response = await mockAPI.getAnalytics(businessId);
    
    if (response.success) {
      // Render the analytics
      renderAnalytics(response.data);
    } else {
      showAnalyticsError('Failed to load analytics data');
    }
  } catch (error) {
    console.error('Error loading analytics:', error);
    showAnalyticsError('An error occurred while loading analytics');
  }
}

/**
 * Check if the user has permission to view analytics and update UI accordingly
 */
function checkAnalyticsPermissions() {
  // If permissionsHandler is not defined or not initialized, assume user has permission
  if (typeof permissionsHandler === 'undefined' || !permissionsHandler.initialized) {
    console.log('Permissions handler not initialized, assuming user has analytics permissions');
    return;
  }
  
  // Check if user has permission to view analytics
  const canViewAnalytics = permissionsHandler.hasPermission('view_analytics');
  
  // If user can't view analytics, show a message
  if (!canViewAnalytics) {
    const analyticsContainer = document.getElementById('analyticsContainer');
    if (analyticsContainer) {
      analyticsContainer.innerHTML = `
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                You do not have permission to view analytics. Contact an administrator to request access.
              </p>
            </div>
          </div>
        </div>
      `;
    }
  }
}

// Make the function globally accessible
window.loadAnalyticsForBusiness = loadAnalyticsForBusiness;
window.checkAnalyticsPermissions = checkAnalyticsPermissions;

/**
 * Show loading state for analytics
 */
function showAnalyticsLoading() {
  const chartContainers = [
    document.getElementById('sentimentChart'),
    document.getElementById('reviewActivityChart'),
    document.getElementById('topicVisualization')
  ];
  
  chartContainers.forEach(container => {
    if (container) {
      container.innerHTML = `
        <div class="flex items-center justify-center h-full">
          <div class="text-gray-500">
            <i class="fas fa-spinner fa-spin mr-2"></i>
            Loading chart data...
          </div>
        </div>
      `;
    }
  });
}

/**
 * Show error state for analytics
 */
function showAnalyticsError(message) {
  const chartContainers = [
    document.getElementById('sentimentChart'),
    document.getElementById('reviewActivityChart'),
    document.getElementById('topicVisualization')
  ];
  
  chartContainers.forEach(container => {
    if (container) {
      container.innerHTML = `
        <div class="flex flex-col items-center justify-center h-full">
          <div class="text-red-500 mb-2">
            <i class="fas fa-exclamation-circle text-xl"></i>
          </div>
          <p class="text-gray-700 mb-3">${message}</p>
          <button class="retry-analytics-btn btn-secondary text-sm py-1 px-3">
            <i class="fas fa-sync-alt mr-1"></i> Retry
          </button>
        </div>
      `;
    }
  });
  
  // Add retry functionality
  document.querySelectorAll('.retry-analytics-btn').forEach(button => {
    button.addEventListener('click', () => {
      if (selectedBusinessId) {
        loadAnalyticsForBusiness(selectedBusinessId);
      }
    });
  });
}

/**
 * Render the analytics
 */
function renderAnalytics(analytics) {
  console.log('Rendering analytics:', analytics);
  
  // Update summary metrics
  updateSummaryMetrics(analytics);
  
  // Render sentiment trend chart
  renderSentimentTrendChart(analytics.sentimentTrend);
  
  // Render review activity chart
  renderReviewActivityChart(analytics.reviewActivityData);
  
  // Render topic discovery visualization
  renderTopicDiscoveryVisualization(analytics.topTopics);
}

/**
 * Update summary metrics
 */
function updateSummaryMetrics(analytics) {
  // Overall Rating
  const overallRatingElement = document.querySelector('.text-2xl.font-semibold');
  if (overallRatingElement) {
    overallRatingElement.textContent = analytics.overallRating.toFixed(1);
  }
  
  // Total Reviews
  const totalReviewsElement = document.querySelectorAll('.text-2xl.font-semibold')[1];
  if (totalReviewsElement) {
    totalReviewsElement.textContent = analytics.totalReviews;
  }
  
  // Response Rate
  const responseRateElement = document.querySelectorAll('.text-2xl.font-semibold')[2];
  if (responseRateElement) {
    responseRateElement.textContent = `${analytics.responseRate}%`;
  }
  
  // Average Response Time
  const avgResponseTimeElement = document.querySelectorAll('.text-2xl.font-semibold')[3];
  if (avgResponseTimeElement) {
    avgResponseTimeElement.textContent = analytics.averageResponseTime;
  }
}

/**
 * Render sentiment trend chart
 */
function renderSentimentTrendChart(sentimentTrend) {
  const chartContainer = document.getElementById('sentimentChart');
  
  if (!chartContainer) return;
  
  // Clear previous chart
  chartContainer.innerHTML = '';
  
  // Create canvas element
  const canvas = document.createElement('canvas');
  chartContainer.appendChild(canvas);
  
  // Prepare data
  const labels = sentimentTrend.map(item => item.period);
  const data = sentimentTrend.map(item => item.sentiment);
  
  // Create chart
  new Chart(canvas, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Sentiment Score',
        data: data,
        fill: false,
        borderColor: '#4F46E5',
        backgroundColor: '#4F46E5',
        tension: 0.4,
        pointBackgroundColor: '#FFFFFF',
        pointBorderColor: '#4F46E5',
        pointBorderWidth: 2,
        pointRadius: 4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          min: 0,
          max: 5,
          ticks: {
            stepSize: 1
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          titleColor: '#1F2937',
          bodyColor: '#1F2937',
          borderColor: '#E5E7EB',
          borderWidth: 1,
          padding: 10,
          displayColors: false,
          callbacks: {
            label: function(context) {
              return `Sentiment: ${context.parsed.y.toFixed(1)} / 5.0`;
            }
          }
        }
      }
    }
  });
}

/**
 * Render review activity chart
 */
function renderReviewActivityChart(activityData) {
  console.log('Rendering review activity chart with data:', activityData);
  
  const chartContainer = document.getElementById('reviewActivityChart');
  if (!chartContainer) {
    console.error('Chart container not found');
    return;
  }
  
  // Clear existing chart if any
  chartContainer.innerHTML = '';
  
  // Create canvas for the chart
  const canvas = document.createElement('canvas');
  chartContainer.appendChild(canvas);
  
  // Check if Chart.js is loaded
  if (typeof Chart === 'undefined') {
    console.error('Chart.js not loaded');
    chartContainer.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500">Chart library not loaded</p></div>';
    return;
  }
  
  // Prepare data for the chart
  const labels = activityData.map(item => item.date);
  const newReviewsData = activityData.map(item => item.newReviews);
  const respondedData = activityData.map(item => item.responded);
  
  // Create the chart
  new Chart(canvas, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [
        {
          label: 'New Reviews',
          data: newReviewsData,
          backgroundColor: 'rgba(99, 102, 241, 0.7)',
          borderColor: 'rgb(99, 102, 241)',
          borderWidth: 1
        },
        {
          label: 'Responded',
          data: respondedData,
          backgroundColor: 'rgba(16, 185, 129, 0.7)',
          borderColor: 'rgb(16, 185, 129)',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            maxRotation: 90,
            minRotation: 0,
            autoSkip: true,
            maxTicksLimit: 10
          }
        },
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      },
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          callbacks: {
            footer: (tooltipItems) => {
              // Calculate response rate for this day
              const dataIndex = tooltipItems[0].dataIndex;
              const newReviews = newReviewsData[dataIndex];
              const responded = respondedData[dataIndex];
              
              if (newReviews === 0) return 'No reviews on this day';
              
              const responseRate = Math.round((responded / newReviews) * 100);
              return `Response Rate: ${responseRate}%`;
            }
          }
        }
      }
    }
  });
}

/**
 * Render topic discovery visualization
 */
function renderTopicDiscoveryVisualization(topicsData) {
  const chartContainer = document.getElementById('topicVisualization');
  
  if (!chartContainer) return;
  
  // For simplicity, we'll use a horizontal bar chart instead of a word cloud
  // Clear previous chart
  chartContainer.innerHTML = '';
  
  // Create canvas element
  const canvas = document.createElement('canvas');
  chartContainer.appendChild(canvas);
  
  // Prepare data
  const labels = topicsData.map(item => item.topic);
  const counts = topicsData.map(item => item.count);
  const sentiments = topicsData.map(item => item.sentiment);
  
  // Create a gradient based on sentiment
  const colors = sentiments.map(sentiment => {
    // Color ranges from red (1) to green (5)
    if (sentiment >= 4.5) return '#10B981'; // Green
    if (sentiment >= 4.0) return '#34D399'; // Light green
    if (sentiment >= 3.5) return '#FBBF24'; // Yellow
    if (sentiment >= 3.0) return '#F59E0B'; // Orange
    return '#EF4444'; // Red
  });
  
  // Create chart
  new Chart(canvas, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        axis: 'y',
        data: counts,
        backgroundColor: colors,
        borderRadius: 6
      }]
    },
    options: {
      indexAxis: 'y',
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          titleColor: '#1F2937',
          bodyColor: '#1F2937',
          borderColor: '#E5E7EB',
          borderWidth: 1,
          padding: 10,
          displayColors: false,
          callbacks: {
            label: function(context) {
              const index = context.dataIndex;
              return [
                `Mentions: ${counts[index]}`,
                `Sentiment: ${sentiments[index].toFixed(1)} / 5.0`
              ];
            }
          }
        }
      }
    }
  });
}
