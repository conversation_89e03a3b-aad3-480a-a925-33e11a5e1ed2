@extends('admin.layouts.app')

@section('title', 'Create Email Template')
@section('page-title', 'Create Email Template')

@push('styles')
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .variable-tag {
        @apply px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded cursor-pointer hover:bg-blue-200;
    }
    .ql-editor {
        min-height: 300px;
    }
    .preview-frame {
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        min-height: 400px;
    }
</style>
@endpush

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create Email Template</h1>
            <p class="text-gray-600">Create a new email template for system or custom use</p>
        </div>
        <a href="{{ route('admin.email-templates.index') }}" 
           class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
            <i class="fas fa-arrow-left mr-2"></i>Back to Templates
        </a>
    </div>

    <form method="POST" action="{{ route('admin.email-templates.store') }}" id="templateForm">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Template Name *</label>
                                <input type="text" name="name" value="{{ old('name') }}" required
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Category *</label>
                                <select name="category" required
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('category') border-red-500 @enderror">
                                    <option value="">Select Category</option>
                                    <option value="system" {{ old('category') == 'system' ? 'selected' : '' }}>System</option>
                                    <option value="custom" {{ old('category') == 'custom' ? 'selected' : '' }}>Custom</option>
                                </select>
                                @error('category')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Type *</label>
                                <select name="type" required
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('type') border-red-500 @enderror">
                                    <option value="">Select Type</option>
                                    @foreach($availableTypes as $value => $label)
                                        <option value="{{ $value }}" {{ old('type') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                    @endforeach
                                </select>
                                @error('type')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex items-center space-x-4 pt-6">
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Active</span>
                                </label>

                                <label class="flex items-center">
                                    <input type="checkbox" name="is_default" value="1" {{ old('is_default') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Default for Type</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Subject Line *</label>
                            <input type="text" name="subject" value="{{ old('subject') }}" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('subject') border-red-500 @enderror"
                                   placeholder="e.g., Welcome to {{app_name}}!">
                            @error('subject')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea name="description" rows="2"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('description') border-red-500 @enderror"
                                      placeholder="Brief description of this template">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Email Content -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Email Content</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">HTML Content *</label>
                            <div id="html-editor" style="height: 300px;">{!! old('html_content') !!}</div>
                            <input type="hidden" name="html_content" id="html_content">
                            @error('html_content')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Plain Text Content (Optional)</label>
                            <textarea name="text_content" rows="8"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('text_content') border-red-500 @enderror"
                                      placeholder="Plain text version of the email">{{ old('text_content') }}</textarea>
                            @error('text_content')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Variables Configuration -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Variables Configuration</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Available Variables</label>
                            <div id="available-variables" class="border border-gray-300 rounded-md p-3 min-h-[100px]">
                                <p class="text-sm text-gray-500">Select variables that can be used in this template</p>
                            </div>
                            <input type="hidden" name="available_variables" id="available_variables_input">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Variables</label>
                            <div id="required-variables" class="border border-gray-300 rounded-md p-3 min-h-[100px]">
                                <p class="text-sm text-gray-500">Select variables that are required for this template</p>
                            </div>
                            <input type="hidden" name="required_variables" id="required_variables_input">
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4">
                    <a href="{{ route('admin.email-templates.index') }}" 
                       class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </a>
                    <button type="button" id="preview-btn"
                            class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        <i class="fas fa-eye mr-2"></i>Preview
                    </button>
                    <button type="submit"
                            class="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <i class="fas fa-save mr-2"></i>Create Template
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Global Variables -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Global Variables</h3>
                        <p class="text-sm text-gray-600">Click to insert into content</p>
                    </div>
                    <div class="p-6">
                        <div class="space-y-2">
                            @foreach($globalVariables as $key => $label)
                                <div class="variable-tag" data-variable="{{ $key }}" title="{{ $label }}">
                                    {{ '{{' . $key . '}}' }}
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Live Preview</h3>
                    </div>
                    <div class="p-6">
                        <iframe id="preview-frame" class="preview-frame w-full" src="about:blank"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Preview Modal -->
<div id="preview-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div class="p-6 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Email Preview</h3>
                <button type="button" id="close-preview" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[70vh]">
                <div class="mb-4">
                    <div class="flex space-x-2">
                        <button type="button" id="preview-web" class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm">Web View</button>
                        <button type="button" id="preview-mobile" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md text-sm">Mobile View</button>
                    </div>
                </div>
                <div id="preview-content" class="border border-gray-300 rounded-md">
                    <iframe id="preview-iframe" class="w-full h-96" src="about:blank"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Quill editor
    const quill = new Quill('#html-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'align': [] }],
                ['link', 'image'],
                ['clean']
            ]
        }
    });

    // Variable management
    let availableVariables = [];
    let requiredVariables = [];

    // Variable insertion
    document.querySelectorAll('.variable-tag').forEach(tag => {
        tag.addEventListener('click', function() {
            const variable = this.dataset.variable;
            const variableText = `{{${variable}}}`;
            
            // Insert into Quill editor
            const range = quill.getSelection();
            if (range) {
                quill.insertText(range.index, variableText);
            } else {
                quill.insertText(quill.getLength(), variableText);
            }

            // Add to available variables if not already present
            if (!availableVariables.includes(variable)) {
                availableVariables.push(variable);
                updateVariableDisplay();
            }
        });
    });

    // Update variable display
    function updateVariableDisplay() {
        const availableDiv = document.getElementById('available-variables');
        const requiredDiv = document.getElementById('required-variables');

        // Update available variables
        if (availableVariables.length > 0) {
            availableDiv.innerHTML = availableVariables.map(variable => 
                `<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded mr-2 mb-2 cursor-pointer" 
                       onclick="toggleRequired('${variable}')">{{${variable}}}</span>`
            ).join('');
        } else {
            availableDiv.innerHTML = '<p class="text-sm text-gray-500">No variables selected</p>';
        }

        // Update required variables
        if (requiredVariables.length > 0) {
            requiredDiv.innerHTML = requiredVariables.map(variable => 
                `<span class="inline-block px-2 py-1 text-xs bg-red-100 text-red-800 rounded mr-2 mb-2 cursor-pointer" 
                       onclick="toggleRequired('${variable}')">{{${variable}}}</span>`
            ).join('');
        } else {
            requiredDiv.innerHTML = '<p class="text-sm text-gray-500">No required variables</p>';
        }

        // Update hidden inputs
        document.getElementById('available_variables_input').value = JSON.stringify(availableVariables);
        document.getElementById('required_variables_input').value = JSON.stringify(requiredVariables);
    }

    // Toggle required variable
    window.toggleRequired = function(variable) {
        const index = requiredVariables.indexOf(variable);
        if (index > -1) {
            requiredVariables.splice(index, 1);
        } else {
            requiredVariables.push(variable);
        }
        updateVariableDisplay();
    };

    // Form submission
    document.getElementById('templateForm').addEventListener('submit', function() {
        document.getElementById('html_content').value = quill.root.innerHTML;
    });

    // Preview functionality
    document.getElementById('preview-btn').addEventListener('click', function() {
        document.getElementById('html_content').value = quill.root.innerHTML;
        document.getElementById('preview-modal').classList.remove('hidden');
        updatePreview();
    });

    document.getElementById('close-preview').addEventListener('click', function() {
        document.getElementById('preview-modal').classList.add('hidden');
    });

    function updatePreview() {
        const htmlContent = quill.root.innerHTML;
        const iframe = document.getElementById('preview-iframe');
        const doc = iframe.contentDocument || iframe.contentWindow.document;
        
        doc.open();
        doc.write(htmlContent);
        doc.close();
    }

    // Mobile/Web preview toggle
    document.getElementById('preview-web').addEventListener('click', function() {
        this.classList.add('bg-indigo-600', 'text-white');
        this.classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-mobile').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-mobile').classList.remove('bg-indigo-600', 'text-white');
        document.getElementById('preview-iframe').style.width = '100%';
    });

    document.getElementById('preview-mobile').addEventListener('click', function() {
        this.classList.add('bg-indigo-600', 'text-white');
        this.classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-web').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-web').classList.remove('bg-indigo-600', 'text-white');
        document.getElementById('preview-iframe').style.width = '375px';
    });
});
</script>
@endpush
