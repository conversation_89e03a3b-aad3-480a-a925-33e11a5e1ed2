<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminSiteSetting;
use App\Http\Requests\Admin\SiteSettingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\Rule;

class SiteSettingController extends Controller
{
    /**
     * Display site settings grouped by category
     */
    public function index(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        // Get settings based on admin access level
        $query = AdminSiteSetting::query();
        
        if ($admin->role !== 'super_admin') {
            $query->where('access_level', '!=', 'super_admin');
        }
        
        if ($admin->role === 'moderator') {
            $query->where('access_level', 'moderator');
        }

        // Apply category filter if provided
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        $settings = $query->orderBy('category')
            ->orderBy('group')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('category')
            ->map(function ($categorySettings) {
                return $categorySettings->groupBy('group');
            });

        // Get available categories for filter
        $categories = AdminSiteSetting::select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');

        return view('admin.site-settings.index', compact('settings', 'categories'));
    }

    /**
     * Show the form for editing site settings
     */
    public function edit(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        $category = $request->input('category', 'general');
        
        // Get settings for the specified category
        $query = AdminSiteSetting::where('category', $category);
        
        if ($admin->role !== 'super_admin') {
            $query->where('access_level', '!=', 'super_admin');
        }
        
        if ($admin->role === 'moderator') {
            $query->where('access_level', 'moderator');
        }

        $settings = $query->orderBy('group')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('group');

        // Get available categories
        $categories = AdminSiteSetting::select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');

        return view('admin.site-settings.edit', compact('settings', 'categories', 'category'));
    }

    /**
     * Update site settings
     */
    public function update(SiteSettingRequest $request)
    {
        $admin = Auth::guard('admin')->user();
        $settings = $request->validated()['settings'];
        $updated = 0;

        foreach ($settings as $key => $value) {
            $setting = AdminSiteSetting::where('key', $key)->first();

            if (!$setting) {
                continue;
            }

            // Update the setting (validation already handled by request class)
            $setting->update([
                'value' => $value,
                'updated_by' => $admin->id,
            ]);

            $updated++;
        }

        return back()->with('success', "Successfully updated {$updated} settings.");
    }

    /**
     * Reset a setting to its default value
     */
    public function reset(Request $request)
    {
        // Rate limiting for setting resets
        $key = 'setting-reset:' . Auth::guard('admin')->id();
        if (RateLimiter::tooManyAttempts($key, 10)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many reset attempts. Please wait before trying again.'
            ], 429);
        }

        RateLimiter::hit($key, 60); // 1 minute
        $admin = Auth::guard('admin')->user();
        $key = $request->input('key');
        
        $setting = AdminSiteSetting::where('key', $key)->first();
        
        if (!$setting) {
            return response()->json(['success' => false, 'message' => 'Setting not found']);
        }

        // Check access level
        if ($admin->role !== 'super_admin' && $setting->access_level === 'super_admin') {
            return response()->json(['success' => false, 'message' => 'Access denied']);
        }

        $setting->update([
            'value' => $setting->default_value,
            'updated_by' => $admin->id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Setting reset to default value',
            'value' => $setting->default_value
        ]);
    }

    /**
     * Get setting value by key (API endpoint)
     */
    public function getValue(Request $request, $key)
    {
        $setting = AdminSiteSetting::where('key', $key)->first();
        
        if (!$setting) {
            return response()->json(['error' => 'Setting not found'], 404);
        }

        // Check if setting is public or user has access
        $admin = Auth::guard('admin')->user();
        if (!$setting->is_public && !$admin) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        if ($admin && $admin->role !== 'super_admin' && $setting->access_level === 'super_admin') {
            return response()->json(['error' => 'Access denied'], 403);
        }

        return response()->json([
            'key' => $setting->key,
            'value' => $setting->getTypedValue(),
            'type' => $setting->type
        ]);
    }

    /**
     * Bulk update settings via API
     */
    public function bulkUpdate(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        $settings = $request->input('settings', []);
        $results = [];

        foreach ($settings as $key => $value) {
            $setting = AdminSiteSetting::where('key', $key)->first();
            
            if (!$setting) {
                $results[$key] = ['success' => false, 'message' => 'Setting not found'];
                continue;
            }

            // Check access level
            if ($admin->role !== 'super_admin' && $setting->access_level === 'super_admin') {
                $results[$key] = ['success' => false, 'message' => 'Access denied'];
                continue;
            }

            // Validate the value
            $validationResult = $this->validateSettingValue($setting, $value);
            
            if (!$validationResult['valid']) {
                $results[$key] = ['success' => false, 'errors' => $validationResult['errors']];
                continue;
            }

            // Update the setting
            $setting->update([
                'value' => $validationResult['value'],
                'updated_by' => $admin->id,
            ]);

            $results[$key] = ['success' => true, 'message' => 'Updated successfully'];
        }

        return response()->json(['results' => $results]);
    }

    /**
     * Export settings as JSON
     */
    public function export(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        $query = AdminSiteSetting::query();
        
        if ($admin->role !== 'super_admin') {
            $query->where('access_level', '!=', 'super_admin');
        }

        $settings = $query->get()->map(function ($setting) {
            return [
                'key' => $setting->key,
                'name' => $setting->name,
                'value' => $setting->getTypedValue(),
                'type' => $setting->type,
                'category' => $setting->category,
                'group' => $setting->group,
                'description' => $setting->description,
            ];
        });

        $filename = 'site_settings_' . now()->format('Y-m-d_H-i-s') . '.json';

        return response()->json($settings, 200, [
            'Content-Type' => 'application/json',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Clear all settings cache
     */
    public function clearCache()
    {
        AdminSiteSetting::clearCache();
        
        return back()->with('success', 'Settings cache cleared successfully.');
    }

    /**
     * Validate setting value based on its type and rules
     */
    private function validateSettingValue($setting, $value)
    {
        $rules = [];
        
        // Add type-specific validation
        switch ($setting->type) {
            case 'integer':
                $rules[] = 'integer';
                break;
            case 'float':
                $rules[] = 'numeric';
                break;
            case 'boolean':
                $rules[] = 'boolean';
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'email':
                $rules[] = 'email';
                break;
            case 'url':
                $rules[] = 'url';
                break;
            case 'json':
                if (is_string($value)) {
                    $decoded = json_decode($value, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return ['valid' => false, 'errors' => ['Invalid JSON format']];
                    }
                    $value = $decoded;
                }
                break;
        }

        // Add custom validation rules if defined
        if ($setting->validation_rules) {
            $rules = array_merge($rules, $setting->validation_rules);
        }

        // Add required validation if setting is required
        if ($setting->is_required) {
            array_unshift($rules, 'required');
        }

        if (empty($rules)) {
            return ['valid' => true, 'value' => $value];
        }

        $validator = Validator::make(['value' => $value], ['value' => $rules]);

        if ($validator->fails()) {
            return ['valid' => false, 'errors' => $validator->errors()->get('value')];
        }

        return ['valid' => true, 'value' => $value];
    }
}
