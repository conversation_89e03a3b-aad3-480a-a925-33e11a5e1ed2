<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ReviewMaster AI - Pricing</title>
    <link rel="shortcut icon" type="image/x-icon" href="{{ url('rm-icon.ico') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>

    @vite(['resources/assets/css/styles.css'])
    <style>
        .header-scrolled {
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(8px);
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let mobileEventListenersAdded = false;

            function handleResponsiveLayout() {
                const screenWidth = window.innerWidth;
                const sideMenuBar = document.getElementById('sideMenuBar');
                const menubarToggle = document.getElementById('menubarToggle');
                const filterMenuBtn = document.getElementById('filterMenuBtn');
                const backdrop = document.querySelector('.backdrop');

                if (screenWidth >= 768) {
                    // Desktop view
                    if (sideMenuBar) {
                        sideMenuBar.classList.add('hidden');
                    }
                    document.body.classList.remove('sidebar-active', 'menu-active', 'filter-sidebar-active');
                    if (backdrop) {
                        backdrop.classList.add('hidden');
                    }
                    if (menubarToggle) {
                        menubarToggle.classList.remove('active');
                    }
                } else {
                    // Mobile view - add event listeners only once
                    if (!mobileEventListenersAdded) {
                        if (menubarToggle) {
                            menubarToggle.addEventListener('click', function() {
                                if (sideMenuBar) {
                                    sideMenuBar.classList.remove('hidden');
                                }
                                document.body.classList.toggle('menu-active');
                                this.classList.toggle('active');
                                if (backdrop) {
                                    backdrop.classList.remove('hidden');
                                }
                            });
                        }

                        if (filterMenuBtn) {
                            filterMenuBtn.addEventListener('click', function() {
                                document.body.classList.toggle('filter-sidebar-active');
                                if (backdrop) {
                                    backdrop.classList.remove('hidden');
                                }
                            });
                        }

                        const filterClose = document.getElementsByClassName('filter-close')[0];
                        if (filterClose) {
                            filterClose.addEventListener('click', function() {
                                document.body.classList.remove('filter-sidebar-active');
                            });
                        }

                        if (backdrop) {
                            backdrop.addEventListener('click', function() {
                                document.body.classList.remove('sidebar-active', 'menu-active', 'filter-sidebar-active');
                                this.classList.add('hidden');
                                if (menubarToggle) {
                                    menubarToggle.classList.remove('active');
                                }
                            });
                        }

                        mobileEventListenersAdded = true;
                    }
                }
            }

            // Initial setup
            handleResponsiveLayout();

            // Listen for window resize events
            window.addEventListener('resize', function() {
                handleResponsiveLayout();
            });
        })
    </script>
</head>

<body class="font-poppins bg-gray-100 text-gray-900 flex flex-col justify-between min-h-screen">

    <!-- Header Section -->
    @include('partials.header-nav')
    @include('partials.sidebar')
    <!-- Main Content Section -->
    <main>
        @yield('content')
    </main>


    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-6 md:py-8">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center flex-col text-center">
                <!-- <h4 class="text-lg font-semibold mb-4">ReviewMaster AI</h4> -->
                <a href="#" class="flex items-center mb-4">
                    <img src="{{ url('logo.png') }}" alt="Logo" width="185" height="43">
                </a>
                <p class="text-gray-400">Empowering your businesses to manage and improve the online reputation with AI</p>
            </div>
        </div>
    </footer>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.getElementById('mainHeader');

            window.addEventListener('scroll', () => {
                if (window.scrollY > 50) {
                    header.classList.add('header-scrolled');
                } else {
                    header.classList.remove('header-scrolled');
                }
            });

            const faqButtons = document.querySelectorAll('.faq-button');

            faqButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const content = button.nextElementSibling;
                    const icon = button.querySelector('.fas');

                    // Close all other FAQs
                    document.querySelectorAll('.faq-content').forEach(item => {
                        if (item !== content) {
                            item.classList.add('hidden');
                            item.previousElementSibling.querySelector('.fas').classList.remove('rotate-180');
                        }
                    });

                    // Toggle current FAQ
                    content.classList.toggle('hidden');
                    icon.classList.toggle('rotate-180');
                });
            });
        });
    </script>
    @vite(['resources/assets/js/app.js'])
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('js/permissions-handler.js') }}"></script>


    @if(isset($businessAccount))
    <script>
        // Initialize permissions handler with current business account
        document.addEventListener('DOMContentLoaded', function() {
            permissionsHandler.loadUserPermissions("{{ $businessAccount->id }}");
        });
    </script>
    @endif

    @stack('scripts')
</body>

</html>