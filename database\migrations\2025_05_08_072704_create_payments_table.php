<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('plan_id')->nullable();
            $table->enum('payment_type', ['ONE_TIME_PAYMENT', 'COUPON_CODE']);
            $table->enum('gateway_name', ['STRIPE', 'RAZORPAY', 'NONE'])->default('NONE');
            $table->string('transaction_id', 100)->nullable();
            $table->string('order_id', 100)->nullable();
            $table->string('coupon_code', 50)->nullable();
            $table->decimal('amount', 10, 2)->nullable();
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('original_amount', 10, 2)->nullable();
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->string('currency', 10)->default('INR');
            $table->enum('payment_status', ['PENDING', 'SUCCESS', 'FAILED', 'REFUNDED'])->default('PENDING');
            $table->string('payment_method', 50)->nullable();
            $table->enum('billing_cycle', ['monthly', 'annual'])->default('monthly');
            $table->timestamp('payment_date')->useCurrent();
            $table->string('refund_id', 100)->nullable();
            $table->timestamp('refund_date')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('plan_id')->references('id')->on('plans')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
