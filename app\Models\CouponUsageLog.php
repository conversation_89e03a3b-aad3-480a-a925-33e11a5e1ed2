<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CouponUsageLog extends Model
{
    protected $table = 'coupon_usage_logs';
    
    protected $fillable = [
        'coupon_id',
        'user_id',
        'payment_id',
        'subscription_id',
        'original_amount',
        'discount_amount',
        'final_amount',
        'coupon_code',
        'status',
        'metadata'
    ];

    protected $casts = [
        'original_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * Get the coupon that owns this usage log
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }

    /**
     * Get the user that owns this usage log
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment associated with this usage log
     */
    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the subscription associated with this usage log
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Mark usage as completed
     */
    public function markAsUsed()
    {
        $this->update(['status' => 'used']);
    }

    /**
     * Mark usage as refunded
     */
    public function markAsRefunded()
    {
        $this->update(['status' => 'refunded']);
        
        // Decrement coupon usage count
        $this->coupon->decrement('usage_count');
    }
}
