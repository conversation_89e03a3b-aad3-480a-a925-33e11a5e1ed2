<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Business Reviews</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body class="bg-white">
    <div class="flex flex-col lg:flex-row w-full bg-white overflow-hidden shadow-2xl h-auto lg:h-screen">
        <!-- Left Section -->
        <div class="flex-col justify-center w-2/2 lg:w-1/2 p-4 md:p-12 bg-[#0F1D36] text-white hidden lg:flex">
            <div class="max-w-[500px] mx-auto">
                <div class="flex items-center justify-center mb-20 text-center">
                    <!-- <i class="fas fa-star-half-alt text-5xl text-white mr-4"></i>
                    <h1 class="text-4xl font-bold">ReviewMaster AI</h1> -->
                    <img src="{{ url('logo.png') }}" alt="ReviewMaster AI Logo" width="285" height="43" class="mr-2">
                </div>
                <h2 class="text-3xl font-bold mb-4 text-center hidden md:block">Welcome Back!</h2>
                <p class="text-purple-100 mb-12 text-center hidden md:block">Log in to your account to manage your reviews, respond to customers, and grow your business with AI-powered insights.</p>

                <!-- <div class="bg-purple-400/20 p-6 rounded-xl mb-8 hidden md:block">
                    <p class="text-sm text-purple-100"><i class="fas fa-clock mr-2"></i>We'll remember your device for 30 days.</p>
                </div> -->
            </div>
        </div>

        <!-- Right Section -->
        <div class="w-2/2 lg:w-1/2 flex flex-col items-center justify-center h-screen">
            <div class="flex items-center lg:hidden justify-center p-4 text-center bg-[#0F1D36] absolute top-0 left-0 w-full z-10">
                <!-- <i class="fas fa-star-half-alt text-5xl text-white mr-4"></i>
                <h1 class="text-4xl font-bold">ReviewMaster AI</h1> -->
                <img src="{{ url('logo.png') }}" alt="ReviewMaster AI Logo" width="285" height="43" class="mr-2 w-[220px] h-auto md:w-[285px] md:h-[43px]">
            </div>
            <div class="max-w-full w-full h-[calc(100vh-65px)] md:h-auto overflow-auto pt-[65px] md:pt-[75px]">
                <div class="p-6 w-full max-w-[550px] mx-auto">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">Sign Up</h2>
                    <form class="flex flex-col gap-4 relative" method="POST" action="{{ route('register') }}">
                        @csrf
                        <div class="text-center mt-6">
                            <a href="{{ route('auth.google') }}" type="button" class="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition flex items-center justify-center gap-2">
                                <i class="fa-brands fa-google"></i>
                                <span class="text-gray-700">Google</span>
                            </a>
                        </div>
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-300"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-white text-gray-500">Or continue with</span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2" for="email">Name</label>
                            <div class="relative">
                                <input type="text" id="email" name="name" placeholder="Name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                                <i class="fas fa-user absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            </div>
                            @if ($errors->has('name'))
                            <div class="bg-red-100 border border-red-200 text-red-700 px-4 py-1 mt-2 text-sm rounded relative">
                                {{ $errors->first('name') }}
                            </div>
                            @endif
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2" for="email">Email Address</label>
                            <div class="relative">
                                <input type="email" id="email" name="email" placeholder="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                                <i class="fas fa-envelope absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            </div>
                            @if ($errors->has('email'))
                            <div class="bg-red-100 border border-red-200 text-red-700 px-4 py-1 mt-2 text-sm rounded relative">
                                {{ $errors->first('email') }}
                            </div>
                            @endif
                        </div>

                        <div>
                            <div class="flex justify-between mb-2">
                                <label class="text-gray-700" for="password">Password</label>
                            </div>
                            <div class="relative">
                                <input type="password" id="password" name="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                                <i class="fas fa-eye absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer" id="togglePassword" onclick="togglePasswordVisibility('password')"></i>
                            </div>
                            @if ($errors->has('password'))
                            <div class="bg-red-100 border border-red-200 text-red-700 px-4 py-1 mt-2 text-sm rounded relative">
                                {{ $errors->first('password') }}
                            </div>
                            @endif
                        </div>

                        <div>
                            <div class="flex justify-between mb-2">
                                <label class="text-gray-700" for="password">Confirm Password</label>
                            </div>
                            <div class="relative">
                                <input type="password" id="confirm_password" name="confirm_password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                                <i class="fas fa-eye absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer" id="toggleConfirmPassword" onclick="togglePasswordVisibility('confirm_password')"></i>
                            </div>
                            @if ($errors->has('confirm_password'))
                            <div class="bg-red-100 border border-red-200 text-red-700 px-4 py-1 mt-2 text-sm rounded relative">
                                {{ $errors->first('confirm_password') }}
                            </div>
                            @endif
                        </div>

                        <button type="submit" class="w-full bg-[#006AFF] hover:bg-blue-700  text-white font-semibold py-3 px-4 rounded-lg transition flex items-center justify-center gap-2">
                            Sign Up
                            <i class="fas fa-arrow-right"></i>
                        </button>



                        <p class="text-center text-gray-600">
                            Don't have an account? <a href="{{ route('login') }}" class="text-[#006AFF] hover:text-purple-700 font-semibold">Sign In</a>
                        </p>

                        <div class="flex flex-wrap flex-col md:flex-row items-center text-center lg:text-inline justify-center gap-2 text-gray-500 text-sm">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-lock"></i>
                                <span>Secure Login</span>
                            </div>
                            <div class="w-1 h-1 bg-gray-400 rounded-full mx-1 hidden md:block"></div>
                            <span>Your connection to this site is encrypted and secure</span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        function togglePasswordVisibility(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            let toggleIcon;

            if (fieldId === 'password') {
                toggleIcon = document.getElementById('togglePassword');
            } else if (fieldId === 'confirm_password') {
                toggleIcon = document.getElementById('toggleConfirmPassword');
            }

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.add('fa-eye-slash');
                toggleIcon.classList.remove('fa-eye');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        var plan = '{{ isset($plan) ? $plan : 0 }}';
        if (plan) {
            localStorage.setItem('plan', plan);
        }
    </script>
</body>

</html>