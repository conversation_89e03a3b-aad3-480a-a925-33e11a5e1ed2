@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icomoon.eot?9kdwfw');
  src:  url('../fonts/icomoon.eot?9kdwfw#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?9kdwfw') format('truetype'),
    url('../fonts/icomoon.woff?9kdwfw') format('woff'),
    url('../fonts/icomoon.svg?9kdwfw#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-star-half:before {
  content: "\e901";
  color: #ffbb0e;
}
.icon-star:before {
  content: "\e900";
}
