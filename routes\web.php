<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\GoogleLoginController;
use App\Http\Controllers\BusinessController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\TeamController;
use App\Http\Middleware\RedirectIfAuthenticated;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\ActivityDashboardController;
use App\Http\Controllers\SubscriptionUsageController;
use Illuminate\Http\Request;

Route::middleware([RedirectIfAuthenticated::class])->group(function () {
    Route::get('/', [AuthController::class, 'showLoginForm'])->name('home');

    // User Google Authentication (for login only)
    Route::get('auth/google', [GoogleLoginController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('auth/google/callback', [GoogleLoginController::class, 'handleGoogleCallback']);

    Route::get('/forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');

    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');

    // Route::get('auth/google/connect', [GoogleLoginController::class, 'connectGoogle'])->name('google.connect');
    // Route::get('auth/google/connect/callback', [GoogleLoginController::class, 'handleConnectedGoogleCallback']);

    // Custom Login Routes
    Route::get('register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('register', [AuthController::class, 'register'])->name('register');

    Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('login', [AuthController::class, 'login']);

    Route::post('coupon-code', [AuthController::class, 'submitCouponCode'])->name('submit.couponcode');
});


// Password Setting Routes (after Google Login)
Route::middleware('auth')->group(function () {
    // Google Business Authentication (for business management)
    Route::get('auth/google/business', [GoogleLoginController::class, 'redirectToGoogleBusiness'])->name('auth.google.business');
    Route::get('auth/google/business/callback', [GoogleLoginController::class, 'handleGoogleBusinessCallback']);

    Route::get('/profile/data', [ProfileController::class, 'getProfileData']);
    Route::post('/profile/update', [ProfileController::class, 'update'])->name('profile.update');

    Route::get('set-password', [AuthController::class, 'showSetPasswordForm'])->name('set.password.form');
    Route::post('set-password', [AuthController::class, 'setPassword'])->name('set.password');
    Route::get('dashboard', [AuthController::class, 'dashboard'])->name('dashboard');
    Route::get('logout', [AuthController::class, 'logout'])->name('logout');

    Route::get('/business-dashboard', [BusinessController::class, 'businessDashboard'])->name('business.dashboard');
    Route::get('/business-setup', [BusinessController::class, 'businessSetup'])->name('business.setup');
    Route::post('/business/connect', [BusinessController::class, 'connectBusiness'])->name('business.connect');
    Route::post('/business/remove', [BusinessController::class, 'removeBusiness'])->name('business.remove');
    Route::get('/business/fetch-associated', [BusinessController::class, 'fetchAssociatedBusinesses'])->name('business.fetch-associated');
    //Route::get('/subscriptions', [BusinessController::class, 'subscriptions'])->name('business.subscriptions');
    Route::get('/checkout', [CheckoutController::class, 'index'])->name('business.subscriptions');

    // Simple User Dashboard
    Route::get('/simple-user-dashboard', function () {
        return view('simple-user-dashboard');
    })->name('simple.user.dashboard');

    // New Professional Checkout Routes
    Route::prefix('checkout')->name('checkout.')->group(function () {
        Route::get('/{test?}', [CheckoutController::class, 'index'])->name('index');
        Route::get('/demo', function () {
            return view('checkout.demo');
        })->name('demo');
        Route::post('/validate-coupon', [CheckoutController::class, 'validateCoupon'])->name('validate-coupon');
        Route::post('/process', [CheckoutController::class, 'processCheckout'])->name('process');
    });

    // Billing Details Routes
    Route::prefix('billing')->name('billing.')->group(function () {
        Route::get('/details', [\App\Http\Controllers\BillingController::class, 'index'])->name('details');
        Route::get('/data', [\App\Http\Controllers\BillingController::class, 'getBillingData'])->name('data');
        Route::get('/past-subscriptions', [\App\Http\Controllers\BillingController::class, 'getPastSubscriptions'])->name('past.subscriptions');
        Route::get('/payment-history', [\App\Http\Controllers\BillingController::class, 'getPaymentHistory'])->name('payment.history');
        Route::get('/current-usage', [\App\Http\Controllers\BillingController::class, 'getCurrentUsage'])->name('current.usage');
        Route::get('/portal', [\App\Http\Controllers\BillingController::class, 'billingPortal'])->name('portal');
    });

    // Existing Payment Routes (keep for backward compatibility)
    Route::post('/payment/process', [PaymentController::class, 'processPayment'])->name('payment.process');
    Route::get('/payment/success', [PaymentController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/payment/cancel', [PaymentController::class, 'paymentCancel'])->name('payment.cancel');
    Route::get('/billing/portal', [PaymentController::class, 'billingPortal'])->name('billing.portal');

    Route::post('/templates', [TemplateController::class, 'addTemplate'])->name('templates.add-template');
    Route::get('/templates/{id}', [TemplateController::class, 'editTemplate'])->name('templates.edit-template');
    Route::put('/templates/{id}', [TemplateController::class, 'updateTemplate'])->name('templates.update-template');
    Route::delete('/templates/{id}', [TemplateController::class, 'deleteTemplate'])->name('templates.delete-template');

    Route::get('/template/{id}', [TemplateController::class, 'getTemplate'])->name('templates.get-template');

    Route::get('/auth/google/reviews', [GoogleLoginController::class, 'showReviews'])->name("google.reviews");
    Route::post('/manage-settings', [SettingController::class, 'manageSetting']);
    Route::get('/get-settings/{id}', [SettingController::class, 'getSettings']);
    Route::post('/replay-to-review', [GoogleLoginController::class, 'replyToReview']);
    Route::post('/update-reply', [GoogleLoginController::class, 'updateReply']);
    Route::post('/delete-reply', [GoogleLoginController::class, 'deleteReply']);

    Route::post('/generate-reply', [ReviewController::class, 'generateReply']);
    Route::get('/generate-reply-page', [ReviewController::class, 'showGenerateReplyPage'])->name('generate-reply-page');
    Route::get('/generate-sentiment/{starRating}/{reviewText?}/{reviewId?}', [ReviewController::class, 'generateSentiment'])->where('reviewText', '.*');
    Route::post('/get-default-template', [ReviewController::class, 'getDefaultTemplate']);
    Route::post('/get-reply', [ReviewController::class, 'getReply']);
    Route::post('/improve-text', [ReviewController::class, 'improvedText'])->name('improve-text');
    Route::get('/export-reviews/{business_name}', [ReviewController::class, 'exportReviews'])->name('export-reviews');
    Route::get('/export-reviews-pdf/{business_name}', [ReviewController::class, 'exportReviewsAsPdf'])->name('export-reviews.pdf');

    Route::post('/businesses/{business}/reviews/{order?}', [BusinessController::class, 'filterReviews']);
    Route::post('/business/reviews/fetch', [GoogleLoginController::class, 'fetchLatestReviews'])->name('business.reviews.fetch');
    Route::get('/business/reviews/auto-reply', [BusinessController::class, 'generateAutoReplies'])->name('business.reviews.auto-reply');
    Route::post('/business/reviews/send-auto-reply', [BusinessController::class, 'sendAutoReplies'])->name('business.reviews.send-auto-reply');
    Route::get('/api/sentiment-trend', [BusinessController::class, 'getSentimentTrendApi']);
    Route::get('/api/review-activity', [BusinessController::class, 'getReviewActivityApi']);
    Route::get('/get-pending-reviews/{accountId}/{businessId}/{locationId}', [BusinessController::class, 'getPendingReviews'])->name('get-pending-reviews');

    Route::get('/business/reviews/paginate', [BusinessController::class, 'paginateReviews'])->name('business.reviews.paginate');
    Route::get('/business/{id}/chart-data', [BusinessController::class, 'getChartData'])->name('business.chart-data');
    Route::get('/business/analytics-data', [BusinessController::class, 'getAnalyticsData'])->name('business.analytics-data');
    Route::get('/business/{id}/auto-reply-settings', [BusinessController::class, 'getAutoReplySettings'])->name('business.auto-reply-settings');
    Route::post('/business/reviews/fetch-next', [BusinessController::class, 'fetchNextReviews'])->name('business.reviews.fetch-next');
    Route::post('/business/allBusiness', [BusinessController::class, 'allBusiness'])->name('business.allBusiness');
    Route::get('/countreviews/{locationId}/{rating}', [BusinessController::class, 'countReviews'])->name('business.count-reviews');

    Route::get('/myplans', [BusinessController::class, 'myPlans'])->name('business.plans');

    // Subscription management routes
    Route::get('/subscription', [CheckoutController::class, 'showSubscription'])->name('subscription.details');
    Route::get('/subscription/data', [CheckoutController::class, 'getSubscriptionData'])->name('subscription.data');
    Route::post('/subscription/free', [CheckoutController::class, 'signupFreePlan'])->name('subscription.free');
    Route::get('/plans/pricing', [CheckoutController::class, 'getPlansWithPricing'])->name('plans.pricing');


    Route::get('/teamRequest', [TeamController::class, 'teamRequest'])->name('teamRequest');
    // Team Management Routes
    Route::get('/team/accept/{token}', [TeamController::class, 'acceptInvitation'])->name('team.accept');
    Route::get('/team/decline/{token}', [TeamController::class, 'declineInvitation'])->name('team.decline');
    Route::get('/team/edit-permissions/{teamMemberId}', [TeamController::class, 'editPermissions'])->name('edit-permissions');
    Route::post('/team/update-permissions/{teamMemberId}', [TeamController::class, 'updatePermissions'])->name('update-permissions');
    Route::post('/team/remove-member/{teamMemberId}', [TeamController::class, 'removeMember'])->name('remove-member');
    Route::prefix('team')->name('team.')->group(function () {
        Route::get('/{businessAccountId}/{businessId?}', [TeamController::class, 'index'])->name('index');
        Route::get('/{businessAccountId}/invite', [TeamController::class, 'create'])->name('create');
        Route::post('/{businessAccountId}/invite', [TeamController::class, 'invite'])->name('invite');
        // Route::get('/accept-invitation/{token}', [TeamController::class, 'acceptInvitation'])->name('accept');
        // Route::get('/decline-invitation/{token}', [TeamController::class, 'declineInvitation'])->name('decline');
        Route::get('/edit/{teamMemberId}', [TeamController::class, 'edit'])->name('edit');
        Route::put('/{teamMemberId}', [TeamController::class, 'update'])->name('update');
        Route::delete('/{teamMemberId}', [TeamController::class, 'destroy'])->name('destroy');

        // Team Analytics Routes
        Route::get('/analytics/{businessId}', [TeamController::class, 'getTeamAnalytics'])->name('analytics');
        Route::get('/validation/{businessId}', [TeamController::class, 'getTeamValidation'])->name('validation');
    });
    Route::post('/business/changeStatus', [BusinessController::class, 'changeStatus'])->name('business.changeStatus');

    // Activity Dashboard Routes
    Route::get('/activity-dashboard', [ActivityDashboardController::class, 'index'])->name('activity.dashboard');
    Route::get('/activity-dashboard/export', [ActivityDashboardController::class, 'export'])->name('activity.export');


    // Test Subscription Validation Routes (for development/testing)
    Route::prefix('test-subscription-validation')->name('test.subscription.validation.')->group(function () {
        Route::get('/all', [\App\Http\Controllers\TestSubscriptionValidationController::class, 'testAllValidations'])->name('all');
        Route::get('/traits', [\App\Http\Controllers\TestSubscriptionValidationController::class, 'testTraitValidations'])->name('traits');
        Route::get('/wrapper', [\App\Http\Controllers\TestSubscriptionValidationController::class, 'testValidationWrapper'])->name('wrapper');
        Route::get('/performance', [\App\Http\Controllers\TestSubscriptionValidationController::class, 'testValidationPerformance'])->name('performance');
        Route::get('/scenarios', [\App\Http\Controllers\TestSubscriptionValidationController::class, 'simulateLimitScenarios'])->name('scenarios');
    });

    // Subscription Usage API Routes
    Route::prefix('api/subscription-usage')->name('api.subscription.usage.')->group(function () {
        Route::get('/data', [SubscriptionUsageController::class, 'getUsageData'])->name('data');
        Route::post('/check-action', [SubscriptionUsageController::class, 'checkActionLimit'])->name('check.action');
        Route::get('/summary', [SubscriptionUsageController::class, 'getUsageSummary'])->name('summary');
        Route::get('/upgrade-suggestions', [SubscriptionUsageController::class, 'getUpgradeSuggestions'])->name('upgrade.suggestions');
        Route::post('/refresh', [SubscriptionUsageController::class, 'refreshUsage'])->name('refresh');
        Route::get('/trends', [SubscriptionUsageController::class, 'getUsageTrends'])->name('trends');
        Route::post('/validate-limit', [SubscriptionUsageController::class, 'validateLimit'])->name('validate.limit');
    });

    // Subscription upgrade modal content route
    Route::get('/subscription/upgrade-modal-content', [CheckoutController::class, 'getUpgradeModalContent'])->name('subscription.upgrade.modal.content');
});
