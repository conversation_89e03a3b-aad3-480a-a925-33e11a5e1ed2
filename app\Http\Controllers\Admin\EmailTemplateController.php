<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminEmailTemplate;
use App\Http\Requests\Admin\EmailTemplateRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\Rule;

class EmailTemplateController extends Controller
{
    /**
     * Display a listing of email templates
     */
    public function index(Request $request)
    {
        $query = AdminEmailTemplate::with(['creator', 'updater'])
            ->orderBy('category')
            ->orderBy('type')
            ->orderBy('name');

        // Apply filters
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $templates = $query->paginate(20)->withQueryString();

        // Get filter options
        $categories = AdminEmailTemplate::select('category')->distinct()->pluck('category');
        $types = AdminEmailTemplate::select('type')->distinct()->pluck('type');

        return view('admin.email-templates.index', compact('templates', 'categories', 'types'));
    }

    /**
     * Show the form for creating a new email template
     */
    public function create()
    {
        $availableTypes = $this->getAvailableTypes();
        $globalVariables = AdminEmailTemplate::getGlobalVariables();
        
        return view('admin.email-templates.create', compact('availableTypes', 'globalVariables'));
    }

    /**
     * Store a newly created email template
     */
    public function store(EmailTemplateRequest $request)
    {

        // If setting as default, unset other defaults for this type
        if ($request->boolean('is_default')) {
            AdminEmailTemplate::where('type', $request->type)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $template = AdminEmailTemplate::create([
            'name' => $request->name,
            'subject' => $request->subject,
            'description' => $request->description,
            'html_content' => $request->html_content,
            'text_content' => $request->text_content,
            'category' => $request->category,
            'type' => $request->type,
            'available_variables' => $request->available_variables,
            'required_variables' => $request->required_variables,
            'is_active' => $request->boolean('is_active', true),
            'is_default' => $request->boolean('is_default', false),
            'created_by' => Auth::guard('admin')->id(),
            'updated_by' => Auth::guard('admin')->id(),
        ]);

        return redirect()->route('admin.email-templates.show', $template)
            ->with('success', 'Email template created successfully.');
    }

    /**
     * Display the specified email template
     */
    public function show(AdminEmailTemplate $emailTemplate)
    {
        $emailTemplate->load(['creator', 'updater']);
        
        return view('admin.email-templates.show', compact('emailTemplate'));
    }

    /**
     * Show the form for editing the specified email template
     */
    public function edit(AdminEmailTemplate $emailTemplate)
    {
        $availableTypes = $this->getAvailableTypes();
        $globalVariables = AdminEmailTemplate::getGlobalVariables();
        
        return view('admin.email-templates.edit', compact('emailTemplate', 'availableTypes', 'globalVariables'));
    }

    /**
     * Update the specified email template
     */
    public function update(EmailTemplateRequest $request, AdminEmailTemplate $emailTemplate)
    {

        // If setting as default, unset other defaults for this type
        if ($request->boolean('is_default') && !$emailTemplate->is_default) {
            AdminEmailTemplate::where('type', $request->type)
                ->where('id', '!=', $emailTemplate->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $emailTemplate->update([
            'name' => $request->name,
            'subject' => $request->subject,
            'description' => $request->description,
            'html_content' => $request->html_content,
            'text_content' => $request->text_content,
            'category' => $request->category,
            'type' => $request->type,
            'available_variables' => $request->available_variables,
            'required_variables' => $request->required_variables,
            'is_active' => $request->boolean('is_active', true),
            'is_default' => $request->boolean('is_default', false),
            'updated_by' => Auth::guard('admin')->id(),
        ]);

        return redirect()->route('admin.email-templates.show', $emailTemplate)
            ->with('success', 'Email template updated successfully.');
    }

    /**
     * Remove the specified email template
     */
    public function destroy(AdminEmailTemplate $emailTemplate)
    {
        // Prevent deletion of system templates
        if ($emailTemplate->category === 'system') {
            return back()->with('error', 'System templates cannot be deleted.');
        }

        $emailTemplate->delete();

        return redirect()->route('admin.email-templates.index')
            ->with('success', 'Email template deleted successfully.');
    }

    /**
     * Preview email template
     */
    public function preview(Request $request, AdminEmailTemplate $emailTemplate)
    {
        $variables = $request->input('variables', []);
        
        // Add some default variables for preview
        $defaultVariables = [
            'app_name' => config('app.name', 'ReviewBiz'),
            'app_url' => config('app.url'),
            'user_name' => 'John Doe',
            'user_email' => '<EMAIL>',
            'admin_name' => Auth::guard('admin')->user()->name,
            'current_date' => now()->format('F j, Y'),
            'current_year' => now()->year,
            'support_email' => '<EMAIL>',
            'company_name' => 'ReviewBiz',
            'company_address' => '123 Business St, City, State 12345',
        ];

        $variables = array_merge($defaultVariables, $variables);
        $rendered = $emailTemplate->renderContent($variables);

        if ($request->input('format') === 'text') {
            return response($rendered['text'] ?: strip_tags($rendered['html']))
                ->header('Content-Type', 'text/plain');
        }

        return response($rendered['html'])
            ->header('Content-Type', 'text/html');
    }

    /**
     * Send test email
     */
    public function sendTest(Request $request, AdminEmailTemplate $emailTemplate)
    {
        // Rate limiting for test emails
        $key = 'test-email:' . Auth::guard('admin')->id();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many test emails sent. Please wait before trying again.'
            ], 429);
        }

        RateLimiter::hit($key, 300); // 5 minutes
        $validator = Validator::make($request->all(), [
            'test_email' => 'required|email',
            'variables' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()]);
        }

        try {
            $variables = $request->input('variables', []);
            
            // Add default variables
            $defaultVariables = [
                'app_name' => config('app.name', 'ReviewBiz'),
                'app_url' => config('app.url'),
                'user_name' => 'Test User',
                'user_email' => $request->test_email,
                'admin_name' => Auth::guard('admin')->user()->name,
                'current_date' => now()->format('F j, Y'),
                'current_year' => now()->year,
                'support_email' => '<EMAIL>',
                'company_name' => 'ReviewBiz',
                'company_address' => '123 Business St, City, State 12345',
            ];

            $variables = array_merge($defaultVariables, $variables);
            $rendered = $emailTemplate->renderContent($variables);

            Mail::send([], [], function ($message) use ($rendered, $request) {
                $message->to($request->test_email)
                    ->subject('[TEST] ' . $rendered['subject'])
                    ->html($rendered['html']);
                
                if ($rendered['text']) {
                    $message->text($rendered['text']);
                }
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully to ' . $request->test_email
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get available template types
     */
    private function getAvailableTypes()
    {
        return [
            'welcome' => 'Welcome Email',
            'password_reset' => 'Password Reset',
            'email_verification' => 'Email Verification',
            'subscription_created' => 'Subscription Created',
            'subscription_expired' => 'Subscription Expired',
            'subscription_cancelled' => 'Subscription Cancelled',
            'payment_success' => 'Payment Success',
            'payment_failed' => 'Payment Failed',
            'invoice' => 'Invoice',
            'notification' => 'General Notification',
            'marketing' => 'Marketing Email',
            'other' => 'Other',
        ];
    }
}
