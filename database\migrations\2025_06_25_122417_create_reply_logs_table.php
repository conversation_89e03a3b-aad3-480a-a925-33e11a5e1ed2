<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reply_logs', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('subscription_id');
            $table->unsignedBigInteger('business_id');
            $table->unsignedBigInteger('review_id')->nullable(); // Google review ID
            $table->string('reply_type', 50)->default('manual'); // manual, auto, scheduled
            $table->text('reply_content')->nullable();
            $table->enum('status', ['sent', 'failed', 'pending'])->default('sent');
            $table->integer('month'); // 1-12
            $table->integer('year'); // e.g., 2025
            $table->json('metadata')->nullable(); // Additional context
            $table->timestamps();

            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('cascade');
            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');

            // Indexes for performance
            $table->index(['user_id', 'month', 'year']);
            $table->index(['subscription_id', 'month', 'year']);
            $table->index(['business_id', 'month', 'year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reply_logs');
    }
};
