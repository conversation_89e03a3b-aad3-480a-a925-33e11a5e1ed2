<div class="bg-white p-4 border-b border-gray-200 flex justify-between gap-4 flex-wrap">

    <div class="flex flex-wrap items-end gap-4">
        <div class="filter-group">
            <label class="text-xs text-gray-500 block mb-1">Date Range</label>
            <select id="dateRangeFilter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 getFilter" data-business="{{ $business['id'] }}">
                <option value="all">All</option>
                <option value="last_week">Last week</option>
                <option value="last_month">Last month</option>
                <option value="last_3_months">Last 3 months</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="text-xs text-gray-500 block mb-1">Rating</label>
            <div class="flex">
                <button class="rating-btn px-2 py-1 border border-gray-300 rounded-l-md hover:bg-gray-100 btnRating getFilter" data-rating="ONE" data-business="{{ $business['id'] }}">
                    <i class="icon-star text-yellow-400"></i>
                    <span>1</span>
                </button>
                <button class="rating-btn px-2 py-1 border-t border-r border-b border-gray-300 hover:bg-gray-100 btnRating getFilter" data-rating="TWO" data-business="{{ $business['id'] }}">
                    <i class="icon-star text-yellow-400"></i>
                    <span>2</span>
                </button>
                <button class="rating-btn px-2 py-1 border-t border-r border-b border-gray-300 hover:bg-gray-100 btnRating getFilter" data-rating="THREE" data-business="{{ $business['id'] }}">
                    <i class="icon-star text-yellow-400"></i>
                    <span>3</span>
                </button>
                <button class="rating-btn px-2 py-1 border-t border-b border-gray-300 hover:bg-gray-100 btnRating getFilter" data-rating="FOUR" data-business="{{ $business['id'] }}">
                    <i class="icon-star text-yellow-400"></i>
                    <span>4</span>
                </button>
                <button class="rating-btn px-2 py-1 border border-gray-300 rounded-r-md hover:bg-gray-100 btnRating getFilter" data-rating="FIVE" data-business="{{ $business['id'] }}">
                    <i class="icon-star text-yellow-400"></i>
                    <span>5</span>
                </button>
            </div>
        </div>
        <div class="filter-group">
            <label class="text-xs text-gray-500 block mb-1">Type</label>
            <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 getFilter" data-business="{{ $business['id'] }}">
                <option value="all">All</option>
                <option value="replied">Replied</option>
                <option value="not_replied">Not Replied</option>
                <!-- <option value="with_photos">With Photos</option> -->
            </select>
        </div>
        <div class="filter-group">
            <label class="text-xs text-gray-500 block mb-1">Show Order</label>
            <select id="showOrderFilter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <option value="desc" {{ isset($currentOrder) && $currentOrder == 'desc' ? 'selected' : '' }}>Descending</option>
                <option value="asc" {{ isset($currentOrder) && $currentOrder == 'asc' ? 'selected' : '' }}>Ascending</option>
            </select>
            <input type="hidden" id="businessId" value="{{ $businessId }}">
            <input type="hidden" id="locationName" value="{{ $selectedLocationName }}">
        </div>
        <form method="POST" action="{{ route('business.reviews.fetch') }}">
            @csrf
            <input type="hidden" name="businessId" value="{{ $businessId }}">
            <input type="hidden" name="location" value="{{ $selectedLocationName }}">
            <button type="submit" class="flex items-center px-3 py-1 text-sm bg-white-600 text-indigo-600 border border-indigo-600 rounded-md font-medium hover:text-white hover:bg-indigo-600">
                <i class="fas fa-sync mr-2"></i> Fetch New Reviews
            </button>
        </form>
        @if($business->setting->auto_reply)
        <button
            onclick="sendAutoReplies('{{ $businessId }}', '{{ $selectedLocationName }}')"
            class="flex items-center px-3 py-1 text-sm bg-white-600 text-indigo-600 border border-indigo-600 rounded-md font-medium hover:text-white hover:bg-indigo-600">
            Send Auto Replies
        </button>
        @endif

        <form method="POST" action="{{ route('business.reviews.fetch-next') }}">
            @csrf
            <input type="hidden" name="businessId" value="{{ $businessId }}">
            <input type="hidden" name="location" value="{{ $selectedLocationName }}">
            <button type="submit" class="flex items-center px-3 py-1 text-sm bg-white-600 text-indigo-600 border border-indigo-600 rounded-md font-medium hover:text-white hover:bg-indigo-600">
                <i class="fas fa-sync mr-2"></i> Fetch Next Reviews
            </button>
        </form>
    </div>
    <div class="flex gap-4 items-center">
        <span class="text-indigo-600 flex-shrink-0 text-sm">Reviews <span id="reviewDynamicCount">{{ $reviews->count() }} </span> out of <span id="totalDynamicReviews">{{ $totalReviews }}</span></span>
    </div>

</div>