<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Plan;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments
     */
    public function index(Request $request)
    {
        $query = Payment::with(['user', 'plan']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('plan', function($planQuery) use ($search) {
                    $planQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('transaction_id', 'like', "%{$search}%")
                ->orWhere('order_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        if ($request->filled('gateway')) {
            $query->where('gateway_name', $request->gateway);
        }

        if ($request->filled('currency')) {
            $query->where('currency', $request->currency);
        }

        if ($request->filled('plan_id')) {
            $query->where('plan_id', $request->plan_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        if ($request->filled('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }

        if ($request->filled('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'payment_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $payments = $query->paginate(20)->withQueryString();

        // Get filter options
        $plans = Plan::select('id', 'name')->get();
        $gateways = Payment::distinct()->pluck('gateway_name')->filter();
        $currencies = Payment::distinct()->pluck('currency')->filter();
        $statuses = ['PENDING', 'SUCCESS', 'FAILED', 'REFUNDED'];

        // Calculate statistics
        $stats = $this->getPaymentStatistics($request);

        return view('admin.payments.index', compact(
            'payments', 'plans', 'gateways', 'currencies', 'statuses', 'stats'
        ));
    }

    /**
     * Display the specified payment
     */
    public function show(Payment $payment)
    {
        $payment->load(['user', 'plan']);

        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Export payments data
     */
    public function export(Request $request)
    {
        $query = Payment::with(['user', 'plan']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('plan', function($planQuery) use ($search) {
                    $planQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('transaction_id', 'like', "%{$search}%")
                ->orWhere('order_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        if ($request->filled('gateway')) {
            $query->where('gateway_name', $request->gateway);
        }

        if ($request->filled('currency')) {
            $query->where('currency', $request->currency);
        }

        if ($request->filled('plan_id')) {
            $query->where('plan_id', $request->plan_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        $payments = $query->orderBy('payment_date', 'desc')->get();

        $filename = 'payments_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        return response()->stream(function() use ($payments) {
            $handle = fopen('php://output', 'w');

            // CSV headers
            fputcsv($handle, [
                'ID', 'User Name', 'User Email', 'Plan', 'Amount', 'Currency',
                'Status', 'Gateway', 'Payment Method', 'Transaction ID', 'Order ID',
                'Payment Date', 'Billing Cycle', 'Original Amount', 'Discount Amount',
                'Tax Amount', 'Coupon Code', 'Created At'
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($handle, [
                    $payment->id,
                    $payment->user->name ?? 'N/A',
                    $payment->user->email ?? 'N/A',
                    $payment->plan->name ?? 'N/A',
                    $payment->amount,
                    $payment->currency,
                    $payment->payment_status,
                    $payment->gateway_name,
                    $payment->payment_method ?? 'N/A',
                    $payment->transaction_id ?? 'N/A',
                    $payment->order_id ?? 'N/A',
                    $payment->payment_date ? $payment->payment_date->format('Y-m-d H:i:s') : 'N/A',
                    $payment->billing_cycle,
                    $payment->original_amount ?? 'N/A',
                    $payment->discount_amount ?? 0,
                    $payment->tax_amount ?? 0,
                    $payment->coupon_code ?? 'N/A',
                    $payment->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($handle);
        }, 200, $headers);
    }

    /**
     * Process refund for payment
     */
    public function refund(Payment $payment)
    {
        try {
            // Update payment status to refunded
            $payment->update([
                'payment_status' => 'REFUNDED',
                'refund_date' => now(),
                'refund_id' => 'MANUAL_REFUND_' . time()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process refund: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment statistics
     */
    private function getPaymentStatistics($request)
    {
        $query = Payment::query();

        // Apply same filters as index for consistent stats
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('plan', function($planQuery) use ($search) {
                    $planQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('transaction_id', 'like', "%{$search}%")
                ->orWhere('order_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        if ($request->filled('gateway')) {
            $query->where('gateway_name', $request->gateway);
        }

        if ($request->filled('currency')) {
            $query->where('currency', $request->currency);
        }

        if ($request->filled('plan_id')) {
            $query->where('plan_id', $request->plan_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        $totalPayments = $query->count();
        $totalRevenue = $query->sum('amount');
        $successfulPayments = $query->where('payment_status', 'SUCCESS')->count();
        $pendingPayments = $query->where('payment_status', 'PENDING')->count();
        $failedPayments = $query->where('payment_status', 'FAILED')->count();
        $refundedPayments = $query->where('payment_status', 'REFUNDED')->count();

        $successRate = $totalPayments > 0 ? round(($successfulPayments / $totalPayments) * 100, 2) : 0;
        $averageAmount = $totalPayments > 0 ? round($totalRevenue / $totalPayments, 2) : 0;

        return [
            'total_payments' => $totalPayments,
            'total_revenue' => $totalRevenue,
            'successful_payments' => $successfulPayments,
            'pending_payments' => $pendingPayments,
            'failed_payments' => $failedPayments,
            'refunded_payments' => $refundedPayments,
            'success_rate' => $successRate,
            'average_amount' => $averageAmount
        ];
    }
}
