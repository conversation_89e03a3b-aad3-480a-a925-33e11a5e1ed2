<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\SubscriptionUsageTrigger;
use App\Services\SubscriptionValidationWrapper;
use App\Traits\ValidatesSubscriptionUsage;

class TestSubscriptionValidationController extends Controller
{
    use ValidatesSubscriptionUsage;

    protected $usageTrigger;
    protected $validationWrapper;

    public function __construct(SubscriptionUsageTrigger $usageTrigger, SubscriptionValidationWrapper $validationWrapper)
    {
        $this->usageTrigger = $usageTrigger;
        $this->validationWrapper = $validationWrapper;
    }

    /**
     * Test all validation triggers
     */
    public function testAllValidations(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        try {
            $results = [];
            
            // Test business connection validation
            $results['business_connection'] = $this->usageTrigger->triggerBusinessConnectionCheck($user->id);
            
            // Test reply send validation
            $results['reply_send'] = $this->usageTrigger->triggerReplySendCheck($user->id);
            
            // Test team member invite validation (using first business if available)
            $businessId = $request->input('business_id', 1);
            $results['team_member_invite'] = $this->usageTrigger->triggerTeamMemberInviteCheck($user->id, $businessId);
            
            // Test feature access validation
            $results['feature_access_export'] = $this->usageTrigger->triggerFeatureAccessCheck($user->id, 'data_export_enabled');
            $results['feature_access_api'] = $this->usageTrigger->triggerFeatureAccessCheck($user->id, 'api_access_level');
            
            // Test comprehensive check
            $results['comprehensive'] = $this->usageTrigger->triggerComprehensiveCheck($user->id);
            
            return response()->json([
                'success' => true,
                'message' => 'All validation triggers tested',
                'user_id' => $user->id,
                'results' => $results,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Validation test failed',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Test trait-based validation methods
     */
    public function testTraitValidations(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        try {
            $results = [];
            
            // Test trait methods
            $results['business_connection_trait'] = $this->validateBusinessConnectionUsage();
            $results['reply_send_trait'] = $this->validateReplySendUsage();
            
            $businessId = $request->input('business_id', 1);
            $results['team_member_invite_trait'] = $this->validateTeamMemberInviteUsage($businessId);
            $results['feature_access_trait'] = $this->validateFeatureAccessUsage('data_export_enabled');
            
            // Test comprehensive status
            $results['comprehensive_status'] = $this->getComprehensiveUsageStatus();
            $results['has_warnings'] = $this->hasUsageWarnings();
            $results['has_critical'] = $this->hasCriticalUsageIssues();
            $results['usage_alerts'] = $this->getUsageAlerts();
            
            return response()->json([
                'success' => true,
                'message' => 'Trait validation methods tested',
                'user_id' => $user->id,
                'results' => $results,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Trait validation test failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test validation wrapper functionality
     */
    public function testValidationWrapper(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        try {
            $results = [];
            
            // Test business connection wrapper
            $results['business_connection_wrapper'] = $this->validationWrapper->validateBusinessConnection($user->id, function() {
                return ['action' => 'connect_business', 'result' => 'success', 'timestamp' => now()];
            });
            
            // Test reply send wrapper
            $results['reply_send_wrapper'] = $this->validationWrapper->validateReplySend($user->id, function() {
                return ['action' => 'send_reply', 'result' => 'success', 'timestamp' => now()];
            });
            
            // Test team member invite wrapper
            $businessId = $request->input('business_id', 1);
            $results['team_member_wrapper'] = $this->validationWrapper->validateTeamMemberInvite($user->id, $businessId, function() {
                return ['action' => 'invite_team_member', 'result' => 'success', 'timestamp' => now()];
            });
            
            // Test feature access wrapper
            $results['feature_access_wrapper'] = $this->validationWrapper->validateFeatureAccess($user->id, 'data_export_enabled', function() {
                return ['action' => 'export_data', 'result' => 'success', 'timestamp' => now()];
            });
            
            // Test batch validation
            $results['batch_validation'] = $this->validationWrapper->batchValidate([
                'business_connection' => ['type' => 'business_connection', 'params' => ['user_id' => $user->id]],
                'reply_send' => ['type' => 'reply_send', 'params' => ['user_id' => $user->id]],
                'team_invite' => ['type' => 'team_member_invite', 'params' => ['user_id' => $user->id, 'business_id' => $businessId]]
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Validation wrapper tested',
                'user_id' => $user->id,
                'results' => $results,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Validation wrapper test failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test real-time validation performance
     */
    public function testValidationPerformance(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        try {
            $iterations = $request->input('iterations', 10);
            $results = [];
            
            for ($i = 0; $i < $iterations; $i++) {
                $startTime = microtime(true);
                
                // Test multiple validations
                $businessValidation = $this->usageTrigger->triggerBusinessConnectionCheck($user->id);
                $replyValidation = $this->usageTrigger->triggerReplySendCheck($user->id);
                $comprehensiveCheck = $this->usageTrigger->triggerComprehensiveCheck($user->id);
                
                $endTime = microtime(true);
                $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
                
                $results[] = [
                    'iteration' => $i + 1,
                    'execution_time_ms' => round($executionTime, 2),
                    'business_allowed' => $businessValidation['allowed'],
                    'reply_allowed' => $replyValidation['allowed'],
                    'has_alerts' => $comprehensiveCheck['has_alerts'] ?? false
                ];
            }
            
            $avgTime = array_sum(array_column($results, 'execution_time_ms')) / count($results);
            $maxTime = max(array_column($results, 'execution_time_ms'));
            $minTime = min(array_column($results, 'execution_time_ms'));
            
            return response()->json([
                'success' => true,
                'message' => 'Validation performance tested',
                'user_id' => $user->id,
                'iterations' => $iterations,
                'performance_stats' => [
                    'average_time_ms' => round($avgTime, 2),
                    'max_time_ms' => $maxTime,
                    'min_time_ms' => $minTime
                ],
                'detailed_results' => $results,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Performance test failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simulate subscription limit scenarios
     */
    public function simulateLimitScenarios(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        try {
            // Get current usage data
            $comprehensiveCheck = $this->usageTrigger->triggerComprehensiveCheck($user->id);
            
            $scenarios = [
                'current_status' => $comprehensiveCheck,
                'simulated_scenarios' => [
                    'approaching_business_limit' => [
                        'description' => 'User approaching business connection limit',
                        'trigger_at' => '80% of limit'
                    ],
                    'exceeded_reply_limit' => [
                        'description' => 'User exceeded monthly reply limit',
                        'trigger_at' => '100% of limit'
                    ],
                    'team_member_limit_warning' => [
                        'description' => 'User approaching team member limit',
                        'trigger_at' => '90% of limit'
                    ]
                ]
            ];
            
            return response()->json([
                'success' => true,
                'message' => 'Limit scenarios simulated',
                'user_id' => $user->id,
                'scenarios' => $scenarios,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Scenario simulation failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
