<?php

namespace App\Services;

use App\Models\BusinessActivityLog;
use App\Models\Subscription;
use App\Models\Business;
use App\Models\TeamMember;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SubscriptionUsageTracker
{
    protected $subscriptionService;
    
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Get real-time usage statistics with caching
     */
    public function getRealTimeUsage(int $userId, bool $forceRefresh = false): array
    {
        $cacheKey = "subscription_usage_{$userId}";
        
        if (!$forceRefresh && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $usage = $this->calculateRealTimeUsage($userId);
        
        // Cache for 5 minutes
        Cache::put($cacheKey, $usage, 300);
        
        return $usage;
    }

    /**
     * Calculate real-time usage statistics
     */
    private function calculateRealTimeUsage(int $userId): array
    {
        $subscription = $this->subscriptionService->getActiveSubscription($userId);
        
        if (!$subscription) {
            return $this->getNoSubscriptionUsage();
        }

        $now = Carbon::now();
        $startOfMonth = $now->copy()->startOfMonth();
        
        return [
            'subscription_info' => $this->getSubscriptionInfo($subscription),
            'business_connections' => $this->getBusinessConnectionUsage($userId, $subscription),
            'team_members' => $this->getTeamMemberUsage($userId, $subscription),
            'monthly_replies' => $this->getMonthlyReplyUsage($userId, $subscription, $startOfMonth, $now),
            'daily_activities' => $this->getDailyActivityUsage($userId, $now),
            'feature_usage' => $this->getFeatureUsage($userId, $subscription),
            'limits_status' => $this->getLimitsStatus($userId, $subscription),
            'upgrade_alerts' => $this->getUpgradeAlerts($userId, $subscription)
        ];
    }

    /**
     * Get subscription information
     */
    private function getSubscriptionInfo(Subscription $subscription): array
    {
        $daysRemaining = Carbon::parse($subscription->expiry_date)->diffInDays(now());
        
        return [
            'plan_name' => $subscription->plan->name,
            'status' => $subscription->status,
            'expires_at' => $subscription->expiry_date,
            'days_remaining' => $daysRemaining,
            'is_expiring_soon' => $daysRemaining <= 7,
            'features' => $subscription->features ?? []
        ];
    }

    /**
     * Get business connection usage
     */
    private function getBusinessConnectionUsage(int $userId, Subscription $subscription): array
    {
        $current = Business::where('user_id', $userId)->count();
        $limit = $subscription->plan->getBusinessConnectionsLimit();
        $percentage = $limit > 0 ? ($current / $limit) * 100 : 0;
        
        return [
            'current' => $current,
            'limit' => $limit,
            'remaining' => max(0, $limit - $current),
            'percentage' => round($percentage, 1),
            'status' => $this->getUsageStatus($percentage),
            'can_add_more' => $current < $limit
        ];
    }

    /**
     * Get team member usage
     */
    private function getTeamMemberUsage(int $userId, Subscription $subscription): array
    {
        $current = TeamMember::whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('status', 'active')->count() + 1; // +1 for owner
        
        $limit = $subscription->plan->getTeamMembersLimit();
        $percentage = $limit > 0 ? ($current / $limit) * 100 : 0;
        
        return [
            'current' => $current,
            'limit' => $limit,
            'remaining' => max(0, $limit - $current),
            'percentage' => round($percentage, 1),
            'status' => $this->getUsageStatus($percentage),
            'can_invite_more' => $current < $limit
        ];
    }

    /**
     * Get monthly reply usage
     */
    private function getMonthlyReplyUsage(int $userId, Subscription $subscription, Carbon $startOfMonth, Carbon $now): array
    {
        $current = BusinessActivityLog::forUser($userId)
                                     ->byActivityType('send_reply')
                                     ->successful()
                                     ->countsTowardLimit()
                                     ->inDateRange($startOfMonth, $now)
                                     ->sum('activity_count');
        
        $hasUnlimited = $subscription->plan->hasUnlimitedReplies();
        $limit = $hasUnlimited ? -1 : $subscription->plan->getMonthlyReplyLimit();
        $percentage = $hasUnlimited ? 0 : ($limit > 0 ? ($current / $limit) * 100 : 0);
        
        return [
            'current' => $current,
            'limit' => $limit,
            'remaining' => $hasUnlimited ? -1 : max(0, $limit - $current),
            'percentage' => round($percentage, 1),
            'status' => $hasUnlimited ? 'unlimited' : $this->getUsageStatus($percentage),
            'unlimited' => $hasUnlimited,
            'can_send_more' => $hasUnlimited || $current < $limit,
            'days_remaining_in_month' => $now->copy()->endOfMonth()->diffInDays($now)
        ];
    }

    /**
     * Get daily activity usage
     */
    private function getDailyActivityUsage(int $userId, Carbon $today): array
    {
        $startOfDay = $today->copy()->startOfDay();
        $endOfDay = $today->copy()->endOfDay();
        
        $activities = BusinessActivityLog::forUser($userId)
                                        ->successful()
                                        ->inDateRange($startOfDay, $endOfDay)
                                        ->selectRaw('activity_type, COUNT(*) as count, SUM(activity_count) as total_count')
                                        ->groupBy('activity_type')
                                        ->get()
                                        ->keyBy('activity_type');
        
        return [
            'total_activities' => $activities->sum('count'),
            'total_actions' => $activities->sum('total_count'),
            'breakdown' => $activities->toArray(),
            'most_active_type' => $activities->sortByDesc('count')->keys()->first()
        ];
    }

    /**
     * Get feature usage statistics
     */
    private function getFeatureUsage(int $userId, Subscription $subscription): array
    {
        $features = $subscription->features ?? [];
        $usage = [];
        
        foreach ($features as $feature) {
            if (isset($feature['key'])) {
                $usage[$feature['key']] = [
                    'enabled' => $feature['value'] ?? false,
                    'title' => $feature['title'] ?? $feature['key'],
                    'usage_count' => $this->getFeatureUsageCount($userId, $feature['key'])
                ];
            }
        }
        
        return $usage;
    }

    /**
     * Get feature usage count
     */
    private function getFeatureUsageCount(int $userId, string $featureKey): int
    {
        $activityTypeMap = [
            'data_export_enabled' => 'export_data',
            'template_access' => 'create_template',
            'api_access_level' => 'api_call'
        ];
        
        if (isset($activityTypeMap[$featureKey])) {
            return BusinessActivityLog::forUser($userId)
                                     ->byActivityType($activityTypeMap[$featureKey])
                                     ->successful()
                                     ->thisMonth()
                                     ->count();
        }
        
        return 0;
    }

    /**
     * Get limits status
     */
    private function getLimitsStatus(int $userId, Subscription $subscription): array
    {
        $businessUsage = $this->getBusinessConnectionUsage($userId, $subscription);
        $teamUsage = $this->getTeamMemberUsage($userId, $subscription);
        $replyUsage = $this->getMonthlyReplyUsage($userId, $subscription, Carbon::now()->startOfMonth(), Carbon::now());
        
        return [
            'any_limit_exceeded' => $businessUsage['percentage'] >= 100 || 
                                   $teamUsage['percentage'] >= 100 || 
                                   (!$replyUsage['unlimited'] && $replyUsage['percentage'] >= 100),
            'any_limit_warning' => $businessUsage['percentage'] >= 80 || 
                                  $teamUsage['percentage'] >= 80 || 
                                  (!$replyUsage['unlimited'] && $replyUsage['percentage'] >= 80),
            'limits_approaching' => [
                'business_connections' => $businessUsage['percentage'] >= 80,
                'team_members' => $teamUsage['percentage'] >= 80,
                'monthly_replies' => !$replyUsage['unlimited'] && $replyUsage['percentage'] >= 80
            ]
        ];
    }

    /**
     * Get upgrade alerts
     */
    private function getUpgradeAlerts(int $userId, Subscription $subscription): array
    {
        $alerts = [];
        $limitsStatus = $this->getLimitsStatus($userId, $subscription);
        
        if ($limitsStatus['any_limit_exceeded']) {
            $alerts[] = [
                'type' => 'limit_exceeded',
                'urgency' => 'high',
                'message' => 'You have exceeded one or more subscription limits. Upgrade now to continue using all features.',
                'action' => 'upgrade_now'
            ];
        } elseif ($limitsStatus['any_limit_warning']) {
            $alerts[] = [
                'type' => 'limit_warning',
                'urgency' => 'medium',
                'message' => 'You are approaching your subscription limits. Consider upgrading to avoid interruptions.',
                'action' => 'consider_upgrade'
            ];
        }
        
        // Subscription expiry alerts
        $subscriptionInfo = $this->getSubscriptionInfo($subscription);
        if ($subscriptionInfo['is_expiring_soon']) {
            $alerts[] = [
                'type' => 'subscription_expiring',
                'urgency' => 'high',
                'message' => "Your subscription expires in {$subscriptionInfo['days_remaining']} days. Renew now to avoid service interruption.",
                'action' => 'renew_subscription'
            ];
        }
        
        return $alerts;
    }

    /**
     * Get usage status based on percentage
     */
    private function getUsageStatus(float $percentage): string
    {
        if ($percentage >= 100) return 'exceeded';
        if ($percentage >= 90) return 'critical';
        if ($percentage >= 80) return 'warning';
        if ($percentage >= 60) return 'moderate';
        return 'normal';
    }

    /**
     * Get no subscription usage data
     */
    private function getNoSubscriptionUsage(): array
    {
        return [
            'subscription_info' => null,
            'business_connections' => ['current' => 0, 'limit' => 0, 'status' => 'no_subscription'],
            'team_members' => ['current' => 0, 'limit' => 0, 'status' => 'no_subscription'],
            'monthly_replies' => ['current' => 0, 'limit' => 0, 'status' => 'no_subscription'],
            'daily_activities' => ['total_activities' => 0, 'breakdown' => []],
            'feature_usage' => [],
            'limits_status' => ['any_limit_exceeded' => false, 'any_limit_warning' => false],
            'upgrade_alerts' => [
                [
                    'type' => 'no_subscription',
                    'urgency' => 'high',
                    'message' => 'Subscribe to a plan to start managing your business reviews and access all features.',
                    'action' => 'subscribe_now'
                ]
            ]
        ];
    }

    /**
     * Clear usage cache for user
     */
    public function clearUsageCache(int $userId): void
    {
        Cache::forget("subscription_usage_{$userId}");
    }

    /**
     * Refresh usage cache for user
     */
    public function refreshUsageCache(int $userId): array
    {
        $this->clearUsageCache($userId);
        return $this->getRealTimeUsage($userId, true);
    }
}
