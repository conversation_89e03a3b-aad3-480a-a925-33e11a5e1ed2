<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    protected $table = 'plans';

    protected $fillable = [
        'name',
        'short_description',
        'long_description',
        'currency',
        'currency_symbol',
        'price',
        'annual_price',
        'annual_discount_percentage',
        'tax_percentage',
        'duration_days',
        'features',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'annual_price' => 'decimal:2',
        'tax_percentage' => 'decimal:2',
        'annual_discount_percentage' => 'integer',
        'duration_days' => 'integer',
        'is_active' => 'boolean',
        'features' => 'array'
    ];

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'plan_id', 'id');
    }

    /**
     * Get feature value by key
     */
    public function getFeatureValue(string $key)
    {
        if (!$this->features || !is_array($this->features)) {
            return null;
        }

        foreach ($this->features as $feature) {            
            if (isset($feature['key']) && $feature['key'] === $key) {                
                return $feature['value'] ?? null;
            }
        }

        return null;
    }

    /**
     * Check if plan allows unlimited replies
     */
    public function hasUnlimitedReplies(): bool
    {
        $monthlyLimit = $this->getFeatureValue('monthly_reply_limit');
        return $monthlyLimit === -1 || strtolower($monthlyLimit) === 'unlimited';
    }

    /**
     * Get monthly reply limit
     */
    public function getMonthlyReplyLimit(): int
    {
        $limit = $this->getFeatureValue('monthly_reply_limit');        
        if(str_contains($limit, '/')) {
            $limit = explode('/', $limit)[0];
        }
        return is_numeric($limit) ? (int)$limit : 100;
    }

    /**
     * Get business connections limit
     */
    public function getBusinessConnectionsLimit(): int
    {
        $limit = $this->getFeatureValue('business_connections_limit');
        return is_numeric($limit) ? (int)$limit : 1;
    }

    /**
     * Get team members limit
     */
    public function getTeamMembersLimit(): int
    {
        $limit = $this->getFeatureValue('team_members_limit');
        return is_numeric($limit) ? (int)$limit : 1;
    }

    /**
     * Check if plan has specific feature enabled
     */
    public function hasFeature(string $feature): bool
    {
        $value = $this->getFeatureValue($feature);

        // Handle boolean features
        if (is_bool($value)) {
            return $value;
        }

        // Handle string boolean values
        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'enabled', 'custom']);
        }

        // Handle numeric values (consider non-zero as enabled)
        if (is_numeric($value)) {
            return $value > 0;
        }

        return false;
    }

    /**
     * Check if plan has specific API access level
     */
    public function hasApiAccess(string $level = 'basic'): bool
    {
        $currentLevel = $this->getFeatureValue('api_access_level') ?? 'none';
        $levels = ['none', 'basic', 'advanced'];

        $currentIndex = array_search($currentLevel, $levels);
        $requiredIndex = array_search($level, $levels);

        return $currentIndex !== false && $requiredIndex !== false && $currentIndex >= $requiredIndex;
    }

    /**
     * Check if plan has advanced analytics
     */
    public function hasAdvancedAnalytics(): bool
    {
        $analyticsLevel = $this->getFeatureValue('analytics_level') ?? 'basic';
        return $analyticsLevel === 'advanced';
    }

    /**
     * Check if plan allows custom AI training
     */
    public function hasCustomAiTraining(): bool
    {
        $aiLevel = $this->getFeatureValue('ai_customization_level') ?? 'default';
        return $aiLevel === 'custom_training';
    }

    /**
     * Check if plan allows custom templates
     */
    public function hasCustomTemplates(): bool
    {
        $templateAccess = $this->getFeatureValue('template_access') ?? 'default';
        return $templateAccess === 'custom';
    }

    /**
     * Get support level
     */
    public function getSupportLevel(): string
    {
        return $this->getFeatureValue('support_level') ?? 'email';
    }

    /**
     * Get the effective price based on billing cycle
     */
    public function getEffectivePrice(bool $isAnnual = false): float
    {
        if ($isAnnual && $this->annual_price) {
            return $this->annual_price;
        }

        if ($isAnnual && $this->annual_discount_percentage > 0) {
            $annualPrice = $this->price * 12;
            $discount = ($annualPrice * $this->annual_discount_percentage) / 100;
            return $annualPrice - $discount;
        }

        return $this->price;
    }

    /**
     * Get annual savings amount
     */
    public function getAnnualSavings(): float
    {
        if ($this->annual_discount_percentage > 0) {
            $regularAnnualPrice = $this->price * 12;
            $discountedPrice = $this->getEffectivePrice(true);
            return $regularAnnualPrice - $discountedPrice;
        }

        return 0;
    }

    /**
     * Get regular annual price (without discount)
     */
    public function getRegularAnnualPrice(): float
    {
        return $this->price * 12;
    }

    /**
     * Get pricing display data for frontend
     */
    public function getPricingDisplay(): array
    {
        $regularAnnualPrice = $this->getRegularAnnualPrice();
        $discountedAnnualPrice = $this->getEffectivePrice(true);
        $savings = $this->getAnnualSavings();

        return [
            'monthly' => [
                'price' => $this->price,
                'formatted' => $this->currency_symbol . number_format($this->price, 2)
            ],
            'annual' => [
                'regular_price' => $regularAnnualPrice,
                'discounted_price' => $discountedAnnualPrice,
                'formatted_regular' => $this->currency_symbol . number_format($regularAnnualPrice, 2),
                'formatted_discounted' => $this->currency_symbol . number_format($discountedAnnualPrice, 2),
                'discount_percentage' => $this->annual_discount_percentage,
                'savings' => $savings,
                'formatted_savings' => $this->currency_symbol . number_format($savings, 2),
                'has_discount' => $this->annual_discount_percentage > 0
            ]
        ];
    }

    /**
     * Check if this is a free plan
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get plan tier for comparison
     */
    public function getTier(): int
    {
        $tierMap = [
            'Free' => 0,
            'Business' => 1,
            'Enterprise' => 2,
            'Agency' => 3
        ];

        return $tierMap[$this->name] ?? 0;
    }
}
