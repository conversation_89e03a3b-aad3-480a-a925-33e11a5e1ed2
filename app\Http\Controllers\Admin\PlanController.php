<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PlanController extends Controller
{
    /**
     * Display a listing of plans with advanced filtering and statistics
     */
    public function index(Request $request)
    {
        $query = Plan::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('currency', 'like', "%{$search}%");
            });
        }

        // Filter by currency
        if ($request->filled('currency')) {
            $query->where('currency', $request->currency);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by price range
        if ($request->filled('price_min')) {
            $query->where('price', '>=', $request->price_min);
        }
        if ($request->filled('price_max')) {
            $query->where('price', '<=', $request->price_max);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSorts = ['name', 'price', 'currency', 'created_at', 'is_active'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Get plans with subscription counts
        $plans = $query->withCount(['subscriptions' => function($q) {
            $q->where('status', 'active');
        }])->paginate(10)->appends($request->all());

        // Statistics
        $statistics = [
            'total_plans' => Plan::count(),
            'active_plans' => Plan::where('is_active', true)->count(),
            'inactive_plans' => Plan::where('is_active', false)->count(),
            'total_subscriptions' => Subscription::where('status', 'active')->count(),
            'inr_plans' => Plan::where('currency', 'INR')->count(),
            'usd_plans' => Plan::where('currency', 'USD')->count(),
            'free_plans' => Plan::where('price', 0)->count(),
            'paid_plans' => Plan::where('price', '>', 0)->count(),
        ];

        return view('admin.plans.index', compact('plans', 'statistics'));
    }

    /**
     * Show the form for creating a new plan
     */
    public function create()
    {
        $currencies = [
            'INR' => ['symbol' => '₹', 'name' => 'Indian Rupee'],
            'USD' => ['symbol' => '$', 'name' => 'US Dollar']
        ];

        $defaultFeatures = $this->getDefaultFeatures();

        return view('admin.plans.create', compact('currencies', 'defaultFeatures'));
    }

    /**
     * Store a newly created plan
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'short_description' => 'required|string|max:255',
            'long_description' => 'required|string',
            'currency' => 'required|in:INR,USD',
            'price' => 'required|numeric|min:0',
            'annual_discount_percentage' => 'nullable|integer|min:0|max:100',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'duration_days' => 'required|integer|min:1',
            'features' => 'required|array',
            'features.*.title' => 'required|string',
            'features.*.key' => 'required|string',
            'features.*.value' => 'required',
            'features.*.marketing_status' => 'boolean',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Set currency symbol
        $data['currency_symbol'] = $request->currency === 'USD' ? '$' : '₹';

        // Calculate annual price if discount is provided
        if ($request->annual_discount_percentage > 0) {
            $regularAnnualPrice = $request->price * 12;
            $discount = ($regularAnnualPrice * $request->annual_discount_percentage) / 100;
            $data['annual_price'] = $regularAnnualPrice - $discount;
        }

        // Process features
        $features = [];
        foreach ($request->features as $feature) {
            $features[] = [
                'title' => $feature['title'],
                'key' => $feature['key'],
                'value' => $this->processFeatureValue($feature['value']),
                'marketing_status' => isset($feature['marketing_status']) ? (bool)$feature['marketing_status'] : true
            ];
        }
        $data['features'] = $features;

        Plan::create($data);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan created successfully!');
    }

    /**
     * Display the specified plan with analytics
     */
    public function show(Plan $plan)
    {
        // Get subscription analytics for this plan
        $subscriptionStats = [
            'total_subscriptions' => $plan->subscriptions()->count(),
            'active_subscriptions' => $plan->subscriptions()->where('status', 'active')->count(),
            'expired_subscriptions' => $plan->subscriptions()->where('status', 'expired')->count(),
            'cancelled_subscriptions' => $plan->subscriptions()->where('status', 'cancelled')->count(),
        ];

        // Monthly subscription trends (last 12 months)
        $monthlyTrends = $plan->subscriptions()
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Revenue analytics
        $revenueStats = [
            'total_revenue' => $plan->subscriptions()
                ->join('payments', 'subscriptions.payment_id', '=', 'payments.id')
                ->where('payments.payment_status', 'SUCCESS')
                ->sum('payments.amount'),
            'monthly_revenue' => $plan->subscriptions()
                ->join('payments', 'subscriptions.payment_id', '=', 'payments.id')
                ->where('payments.payment_status', 'SUCCESS')
                ->where('subscriptions.created_at', '>=', Carbon::now()->startOfMonth())
                ->sum('payments.amount'),
        ];

        // Recent subscriptions
        $recentSubscriptions = $plan->subscriptions()
            ->with(['user', 'payment'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.plans.show', compact(
            'plan',
            'subscriptionStats',
            'monthlyTrends',
            'revenueStats',
            'recentSubscriptions'
        ));
    }

    /**
     * Show the form for editing the specified plan
     */
    public function edit(Plan $plan)
    {
        $currencies = [
            'INR' => ['symbol' => '₹', 'name' => 'Indian Rupee'],
            'USD' => ['symbol' => '$', 'name' => 'US Dollar']
        ];

        $defaultFeatures = $this->getDefaultFeatures();

        return view('admin.plans.edit', compact('plan', 'currencies', 'defaultFeatures'));
    }

    /**
     * Update the specified plan
     */
    public function update(Request $request, Plan $plan)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'short_description' => 'required|string|max:255',
            'long_description' => 'required|string',
            'currency' => 'required|in:INR,USD',
            'price' => 'required|numeric|min:0',
            'annual_discount_percentage' => 'nullable|integer|min:0|max:100',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'duration_days' => 'required|integer|min:1',
            'features' => 'required|array',
            'features.*.title' => 'required|string',
            'features.*.key' => 'required|string',
            'features.*.value' => 'required',
            'features.*.marketing_status' => 'boolean',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Set currency symbol
        $data['currency_symbol'] = $request->currency === 'USD' ? '$' : '₹';

        // Calculate annual price if discount is provided
        if ($request->annual_discount_percentage > 0) {
            $regularAnnualPrice = $request->price * 12;
            $discount = ($regularAnnualPrice * $request->annual_discount_percentage) / 100;
            $data['annual_price'] = $regularAnnualPrice - $discount;
        } else {
            $data['annual_price'] = null;
        }

        // Process features
        $features = [];
        foreach ($request->features as $feature) {
            $features[] = [
                'title' => $feature['title'],
                'key' => $feature['key'],
                'value' => $this->processFeatureValue($feature['value']),
                'marketing_status' => isset($feature['marketing_status']) ? (bool)$feature['marketing_status'] : true
            ];
        }
        $data['features'] = $features;

        $plan->update($data);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan updated successfully!');
    }

    /**
     * Remove the specified plan
     */
    public function destroy(Plan $plan)
    {
        // Check if plan has active subscriptions
        $activeSubscriptions = $plan->subscriptions()->where('status', 'active')->count();

        if ($activeSubscriptions > 0) {
            return redirect()->route('admin.plans.index')
                ->with('error', "Cannot delete plan '{$plan->name}' as it has {$activeSubscriptions} active subscriptions.");
        }

        $planName = $plan->name;
        $plan->delete();

        return redirect()->route('admin.plans.index')
            ->with('success', "Plan '{$planName}' deleted successfully!");
    }

    /**
     * Toggle plan status
     */
    public function toggleStatus(Plan $plan)
    {
        $plan->update(['is_active' => !$plan->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Plan status updated successfully!',
            'new_status' => $plan->is_active
        ]);
    }

    /**
     * Get plan analytics data
     */
    public function analytics(Request $request)
    {
        $dateRange = $request->get('date_range', 30);
        $startDate = Carbon::now()->subDays($dateRange);

        // Overview statistics
        $overview = [
            'total_plans' => Plan::count(),
            'active_plans' => Plan::where('is_active', true)->count(),
            'total_subscriptions' => Subscription::where('status', 'active')->count(),
            'total_revenue' => Subscription::join('payments', 'subscriptions.payment_id', '=', 'payments.id')
                ->where('payments.payment_status', 'SUCCESS')
                ->where('subscriptions.created_at', '>=', $startDate)
                ->sum('payments.amount'),
            'average_plan_price' => Plan::where('is_active', true)->avg('price'),
            'conversion_rate' => $this->calculateConversionRate($dateRange)
        ];

        // Plan performance data
        $planPerformance = Plan::withCount(['subscriptions' => function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate);
        }])
        ->with(['subscriptions' => function($q) use ($startDate) {
            $q->join('payments', 'subscriptions.payment_id', '=', 'payments.id')
              ->where('payments.payment_status', 'SUCCESS')
              ->where('subscriptions.created_at', '>=', $startDate)
              ->select('subscriptions.*', 'payments.amount');
        }])
        ->get()
        ->map(function($plan) {
            $revenue = $plan->subscriptions->sum('amount');
            return [
                'name' => $plan->name,
                'subscriptions' => $plan->subscriptions_count,
                'revenue' => $revenue,
                'currency' => $plan->currency_symbol
            ];
        });

        // Monthly trends
        $monthlyTrends = Subscription::select(
            DB::raw('DATE_FORMAT(subscriptions.created_at, "%Y-%m") as month'),
            DB::raw('COUNT(*) as subscriptions'),
            DB::raw('SUM(CASE WHEN payments.payment_status = "SUCCESS" THEN payments.amount ELSE 0 END) as revenue')
        )
        ->join('payments', 'subscriptions.payment_id', '=', 'payments.id')
        ->where('subscriptions.created_at', '>=', Carbon::now()->subMonths(12))
        ->groupBy('month')
        ->orderBy('month')
        ->get();

        return view('admin.plans.analytics', compact(
            'overview',
            'planPerformance',
            'monthlyTrends'
        ));
    }

    /**
     * Export plans data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        $plans = Plan::with('subscriptions')->get();

        if ($format === 'json') {
            return response()->json($plans);
        }

        // CSV Export
        $filename = 'plans_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($plans) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'ID', 'Name', 'Description', 'Currency', 'Price', 'Annual Price',
                'Annual Discount %', 'Tax %', 'Duration Days', 'Active Subscriptions',
                'Total Revenue', 'Status', 'Created At'
            ]);

            foreach ($plans as $plan) {
                $activeSubscriptions = $plan->subscriptions->where('status', 'active')->count();
                $totalRevenue = $plan->subscriptions->sum(function($sub) {
                    return $sub->payment && $sub->payment->status === 'completed' ? $sub->payment->amount : 0;
                });

                fputcsv($file, [
                    $plan->id,
                    $plan->name,
                    $plan->short_description,
                    $plan->currency,
                    $plan->price,
                    $plan->annual_price,
                    $plan->annual_discount_percentage,
                    $plan->tax_percentage,
                    $plan->duration_days,
                    $activeSubscriptions,
                    $totalRevenue,
                    $plan->is_active ? 'Active' : 'Inactive',
                    $plan->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get plan comparison data
     */
    public function getComparePlans(Request $request)
    {
        $currency = $request->get('currency', 'INR');
        $plans = Plan::where('currency', $currency)
            ->where('is_active', true)
            ->orderBy('price')
            ->get();

        $comparisonData = [];
        foreach ($plans as $plan) {
            $comparisonData[] = [
                'id' => $plan->id,
                'name' => $plan->name,
                'price' => $plan->price,
                'currency_symbol' => $plan->currency_symbol,
                'features' => $plan->features,
                'pricing_info' => $plan->getPricingInfo(),
                'is_popular' => $plan->name === 'Business', // Mark Business as popular
            ];
        }

        return response()->json([
            'success' => true,
            'plans' => $comparisonData
        ]);
    }

    /**
     * Get default features structure
     */
    private function getDefaultFeatures()
    {
        return [
            [
                'title' => 'Business Connections',
                'key' => 'business_connections_limit',
                'value' => 1,
                'marketing_status' => true
            ],
            [
                'title' => 'Team Members',
                'key' => 'team_members_limit',
                'value' => 1,
                'marketing_status' => true
            ],
            [
                'title' => 'Monthly Reply Limit',
                'key' => 'monthly_reply_limit',
                'value' => 100,
                'marketing_status' => true
            ],
            [
                'title' => 'Smart Replies',
                'key' => 'smart_replies_enabled',
                'value' => true,
                'marketing_status' => true
            ],
            [
                'title' => 'Analytics Level',
                'key' => 'analytics_level',
                'value' => 'basic',
                'marketing_status' => true
            ],
            [
                'title' => 'AI Customization',
                'key' => 'ai_customization_level',
                'value' => 'default',
                'marketing_status' => true
            ],
            [
                'title' => 'Template Access',
                'key' => 'template_access',
                'value' => 'basic',
                'marketing_status' => true
            ],
            [
                'title' => 'Scheduled Auto Replies',
                'key' => 'scheduled_auto_replies_enabled',
                'value' => false,
                'marketing_status' => true
            ],
            [
                'title' => 'Settings Access',
                'key' => 'settings_access_enabled',
                'value' => true,
                'marketing_status' => true
            ],
            [
                'title' => 'Data Export',
                'key' => 'data_export_enabled',
                'value' => false,
                'marketing_status' => true
            ],
            [
                'title' => 'Beta Features',
                'key' => 'beta_features_enabled',
                'value' => false,
                'marketing_status' => true
            ],
            [
                'title' => 'API Access',
                'key' => 'api_access_level',
                'value' => 'none',
                'marketing_status' => true
            ],
            [
                'title' => 'Support Level',
                'key' => 'support_level',
                'value' => 'email',
                'marketing_status' => true
            ]
        ];
    }

    /**
     * Process feature value based on type
     */
    private function processFeatureValue($value)
    {
        // Handle boolean values
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }

        // Handle numeric values
        if (is_numeric($value)) {
            return (int)$value;
        }

        // Handle unlimited values
        if (strtolower($value) === 'unlimited') {
            return -1;
        }

        // Return as string for other values
        return $value;
    }

    /**
     * Calculate conversion rate for analytics
     */
    private function calculateConversionRate($days)
    {
        $startDate = Carbon::now()->subDays($days);

        // This is a simplified calculation - you might want to implement
        // more sophisticated conversion tracking based on your business logic
        $totalVisitors = 1000; // You would get this from your analytics
        $totalSubscriptions = Subscription::where('created_at', '>=', $startDate)->count();

        return $totalVisitors > 0 ? ($totalSubscriptions / $totalVisitors) * 100 : 0;
    }
}
