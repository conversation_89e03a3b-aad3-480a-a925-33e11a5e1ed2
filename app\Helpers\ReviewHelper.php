<?php

namespace App\Helpers;

use App\Models\Business;
use App\Models\BusinessAccount;
use App\Models\GoogleReview;
use App\Models\TeamMember;
use App\Models\Template;
use App\Services\SettingService;
use App\Services\TemplateService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class ReviewHelper
{
    protected static function extractAccountId($name)
    {
        return explode('/', $name)[0];
    }

    public static function getReviews($locationId, $businessId, $token)
    {
        try {
            // Use GoogleBusinessService for API calls
            $googleBusinessService = new \App\Services\GoogleBusinessService($token);

            $pageToken = Session::get('nextPageToken');
            return $googleBusinessService->getReviewsWithPagination($locationId, $businessId, $token, $pageToken);
        } catch (\Exception $e) {
            Log::error('Failed to fetch reviews', [
                'message' => $e->getMessage(),
                'location_id' => $locationId,
                'business_id' => $businessId
            ]);
            throw $e;
        }
    }

    public static function fetchLatestReviews(Request $request)
    {
        $location = $request->input('location');
        $businessId = $request->input('businessId');
        $token = Session::get('business_google_token');
        $isJsonRequest = $request->wantsJson() || $request->ajax();

        if (!$location) {
            return $isJsonRequest
                ? response()->json(['success' => false, 'message' => 'No location specified.'], 400)
                : redirect()->back()->with('error', 'No location specified.');
        }

        Session::put('selected_location', $location);
        $locationId = explode("/", $location)[1];
        try {
            $reviewsResponse = self::getReviews($locationId, $businessId, $token);

            if (isset($reviewsResponse['nextPageToken'])) {
                Session::put('nextPageToken', $reviewsResponse['nextPageToken']);
            } else {
                Session::forget('nextPageToken');
            }

            // Initialize counters for operations
            $response = [
                'success' => true,
                'new_reviews_added' => 0,
                'reviews_updated' => 0,
                'new_replies_added' => 0,
                'replies_updated' => 0,
                'message' => ''
            ];

            self::getReviewCondition($reviewsResponse, $locationId, $businessId, $response);

            if ($isJsonRequest) {
                return response()->json(['message' => $response['message'], 'success' => true, 'status' => 200]);
            } else {
                return response()->json(['message' => $response['message'], 'success' => true, 'status' => 200]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to fetch reviews', [
                'message' => $e->getMessage(),
                'location_id' => $locationId
            ]);

            if ($isJsonRequest) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch reviews: ' . $e->getMessage()
                ], 500);
            } else {
                return redirect()->back()->with('error', 'Failed to fetch reviews: ' . $e->getMessage());
            }
        }
    }

    public static function getReviewCondition($reviewsResponse, $locationId, $businessId, &$response)
    {
        DB::transaction(function () use ($reviewsResponse, $locationId, $businessId, &$response) {
            // Get all existing Google review IDs for this location
            $existingReviewIds = GoogleReview::where('location_id', $locationId)
                ->where('account_id', $businessId)
                ->whereNull('parent_id')
                ->pluck('review_id')
                ->toArray();

            // Track which review IDs we've seen in the API response
            $googleReviewIds = [];

            foreach ($reviewsResponse['reviews'] ?? [] as $review) {
                $reviewId = $review['reviewId'] ?? null;

                if (!$reviewId) {
                    continue;
                }

                // Add to our list of reviews from Google
                $googleReviewIds[] = $reviewId;

                // Check if review exists and needs update
                $reviewObject = GoogleReview::where('review_id', $reviewId)
                    ->where('parent_id', null)
                    ->first();

                $reviewData = [
                    'review_id'         => $reviewId,
                    'location_id'       => $locationId,
                    'account_id'        => $businessId,
                    'reviewer_name'     => $review['reviewer']['displayName'] ?? null,
                    'reviewer_photo'    => $review['reviewer']['profilePhotoUrl'] ?? null,
                    'star_rating'       => $review['starRating'] ?? null,
                    'comment'           => $review['comment'] ?? null,
                    'created_at_google' => isset($review['createTime']) ? Carbon::parse($review['createTime'])->toDateTimeString() : null,
                    'updated_at_google' => isset($review['updateTime']) ? Carbon::parse($review['updateTime'])->toDateTimeString() : null,
                ];

                if ($reviewObject) {
                    // Update existing review if it has changed
                    $shouldUpdate = false;
                    $updateData = [];

                    // Check if any fields have changed
                    foreach ($reviewData as $key => $value) {
                        if ($key === 'review_id' || $key === 'location_id' || $key === 'account_id' || $key === 'parent_id') {
                            continue;
                        }

                        if ($reviewObject->$key !== $value) {
                            $shouldUpdate = true;
                            $updateData[$key] = $value;
                        }
                    }

                    if ($shouldUpdate) {
                        $reviewObject->update($updateData);
                        $response['reviews_updated']++;
                        Log::info('Updated existing review', [
                            'review_id' => $reviewId,
                            'changes' => $updateData
                        ]);
                    }
                } else {
                    // Create new review
                    $reviewObject = GoogleReview::create($reviewData);
                    $response['new_reviews_added']++;
                }

                // Process review reply if exists
                if (isset($review['reviewReply'])) {
                    $replyData = [
                        'review_id'         => null,
                        'location_id'       => $locationId,
                        'account_id'        => $businessId,
                        'parent_id'         => $reviewObject->id,
                        'reviewer_name'     => 'Admin',
                        'reviewer_photo'    => null,
                        'star_rating'       => null,
                        'comment'           => $review['reviewReply']['comment'],
                        'created_at_google' => null,
                        'updated_at_google' => isset($review['reviewReply']['updateTime']) ? Carbon::parse($review['reviewReply']['updateTime'])->toDateTimeString() : null,
                    ];

                    // Check if reply exists by parent_id
                    $existingReply = GoogleReview::where('parent_id', $reviewObject->id)
                        ->whereNull('review_id')
                        ->first();

                    if ($existingReply) {
                        // Update existing reply if it has changed
                        $shouldUpdate = false;
                        $updateData = [];

                        // Check if any fields have changed
                        foreach ($replyData as $key => $value) {
                            if ($key === 'review_id' || $key === 'location_id' || $key === 'account_id' || $key === 'parent_id') {
                                continue;
                            }

                            if ($existingReply->$key !== $value) {
                                $shouldUpdate = true;
                                $updateData[$key] = $value;
                            }
                        }

                        if ($shouldUpdate) {
                            $existingReply->update($updateData);
                            $response['replies_updated']++;
                            Log::info('Updated existing reply', [
                                'review_id' => $reviewObject->id,
                                'changes' => $updateData
                            ]);
                        }
                    } else {
                        // Create new reply only if it doesn't exist
                        GoogleReview::create($replyData);
                        $response['new_replies_added']++;
                    }
                }
            }

            // Generate the appropriate message based on operations
            if (
                $response['new_reviews_added'] > 0 || $response['reviews_updated'] > 0 ||
                $response['new_replies_added'] > 0 || $response['replies_updated'] > 0
            ) {
                // For new reviews or replies, use a simplified message
                $response['message'] = 'New reviews and reply added';
            } else {
                $response['message'] = 'Review already fetched latest review';
            }
        });
    }

    public static function fetchNextReviews(Request $request)
    {
        $allReviews = [];
        $nextPageToken = null;

        $business = Business::where('location_name', $request->location)->first();

        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $businessGoogleId = $request->businessId;
        $businessAccount = BusinessAccount::where('business_google_id', $businessGoogleId)->first();

        if (!$businessAccount || !$businessAccount->business_google_token) {
            return response()->json(['error' => 'Business account or token not found'], 404);
        }
        $locationId = explode('/', $request->location)[1];
        $nextPageToken = $business->next_token;

        do {
            $params = [];
            if ($nextPageToken) {
                $params['pageToken'] = $nextPageToken;
            }

            // $response = Http::withToken($businessAccount->business_google_token)
            //     ->get("https://mybusiness.googleapis.com/v4/accounts/{$businessGoogleId}/locations/{$locationId}/reviews", $params);
            // $data = $response->json();
            $data = self::getReviews($locationId, $businessGoogleId, $businessAccount->business_google_token);

            if (isset($data['error'])) {
                return response()->json(['error' => $data['error']['message']], 400);
            }

            $allReviews = array_merge($allReviews, $data['reviews'] ?? []);
            $nextPageToken = $data['nextPageToken'] ?? null;

            $business->update(['next_token' => $nextPageToken]);
        } while ($nextPageToken);

        return response()->json([
            'reviews' => $allReviews,
            'nextPageToken' => $nextPageToken,
        ]);
    }

    public static function updateReviewType($reviewId, $locationId, $reviewText = null, $starRating)
    {
        $review = GoogleReview::where(['review_id' => $reviewId, 'location_id' => $locationId])->first();
        if ($review) {
            // If we have review text, use OpenAI to analyze sentiment
            $prompt = "Analyze the sentiment of this Google review and return ONLY ONE WORD (Positive, Negative, or Neutral). " .
                "Consider both the text content and the star rating of $starRating out of 5 stars.\n\n" .
                "Review: \"$reviewText\"";
            try {
                $aiStart = microtime(true);
                $aiEndpoint = 'https://api.openai.com/v1/chat/completions';
                $aiRequest = [
                    'model' => 'gpt-3.5-turbo',
                    'messages' => [
                        ['role' => 'system', 'content' => 'You are a sentiment analysis tool that returns exactly one word: Positive, Negative, or Neutral. Do not return any other text.'],
                        ['role' => 'user', 'content' => $prompt],
                    ],
                    'max_tokens' => 10,
                    'temperature' => 0.3,
                ];
                $response = \Illuminate\Support\Facades\Http::withToken('[masked]')->post($aiEndpoint, $aiRequest);
                $aiDuration = (int)((microtime(true) - $aiStart) * 1000);

                try {
                    \App\Services\ExternalApiLogger::logAi(
                        $aiEndpoint,
                        $aiRequest,
                        $response->json(),
                        $response->status(),
                        $response->successful() ? 'success' : 'fail',
                        $aiDuration
                    );
                } catch (\Exception $e) {
                    Log::error('Failed to log AI call:', [
                        'endpoint' => $aiEndpoint,
                        'request' => $aiRequest,
                        'error' => $e->getMessage()
                    ]);
                }
                if ($response->successful()) {
                    $aiResponse = $response->json();
                    $content = $aiResponse['choices'][0]['message']['content'] ?? '';
                    // Extract just the sentiment word and normalize it
                    $content = trim($content);
                    if (preg_match('/\b(Positive|Negative|Neutral)\b/i', $content, $matches)) {
                        $sentiment = ucfirst(strtolower($matches[0]));
                        // Update the reviewType in the database if reviewId is provided
                        if ($reviewId) {
                            self::updateReviewTypeData($reviewId, strtolower($sentiment));
                        }
                        return $sentiment;
                    }
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error analyzing sentiment with OpenAI: ' . $e->getMessage());
            }
        }
    }

    public function updateReviewTypeData($reviewId, $sentiment)
    {
        try {
            $review = \App\Models\GoogleReview::find($reviewId);
            if ($review) {
                $review->reviewType = strtolower($sentiment);
                $review->save();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Error updating reviewType: ' . $e->getMessage());
            return false;
        }
    }

    public static function checkBusinessLocation($response)
    {
        $results = [];
        if (!isset($response['locations']) || !is_array($response['locations'])) {
            return $results;
        }
        foreach ($response['locations'] as $location) {
            if (!isset($location['name'])) {
                continue;
            }
            $locationName = $location['name'];
            $checkTeamMember = self::checkTeamMember(Auth::user()->id);
            if ($checkTeamMember) {
                $business = Business::where(['location_name' => $locationName])->first();
                $results[] = [
                    'location_name' => $locationName,
                    'exists' => true,
                    'user' => $business->user ?? null
                ];
            } else {
                $business = Business::where(['location_name' => $locationName])->first();
                $results[] = [
                    'location_name' => $locationName,
                    'exists' => true,
                    'user' => $business->user ?? null
                ];
            }
        }

        return $results;
    }

    public static function checkTeamMember($userId)
    {
        $checkTeamMember = TeamMember::where('user_id', $userId)
            ->with(['business', 'invitedBy'])
            ->exists();
        return $checkTeamMember;
    }

    public static function insertBusiness($response, $selectedLocation, $user)
    {
        if (isset($selectedLocation) && isset($response['locations']) && is_array($response['locations'])) {

            foreach ($response['locations'] as $location) {
                if ($location['name'] === $selectedLocation) {
                    $business = Business::firstOrCreate(
                        [
                            'location_name' => $location['name'] ?? null,
                            'user_id' => $user->id
                        ],
                        [
                            'title'                => $location['title'] ?? null,
                            'primary_phone'        => isset($location['phoneNumbers']) ? ($location['phoneNumbers']['primaryPhone'] ?? null) : null,
                            'additional_phones'    => isset($location['phoneNumbers']) ? ($location['phoneNumbers']['additionalPhones'] ?? []) : [],
                            'region_code'          => isset($location['storefrontAddress']) ? ($location['storefrontAddress']['regionCode'] ?? null) : null,
                            'language_code'        => isset($location['storefrontAddress']) ? ($location['storefrontAddress']['languageCode'] ?? null) : null,
                            'postal_code'          => isset($location['storefrontAddress']) ? ($location['storefrontAddress']['postalCode'] ?? null) : null,
                            'administrative_area'  => isset($location['storefrontAddress']) ? ($location['storefrontAddress']['administrativeArea'] ?? null) : null,
                            'locality'             => isset($location['storefrontAddress']) ? ($location['storefrontAddress']['locality'] ?? null) : null,
                            'address_lines'        => isset($location['storefrontAddress']) ? ($location['storefrontAddress']['addressLines'] ?? []) : [],
                            'website'              => $location['websiteUri'] ?? null,
                            'latitude'             => isset($location['latlng']) ? ($location['latlng']['latitude'] ?? null) : null,
                            'longitude'            => isset($location['latlng']) ? ($location['latlng']['longitude'] ?? null) : null,
                            'profile_description'  => isset($location['profile']) ? ($location['profile']['description'] ?? null) : null,
                            'user_id'              => $user->id
                        ]
                    );

                    $templateCount = Template::where('business_id', $business->id)->count();
                    if ($business->wasRecentlyCreated || $templateCount === 0) {
                        TemplateService::createDefaultTemplatesForBusiness($business->id);
                    }
                    SettingService::createDefaultSettingsForBusiness($business->id);
                }
            }
        }
    }

    public static function storeReviews($accountId, $locationId, $reviewsData) {
        try {
            // Process and save reviews            
            if (!isset($reviewsData['reviews']) || !is_array($reviewsData['reviews'])) {
                return false;
            }

            if(str_contains($locationId, '/')) {
                $locationId = explode("/", $locationId)[1];
            }

            $existingReviewIds = GoogleReview::where('location_id', $locationId)
                ->where('account_id', $accountId)
                ->whereNull('parent_id')
                ->pluck('review_id')
                ->toArray();

            foreach ($reviewsData['reviews'] as $review) {                            

                if (!in_array($review['reviewId'], $existingReviewIds)) {
                    // Create new review record
                    $newReview = GoogleReview::create([
                        'review_id' => $review['reviewId'],
                        'location_id' => $locationId,
                        'account_id' => $accountId,
                        'parent_id' => null,
                        'reviewer_name' => $review['reviewer']['displayName'] ?? 'Anonymous',
                        'reviewer_photo' => $review['reviewer']['profilePhotoUrl'] ?? null,
                        'star_rating' => $review['starRating'] ?? null,
                        'comment' => $review['comment'] ?? null,
                        'created_at_google' => isset($review['createTime']) ?
                            Carbon::parse($review['createTime'])->toDateTimeString() : null,
                        'updated_at_google' => isset($review['updateTime']) ?
                            Carbon::parse($review['updateTime'])->toDateTimeString() : null
                    ]);

                    // If review has a reply, save it as well
                    if (isset($review['reviewReply']) && isset($review['reviewReply']['comment'])) {
                        GoogleReview::create([
                            'review_id' => null,
                            'location_id' => $locationId,
                            'account_id' => $accountId,
                            'parent_id' => $newReview->id,
                            'reviewer_name' => 'Business Owner',
                            'reviewer_photo' => null,
                            'star_rating' => null,
                            'comment' => $review['reviewReply']['comment'],
                            'created_at_google' => null,
                            'updated_at_google' => isset($review['reviewReply']['updateTime']) ?
                                Carbon::parse($review['reviewReply']['updateTime'])->toDateTimeString() : null
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to store reviews:', [
                'error' => $e->getMessage(),
                'reviewsData' => $reviewsData
            ]);
            return false;
        }

        return true;
    }
}
