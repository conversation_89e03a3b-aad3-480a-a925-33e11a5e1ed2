<?php

use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Admin\CouponController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\AdminController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "admin" middleware group.
|
*/

// Admin Guest Routes (Login, etc.)
Route::get('/', [AuthController::class, 'showLoginForm'])->name('admin.home');    

Route::middleware(['admin.guest'])->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('admin.login');
    Route::post('/login', [AuthController::class, 'login'])->name('admin.login.post');
});

// Admin Authenticated Routes
Route::middleware(['admin.auth'])->group(function () {
    // Authentication
    Route::post('/logout', [AuthController::class, 'logout'])->name('admin.logout');
    Route::get('/profile', [AuthController::class, 'profile'])->name('admin.profile');
    Route::put('/profile', [AuthController::class, 'updateProfile'])->name('admin.profile.update');

    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');

    // Users Management
    Route::middleware(['admin.permission:users.view'])->group(function () {
        Route::get('/users', [UserController::class, 'index'])->name('admin.users.index');
        Route::get('/users/{user}', [UserController::class, 'show'])->name('admin.users.show');
        Route::get('/users/{user}/subscriptions', [UserController::class, 'subscriptions'])->name('admin.users.subscriptions');
        Route::get('/users/{user}/payments', [UserController::class, 'payments'])->name('admin.users.payments');
        Route::get('/users/{user}/activity', [UserController::class, 'activity'])->name('admin.users.activity');
    });

    Route::middleware(['admin.permission:users.edit'])->group(function () {
        Route::post('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('admin.users.toggle-status');
        Route::post('/users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('admin.users.reset-password');
        Route::post('/users/{user}/send-login-link', [UserController::class, 'sendLoginLink'])->name('admin.users.send-login-link');
    });

    // Login as user route (for admin testing)
    Route::get('/login-as', [UserController::class, 'loginAs'])->name('admin.users.login-as');

    // Plans Management
    Route::middleware(['admin.permission:plans.view'])->group(function () {
        Route::get('/plans', [PlanController::class, 'index'])->name('admin.plans.index');
        Route::get('/plans/analytics', [PlanController::class, 'analytics'])->name('admin.plans.analytics');
        Route::get('/plans/export', [PlanController::class, 'export'])->name('admin.plans.export');
        Route::get('/plans/{plan}', [PlanController::class, 'show'])->name('admin.plans.show');
    });

    Route::middleware(['admin.permission:plans.create'])->group(function () {
        Route::get('/plans/create', [PlanController::class, 'create'])->name('admin.plans.create');
        Route::post('/plans', [PlanController::class, 'store'])->name('admin.plans.store');
    });

    Route::middleware(['admin.permission:plans.edit'])->group(function () {
        Route::get('/plans/{plan}/edit', [PlanController::class, 'edit'])->name('admin.plans.edit');
        Route::put('/plans/{plan}', [PlanController::class, 'update'])->name('admin.plans.update');
        Route::put('/plans/{plan}/toggle-status', [PlanController::class, 'toggleStatus'])->name('admin.plans.toggle-status');
    });

    Route::middleware(['admin.permission:plans.delete'])->group(function () {
        Route::delete('/plans/{plan}', [PlanController::class, 'destroy'])->name('admin.plans.destroy');
    });

    // Coupons Management
    Route::middleware(['admin.permission:coupons.view'])->group(function () {
        Route::get('/coupons', [CouponController::class, 'index'])->name('admin.coupons.index');
        Route::get('/coupons/{coupon}', [CouponController::class, 'show'])->name('admin.coupons.show');
        Route::get('/coupons/analytics', [CouponController::class, 'analytics'])->name('admin.coupons.analytics');
        Route::get('/coupons/export', [CouponController::class, 'export'])->name('admin.coupons.export');
    });

    Route::middleware(['admin.permission:coupons.create'])->group(function () {
        Route::get('/coupons/create', [CouponController::class, 'create'])->name('admin.coupons.create');
        Route::post('/coupons', [CouponController::class, 'store'])->name('admin.coupons.store');
    });

    Route::middleware(['admin.permission:coupons.edit'])->group(function () {
        Route::get('/coupons/{coupon}/edit', [CouponController::class, 'edit'])->name('admin.coupons.edit');
        Route::put('/coupons/{coupon}', [CouponController::class, 'update'])->name('admin.coupons.update');
        Route::post('/coupons/{coupon}/toggle-status', [CouponController::class, 'toggleStatus'])->name('admin.coupons.toggle-status');
        Route::post('/coupons/bulk-action', [CouponController::class, 'bulkAction'])->name('admin.coupons.bulk-action');
    });

    Route::middleware(['admin.permission:coupons.delete'])->group(function () {
        Route::delete('/coupons/{coupon}', [CouponController::class, 'destroy'])->name('admin.coupons.destroy');
    });

    // Payments Management
    Route::middleware(['admin.permission:payments.view'])->group(function () {
        Route::get('/payments', [PaymentController::class, 'index'])->name('admin.payments.index');
        Route::get('/payments/{payment}', [PaymentController::class, 'show'])->name('admin.payments.show');
        Route::get('/payments/export/csv', [PaymentController::class, 'export'])->name('admin.payments.export');
    });

    Route::middleware(['admin.permission:payments.refund'])->group(function () {
        Route::post('/payments/{payment}/refund', [PaymentController::class, 'refund'])->name('admin.payments.refund');
    });

    // Subscriptions Management
    Route::middleware(['admin.permission:subscriptions.view'])->group(function () {
        Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('admin.subscriptions.index');
        Route::get('/subscriptions/export', [SubscriptionController::class, 'export'])->name('admin.subscriptions.export');
        Route::get('/subscriptions/{subscription}', [SubscriptionController::class, 'show'])->name('admin.subscriptions.show');
    });

    Route::middleware(['admin.permission:subscriptions.edit'])->group(function () {
        Route::put('/subscriptions/{subscription}/extend', [SubscriptionController::class, 'extend'])->name('admin.subscriptions.extend');
        Route::put('/subscriptions/{subscription}/cancel', [SubscriptionController::class, 'cancel'])->name('admin.subscriptions.cancel');
    });

    // Admin Management (Super Admin only)
    Route::middleware(['admin.permission:admins.view'])->group(function () {
        Route::get('/admins', [AdminController::class, 'index'])->name('admin.admins.index');
        Route::get('/admins/export', [AdminController::class, 'export'])->name('admin.admins.export');
        Route::get('/admins/permissions', [AdminController::class, 'permissions'])->name('admin.admins.permissions');
        Route::get('/admins/{admin}', [AdminController::class, 'show'])->name('admin.admins.show');
    });

    Route::middleware(['admin.permission:admins.create'])->group(function () {
        Route::get('/admins/create', [AdminController::class, 'create'])->name('admin.admins.create');
        Route::post('/admins', [AdminController::class, 'store'])->name('admin.admins.store');
    });

    Route::middleware(['admin.permission:admins.edit'])->group(function () {
        Route::get('/admins/{admin}/edit', [AdminController::class, 'edit'])->name('admin.admins.edit');
        Route::put('/admins/{admin}', [AdminController::class, 'update'])->name('admin.admins.update');
        Route::post('/admins/{admin}/toggle-status', [AdminController::class, 'toggleStatus'])->name('admin.admins.toggle-status');
    });

    Route::middleware(['admin.permission:admins.delete'])->group(function () {
        Route::delete('/admins/{admin}', [AdminController::class, 'destroy'])->name('admin.admins.destroy');
    });

    // Analytics & Reports
    Route::middleware(['admin.permission:analytics.view'])->group(function () {
        Route::get('/analytics', [DashboardController::class, 'analytics'])->name('admin.analytics');
    });

    Route::middleware(['admin.permission:analytics.export'])->group(function () {
        Route::get('/analytics/export', [DashboardController::class, 'export'])->name('admin.analytics.export');
    });

    // Logs Management
    Route::middleware(['admin.permission:logs.view'])->group(function () {
        Route::get('/logs', [\App\Http\Controllers\Admin\LogController::class, 'index'])->name('admin.logs.index');
        Route::get('/logs/{log}', [\App\Http\Controllers\Admin\LogController::class, 'show'])->name('admin.logs.show');
        Route::get('/logs-analytics', [\App\Http\Controllers\Admin\LogController::class, 'analytics'])->name('admin.logs.analytics');
    });

    Route::middleware(['admin.permission:logs.export'])->group(function () {
        Route::get('/logs/export', [\App\Http\Controllers\Admin\LogController::class, 'export'])->name('admin.logs.export');
    });

    // Email Templates Management
    Route::middleware(['admin.permission:email_templates.view'])->group(function () {
        Route::get('/email-templates', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'index'])->name('admin.email-templates.index');
        Route::get('/email-templates/{emailTemplate}', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'show'])->name('admin.email-templates.show');
        Route::get('/email-templates/{emailTemplate}/preview', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'preview'])->name('admin.email-templates.preview');
    });

    Route::middleware(['admin.permission:email_templates.create'])->group(function () {
        Route::get('/email-templates/create', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'create'])->name('admin.email-templates.create');
        Route::post('/email-templates', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'store'])->name('admin.email-templates.store');
        Route::post('/email-templates/{emailTemplate}/test', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'sendTest'])->name('admin.email-templates.test');
    });

    Route::middleware(['admin.permission:email_templates.edit'])->group(function () {
        Route::get('/email-templates/{emailTemplate}/edit', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'edit'])->name('admin.email-templates.edit');
        Route::put('/email-templates/{emailTemplate}', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'update'])->name('admin.email-templates.update');
    });

    Route::middleware(['admin.permission:email_templates.delete'])->group(function () {
        Route::delete('/email-templates/{emailTemplate}', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'destroy'])->name('admin.email-templates.destroy');
    });

    // Site Settings Management
    Route::middleware(['admin.permission:site_settings.view'])->group(function () {
        Route::get('/site-settings', [\App\Http\Controllers\Admin\SiteSettingController::class, 'index'])->name('admin.site-settings.index');
        Route::get('/site-settings/edit', [\App\Http\Controllers\Admin\SiteSettingController::class, 'edit'])->name('admin.site-settings.edit');
        Route::get('/site-settings/export', [\App\Http\Controllers\Admin\SiteSettingController::class, 'export'])->name('admin.site-settings.export');
        Route::get('/api/site-settings/{key}', [\App\Http\Controllers\Admin\SiteSettingController::class, 'getValue'])->name('admin.site-settings.get-value');
    });

    Route::middleware(['admin.permission:site_settings.edit'])->group(function () {
        Route::put('/site-settings', [\App\Http\Controllers\Admin\SiteSettingController::class, 'update'])->name('admin.site-settings.update');
        Route::post('/site-settings/reset', [\App\Http\Controllers\Admin\SiteSettingController::class, 'reset'])->name('admin.site-settings.reset');
        Route::post('/site-settings/bulk-update', [\App\Http\Controllers\Admin\SiteSettingController::class, 'bulkUpdate'])->name('admin.site-settings.bulk-update');
        Route::post('/site-settings/clear-cache', [\App\Http\Controllers\Admin\SiteSettingController::class, 'clearCache'])->name('admin.site-settings.clear-cache');
    });
});
