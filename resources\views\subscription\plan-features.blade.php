@php
    if (!isset($features) || !is_array($features)) {
        $features = [];
    }
    
    // Filter key features for display
    $keyFeatures = collect($features)->filter(function($feature) {
        return isset($feature['status']) && $feature['status'] === 'enabled' && 
               in_array($feature['key'] ?? '', [
                   'business_connections_limit', 
                   'team_members_limit', 
                   'monthly_reply_limit', 
                   'template_access', 
                   'analytics_level', 
                   'support_level'
               ]);
    })->take(6);
@endphp

@if($keyFeatures->isEmpty())
    <p class="text-sm text-gray-500">No features listed</p>
@else
    @foreach($keyFeatures as $feature)
        @php
            $displayText = $feature['title'] ?? '';
            
            // Format specific features for better display
            switch($feature['key'] ?? '') {
                case 'business_connections_limit':
                    $value = $feature['value'] ?? 0;
                    $displayText = $value . ' Business Location' . ($value > 1 ? 's' : '');
                    break;
                case 'team_members_limit':
                    $value = $feature['value'] ?? 0;
                    $displayText = $value . ' Team Member' . ($value > 1 ? 's' : '');
                    break;
                case 'monthly_reply_limit':
                    $value = $feature['value'] ?? 0;
                    $displayText = $value === -1 ? 'Unlimited Replies' : $value . ' Monthly Replies';
                    break;
                case 'template_access':
                    $value = $feature['value'] ?? 'default';
                    $displayText = $value === 'custom' ? 'Custom Templates' : 'Default Templates';
                    break;
                case 'analytics_level':
                    $value = $feature['value'] ?? 'basic';
                    $displayText = ucfirst($value) . ' Analytics';
                    break;
                case 'support_level':
                    $value = $feature['value'] ?? 'basic';
                    $displayText = ucfirst($value) . ' Support';
                    break;
                default:
                    $displayText = $feature['title'] ?? ($feature['value'] . ' ' . $feature['key']);
            }
        @endphp
        
        <div class="flex items-center text-sm">
            <i class="fas fa-check text-green-500 mr-2"></i>
            <span>{{ $displayText }}</span>
        </div>
    @endforeach
@endif
