<?php

namespace App\Helpers;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\BusinessAccount;

class GoogleTokenRefreshHelper
{
    protected static $client;
    protected static $googleClientId;
    protected static $googleClientSecret;
    protected static $tokenEndpoint = 'https://oauth2.googleapis.com/token';

    /**
     * Initialize the helper with Google API credentials
     */
    protected static function init()
    {
        if (!self::$client) {
            self::$client = new Client();
            self::$googleClientId = config('services.google.client_id');
            self::$googleClientSecret = config('services.google.client_secret');
        }
    }

    /**
     * Refresh tokens for all business accounts
     * 
     * @return array Statistics about the refresh operation
     */
    public static function refreshBusinessTokens()
    {
        self::init();

        // Fetch business accounts with a refresh token
        $businessAccounts = BusinessAccount::whereNotNull('business_refresh_token')->get();

        $stats = [
            'total' => $businessAccounts->count(),
            'success' => 0,
            'failed' => 0
        ];

        if ($businessAccounts->isEmpty()) {
            Log::info('No business accounts found with refresh tokens to refresh.');
            return $stats;
        }

        foreach ($businessAccounts as $account) {
            try {
                $refreshed = self::refreshSingleBusinessToken($account);
                $stats[$refreshed ? 'success' : 'failed']++;
            } catch (\Exception $e) {
                Log::error('Error refreshing business token', [
                    'business_account_id' => $account->id,
                    'error' => $e->getMessage()
                ]);
                $stats['failed']++;
            }
        }

        Log::info('Business token refresh completed', $stats);
        return $stats;
    }

    /**
     * Refresh tokens for all users with Google login
     * 
     * @return array Statistics about the refresh operation
     */
    public static function refreshUserTokens()
    {
        Log::info('coming in user refresh token');
        self::init();

        // Fetch users with a refresh token
        $users = User::whereNotNull('refresh_token')->get();

        $stats = [
            'total' => $users->count(),
            'success' => 0,
            'failed' => 0
        ];

        if ($users->isEmpty()) {
            Log::info('No users found with refresh tokens to refresh.');
            return $stats;
        }

        foreach ($users as $user) {
            try {
                $refreshed = self::refreshSingleUserToken($user);
                $stats[$refreshed ? 'success' : 'failed']++;
            } catch (\Exception $e) {
                Log::error('Error refreshing user token', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                $stats['failed']++;
            }
        }

        Log::info('User token refresh completed', $stats);
        return $stats;
    }

    /**
     * Refresh a single business account token
     * 
     * @param BusinessAccount $account
     * @return bool Success status
     */
    public static function refreshSingleBusinessToken(BusinessAccount $account)
    {
        self::init();

        if (empty($account->business_refresh_token)) {
            Log::warning('Cannot refresh business token: no refresh token', ['business_account_id' => $account->id]);
            return false;
        }

        try {
            $response = self::$client->post(self::$tokenEndpoint, [
                'form_params' => [
                    'client_id' => self::$googleClientId,
                    'client_secret' => self::$googleClientSecret,
                    'refresh_token' => $account->business_refresh_token,
                    'grant_type' => 'refresh_token',
                ],
            ]);

            $data = json_decode($response->getBody(), true);
            if (!isset($data['access_token'])) {
                Log::error('Google API did not return an access token for business account', [
                    'business_account_id' => $account->id,
                    'response' => $data
                ]);
                return false;
            }

            $account->update([
                'business_google_token' => $data['access_token'],
                'business_refresh_token' => $data['refresh_token'] ?? $account->business_refresh_token,
                'token_expires_at' => now()->addSeconds($data['expires_in'] ?? 3600),
                //'token_expires_at' => now()->addSeconds(300)
            ]);

            Log::info('Google tokens refreshed successfully for business account', [
                'business_account_id' => $account->id,
                'user_id' => $account->user_id,
                'expires_in' => $data['expires_in'] ?? 3600
            ]);

            return true;
        } catch (RequestException $e) {
            $response = $e->hasResponse() ? $e->getResponse() : null;
            $statusCode = $response ? $response->getStatusCode() : 'unknown';
            $responseBody = $response ? (string) $response->getBody() : 'no response body';

            Log::error('Failed to refresh Google token for business account', [
                'business_account_id' => $account->id,
                'status_code' => $statusCode,
                'response' => $responseBody,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Refresh a single user token
     * 
     * @param User $user
     * @return bool Success status
     */
    public static function refreshSingleUserToken(User $user)
    {
        self::init();

        if (empty($user->refresh_token)) {
            Log::warning('Cannot refresh user token: no refresh token', ['user_id' => $user->id]);
            return false;
        }

        try {
            $response = self::$client->post(self::$tokenEndpoint, [
                'form_params' => [
                    'client_id' => self::$googleClientId,
                    'client_secret' => self::$googleClientSecret,
                    'refresh_token' => $user->refresh_token,
                    'grant_type' => 'refresh_token',
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if (!isset($data['access_token'])) {
                Log::error('Google API did not return an access token for user', [
                    'user_id' => $user->id,
                    'response' => $data
                ]);
                return false;
            }

            $user->update([
                'google_token' => $data['access_token'],
                // Only update refresh token if a new one was provided
                'refresh_token' => $data['refresh_token'] ?? $user->refresh_token,
                'token_expires_at' => now()->addSeconds($data['expires_in'] ?? 3600),
            ]);

            Log::info('Google tokens refreshed successfully for user', [
                'user_id' => $user->id,
                'expires_in' => $data['expires_in'] ?? 3600
            ]);

            return true;
        } catch (RequestException $e) {
            $response = $e->hasResponse() ? $e->getResponse() : null;
            $statusCode = $response ? $response->getStatusCode() : 'unknown';
            $responseBody = $response ? (string) $response->getBody() : 'no response body';

            Log::error('Failed to refresh Google token for user', [
                'user_id' => $user->id,
                'status_code' => $statusCode,
                'response' => $responseBody,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
