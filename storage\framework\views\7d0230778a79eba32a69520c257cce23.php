<?php $__env->startSection('title', 'Email Templates'); ?>
<?php $__env->startSection('page-title', 'Email Templates'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .template-card {
        transition: all 0.2s ease;
    }
    .template-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    .status-badge {
        @apply px-2 py-1 text-xs font-medium rounded-full;
    }
    .status-active { @apply bg-green-100 text-green-800; }
    .status-inactive { @apply bg-red-100 text-red-800; }
    .category-badge {
        @apply px-2 py-1 text-xs font-medium rounded;
    }
    .category-system { @apply bg-blue-100 text-blue-800; }
    .category-custom { @apply bg-purple-100 text-purple-800; }
    .type-badge {
        @apply px-2 py-1 text-xs font-medium rounded bg-gray-100 text-gray-800;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Email Templates</h1>
            <p class="text-gray-600">Manage system and custom email templates</p>
        </div>
        <?php if(auth('admin')->user()->hasPermission('email_templates.create')): ?>
            <a href="<?php echo e(route('admin.email-templates.create')); ?>" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <i class="fas fa-plus mr-2"></i>Create Template
            </a>
        <?php endif; ?>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filters</h3>
        </div>
        <div class="p-6">
            <form method="GET" action="<?php echo e(route('admin.email-templates.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Category Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select name="category" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category); ?>" <?php echo e(request('category') == $category ? 'selected' : ''); ?>>
                                    <?php echo e(ucfirst($category)); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select name="type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">All Types</option>
                            <?php $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($type); ?>" <?php echo e(request('type') == $type ? 'selected' : ''); ?>>
                                    <?php echo e(ucfirst(str_replace('_', ' ', $type))); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">All Status</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                        </select>
                    </div>

                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                               placeholder="Search templates..."
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <i class="fas fa-search mr-2"></i>Apply Filters
                    </button>
                    <a href="<?php echo e(route('admin.email-templates.index')); ?>" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        <i class="fas fa-times mr-2"></i>Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="template-card bg-white rounded-lg shadow overflow-hidden">
                <!-- Template Header -->
                <div class="p-4 sm:p-6 border-b border-gray-200">
                    <div class="flex items-start justify-between mb-3">
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 truncate pr-2"><?php echo e($template->name); ?></h3>
                        <div class="flex flex-col sm:flex-row items-end sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 flex-shrink-0">
                            <?php if($template->is_default): ?>
                                <span class="px-2 py-1 text-xs font-medium rounded bg-yellow-100 text-yellow-800">
                                    Default
                                </span>
                            <?php endif; ?>
                            <span class="status-badge <?php echo e($template->is_active ? 'status-active' : 'status-inactive'); ?>">
                                <?php echo e($template->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2 mb-3">
                        <span class="category-badge <?php echo e($template->category === 'system' ? 'category-system' : 'category-custom'); ?>">
                            <?php echo e(ucfirst($template->category)); ?>

                        </span>
                        <span class="type-badge">
                            <?php echo e(ucfirst(str_replace('_', ' ', $template->type))); ?>

                        </span>
                    </div>

                    <?php if($template->description): ?>
                        <p class="text-sm text-gray-600 line-clamp-2"><?php echo e($template->description); ?></p>
                    <?php endif; ?>
                </div>

                <!-- Template Content Preview -->
                <div class="p-4 sm:p-6">
                    <div class="mb-4">
                        <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Subject</label>
                        <p class="text-sm text-gray-900 truncate"><?php echo e($template->subject); ?></p>
                    </div>

                    <div class="mb-4">
                        <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Variables</label>
                        <?php if($template->available_variables && count($template->available_variables) > 0): ?>
                            <div class="flex flex-wrap gap-1">
                                <?php $__currentLoopData = array_slice($template->available_variables, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                        <?php echo e($variable); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if(count($template->available_variables) > 3): ?>
                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                        +<?php echo e(count($template->available_variables) - 3); ?> more
                                    </span>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-xs text-gray-500">No variables</p>
                        <?php endif; ?>
                    </div>

                    <div class="text-xs text-gray-500">
                        <p>Created: <?php echo e($template->created_at->format('M j, Y')); ?></p>
                        <?php if($template->creator): ?>
                            <p>By: <?php echo e($template->creator->name); ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Template Actions -->
                <div class="px-4 sm:px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0">
                        <div class="flex items-center space-x-3">
                            <a href="<?php echo e(route('admin.email-templates.show', $template)); ?>"
                               class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                <i class="fas fa-eye mr-1"></i><span class="hidden sm:inline">View</span>
                            </a>

                            <a href="<?php echo e(route('admin.email-templates.preview', $template)); ?>?format=html"
                               target="_blank"
                               class="text-green-600 hover:text-green-900 text-sm font-medium">
                                <i class="fas fa-external-link-alt mr-1"></i><span class="hidden sm:inline">Preview</span>
                            </a>
                        </div>

                        <div class="flex items-center space-x-3">
                            <?php if(auth('admin')->user()->hasPermission('email_templates.edit')): ?>
                                <a href="<?php echo e(route('admin.email-templates.edit', $template)); ?>"
                                   class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                    <i class="fas fa-edit"></i><span class="hidden sm:inline ml-1">Edit</span>
                                </a>
                            <?php endif; ?>

                            <?php if(auth('admin')->user()->hasPermission('email_templates.delete') && $template->category !== 'system'): ?>
                                <form method="POST" action="<?php echo e(route('admin.email-templates.destroy', $template)); ?>"
                                      class="inline-block"
                                      onsubmit="return confirm('Are you sure you want to delete this template?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                        <i class="fas fa-trash"></i><span class="hidden sm:inline ml-1">Delete</span>
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full">
                <div class="text-center py-12">
                    <i class="fas fa-envelope text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                    <p class="text-gray-600 mb-4">Get started by creating your first email template.</p>
                    <?php if(auth('admin')->user()->hasPermission('email_templates.create')): ?>
                        <a href="<?php echo e(route('admin.email-templates.create')); ?>" 
                           class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i>Create Template
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if($templates->hasPages()): ?>
        <div class="mt-6">
            <?php echo e($templates->links()); ?>

        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/email-templates/index.blade.php ENDPATH**/ ?>