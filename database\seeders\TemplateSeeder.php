<?php

namespace Database\Seeders;

use App\Models\Template;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        // These templates will be used as default templates for new businesses
        $defaultTemplates = [
            [
                'title' => 'Default Template',
                'description' => 'This is the system default template that cannot be deleted. It will be used when no other templates match.',
                'template_text' => 'Generate a professional response to a {{rating}}-star review posted on {{reviewDate}} that states: {{reviewText}}. The response should be from {{businessName}} and should thank the reviewer for their feedback regardless of sentiment. Acknowledge their input, express appreciation for their time, and provide {{businessContact}} for any further assistance. Keep the response balanced and concise (3-4 sentences).',
                'ratings' => 5,
                'sentiment' => 'ANY',
                'length' => 'ANY',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Thank You Response',
                'description' => 'A warm response for positive reviews',
                'template_text' => 'Generate a warm, friendly response to a positive {{rating}}-star review posted by {{reviewerFirstName}} on {{reviewDate}} that states: "{{reviewText}}". The response should be from {{businessName}} and should express genuine gratitude, acknowledge specific positive points mentioned in the review, and encourage the customer to return. Keep the response concise (2-3 sentences) and authentic.',
                'ratings' => 4,
                'sentiment' => 'POSITIVE',
                'length' => 'ANY',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Apology Response',
                'description' => 'A sincere apology for negative experiences',
                'template_text' => 'Create a sincere apology response to {{reviewerFirstName}} who gave a {{rating}}-star rating on {{reviewDate}} with the review: "{{reviewText}}". The response should acknowledge the issues raised, express genuine regret without being defensive, and offer a solution by providing {{businessContact}} for direct communication. The tone should be professional and focused on resolution. Keep the response under 4 sentences.',
                'ratings' => 2,
                'sentiment' => 'NEGATIVE',
                'length' => 'ANY',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Neutral Response',
                'description' => 'A balanced response for mixed reviews',
                'template_text' => 'Compose a balanced response to {{reviewerFirstName}} who gave a {{rating}}-star rating on {{reviewDate}} stating: "{{reviewText}}". The response should acknowledge both positive and negative aspects of their feedback, thank them for their input, and mention that {{businessName}} values their opinion for continuous improvement. The response should be neither overly apologetic nor dismissive. Keep it concise (3 sentences maximum).',
                'ratings' => 3,
                'sentiment' => 'NEUTRAL',
                'length' => 'ANY',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Detailed Appreciation',
                'description' => 'A comprehensive thank you for detailed positive reviews',
                'template_text' => 'Generate a comprehensive appreciation response to {{reviewerFirstName}} who took the time to write a detailed positive {{rating}}-star review on {{reviewDate}} stating: "{{reviewText}}". The response should be from {{businessName}}, express specific gratitude for the detailed feedback, acknowledge particular points mentioned in the review, explain how this feedback motivates the team, and warmly invite the customer to return. The tone should be enthusiastic yet professional (4-5 sentences).',
                'ratings' => 5,
                'sentiment' => 'POSITIVE',
                'length' => 'LONG',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Detailed Apology',
                'description' => 'A comprehensive apology for detailed negative reviews',
                'template_text' => 'Create a comprehensive, empathetic apology to {{reviewerFirstName}} who shared a detailed negative {{rating}}-star review on {{reviewDate}} stating: "{{reviewText}}". The response should acknowledge each specific issue raised, express sincere regret on behalf of {{businessName}}, outline steps that will be taken to address the problems, and provide {{businessContact}} with an invitation to discuss resolution directly. The tone should be genuinely apologetic, professional, and solution-oriented (5-6 sentences).',
                'ratings' => 1,
                'sentiment' => 'NEGATIVE',
                'length' => 'LONG',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];

        // Insert templates without business_id as global templates
        DB::table('templates')->insert($defaultTemplates);
    }
}
