const business_id = document.getElementById("business_id")
const businessContact = document.getElementById("business_contact")
const businessName = document.getElementById("business_name")
const replyTone = document.getElementById("autoReplyTone")
const reviewIcon = document.getElementById("reviewIcon")
let replyBtns = document.querySelectorAll(".reply-btn")
let generateReply = document.querySelectorAll(".generateReply")
let postReply = document.querySelectorAll(".postReply")
let closeButton = document.querySelectorAll('.close-reply-modal');
const settingsBtn = document.getElementById("settingsTabBtn")

function showToast(message, type = 'success', duration = 3500, containerId = 'reviewsActionToast') {
        const toast = document.createElement('div');
        toast.className = `mb-2 px-4 py-2 rounded shadow-lg text-white font-semibold transition-all toast-${type}`;
        toast.style.background = type === 'success' ? '#22c55e' : (type === 'error' ? '#ef4444' : '#6366f1');
        toast.style.opacity = 0.95;
        toast.innerHTML = message;
        const container = document.getElementById(containerId);
        if (container) {
            container.appendChild(toast);
            setTimeout(() => {
                toast.style.opacity = 0;
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }
    }

    function insertAtCursor(textarea, text) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const before = textarea.value.substring(0, start);
        const after = textarea.value.substring(end);
        textarea.value = before + text + after;
        textarea.selectionStart = textarea.selectionEnd = start + text.length;
    }

    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    function removeDuplicateTokens(textarea, token) {
        const regex = new RegExp(`(${escapeRegExp(token)})(\\s*\\1)+`, 'g');
        textarea.value = textarea.value.replace(regex, '$1 ');
    }

    async function improveText(reviewId, mainId)
    {
        const textarea = document.getElementById(mainId+'-'+reviewId);
            const originalText = textarea.value;
            const response = await fetch('/improve-text', {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ text: originalText })
            });

            const data = await response.json();
            textarea.value = data.improvedText;
    }

document.addEventListener('DOMContentLoaded', function () {
    const settingsBtn = document.getElementById('settingsTabBtn');
    const saveSettingsBtn = document.getElementById("saveSettingsBtn")
    // const exportSettingsBtn = document.getElementById("exportSettingsBtn")
    const exportReviewBtn = document.getElementById("exportReviewBtn")
    const exportReviewAsPdf = document.getElementById("exportReviewAsPdf")
    const showOrderFilter = document.getElementById('showOrderFilter');
    const sortOrderCls = document.querySelectorAll('.sortOrderCls')

    if(settingsBtn)
    {
        settingsBtn.addEventListener('click', function () {
            const businessId = this.getAttribute("data-business-id");
            if (businessId) {
                fetchSettings(businessId);
            }
        });
    }


    if(exportReviewBtn)
    {
        exportReviewBtn.addEventListener("click", () => {
            exportReviews(businessName);
        })
    }

    if(exportReviewAsPdf)
    {
        exportReviewAsPdf.addEventListener("click", () => {
            exportReviewPDF(businessName);
        })
    }
    
    if(saveSettingsBtn)
    {
        saveSettingsBtn.addEventListener('click', function () {
            saveSettings();
        });
    }

    if(showOrderFilter)
    {
        showOrderFilter.addEventListener('change', function () {
            const order = this.value;
            const businessId = this.getAttribute("data-business");

            initializeReviewsFilter(businessId, null, order, null);
            document.getElementById('loadMoreBtnFilter').style.display = 'block';
            localStorage.setItem('data-page', 2)
        });
    }

    function fetchSettings(businessId) {
        fetch('/get-settings/' + businessId)
            .then(response => response.json())
            .then(data => {
                if (data.success === true) {
                    document.getElementById("aiProvider").value = data.data.ai_provider;
                    document.getElementById("aiModel").value = data.data.model_version;
                    document.getElementById("responseStyle").value = data.data.response_style;
                    document.getElementById("responseLength").value = data.data.response_length;
                    
                    // Trigger display updates for sliders
                    const responseStyleEvent = new Event('input');
                    const responseLengthEvent = new Event('input');
                    document.getElementById("responseStyle").dispatchEvent(responseStyleEvent);
                    document.getElementById("responseLength").dispatchEvent(responseLengthEvent);
                    document.getElementById("signOffText").value = data.data.custom_signoff_text;
                    document.getElementById("aiLanguage").value = data.data.response_language;
                    document.getElementById("customInstructions").value = data.data.custom_instruction;
                    document.getElementById("timezone").value = data.data.timezone;
                    document.getElementById("reviewCheckingEnabled").checked = data.data.auto_check_reviews;
                    document.getElementById("reviewCheckingInterval").value = data.data.check_interval_minutes;
                    document.getElementById("autoReplyEnabled").checked = data.data.auto_reply;
                    //document.getElementById("autoReplyConditionsRatingMin").value = data.data.auto_reply_rating_min;
                    //document.getElementById("autoReplyConditionsLength").value = data.data.review_length_filter;
                    //document.getElementById("autoReplyTimingMode").value = data.data.reply_timing;
                }
            })
            .catch(error => {
                console.error('Error fetching settings:', error);
            });
    }

    // Validation functions for settings
    function validateSettings() {
        let isValid = true;
        const errors = [];
        
        // Clear all previous validation errors
        clearValidationErrors();
        
        // 1. AI Configuration Validation
        const aiConfigFields = [
            {
                id: 'aiProvider',
                name: 'AI Provider',
                type: 'select',
                required: true
            },
            {
                id: 'aiModel',
                name: 'Model Version',
                type: 'select',
                required: true
            },
            {
                id: 'responseStyle',
                name: 'Response Style',
                type: 'range',
                required: true,
                min: 0,
                max: 100
            },
            {
                id: 'responseLength',
                name: 'Response Length',
                type: 'range',
                required: true,
                min: 0,
                max: 100
            },
            {
                id: 'signOffText',
                name: 'Custom Sign-off Text',
                type: 'input',
                required: true,
                minLength: 3,
                maxLength: 100
            },
            {
                id: 'aiLanguage',
                name: 'Response Language',
                type: 'select',
                required: true
            },
            {
                id: 'customInstructions',
                name: 'Custom Instructions',
                type: 'textarea',
                required: false,
                maxLength: 500
            }
        ];

        // 2. Business Configuration Validation
        const businessConfigFields = [
            {
                id: 'timezone',
                name: 'Business Timezone',
                type: 'select',
                required: true
            }
        ];

        // 3. Review Checking Configuration Validation
        const reviewCheckingFields = [
            {
                id: 'reviewCheckingInterval',
                name: 'Review Check Interval',
                type: 'select',
                required: false, // Only required if auto check is enabled
                dependsOn: 'reviewCheckingEnabled'
            }
        ];

        // 4. Auto Reply Configuration Validation
        const autoReplyFields = [
            {
                id: 'autoReplyConditionsLength',
                name: 'Review Length Filter',
                type: 'select',
                required: false, // Only required if auto reply is enabled
                dependsOn: 'autoReplyEnabled'
            },
            {
                id: 'autoReplyTimingMode',
                name: 'Auto Reply Timing',
                type: 'select',
                required: false, // Only required if auto reply is enabled
                dependsOn: 'autoReplyEnabled'
            }
        ];

        // Combine all field groups
        const allFields = [...aiConfigFields, ...businessConfigFields, ...reviewCheckingFields, ...autoReplyFields];

        // Validate each field
        allFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!element) return;

            let value = element.value ? element.value.trim() : '';
            let shouldValidate = true;

            // Check if field depends on another field being enabled
            if (field.dependsOn) {
                const dependentElement = document.getElementById(field.dependsOn);
                shouldValidate = dependentElement && dependentElement.checked;
            }

                         if (shouldValidate) {
                 let fieldValid = true;
                 
                 // Required field validation
                 if (field.required && (!value || value === '')) {
                     isValid = false;
                     fieldValid = false;
                     errors.push(field.name);
                     addFieldError(element, `${field.name} is required`);
                 }
                 // Min/Max length validation for text inputs
                 else if (value && field.minLength && value.length < field.minLength) {
                     isValid = false;
                     fieldValid = false;
                     errors.push(field.name);
                     addFieldError(element, `${field.name} must be at least ${field.minLength} characters`);
                 }
                 else if (value && field.maxLength && value.length > field.maxLength) {
                     isValid = false;
                     fieldValid = false;
                     errors.push(field.name);
                     addFieldError(element, `${field.name} must not exceed ${field.maxLength} characters`);
                 }
                 // Range validation for sliders
                 else if (field.type === 'range' && value) {
                     const numValue = parseInt(value);
                     if (field.min !== undefined && numValue < field.min) {
                         isValid = false;
                         fieldValid = false;
                         errors.push(field.name);
                         addFieldError(element, `${field.name} must be at least ${field.min}`);
                     }
                     if (field.max !== undefined && numValue > field.max) {
                         isValid = false;
                         fieldValid = false;
                         errors.push(field.name);
                         addFieldError(element, `${field.name} must not exceed ${field.max}`);
                     }
                 }
                 
                 // Show validation status for the field
                 showFieldValidationStatus(element, fieldValid);
             }
        });

                 // 5. Custom validation for auto-reply settings (Template Loop Validation)
         const autoReplyEnabled = document.getElementById('autoReplyEnabled');
         if (autoReplyEnabled && autoReplyEnabled.checked) {
             let hasActiveRating = false;
             let templateErrors = [];
             let validTemplateCount = 0;
             let totalActiveRatings = 0;

             // Loop through each rating block (1-5 stars)
             for (let rating = 1; rating <= 5; rating++) {
                 const block = document.querySelector(`.auto-reply-block[data-rating="${rating}"]`);
                 if (!block) continue;

                 const toggleName = `toggleOption-${rating}`;
                 const templateName = `templateSelect-${rating}`;
                 
                 const checkbox = block.querySelector(`input[name="${toggleName}"]`);
                 const templateSelect = block.querySelector(`select[name="${templateName}"]`);

                 if (checkbox && checkbox.checked) {
                     hasActiveRating = true;
                     totalActiveRatings++;
                     
                     // Validate template selection for this rating
                     if (!templateSelect || !templateSelect.value || templateSelect.value === '') {
                         isValid = false;
                         const errorMsg = `Template is required for ${rating}-star reviews`;
                         templateErrors.push(`${rating}-star: ${errorMsg}`);
                         errors.push(`${rating}-star template`);
                         
                         if (templateSelect) {
                             addFieldError(templateSelect, errorMsg);
                             // Add visual indicator to the block
                             block.classList.add('has-error');
                         }
                     } else {
                         validTemplateCount++;
                         // Show success indicator for valid template selection
                         block.classList.remove('has-error');
                         block.classList.add('validation-passed');
                     }
                 } else {
                     // Remove any previous error styling for disabled ratings
                     block.classList.remove('has-error', 'validation-passed');
                 }
             }

             // Check if at least one rating is enabled
             if (!hasActiveRating) {
                 isValid = false;
                 errors.push('At least one rating must be enabled for auto-reply');
                 showGlobalError('Please enable at least one rating for auto-reply or disable the auto-reply feature');
             } else {
                 // Show template validation summary
                 if (templateErrors.length > 0) {
                     const templateSummary = `Template Validation (${validTemplateCount}/${totalActiveRatings} valid): ${templateErrors.join('; ')}`;
                     showGlobalError(templateSummary);
                 } else {
                     // All templates are valid
                     console.log(`✓ All ${totalActiveRatings} active rating templates are properly configured`);
                 }
             }
         }

        // 6. Advanced Business Logic Validations
        
        // Check if review checking interval is valid when auto-check is enabled
        const reviewCheckingEnabled = document.getElementById('reviewCheckingEnabled');
        const reviewCheckingInterval = document.getElementById('reviewCheckingInterval');
        if (reviewCheckingEnabled && reviewCheckingEnabled.checked) {
            if (!reviewCheckingInterval || !reviewCheckingInterval.value) {
                isValid = false;
                errors.push('Review Check Interval');
                if (reviewCheckingInterval) {
                    addFieldError(reviewCheckingInterval, 'Please select a check interval when auto-checking is enabled');
                }
            }
        }

        // Validate auto-reply timing configuration
        if (autoReplyEnabled && autoReplyEnabled.checked) {
            const timingMode = document.getElementById('autoReplyTimingMode');
            const lengthFilter = document.getElementById('autoReplyConditionsLength');
            
            if (!timingMode || !timingMode.value) {
                isValid = false;
                errors.push('Auto Reply Timing');
                if (timingMode) {
                    addFieldError(timingMode, 'Please select when to send auto-replies');
                }
            }

            if (!lengthFilter || !lengthFilter.value) {
                isValid = false;
                errors.push('Review Length Filter');
                if (lengthFilter) {
                    addFieldError(lengthFilter, 'Please select which review lengths to auto-reply to');
                }
            }
        }

                 // 7. Generate validation summary
         const validationSummary = generateValidationSummary(isValid, errors);
         
         return { isValid, errors, summary: validationSummary };
    }

    // Generate comprehensive validation summary
    function generateValidationSummary(isValid, errors) {
        const summary = {
            totalFields: 0,
            validFields: 0,
            invalidFields: 0,
            categories: {
                aiConfig: { total: 0, valid: 0, errors: [] },
                businessConfig: { total: 0, valid: 0, errors: [] },
                reviewChecking: { total: 0, valid: 0, errors: [] },
                autoReply: { total: 0, valid: 0, errors: [] },
                templates: { total: 0, valid: 0, errors: [] }
            }
        };

        // Count AI Configuration fields
        const aiFields = ['aiProvider', 'aiModel', 'responseStyle', 'responseLength', 'signOffText', 'aiLanguage', 'customInstructions'];
        aiFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                summary.categories.aiConfig.total++;
                summary.totalFields++;
                if (!element.classList.contains('error-border')) {
                    summary.categories.aiConfig.valid++;
                    summary.validFields++;
                } else {
                    summary.invalidFields++;
                    const fieldName = fieldId.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    summary.categories.aiConfig.errors.push(fieldName);
                }
            }
        });

        // Count Business Configuration fields
        const businessFields = ['timezone'];
        businessFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                summary.categories.businessConfig.total++;
                summary.totalFields++;
                if (!element.classList.contains('error-border')) {
                    summary.categories.businessConfig.valid++;
                    summary.validFields++;
                } else {
                    summary.invalidFields++;
                    summary.categories.businessConfig.errors.push('Business Timezone');
                }
            }
        });

        // Count Review Checking fields
        const reviewCheckingEnabled = document.getElementById('reviewCheckingEnabled');
        if (reviewCheckingEnabled && reviewCheckingEnabled.checked) {
            const intervalField = document.getElementById('reviewCheckingInterval');
            if (intervalField) {
                summary.categories.reviewChecking.total++;
                summary.totalFields++;
                if (!intervalField.classList.contains('error-border')) {
                    summary.categories.reviewChecking.valid++;
                    summary.validFields++;
                } else {
                    summary.invalidFields++;
                    summary.categories.reviewChecking.errors.push('Review Check Interval');
                }
            }
        }

        // Count Auto Reply fields
        const autoReplyEnabled = document.getElementById('autoReplyEnabled');
        if (autoReplyEnabled && autoReplyEnabled.checked) {
            const autoReplyFields = ['autoReplyConditionsLength', 'autoReplyTimingMode'];
            autoReplyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    summary.categories.autoReply.total++;
                    summary.totalFields++;
                    if (!element.classList.contains('error-border')) {
                        summary.categories.autoReply.valid++;
                        summary.validFields++;
                    } else {
                        summary.invalidFields++;
                        const fieldName = fieldId.includes('Length') ? 'Review Length Filter' : 'Auto Reply Timing';
                        summary.categories.autoReply.errors.push(fieldName);
                    }
                }
            });

            // Count template validations
            for (let rating = 1; rating <= 5; rating++) {
                const block = document.querySelector(`.auto-reply-block[data-rating="${rating}"]`);
                if (block) {
                    const checkbox = block.querySelector(`input[name="toggleOption-${rating}"]`);
                    if (checkbox && checkbox.checked) {
                        summary.categories.templates.total++;
                        summary.totalFields++;
                        if (!block.classList.contains('has-error')) {
                            summary.categories.templates.valid++;
                            summary.validFields++;
                        } else {
                            summary.invalidFields++;
                            summary.categories.templates.errors.push(`${rating}-star template`);
                        }
                    }
                }
            }
        }

        return summary;
    }

    // Helper function to add field error
    function addFieldError(element, message) {
        // Add error styling
        element.classList.add('error-border');
        
        // For template selects in auto-reply blocks, also highlight the entire block
        const autoReplyBlock = element.closest('.auto-reply-block');
        if (autoReplyBlock) {
            autoReplyBlock.classList.add('has-error', 'template-error');
        }
        
        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-red-500 text-xs mt-1';
        errorDiv.textContent = message;
        element.parentNode.appendChild(errorDiv);

        // Auto-remove field error after 10 seconds
        const fieldErrorTimer = setTimeout(() => {
            if (errorDiv && errorDiv.parentNode) {
                console.log(`⏰ Auto-removing field error: ${message}`);
                errorDiv.remove();
            }
            element.classList.remove('error-border');
            if (autoReplyBlock) {
                autoReplyBlock.classList.remove('has-error', 'template-error');
            }
        }, 10000); // 10 seconds

        // Store timer for cleanup
        validationTimers.push(fieldErrorTimer);
    }

    // Store validation timers
    let validationTimers = [];

    // Helper function to clear all validation errors
    function clearValidationErrors() {
        // Clear all existing validation timers
        const timerCount = validationTimers.length;
        validationTimers.forEach(timer => clearTimeout(timer));
        validationTimers = [];

        // Remove error styles and messages
        const errorElements = document.querySelectorAll('.field-error').length;
        const borderElements = document.querySelectorAll('.error-border').length;
        
        document.querySelectorAll('.field-error').forEach(el => el.remove());
        document.querySelectorAll('.error-border').forEach(el => el.classList.remove('error-border'));
        document.querySelectorAll('.has-error').forEach(el => el.classList.remove('has-error'));
        document.querySelectorAll('.template-error').forEach(el => el.classList.remove('template-error'));
        document.querySelectorAll('.validation-passed').forEach(el => el.classList.remove('validation-passed'));
        document.querySelectorAll('.validation-failed').forEach(el => el.classList.remove('validation-failed'));
        document.querySelectorAll('.global-error, .global-success').forEach(el => el.remove());

        if (timerCount > 0 || errorElements > 0 || borderElements > 0) {
            console.log(`🧹 Cleared ${timerCount} timers, ${errorElements} error messages, ${borderElements} error borders`);
        }
    }

    // Helper function to show field validation status
    function showFieldValidationStatus(element, isValid) {
        const label = element.parentNode.querySelector('label');
        if (label) {
            label.classList.remove('validation-passed', 'validation-failed');
            label.classList.add(isValid ? 'validation-passed' : 'validation-failed');
        }
    }

    function showGlobalError(message) {
        // Remove existing global error
        const existingError = document.querySelector('.global-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Create new global error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'global-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span>${message}</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-xs opacity-70">
                        <span id="globalErrorCountdown">10s</span>
                    </div>
                    <button class="ml-2 text-red-500 hover:text-red-700 font-bold text-lg leading-none" onclick="this.parentElement.parentElement.parentElement.remove();" title="Close">
                        ×
                    </button>
                </div>
            </div>
        `;
        
        // Insert at the top of settings content
        const settingContent = document.getElementById('settingContent');
        if (settingContent) {
            settingContent.insertBefore(errorDiv, settingContent.firstChild);
            
            // Scroll to top to show error
            errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Add countdown timer
        const countdownElement = errorDiv.querySelector('#globalErrorCountdown');
        let countdown = 10;
        
        const countdownTimer = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = `${countdown}s`;
            }
            if (countdown <= 0) {
                clearInterval(countdownTimer);
            }
        }, 1000);

        // Auto-remove global error after 10 seconds
        const globalErrorTimer = setTimeout(() => {
            if (errorDiv && errorDiv.parentNode) {
                console.log('⏰ Auto-removing global validation errors after 10 seconds');
                // Add fade-out animation
                errorDiv.style.transition = 'opacity 0.5s ease-out';
                errorDiv.style.opacity = '0';
                setTimeout(() => {
                    errorDiv.remove();
                }, 500);
            }
            clearInterval(countdownTimer);
        }, 10000); // 10 seconds

        // Store timer for cleanup
        validationTimers.push(globalErrorTimer);
        validationTimers.push(countdownTimer);
    }

    function showSuccessMessage(message) {
        // Remove existing messages
        document.querySelectorAll('.global-error, .global-success').forEach(el => el.remove());
        
        // Create success message
        const successDiv = document.createElement('div');
        successDiv.className = 'global-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4';
        successDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-xs opacity-70">
                        <span id="globalSuccessCountdown">5s</span>
                    </div>
                    <button class="ml-2 text-green-500 hover:text-green-700 font-bold text-lg leading-none" onclick="this.parentElement.parentElement.parentElement.remove();" title="Close">
                        ×
                    </button>
                </div>
            </div>
        `;
        
        // Insert at the top of settings content
        const settingContent = document.getElementById('settingContent');
        if (settingContent) {
            settingContent.insertBefore(successDiv, settingContent.firstChild);
            
            // Add countdown timer for success (5 seconds)
            const countdownElement = successDiv.querySelector('#globalSuccessCountdown');
            let countdown = 5;
            
            const countdownTimer = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = `${countdown}s`;
                }
                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                }
            }, 1000);

            // Auto-remove success message after 5 seconds
            const successTimer = setTimeout(() => {
                if (successDiv && successDiv.parentNode) {
                    console.log('⏰ Auto-removing success message after 5 seconds');
                    // Add fade-out animation
                    successDiv.style.transition = 'opacity 0.5s ease-out';
                    successDiv.style.opacity = '0';
                    setTimeout(() => {
                        successDiv.remove();
                    }, 500);
                }
                clearInterval(countdownTimer);
            }, 5000); // 5 seconds for success messages

            // Store timers for cleanup
            validationTimers.push(successTimer);
            validationTimers.push(countdownTimer);
        }
    }

    function saveSettings() {
        // Run validation first (always show fresh errors for 10 seconds)
        const validation = validateSettings();
        
        if (!validation.isValid) {
            console.warn(`❌ Validation failed: ${validation.errors.length} error(s) found. Showing for 10 seconds...`);
            // Show detailed validation summary
            const summary = validation.summary;
            let errorMessage = `Validation Failed (${summary.validFields}/${summary.totalFields} fields valid):\n`;
            
            // Add category-specific errors
            Object.keys(summary.categories).forEach(category => {
                const cat = summary.categories[category];
                if (cat.errors.length > 0) {
                    const categoryName = category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    errorMessage += `\n• ${categoryName}: ${cat.errors.join(', ')}`;
                }
            });
            
            showGlobalError(errorMessage);
            showToast(`Validation failed: ${summary.invalidFields} field(s) need attention`, 'error');
            
            // Log detailed summary for debugging
            console.warn('Validation Summary:', summary);
            console.warn('Specific Errors:', validation.errors);
            
            return false;
        }

        // Show loading state
        const saveBtn = document.getElementById('saveSettingsBtn');
        const originalText = saveBtn ? saveBtn.innerHTML : '';
        if (saveBtn) {
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Saving...';
            saveBtn.disabled = true;
        }

        let autoReplySettings = [];

        document.querySelectorAll(".auto-reply-block").forEach((block) => {
            const rating = parseInt(block.getAttribute("data-rating"));
            const toggleName = `toggleOption-${rating}`;
            const templateName = `templateSelect-${rating}`;

            const status = block.querySelector(`input[name="${toggleName}"]:checked`)?.value || "off";
            const template = block.querySelector(`select[name="${templateName}"]`)?.value || "";

            autoReplySettings.push({
                reply_rating: rating,
                status: status,
                template: template
            });
        });
        
        let data = {
            // business_id: document.getElementById("business_id").value,
            business_id: document.getElementById("settingsTabBtn")?.getAttribute("data-business-id") || '',
            aiProvider: document.getElementById("aiProvider")?.value || '',
            aiModel: document.getElementById("aiModel")?.value || '',
            responseStyle: document.getElementById("responseStyle")?.value || '',
            responseLength: document.getElementById("responseLength")?.value || '',
            signOffText: document.getElementById("signOffText")?.value || '',
            aiLanguage: document.getElementById("aiLanguage")?.value || '',
            customInstructions: document.getElementById("customInstructions")?.value || '',
            timezone: document.getElementById("timezone")?.value || '',
            reviewCheckingEnabled: document.getElementById("reviewCheckingEnabled")?.checked || false,
            reviewCheckingInterval: document.getElementById("reviewCheckingInterval")?.value || '',
            autoReplyEnabled: document.getElementById("autoReplyEnabled")?.checked || false,
            autoReplyConditionsLength: document.getElementById("autoReplyConditionsLength")?.value || '',
            autoReplyTimingMode: document.getElementById("autoReplyTimingMode")?.value || '',
            auto_reply_settings: autoReplySettings,
            from_auto_reply_date: document.getElementById("fromAutoReplyDate")?.value || ''
        }
        
        fetch('/manage-settings', {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            // Restore button state
            if (saveBtn) {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }

            if (data.success === true) {
                document.getElementById("settingCompletion")?.classList.remove('hidden');
                
                // Show success with validation summary
                const summary = validation.summary;
                const successMessage = `Settings saved successfully! ✓ All ${summary.totalFields} fields validated and saved.`;
                showSuccessMessage(successMessage);
                showToast(`Settings saved! (${summary.totalFields} fields processed)`, 'success');
                
                console.log('✓ Settings saved successfully with validation summary:', summary);
            } else {
                document.getElementById("settingCompletion")?.classList.add('hidden');
                showGlobalError(data.message || 'Failed to save settings. Please try again.');
                showToast('Failed to save settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            
            // Restore button state
            if (saveBtn) {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }
            
            showGlobalError('An error occurred while saving settings. Please try again.');
            showToast('An error occurred while saving settings', 'error');
        });
    }

    function exportReviews(businessName) {
        fetch('/export-reviews/'+businessName.value)
            .then(response => response.blob())
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = businessName.value+"_reviews_export.csv";
                a.click();
                window.URL.revokeObjectURL(url);
            });
    }

    function exportReviewPDF(businessName)
    {
        fetch('/export-reviews-pdf/'+businessName.value)
            .then(response => response.blob())
            // .then(response => response.json())
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = businessName.value+"_reviews_export.pdf";
                a.click();
                window.URL.revokeObjectURL(url);
            })
    }


});

replyBtns.forEach(btn => {
    btn.addEventListener("click", () => {
        let reviewId = btn.getAttribute("data-review-id");
        let modal = document.getElementById(`replyModal-${reviewId}`);
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    });
});

function bindReviewEvents() {
    // Pagination dropdown
    const perPageSelect = document.getElementById('per_page');
    if (perPageSelect) {
        // Remove any existing event listeners
        perPageSelect.removeEventListener('change', loadReviews);
        // Add the event listener
        perPageSelect.addEventListener('change', loadReviews);
        console.log('Attached change event to per_page dropdown');
    } else {
        //console.error('per_page dropdown not found');
    }

    // Reply button
    document.querySelectorAll(".reply-btn").forEach(btn => {
        btn.onclick = function () {
            let reviewId = btn.getAttribute("data-review-id");
            let modal = document.getElementById(`replyModal-${reviewId}`);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        };
    });

    closeButton.forEach(btn => {
        btn.addEventListener("click", () => {
            let reviewId = btn.getAttribute("data-review-id");
            let formDiv = document.getElementById("replyForm-" + reviewId);
            formDiv.style.display = "none";
        });
    })

    // Post reply button
    document.querySelectorAll(".postReply").forEach(btn => {
        btn.onclick = async function (e) {
            const clickedBtn = e.currentTarget;

            let id = btn.getAttribute("data-review-id");
            let reviewId = btn.getAttribute("data-reviewId");
            let reviewLocation = btn.getAttribute("data-location");
            let reviewComment = document.getElementById("replyPreview-" + id).innerText;


            let data = {
                reviewId: reviewId,
                locationName: 'locations/' + reviewLocation,
                comment: reviewComment
            };

            let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            await fetch('/replay-to-review', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('replyModal').classList.remove('hidden');
                    //document.getElementById("replyPreview-" + id).innerHTML = ''
                    clickedBtn.disabled = true;

                    // Add event listener to the 'Got it!' button to reload page after confirmation
                    const gotItBtn = document.querySelector('#replyModal button');
                    gotItBtn.addEventListener('click', function () {
                        document.getElementById('replyModal').classList.add('hidden');
                        window.location.reload();
                    }, { once: true }); // Use once:true to prevent multiple event listeners
                })
                .catch(error => {
                    console.error('Error:', error);
                    // alert('Failed to post reply.');
                });
        };
    });


    // Edit reply button
    document.querySelectorAll(".edit-reply-btn").forEach(btn => {
        btn.onclick = function () {
            console.log("here")
            let replyId = btn.getAttribute("data-reply-id");
            let editForm = document.getElementById("editReplyForm-" + replyId);
            if (editForm) {
                editForm.style.display = "block";
            }
        };
    });

    // Cancel edit button
    document.querySelectorAll(".cancel-reply-edit").forEach(btn => {
        btn.onclick = function () {
            let replyId = btn.getAttribute("data-reply-id");
            let editForm = document.getElementById("editReplyForm-" + replyId);
            if (editForm) {
                editForm.style.display = "none";
            }
        };
    });

    // Save edit button
    document.querySelectorAll(".save-reply-edit").forEach(btn => {
        btn.onclick = async function () {
            let replyId = btn.getAttribute("data-reply-id");
            let reviewId = btn.getAttribute("data-review-id");
            let editedText = document.getElementById("editReplyText-" + replyId).value;

            try {
                const response = await fetch("/update-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        replyId: replyId,
                        reviewId: reviewId,
                        comment: editedText
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // Update the reply text in the UI
                    let editForm = document.getElementById("editReplyForm-" + replyId);
                    if (editForm) {
                        editForm.style.display = "none";
                        // Find the paragraph with the reply text and update it
                        editForm.previousElementSibling.textContent = editedText;
                    }
                } else {
                    alert("Failed to update reply: " + (data.message || "Unknown error"));
                }
            } catch (error) {
                console.error("Error updating reply:", error);
                alert("Failed to update reply. Please try again.");
            }
        };
    });

}

// --- Filter Function ---
function initializeReviewsFilter(businessId, buttonElement, order, filters) {
    const dateRange = document.getElementById('dateRangeFilter').value;
    const type = document.getElementById('typeFilter').value;

    // Get all selected ratings
    let selectedRatings = [];

    // If a button element was clicked and it has a rating
    if (buttonElement && buttonElement.dataset && buttonElement.dataset.rating) {
        // Toggle the active class on the clicked button
        if (buttonElement.classList.contains('activated')) {
            buttonElement.classList.remove('bg-indigo-600', 'text-white', 'activated');
        } else {
            buttonElement.classList.add('bg-indigo-600', 'text-white', 'activated');
        }
    }

    // Collect all activated ratings
    document.querySelectorAll('.rating-btn.activated').forEach(btn => {
        selectedRatings.push(btn.dataset.rating);
    });

    let filterData = {};
    if(filters){
        filterData = filters;
    }else{
        filterData = {
            date_range: dateRange,
            ratings: selectedRatings.length > 0 ? selectedRatings : null,
            type: type,
            per_page: 10
        };
    }

    fetch(`/businesses/${businessId}/reviews/${order}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(filterData)
    })
        .then(response => response.json())
        .then(data => {

            const reviewListContainer = document.getElementById('reviewList');
            reviewListContainer.innerHTML = '';

            document.getElementById('reviewDynamicCount').textContent = data.reviews.length;

            data.reviews.forEach(review => {
                const reviewCard = createReviewCardFromJSON(review, data.templates, data.locationName);
                reviewListContainer.appendChild(reviewCard);

                const loadMoreButtonDiv = document.getElementById('loadMoreDataButtonFilter');
                if (loadMoreButtonDiv) {
                    loadMoreButtonDiv.classList.remove('hidden');
                }
            });

            // If no reviews found, show a message
            if (data.reviews.length === 0) {
                const noReviewsMessage = document.createElement('div');
                noReviewsMessage.className = 'flex flex-col items-center justify-center gap-2 max-w-[500px] min-h-[300px] text-center p-8 mx-auto';
                noReviewsMessage.innerHTML = `
                <div class="flex gap-4 item-center text-2xl">
                    <i class="icon-star text-yellow-400"></i>
                    <i class="text-indigo-600">No REVIEWS</i> found
                    <i class="icon-star text-yellow-400"></i>
                </div>
                <p>No reviews match your filter criteria.</p>
            `;
                reviewListContainer.appendChild(noReviewsMessage);
                document.getElementById('loadMoreDataButtonFilter').classList.add('hidden');
            }

            // Rebind event listeners to the new review cards
            initReviewEventListeners();
        })
        .catch(error => {
            console.error('Error fetching reviews:', error);
        });
}

function loadReviews() {
    const perPage = document.getElementById('per_page').value;
    const selectElement = document.getElementById('per_page');
    //const businessId = document.querySelector('[name="business_id"]')?.value;
    const businessId = selectElement.getAttribute('data-business');

    if (!businessId) {
        console.error('Business ID not found');
        return;
    }

    const url = `/business/reviews/paginate?per_page=${perPage}&business_id=${businessId}`;
    loadReviewsFromUrl(url);
}

// Function to load reviews from a specific URL (for pagination links)
function loadReviewsFromUrl(url) {
    // Show loading indicator
    const reviewsContainer = document.getElementById('reviews-container');
    reviewsContainer.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin text-indigo-600 text-2xl"></i></div>';

    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);

            if (data.error) {
                console.error('Error from server:', data.error);
                reviewsContainer.innerHTML = `<div class="text-center p-4 text-red-500">Error: ${data.error}</div>`;
                return;
            }

            // Update the reviews container
            reviewsContainer.innerHTML = data.html;

            // Update pagination links
            document.getElementById('pagination-links').innerHTML = data.links;

            // Update review count
            document.getElementById('current-count').textContent = data.current;
            document.getElementById('total-count').textContent = data.total;

            console.log('Reviews updated successfully');
            document.getElementById('reviews-container').innerHTML = data.html;
            // Reinitialize any event listeners for the new content
            initReviewEventListeners();
        })
        .catch(error => {
            console.error('Error loading reviews:', error);
            reviewsContainer.innerHTML = '<div class="text-center p-4 text-red-500">Error loading reviews. Please try again.</div>';
        });
}

// Function to analyze sentiment for all reviews on the page
function analyzeSentimentForAllReviews() {
    document.querySelectorAll('.reviewList').forEach(reviewCard => {
        const reviewId = reviewCard.getAttribute('data-review-id');

        if (!reviewId) return;

        const sentimentDisplay = document.getElementById("sentimentDisplay-" + reviewId);
        if (!sentimentDisplay) return;

        // Check if the sentiment badge already exists with valid content
        const existingSentimentBadge = sentimentDisplay.querySelector('.sentiment-badge');
        if (existingSentimentBadge &&
            (existingSentimentBadge.textContent.toLowerCase() === 'positive' ||
                existingSentimentBadge.textContent.toLowerCase() === 'negative' ||
                existingSentimentBadge.textContent.toLowerCase() === 'neutral')) {
            // Sentiment already displayed from database, no need to analyze again
            return;
        }

        // Show analyzing indicator
        sentimentDisplay.innerHTML = '<span class="analyzing">Analyzing...</span>';

        // Get review comment and star rating
        const reviewComment = document.getElementById("reviewComment-" + reviewId)?.innerText || '';
        const starRating = parseInt(document.getElementById("starRating-" + reviewId)?.value || 3);

        // Encode the review text for the URL
        const encodedReviewText = encodeURIComponent(reviewComment);

        // Call the sentiment generation endpoint with reviewId to check database first
        fetch(`/generate-sentiment/${starRating}/${encodedReviewText}/${reviewId}`)
            .then(response => response.text())
            .then(sentiment => {
                // Display the sentiment badge
                const sentimentClass = sentiment.toLowerCase();
                sentimentDisplay.innerHTML = `<span class="sentiment-badge ${sentimentClass}">${sentiment}</span>`;

                // Also update any sentiment select dropdowns if they exist
                const sentimentSelect = document.getElementById("sentimentSelect-" + reviewId);
                if (sentimentSelect) {
                    sentimentSelect.value = sentimentClass;
                }
            })
            .catch(error => {
                console.error("Error getting sentiment:", error);
                // Fallback to star rating-based sentiment
                let sentiment = 'Neutral';
                if (starRating >= 4) {
                    sentiment = 'Positive';
                } else if (starRating <= 2) {
                    sentiment = 'Negative';
                }

                const sentimentClass = sentiment.toLowerCase();
                sentimentDisplay.innerHTML = `<span class="sentiment-badge ${sentimentClass}">${sentiment}</span>`;
            });
    });
}

// Function to reinitialize event listeners for the new content
function initReviewEventListeners() {
    // Reinitialize reply buttons
    replyBtns = document.querySelectorAll(".reply-btn");
    generateReply = document.querySelectorAll(".generateReply");
    
    closeButton = document.querySelectorAll('.close-reply-modal');
    const improveReply = document.querySelectorAll(".improveReply");
    const postReplyManual = document.querySelectorAll(".postReplyManual");
    const editPostReplyManual = document.querySelectorAll(".editPostReplyManual");
    const smartPostReply = document.querySelectorAll(".smartPostReply");
    const editGenerateReply = document.querySelectorAll(".editGenerateReply");
    const generateReplyAI = document.querySelectorAll(".generateReplyAI");
    const modifyPromptData = document.querySelectorAll(".modifyPromptData")
    const editGenerateReplyAI = document.querySelectorAll('.editGenerateReplyAI')
    const editModifyPromptData = document.querySelectorAll('.editModifyPromptData')
    const improveEditReply = document.querySelectorAll(".improveEditReply");
    
    // Analyze sentiment for all reviews
    analyzeSentimentForAllReviews();

    // Get edit/delete reply buttons
    const editReplyBtns = document.querySelectorAll(".edit-reply-trigger");
    const deleteReplyBtns = document.querySelectorAll(".delete-reply-btn");
    const saveReplyEditBtns = document.querySelectorAll(".editSmartPostReply");
    const cancelReplyEditBtns = document.querySelectorAll(".cancel-reply-edit");
    const manualReplyText = document.querySelectorAll(".manualReplyText")
    const editManualReplyText = document.querySelectorAll(".editManualReplyText")

    // Re-add event listeners
    replyBtns.forEach(btn => {
        btn.addEventListener("click", (async) => {
            let checkTemMember = document.getElementById("checkTeamMember").value
            if(!checkTemMember)
            {
                validateSubscriptionAndUpgrade('monthly_reply_limit').then(validation => {
                    if (validation.allowed) {
                        return true;
                    }
                });
            }        
            let reviewId = btn.getAttribute("data-review-id");
            let businessId = btn.getAttribute("data-business-id");
            let modal = document.getElementById(`replyModal-${reviewId}`);
            modal.classList.remove('hidden');

            const manualReplyTab = document.getElementById('manualReplyTab-'+reviewId);
            const generateWithAITab = document.getElementById('generateWithAITab-'+reviewId);
            const manualReplyContent = document.getElementById('manualReplyContent-'+reviewId);
            const generateWithAIContent = document.getElementById('generateWithAIContent-'+reviewId);

            manualReplyTab.addEventListener('click', function() {
                // Update tab buttons
                manualReplyTab.classList.add('active-tab');
                manualReplyTab.classList.add('bg-white');
                manualReplyTab.classList.add('shadow');
                generateWithAITab.classList.remove('active-tab');
                generateWithAITab.classList.remove('bg-white');
                generateWithAITab.classList.remove('shadow');
    
                // Show/hide content
                manualReplyContent.classList.remove('hidden');
                generateWithAIContent.classList.add('hidden');
            });
    
            generateWithAITab.addEventListener('click', function() {
                // Update tab buttons
                generateWithAITab.classList.add('active-tab');
                generateWithAITab.classList.add('bg-white');
                generateWithAITab.classList.add('shadow');
                manualReplyTab.classList.remove('active-tab');
                manualReplyTab.classList.remove('bg-white');
                manualReplyTab.classList.remove('shadow');
    
                // Show/hide content
                generateWithAIContent.classList.remove('hidden');
                manualReplyContent.classList.add('hidden');
            });


            // document.body.style.overflow = 'hidden';
            if (modal) {

                fetch(`/get-default-template`, {
                    method: 'POST',
                    body: JSON.stringify({ business_id: businessId }),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        let reviewerFirstName = btn.getAttribute('data-reviewer-name');
                        let rating = btn.getAttribute('data-rating');
                        let reviewDate = btn.getAttribute('data-review-date');
                        let reviewText = btn.getAttribute('data-review-text') === 'null' ? '' : btn.getAttribute('data-review-text');

                        // Fill template with actual values
                        let filledTemplate = data.data.template_text
                            .replace(/{{reviewerFirstName}}/g, reviewerFirstName)
                            .replace(/{{rating}}/g, rating)
                            .replace(/{{reviewDate}}/g, reviewDate)
                            .replace(/{{reviewText}}/g, reviewText)
                            .replace(/{{businessName}}/g, businessName.value)
                            .replace(/{{businessContact}}/g, businessContact.value);


                        document.getElementById(`specificInstructions-${reviewId}`).value = filledTemplate;
                    })
                    .catch(error => {
                        console.error('Error fetching review:', error);
                    });

                
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        });
    });

    manualReplyText.forEach(btn => {
        btn.addEventListener("keyup", () => {
            let reviewId = btn.getAttribute('data-review-id');
            const button = document.getElementById("generateReplyButton-" + reviewId);

            if (btn.value.trim() !== '') {
                button.disabled = false;
            } else {
                button.disabled = true;
            }
        })
    })

    closeButton.forEach(btn => {
        btn.addEventListener("click", () => {
            let reviewId = btn.getAttribute("data-review-id");
            document.getElementById('manualPostReplyButton-' + reviewId).style.display = 'block';
            document.getElementById("generatedReplyId-" + reviewId).classList.remove('hidden');
            document.getElementById("buttonBundles-" + reviewId).classList.add('hidden');
            document.getElementById("postReplyButton-" + reviewId).classList.add('hidden');
            document.getElementById("responseGenerate-" + reviewId).classList.remove('hidden');
            document.getElementById("emotions-" + reviewId).classList.remove('hidden');
            document.getElementById("tagButtons").classList.remove('hidden');
            document.getElementById("templateOptionDiv-" + reviewId).classList.remove('hidden')
            document.getElementById("manualReplyText-" + reviewId).value = ''
            document.getElementById("generateButton-" + reviewId).disabled = false
            let formDiv = document.getElementById("replyModal-" + reviewId);
            formDiv.classList.add('hidden');

        });
    });

    // Initialize generateReply buttons
    generateReply.forEach(btn => {
        btn.onclick = async function (e) {
            const clickedBtn = e.currentTarget;
            clickedBtn.disabled = true;
            // Add loading indicator to button
            const originalButtonText = clickedBtn.innerHTML;
            clickedBtn.innerHTML = '<span class="flex items-center"><i class="fas fa-spinner fa-spin"></i></span>';

            let reviewId = btn.getAttribute("data-review");
            let ai_model = btn.getAttribute("data-ai-model");
            let selectedReviewComment = document.getElementById("manualReplyText-" + reviewId).innerText;
            let selectedReviewerName = btn.getAttribute("data-reviewerName");
            let starRating = btn.getAttribute("data-rating");
            let selectedSentiment = 'positive'
            let signOffText = document.getElementById("signOffText").value
            try {
               
                // Call the sentiment generation endpoint with reviewId to check database first
                try {
                    if (starRating >= 4) {
                        selectedSentiment = "positive";
                    } else if (starRating <= 2) {
                        selectedSentiment = "negative";
                    } else {
                        selectedSentiment = "neutral";
                    }
                } catch (error) {
                    console.error("Error getting sentiment:", error);
                    // Fallback to star rating-based sentiment
                    if (starRating >= 4) {
                        selectedSentiment = "positive";
                    } else if (starRating <= 2) {
                        selectedSentiment = "negative";
                    } else {
                        selectedSentiment = "neutral";
                    }
                }

                // Prepare the prompt for generating the reply
                const prompt = `Generate a reply to this Google review: ${selectedReviewComment}. Use the following settings:
                - Reviewer Name: ${selectedReviewerName}
                - Sentiment: ${selectedSentiment}

IMPORTANT: The reply MUST match the selected sentiment (${selectedSentiment}). If sentiment is 'negative', the reply should acknowledge problems and offer solutions. If 'positive', be appreciative and upbeat. If 'neutral', be balanced and factual.

Always reply in JSON format with the following structure: { "message": { "content": "<reply>" } } and also include ${signOffText} value in this reply`;

                // Generate the reply
                const response = await fetch("/generate-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: ai_model,
                        // custom_instruction: custom_instruction,
                    })
                });
                const data = await response.json();
                
                if (data.choices && data.choices.length > 0) {
                    let jsonResponse = JSON.parse(data.choices[0].message.content);
                    let generatedReply = jsonResponse.message.content.trim();

                    let previewElement = document.getElementById("preview-" + reviewId);
                    if (previewElement) {
                        previewElement.textContent = generatedReply;
                    } else {
                        document.getElementById("manualReplyText-" + reviewId).value = generatedReply;
                        clickedBtn.disabled = true;
                    }
                } else {
                    document.getElementById("manualReplyText-" + reviewId).value = "We couldn't generate a reply. Kindly try again later.";
                }
            } catch (error) {
                console.error("Error in reply generation process:", error);
            } finally {
                // Re-enable the button and restore original text
                // clickedBtn.disabled = false;
                clickedBtn.innerHTML = originalButtonText;
            }
        };
    });

    // Initialize generateReply buttons
    generateReplyAI.forEach(btn => {
        btn.onclick = async function (e) {
            const clickedBtn = e.currentTarget;
            clickedBtn.disabled = true;
            // Add loading indicator to button
            const originalButtonText = clickedBtn.innerHTML;
            clickedBtn.innerHTML = '<span class="flex items-center"><i class="fas fa-spinner fa-spin mr-2"></i> Generating...</span>';

            let reviewId = btn.getAttribute("data-review");
            let ai_model = btn.getAttribute("data-ai-model");
            let custom_instruction = btn.getAttribute("data-custom-instruction");
            let selectedTemplate = document.getElementById("templateSelect-" + reviewId).value;
            let selectedTone = document.getElementById("toneSelect-" + reviewId).value;
            let selectedLanguage = document.getElementById("languageSelect-" + reviewId).value;
            let selectedSentiment = document.getElementById("sentimentSelect-" + reviewId).value;
            let selectedReviewComment = document.getElementById("specificInstructions-" + reviewId).value;
            let selectedReviewerName = btn.getAttribute("data-reviewerName");
            let starRating = btn.getAttribute("data-rating");
            let signOffText = document.getElementById("signOffText").value

            try {
                // If sentiment is not explicitly selected, get it from database or auto-detect it
                if (!selectedSentiment || selectedSentiment === "auto") {
                    // Check if we already have a sentiment badge with valid content
                    const sentimentDisplay = document.getElementById("sentimentDisplay-" + reviewId);
                    const existingSentimentBadge = sentimentDisplay?.querySelector('.sentiment-badge');

                    if (existingSentimentBadge &&
                        (existingSentimentBadge.textContent.toLowerCase() === 'positive' ||
                            existingSentimentBadge.textContent.toLowerCase() === 'negative' ||
                            existingSentimentBadge.textContent.toLowerCase() === 'neutral')) {
                        // Use existing sentiment from the badge (which was loaded from the database)
                        selectedSentiment = existingSentimentBadge.textContent.toLowerCase();
                    } else {
                        // Show loading indicator for sentiment
                        if (sentimentDisplay) {
                            sentimentDisplay.innerHTML = '<span class="analyzing">Analyzing...</span>';
                        }

                        // Encode the review text for the URL
                        const encodedReviewText = encodeURIComponent(selectedReviewComment);

                        // Call the sentiment generation endpoint with reviewId to check database first
                        try {
                            if (starRating >= 4) {
                                selectedSentiment = "positive";
                            } else if (starRating <= 2) {
                                selectedSentiment = "negative";
                            } else {
                                selectedSentiment = "neutral";
                            }
                        } catch (error) {
                            console.error("Error getting sentiment:", error);
                            // Fallback to star rating-based sentiment
                            if (starRating >= 4) {
                                selectedSentiment = "positive";
                            } else if (starRating <= 2) {
                                selectedSentiment = "negative";
                            } else {
                                selectedSentiment = "neutral";
                            }
                        }
                    }
                }

                // Prepare the prompt for generating the reply
                const prompt = `Generate a reply to this Google review: ${selectedReviewComment}. Use the following settings:
- Reviewer Name: ${selectedReviewerName}
- Template: ${selectedTemplate}
- Tone: ${selectedTone}
- Language: ${selectedLanguage}
- Sentiment: ${selectedSentiment}

IMPORTANT: The reply MUST match the selected sentiment (${selectedSentiment}). If sentiment is 'negative', the reply should acknowledge problems and offer solutions. If 'positive', be appreciative and upbeat. If 'neutral', be balanced and factual, and always show only first name of user.

Always reply in JSON format with the following structure: { "message": { "content": "<reply>" } } and also include ${signOffText} value in this reply`;

                // Generate the reply
                const response = await fetch("/generate-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: ai_model,
                        custom_instruction: custom_instruction,
                    })
                });
                const data = await response.json();
                if (data.choices && data.choices.length > 0) {
                    let jsonResponse = JSON.parse(data.choices[0].message.content);
                    let generatedReply = jsonResponse.message.content.trim();

                    let previewElement = document.getElementById("preview-" + reviewId);
                    if (previewElement) {
                        previewElement.textContent = generatedReply;
                    } else {
                        document.getElementById("specificInstructions-" + reviewId).value = generatedReply;
                        document.getElementById("generatedReplyId-" + reviewId).classList.remove('hidden');
                        document.getElementById("buttonBundles-" + reviewId).classList.remove('hidden');
                        document.getElementById("postReplyButton-" + reviewId).classList.remove('hidden');
                        document.getElementById("responseGenerate-" + reviewId).classList.add('hidden');
                        document.getElementById("emotions-" + reviewId).classList.add('hidden');
                        document.getElementById("tagButtons").classList.add('hidden');
                        document.getElementById("templateOptionDiv-" + reviewId).classList.add('hidden')
                        clickedBtn.disabled = true;
                    }
                } else {
                    console.error("No reply generated:", data);
                    document.getElementById("specificInstructions-" + reviewId).value = "We couldn't generate a reply. Kindly try again later.";
                }
            } catch (error) {
                console.error("Error in reply generation process:", error);
            } finally {
                // Re-enable the button and restore original text
                // clickedBtn.disabled = false;
                clickedBtn.innerHTML = originalButtonText;
            }
        };
    });

    modifyPromptData.forEach(btn => {
        btn.onclick = async function (e) {
            let reviewId = btn.getAttribute("data-review");
            document.getElementById("specificInstructions-" + reviewId).classList.add('pt-9');
            document.getElementById("generatedReplyId-" + reviewId).classList.remove('hidden');
            document.getElementById("buttonBundles-" + reviewId).classList.add('hidden');
            document.getElementById("postReplyButton-" + reviewId).classList.add('hidden');
            document.getElementById("responseGenerate-" + reviewId).classList.remove('hidden');
            document.getElementById("emotions-" + reviewId).classList.remove('hidden');
            document.getElementById("tagButtons").classList.remove('hidden');
            document.getElementById("templateOptionDiv-" + reviewId).classList.remove('hidden')
            document.getElementById("generateButton-" + reviewId).disabled = false
        }
    })

    // Template selection for edit reply form
    document.querySelectorAll('[id^="templateSelect-"]').forEach(select => {
        select.addEventListener('change', function () {
            let templateId = this.value;
            let replyId = this.getAttribute('data-reply-id');
            let reviewId = this.getAttribute('data-review-id');
            if (!templateId) return;

            fetch(`/template/${templateId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Get review-specific dynamic data from hidden elements or data attributes
                        let reviewerFirstName = this.getAttribute('data-reviewer-name');
                        let rating = this.getAttribute('data-rating');
                        let reviewDate = this.getAttribute('data-review-date');
                        // let reviewText = this.getAttribute('data-review-text');
                        let reviewText = this.getAttribute('data-review-text') === 'null' ? '' : this.getAttribute('data-review-text');
                        
                        // Fill template with actual values
                        let filledTemplate = data.template.template_text
                            .replace(/{{reviewerFirstName}}/g, reviewerFirstName)
                            .replace(/{{rating}}/g, rating)
                            .replace(/{{reviewDate}}/g, reviewDate)
                            .replace(/{{reviewText}}/g, reviewText)
                            .replace(/{{businessName}}/g, businessName.value)
                            .replace(/{{businessContact}}/g, businessContact.value);

                        // Set the populated text
                        document.getElementById('specificInstructions-' + reviewId).value = filledTemplate;
                    } else {
                        console.error('Template not found');
                    }
                })
                .catch(error => {
                    console.error('Error fetching template:', error);
                });
        });
    });

    improveReply.forEach(btn => {
        btn.onclick = async function (e) {
            let reviewId = btn.getAttribute('data-review')
            improveText(reviewId,'manualReplyText')
        }
    })

    improveEditReply.forEach(btn => {
        btn.onclick = async function (e) {
            let reviewId = btn.getAttribute('data-review')
            improveText(reviewId,'editManualReplyText')
        }
    })

    // Post reply button
    postReplyManual.forEach(btn => {
        btn.onclick = async function (e) {
            const clickedBtn = e.currentTarget;

            let id = btn.getAttribute("data-review-id");
            let reviewId = btn.getAttribute("data-reviewId");
            let reviewLocation = btn.getAttribute("data-location");
            let reviewComment = document.getElementById("manualReplyText-" + id).value;

            let data = {
                reviewId: reviewId,
                locationName:  reviewLocation,
                comment: reviewComment,
                reply_edit: false,
            };

            let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            await fetch('/replay-to-review', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    console.log(data, ' check data')
                        document.getElementById('replyModal-'+id).classList.add('hidden');
                        showToast('Reply Send Successfully', 'success',
                        3500,
                        ''
                    );
                    // window.location.reload();
                    initializeReviewsFilter(
                        document.getElementById("business_id").value,
                        null,
                        document.getElementById('showOrderFilter').value,
                        null
                    );
                })
                .catch(error => {
                    console.error('Error:', error);
                    // alert('Failed to post reply.');
                });
        };
    });

    smartPostReply.forEach(btn => {
        btn.onclick = async function (e) {
            const clickedBtn = e.currentTarget;

            let id = btn.getAttribute("data-review-id");
            let reviewId = btn.getAttribute("data-reviewId");
            let reviewLocation = btn.getAttribute("data-location");
            let reviewComment = document.getElementById("specificInstructions-" + id).value;
            let templateId = document.getElementById("templateSelect-" + id).value;

            let data = {
                reviewId: reviewId,
                locationName:  reviewLocation,
                comment: reviewComment,
                reply_edit: false,
                reply_template_id:templateId
            };

            let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            await fetch('/replay-to-review', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                        document.getElementById('replyModal-'+id).classList.add('hidden');
                        showToast('Reply Send Successfully', 'success',
                        1000,
                        ''
                    );
                    // window.location.reload();
                    initializeReviewsFilter(
                        document.getElementById("business_id").value,
                        null,
                        document.getElementById('showOrderFilter').value,
                        null
                    );
                })
                .catch(error => {
                    console.error('Error:', error);
                    // alert('Failed to post reply.');
                });
        };
    });

    document.addEventListener('click', function (e) {
    if (e.target && e.target.classList.contains('insert-token-button')) {
        const button = e.target;
        const reviewId = button.dataset.reviewId;
        const token = `{{${button.dataset.token}}}`;
        const textarea = document.getElementById(`specificInstructions-${reviewId}`);

        if (textarea && !textarea.value.includes(token)) {
            insertAtCursor(textarea, `${token} `);
            textarea.focus();
        }
    }
});

    // Edit reply button event listeners
    editReplyBtns.forEach(btn => {
        btn.addEventListener("click", () => {
            let replyId = btn.getAttribute("data-reply-id");
            let reviewId = btn.getAttribute("data-review-id");
            //let businessId = btn.getAttribute("data-business-id");
            let editForm = document.getElementById("editReplyModal-" + reviewId);

            const editManualReplyTab = document.getElementById('editManualReplyTab-'+reviewId);
            const editGenerateWithAITab = document.getElementById('editGenerateWithAITab-'+reviewId);
            const manualReplyContent = document.getElementById('editManualReplyContent-'+reviewId);
            const generateWithAIContent = document.getElementById('editGenerateWithAIContent-'+reviewId);
            const editManualReplyText = document.getElementById('editManualReplyText-'+reviewId);

            editManualReplyTab.addEventListener('click', function() {
                // Update tab buttons
                editManualReplyTab.classList.add('active-tab');
                editManualReplyTab.classList.add('bg-white');
                editManualReplyTab.classList.add('shadow');
                editGenerateWithAITab.classList.remove('active-tab');
                editGenerateWithAITab.classList.remove('bg-white');
                editGenerateWithAITab.classList.remove('shadow');
    
                // Show/hide content
                manualReplyContent.classList.remove('hidden');
                generateWithAIContent.classList.add('hidden');
            });
    
            editGenerateWithAITab.addEventListener('click', function() {
                // Update tab buttons
                editGenerateWithAITab.classList.add('active-tab');
                editGenerateWithAITab.classList.add('bg-white');
                editGenerateWithAITab.classList.add('shadow');
                editManualReplyTab.classList.remove('active-tab');
                editManualReplyTab.classList.remove('bg-white');
                editManualReplyTab.classList.remove('shadow');
    
                // Show/hide content
                generateWithAIContent.classList.remove('hidden');
                manualReplyContent.classList.add('hidden');
            });

            
            if (editManualReplyText.value.trim() !== '') {
                document.getElementById("editGenerateReplyButton-" + reviewId).classList.remove = "disabled:opacity-50 disabled:cursor-not-allowed"
                // button.disabled = false;
            } else {
                document.getElementById("editGenerateReplyButton-" + reviewId).classList.add = "disabled:opacity-50 disabled:cursor-not-allowed"
                // button.disabled = true;
            }

            if (editForm) {
                editForm.style.display = "flex";
                fetch(`/get-reply`, {
                    method: 'POST',
                    body: JSON.stringify({ reviewId: reviewId }),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        // document.getElementById(`editSpecificInstructions-${reviewId}`).value = data.data.comment;
                        document.getElementById(`editManualReplyText-${reviewId}`).value = data.data.comment;
                    })
                    .catch(error => {
                        console.error('Error fetching review:', error);
                    });
                    console.log(business_id.value, "this is business id")
                    fetch(`/get-default-template`, {
                        method: 'POST',
                        body: JSON.stringify({ business_id: business_id.value }),
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        let reviewerFirstName = btn.getAttribute('data-reviewer-name');
                        let rating = btn.getAttribute('data-rating');
                        let reviewDate = btn.getAttribute('data-review-date');
                        let reviewText = btn.getAttribute('data-review-text') === 'null' ? '' : btn.getAttribute('data-review-text');

                        // Fill template with actual values
                        let filledTemplate = data.data.template_text
                            .replace(/{{reviewerFirstName}}/g, reviewerFirstName)
                            .replace(/{{rating}}/g, rating)
                            .replace(/{{reviewDate}}/g, reviewDate)
                            .replace(/{{reviewText}}/g, reviewText)
                            .replace(/{{businessName}}/g, businessName.value)
                            .replace(/{{businessContact}}/g, businessContact.value);


                        //document.getElementById(`specificInstructions-${reviewId}`).value = filledTemplate;
                        document.getElementById(`editSpecificInstructions-${reviewId}`).value = filledTemplate;
                    })
                    .catch(error => {
                        console.error('Error fetching review:', error);
                    });
                
                editForm.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

        });
    });

    editManualReplyText.forEach(btn => {
        btn.addEventListener("keyup", () => {
            let reviewId = btn.getAttribute('data-review-id');
            const button = document.getElementById("editGenerateReplyButton-" + reviewId);

            if (btn.value.trim() !== '') {
                button.disabled = false;
            } else {
                button.disabled = true;
            }
        })
    })
    // Cancel edit button event listeners
    cancelReplyEditBtns.forEach(btn => {
        btn.addEventListener("click", () => {
            let replyId = btn.getAttribute("data-reply-id");
            let reviewId = btn.getAttribute("data-review-id");
            let editForm = document.getElementById("editReplyModal-" + reviewId);
            document.getElementById("editSpecificInstructions-" + reviewId).classList.add('pt-9');
            document.getElementById("editGeneratedReplyId-" + reviewId).classList.remove('hidden');
            document.getElementById("editButtonBundles-" + reviewId).classList.add('hidden');
            document.getElementById("editPostReplyButton-" + reviewId).classList.add('hidden');
            document.getElementById("editResponseGenerate-" + reviewId).classList.remove('hidden');
            document.getElementById("editEmotions-" + reviewId).classList.remove('hidden');
            document.getElementById("editTagButtons").classList.remove('hidden');
            document.getElementById("editGenerateButton-" + reviewId).classList.remove('hidden')
            document.getElementById("templateEditOptionDiv-" + reviewId).classList.remove('hidden');
            document.getElementById("editGenerateButton-" + reviewId).disabled = false
            if (editForm) {
                editForm.style.display = "none";
            }
        });
    });

    editPostReplyManual.forEach(btn => {
        btn.onclick = async function (e) {
                let id = btn.getAttribute("data-review-id");
                let reviewId = btn.getAttribute("data-reviewId");
                let replyId = btn.getAttribute("data-reply-id");
                let reviewLocation = btn.getAttribute("data-location");
                let reviewComment = document.getElementById("editManualReplyText-" + id).value;
            
                try {
                let sendData = {
                    review_id: id,
                    replyId:replyId,
                    reviewId: reviewId,
                    locationName:  reviewLocation,
                    comment: reviewComment,
                    reply_edit: true
                };
                const response = await fetch("/update-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(sendData)
                });

                const data = await response.json();
                if (data.success) {
                        document.getElementById('editReplyModal-'+id).classList.add('hidden');
                        showToast('Reply Updated Successfully', 'success',
                        1000,
                        '');
                    // window.location.reload();
                    initializeReviewsFilter(
                        document.getElementById("business_id").value,
                        null,
                        document.getElementById('showOrderFilter').value,
                        null
                    );
                } else {
                    alert("Failed to update reply: " + (data.message || "Unknown error"));
                }
            } catch (error) {
                console.error("Error updating reply:", error);
                alert("Failed to update reply. Please try again.");
            }
        };
    });

    // Template selection for edit reply form
    document.querySelectorAll('[id^="editTemplateSelect-"]').forEach(select => {
        select.addEventListener('change', function () {
            let templateId = this.value;
            let replyId = this.getAttribute('data-reply-id');
            let reviewId = this.getAttribute('data-review-id');
            if (!templateId) return;

            fetch(`/template/${templateId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Get review-specific dynamic data from hidden elements or data attributes
                        let reviewerFirstName = this.getAttribute('data-reviewer-name');
                        let rating = this.getAttribute('data-rating');
                        let reviewDate = this.getAttribute('data-review-date');
                        let reviewText = this.getAttribute('data-review-text') === 'null' ? '' : this.getAttribute('data-review-text');

                        // Fill template with actual values
                        let filledTemplate = data.template.template_text
                            .replace(/{{reviewerFirstName}}/g, reviewerFirstName)
                            .replace(/{{rating}}/g, rating)
                            .replace(/{{reviewDate}}/g, reviewDate)
                            .replace(/{{reviewText}}/g, reviewText)
                            .replace(/{{businessName}}/g, businessName.value)
                            .replace(/{{businessContact}}/g, businessContact.value);

                        // Set the populated text
                        document.getElementById('editSpecificInstructions-' + reviewId).value = filledTemplate;
                    } else {
                        console.error('Template not found');
                    }
                })
                .catch(error => {
                    console.error('Error fetching template:', error);
                });
        });
    });

    editGenerateReply.forEach(btn => {
        btn.onclick = async function (e) {
            const clickedBtn = e.currentTarget;
            clickedBtn.disabled = true;
            // Add loading indicator to button
            const originalButtonText = clickedBtn.innerHTML;
            clickedBtn.innerHTML = '<span class="flex items-center"><i class="fas fa-spinner fa-spin"></i></span>';

            let reviewId = btn.getAttribute("data-review");
            let ai_model = btn.getAttribute("data-ai-model");
            let selectedReviewComment = document.getElementById("editManualReplyText-" + reviewId).innerText;
            let selectedReviewerName = btn.getAttribute("data-reviewerName");
            let starRating = btn.getAttribute("data-rating");
            let selectedSentiment = 'positive'
            try {
               
                // Call the sentiment generation endpoint with reviewId to check database first
                try {
                    if (starRating >= 4) {
                        selectedSentiment = "positive";
                    } else if (starRating <= 2) {
                        selectedSentiment = "negative";
                    } else {
                        selectedSentiment = "neutral";
                    }
                } catch (error) {
                    console.error("Error getting sentiment:", error);
                    // Fallback to star rating-based sentiment
                    if (starRating >= 4) {
                        selectedSentiment = "positive";
                    } else if (starRating <= 2) {
                        selectedSentiment = "negative";
                    } else {
                        selectedSentiment = "neutral";
                    }
                }

                // Prepare the prompt for generating the reply
                const prompt = `Generate a reply to this Google review: ${selectedReviewComment}. Use the following settings:
                - Reviewer Name: ${selectedReviewerName}
                - Sentiment: ${selectedSentiment}

IMPORTANT: The reply MUST match the selected sentiment (${selectedSentiment}). If sentiment is 'negative', the reply should acknowledge problems and offer solutions. If 'positive', be appreciative and upbeat. If 'neutral', be balanced and factual.

Always reply in JSON format with the following structure: { "message": { "content": "<reply>" } } `;

                // Generate the reply
                const response = await fetch("/generate-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: ai_model,
                        // custom_instruction: custom_instruction,
                    })
                });
                const data = await response.json();
                
                if (data.choices && data.choices.length > 0) {
                    let jsonResponse = JSON.parse(data.choices[0].message.content);
                    let generatedReply = jsonResponse.message.content.trim();

                    let previewElement = document.getElementById("preview-" + reviewId);
                    if (previewElement) {
                        previewElement.textContent = generatedReply;
                    } else {
                        document.getElementById("editManualReplyText-" + reviewId).value = generatedReply;
                        clickedBtn.disabled = true;
                    }
                } else {
                    console.error("No reply generated:", data);
                    document.getElementById("editManualReplyText-" + reviewId).value = "We couldn't generate a reply. Kindly try again later.";
                }
            } catch (error) {
                console.error("Error in reply generation process:", error);
            } finally {
                // Re-enable the button and restore original text
                // clickedBtn.disabled = false;
                clickedBtn.innerHTML = originalButtonText;
            }
        };
    });

    // Generate reply button for edit form
    editGenerateReplyAI.forEach(btn => {
        btn.addEventListener('click', function () {
            let reviewId = this.getAttribute('data-review');
            let replyId = this.getAttribute('data-reply-id');
            let aiModel = this.getAttribute('data-ai-model') || 'gpt-3.5-turbo';
            let customInstruction = this.getAttribute('data-custom-instruction') || document.getElementById('editSpecificInstructions-' + reviewId).value;
            let selectedTemplate = document.getElementById("editTemplateSelect-" + reviewId).value;
            let selectedReviewComment = document.getElementById("editSpecificInstructions-" + reviewId).value;
            let selectedReviewerName = btn.getAttribute("data-reviewername");
            let starRating = document.getElementById("editStarRating-" + reviewId)?.value || 3;
            let signOffText = document.getElementById("signOffText").value
            
            // Get values from the form
            const tone = document.getElementById('editToneSelect-' + reviewId).value;
            const language = document.getElementById('editLanguageSelect-' + reviewId).value;
            const sentiment = document.getElementById('editSentimentSelect-' + reviewId).value;
            // Show loading state
            this.disabled = true;
            this.innerHTML = '<span class="flex items-center"><i class="fas fa-spinner fa-spin mr-1"></i> Generating...</span>';

            const prompt = `Generate a reply to this Google review: "${selectedReviewComment}". Use the following settings:
- Reviewer Name: ${selectedReviewerName}
- Template: ${selectedTemplate}
- Tone: ${tone}
- Language: ${language}
- Sentiment: ${sentiment}

IMPORTANT: The reply MUST match the selected sentiment (${sentiment}). If sentiment is 'negative', the reply should acknowledge problems and offer solutions. If 'positive', be appreciative and upbeat. If 'neutral', be balanced and factual, and always show only first name of user.

Always reply in JSON format with the following structure: { "message": { "content": "<reply>" } }  and also include ${signOffText} value in this reply`;

            // Call the AI to generate a reply
            fetch('/generate-reply', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    prompt: prompt,
                    review_id: reviewId,
                    tone: tone,
                    language: language,
                    sentiment: sentiment,
                    ai_model: aiModel,
                    custom_instruction: customInstruction
                })
            })
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    this.disabled = false;
                    this.innerHTML = '<span class="flex items-center"><i class="fas fa-magic mr-1"></i> Generate Reply</span>';
                    let jsonResponse = JSON.parse(data.choices[0].message.content);
                    let generatedReply = jsonResponse.message.content.trim();
                    document.getElementById('editSpecificInstructions-' + reviewId).value = generatedReply;
                    document.getElementById("editSpecificInstructions-" + reviewId).classList.add('pt-2');
                    document.getElementById("editGeneratedReplyId-" + reviewId).classList.remove('hidden');
                    document.getElementById("editButtonBundles-" + reviewId).classList.remove('hidden');
                    document.getElementById("editPostReplyButton-" + reviewId).classList.remove('hidden');
                    document.getElementById("editResponseGenerate-" + reviewId).classList.add('hidden');
                    document.getElementById("editEmotions-" + reviewId).classList.add('hidden');
                    document.getElementById("editTagButtons").classList.add('hidden');
                    document.getElementById("templateEditOptionDiv-" + reviewId).classList.add('hidden');
                })
                .catch(error => {
                    console.error('Error generating reply:', error);
                    this.disabled = false;
                    // this.innerHTML = '<span class="flex items-center"><i class="fas fa-magic mr-1"></i> Generate Reply</span>';
                    document.getElementById('editSpecificInstructions-' + reviewId).value = "We couldn't generate a reply. Kindly try again later.";
                });
        });
    });

    editModifyPromptData.forEach(btn => {
        btn.onclick = async function (e) {
            let reviewId = btn.getAttribute("data-review");
            document.getElementById("editSpecificInstructions-" + reviewId).classList.add('pt-9');
            document.getElementById("editGeneratedReplyId-" + reviewId).classList.remove('hidden');
            document.getElementById("editButtonBundles-" + reviewId).classList.add('hidden');
            document.getElementById("editPostReplyButton-" + reviewId).classList.add('hidden');
            document.getElementById("editResponseGenerate-" + reviewId).classList.remove('hidden');
            document.getElementById("editEmotions-" + reviewId).classList.remove('hidden');
            document.getElementById("editTagButtons").classList.remove('hidden');
            document.getElementById("editGenerateButton-" + reviewId).classList.remove('hidden')
            document.getElementById("templateEditOptionDiv-" + reviewId).classList.remove('hidden');
            document.getElementById("editGenerateButton-" + reviewId).disabled = false
        }
    })

    // Save edit button event listeners
    saveReplyEditBtns.forEach(btn => {
        btn.addEventListener("click", async () => {
            let replyId = btn.getAttribute("data-reply-id");
            let reviewId = btn.getAttribute("data-review-id");
            let editedText = document.getElementById("editSpecificInstructions-" + reviewId).value;
            let templateId = document.getElementById("editTemplateSelect-" + reviewId).value;
            try {
                const response = await fetch("/update-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        replyId: replyId,
                        reviewId: reviewId,
                        comment: editedText,
                        reply_template_id: templateId,
                        reply_edit: true
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // Update the reply text in the UI
                    let editForm = document.getElementById("editReplyModal-" + reviewId);
                    if (editForm) {
                        editForm.style.display = "none";
                        // Find the paragraph with the reply text and update it
                        editForm.previousElementSibling.value = editedText;
                        document.getElementById('editReplyModal-'+reviewId).classList.add('hidden');
                        showToast('Reply Send Successfully', 'success',
                        1000,
                        ''
                    );
                    // window.location.reload();
                    initializeReviewsFilter(
                        document.getElementById("business_id").value,
                        null,
                        document.getElementById('showOrderFilter').value,
                        null
                    );
                    }
                } else {
                    alert("Failed to update reply: " + (data.message || "Unknown error"));
                }
            } catch (error) {
                console.error("Error updating reply:", error);
                alert("Failed to update reply. Please try again.");
            }
        });
    });

    // Delete reply button event listeners
    deleteReplyBtns.forEach(btn => {
        btn.addEventListener("click", async () => {
            if (!confirm("Are you sure you want to delete this reply?")) {
                return;
            }

            let replyId = btn.getAttribute("data-reply-id");
            let reviewId = btn.getAttribute("data-review-id");

            try {
                const response = await fetch("/delete-reply", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        replyId: replyId,
                        reviewId: reviewId
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // Reload the page to reflect the deletion
                    // window.location.reload();
                    initializeReviewsFilter(
                        document.getElementById("business_id").value,
                        null,
                        document.getElementById('showOrderFilter').value,
                        null
                    );
                } else {
                    alert("Failed to delete reply: " + (data.message || "Unknown error"));
                }
            } catch (error) {
                console.error("Error deleting reply:", error);
                alert("Failed to delete reply. Please try again.");
            }
        });
    });

    document.addEventListener('click', function (e) {
    if (e.target && e.target.classList.contains('insert-token-button')) {
        const button = e.target;
        const reviewId = button.dataset.reviewId;
        const token = `{{${button.dataset.token}}}`;
        const textarea = document.getElementById(`editSpecificInstructions-${reviewId}`);

        if (textarea && !textarea.value.includes(token)) {
            insertAtCursor(textarea, `${token} `);
            textarea.focus();
        }
    }
});
}

function initLoadMoreButtonFilter() {
    const loadMoreBtnFilter = document.getElementById('loadMoreBtnFilter');
    if (!loadMoreBtnFilter) return;

    const newLoadMoreBtn = loadMoreBtnFilter.cloneNode(true);
    loadMoreBtnFilter.parentNode.replaceChild(newLoadMoreBtn, loadMoreBtnFilter);
    const reviewListContainer = document.getElementById('reviewList');
    const reviewListIdDynamicDiv = document.getElementById('reviewListIdDynamic');
    

    newLoadMoreBtn.style.display = 'none';

    let isLoading = false;

    function loadMoreReviews() {
        if (isLoading) return;
        isLoading = true;

        const businessId = newLoadMoreBtn.getAttribute('data-business-id');
        const page = parseInt(localStorage.getItem('data-page')) || 2;
        const dateRange = document.getElementById('dateRangeFilter')?.value || 'all';
        const typeFilter = document.getElementById('typeFilter')?.value || 'all';
        const totalReviewsElement = document.getElementById('totalDynamicReviews');
        const loadedReviewsElement = document.getElementById('reviewDynamicCount');
        const order = document.getElementById('showOrderFilter')?.value || 'latest';

        const totalReviews = parseInt(totalReviewsElement?.textContent) || 0;
        const loadedReviews = parseInt(loadedReviewsElement?.textContent) || 0;

        const remaining = totalReviews - loadedReviews;
        const perPage = remaining > 0 ? Math.min(remaining, 10) : 10;

        let selectedRatings = [];
        try {
            const storedRatings = localStorage.getItem('filterRatings');
            if (storedRatings) {
                selectedRatings = JSON.parse(storedRatings);
            }
        } catch (e) {
            console.error('Error parsing stored ratings:', e);
        }

        const filters = {
            business_id: businessId,
            date_range: dateRange,
            type: typeFilter,
            ratings: selectedRatings.length > 0 ? selectedRatings : ['ONE', 'TWO', 'THREE', 'FOUR', 'FIVE'],
            per_page: perPage,
            page: page
        };

        fetch(`/businesses/${businessId}/reviews/${order}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            body: JSON.stringify(filters)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (!data.reviews || !Array.isArray(data.reviews)) {
                    console.error('Invalid reviews data:', data);
                    isLoading = false;
                    return;
                }

                if (data.reviews.length === 0) {
                    newLoadMoreBtn.style.display = 'none';
                    isLoading = false;
                    return;
                }

                data.reviews.forEach(review => {
                    const reviewCard = createReviewCardFromJSON(review, data.templates, data.locationName);
                    if (reviewCard) {
                        reviewListContainer.appendChild(reviewCard);
                    }
                });

                localStorage.setItem('data-page', page + 1);

                initReviewEventListeners();

                const newLoadedReviews = reviewListContainer.querySelectorAll('.review-card').length;
                loadedReviewsElement.textContent = newLoadedReviews;
                totalReviewsElement.textContent = data.total || totalReviews;

                if (newLoadedReviews >= (data.total || totalReviews)) {
                    newLoadMoreBtn.style.display = 'none';
                }

                isLoading = false;
            })
            .catch(error => {
                console.error('Error fetching reviews:', error);
                isLoading = false;
            });
    }

    // Manual click fallback
    newLoadMoreBtn.addEventListener('click', loadMoreReviews);

    // Infinite scroll logic (fix: use window scroll if reviewListContainer is not scrollable)
    function handleScroll(e) {
        let scrollableElem = reviewListContainer;
        let threshold = 684;
        let scrollTop, scrollHeight, clientHeight;

        // If reviewListContainer is not scrollable, use window scroll
        if (
            scrollableElem.scrollHeight <= scrollableElem.clientHeight + 1
        ) {
            scrollableElem = document.documentElement;
            scrollTop = window.scrollY || window.pageYOffset;
            scrollHeight = scrollableElem.scrollHeight;
            clientHeight = window.innerHeight;
        } else {
            scrollTop = scrollableElem.scrollTop;
            scrollHeight = scrollableElem.scrollHeight;
            clientHeight = scrollableElem.clientHeight;
        }

        const scrolledPercent = ((scrollTop + clientHeight) / scrollHeight) * 100;

        if ((scrollTop + clientHeight) >= (scrollHeight - 100) && !isLoading) {
            loadMoreReviews();
        }
    }

    // Listen to both container and window scroll
    reviewListContainer.addEventListener('scroll', handleScroll);
    //window.addEventListener('scroll', handleScroll);
}


// Helper function to create a review card from JSON data
function createReviewCardFromJSON(review, templates, location_name) {
    // If review is null or undefined, return an empty div
    if (!review) {
        const emptyDiv = document.createElement('div');
        return emptyDiv;
    }

    const reviewCard = document.createElement('div');
    reviewCard.className = `reviewList review-card bg-white rounded-xl shadow-sm p-4 border border-[#efefef] ${review.is_latest_review ? "bg-indigo-100" : "" }`;
    reviewCard.setAttribute('data-review-id', review.id);
    reviewCard.setAttribute('data-rating', review.star_rating);
    // Create stars HTML
    let starsHTML = '';
    for (let i = 0; i < review.star_rating_numeric; i++) {
        starsHTML += '<i class="icon-star text-yellow-400"></i>';
    }
    for (let i = 0; i < (5 - review.star_rating_numeric); i++) {
        starsHTML += '<i class="icon-star text-gray-300"></i>';
    }

    // Determine if we have replies
    const hasReply = review.replies && review.replies.length > 0;
    const reply = hasReply ? review.replies[0] : null;

    // Determine sentiment based on star rating
    let sentiment = 'neutral';
    let sentimentClass = 'neutral';
    if (review.star_rating_numeric >= 4) {
        sentiment = 'positive';
        sentimentClass = 'positive';
    } else if (review.star_rating_numeric <= 2) {
        sentiment = 'negative';
        sentimentClass = 'negative';
    }

    // --- Modal HTML helpers ---
    function makeEditModal(reply, review) {
        let magicImage = document.getElementById("magicImage").value
        let templateOptions = templates
            .filter(template => review.star_rating_numeric == template.ratings || template.title === "Default Template")
            .map(template => {
                let isSelected = template.title === "Default Template" ? 'selected' : '';
                return `<option value="${template.id}" ${isSelected}>${template.title}</option>`;
            }).join('');
        return `
            <div id="editReplyModal-${review.id}" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm">
                <div class="bg-white md:rounded-lg shadow-xl w-full md:max-w-[750px] h-full md:h-auto overflow-y-auto">
                    <div class="p-4 border-b border-gray-200 flex-col justify-between items-center">                        
                        <div class="flex items-center justify-between gap-2">
                            <h3 class="text-md md:text-lg font-semibold">Edit Reply</h3>
                            <button class="cancel-reply-edit close-modal bg-gray-200 flex-shrink-0 py-0 px-2 rounded-md text-gray-800 hover:text-gray-700" data-review-id="${review.id}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="flex items-center flex-wrap gap-2 mt-1">
                            <div class="w-[24px] h-[24px] rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                <img src="${review.reviewer_photo || ''}" alt="${review.reviewer_name}">
                            </div>
                            <span class="text-sm text-gray-600">${review.reviewer_name}</span>
                            <div class="text-xs text-gray-500">${review.created_at_google}</div>
                            <div class="flex items-center gap-1 w-full md:w-auto">
                                <span class="text-yellow-400">${starsHTML}</span>
                                
                            </div>
                        </div>
                        <!-- Tab Navigation -->
                        <div class="flex w-full bg-gray-100 rounded-full overflow-hidden p-1 mt-6">
                            <button id="editManualReplyTab-${review.id}" class="tab-button w-1/2 py-2 text-sm font-medium flex items-center justify-center gap-1 rounded-full text-gray-500 hover:text-gray-700 bg-white shado active-tab">
                                <i class="fas fa-paper-plane"></i>
                                Manual Reply
                            </button>
                            <button id="editGenerateWithAITab-${review.id}" class="tab-button w-1/2 py-2 text-sm font-medium flex items-center justify-center gap-1 rounded-full text-gray-500 hover:text-gray-700">
                                <i class="fa-solid fa-wand-magic-sparkles"></i> Smart Reply
                            </button>
                        </div>
                    </div>

                    <div class="">
                        <div class="grid ">
                            <!-- Left Column: Review Information and Tabs -->
                            <div class="flex flex-col">
                                <!-- Manual Reply Tab Content -->
                                <div id="editManualReplyContent-${review.id}" class="tab-content-box h-[calc(100vh-340px)] md:h-auto overflow-y-auto gap-4 relative">
                                    <div class="p-4">
                                        <label class="block text-sm font-normal text-gray-700 mb-0">Edit your reply manually in the text area below </label>
                                        <div class="relative mt-1">
                                            <button id="editGenerateReplyButton-${review.id}" disabled class="w-[26px] h-[26px] bg-white z-10 absolute right-[10px] top-[18px] text-xs text-indigo-600 hover:text-indigo-800 rounded-md improveEditReply ring-1 ring-gray-200 flex items-center justify-center
                                            disabled:opacity-50 disabled:cursor-not-allowed
                                            "
                                                data-reply-id="${reply.id}"
                                                data-review="${review.id}"
                                                data-reviewerName="${review.reviewer_name}"
                                                data-rating="${review.rating}"
                                                data-ai-model="gpt-4"
                                                data-custom-instruction="">
                                                <img src="${magicImage}" alt="Magic Image"/>
                                            </button>
                                            <textarea id="editManualReplyText-${review.id}" data-review-id="${review.id}" class="w-full h-60 md:h-32 p-2 border border-gray-200 rounded-lg text-sm resize-none focus:border-2 focus:border-indigo-500 editManualReplyText" placeholder="Write your reply manually in the text area below"></textarea>
                                        </div>
                                    </div>
                                    <div class="bg-white flex justify-end p-4 gap-2 fixed md:relative bottom-0 left-0 border-t border-gray-200 w-full">
                                        <button id="editManualPostReplyButton-${review.id}" class="w-full md:w-auto px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 editPostReplyManual"
                                                data-reply-id="${reply.id}"
                                                data-review-id="${review.id}"
                                                data-reviewId="${review.review_id}"
                                                data-location="${location_name}">
                                            <i class="fas fa-paper-plane mr-1"></i> Post Reply
                                        </button>
                                    </div>

                                </div>

                                <!-- Generate with AI Tab Content -->
                                <div id="editGenerateWithAIContent-${review.id}" class="tab-content-box hidden h-[calc(100vh-255px)] md:h-auto overflow-y-auto gap-4 relative">
                                    <div class="flex flex-col">
                                        <div class="p-4 flex flex-col gap-4">
                                            <div id="templateEditOptionDiv-${review.id}">
                                                <label class="block text-sm font-medium text-gray-700">Select Template</label>
                                                <select id="editTemplateSelect-${review.id}" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                                data-review-id="${review.id}"
                                                data-reviewer-name="${review.reviewer_name}"
                                                data-rating="${review.star_rating}"
                                                data-review-date="${review.created_at_google}"
                                                data-review-text="${review.comment}">
                                                ${templateOptions}
                                                </select>
                                            </div>
                                            <div class="p-0 pt-0">
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    Specific Instructions
                                                    <span class="text-gray-500">(Prompt)</span>
                                                </label>
                                                <div class="relative border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                                    <span class="w-full p-2 flex-shrink-0 bg-white z-10 text-sm text-indigo-600 hover:text-indigo-800 flex items-center gap-1 rounded-tl-md rounded-tr-md hidden border-b  border-gray-300" id="editGeneratedReplyId-${review.id}">
                                                        <img src="${magicImage}" alt="Magic Image"/> Generated Reply
                                                    </span>
                                                    <textarea id="editSpecificInstructions-${review.id}" class="w-full h-44 md:h-32 block px-3 py-2 text-sm pt-2 focus:outline-none" placeholder="Add any specific instructions you want for the AI..."></textarea>
                                                </div>
                                            </div>
                                            
                                            <div class="flex items-center gap-4 hidden" id="editButtonBundles-${review.id}">
                                                <button class="px-3 py-2 flex-gap-1 text-md bg-white ring-1 ring-gray-200 rounded-md hover:bg-gray-200 text-gray-800 editGenerateReplyAI"
                                                    data-review="${review.id}"
                                                    data-reviewerName="${review.reviewer_name}"
                                                    data-rating="${review.star_rating}"
                                                    data-ai-model="gpt-4"
                                                    data-custom-instruction="">
                                                    <i class="fa-solid fa-rotate"></i> Generate Again
                                                </button>
                                                <button class="px-3 py-2 flex-gap-1 text-md bg-white ring-1 ring-gray-200 rounded-md hover:bg-gray-200 text-gray-800 editModifyPromptData" 
                                                data-review="${review.id}"
                                                data-reviewerName="${review.reviewer_name}"
                                                data-rating="${review.star_rating}">
                                                    <i class="fa-solid fa-sliders"></i> Modify Prompt
                                                </button>
                                            </div>
                                            <div>
                                                <label class="block text-sm hidden font-normal text-gray-700 mb-0">
                                                    Click to Insert Variables
                                                </label>
                                                <div class="flex flex-wrap gap-2 mt-1" id="editTagButtons">
                                                    <button class="insert-token-button py-1 px-2 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 text-sm transition-colors duration-200 rounded-md flex items-center gap-1.5" data-review-id="${review.id}" data-token="businessName">
                                                        businessName
                                                    </button> 
                                                    <button class="insert-token-button py-1 px-2 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 text-sm transition-colors duration-200 rounded-md flex items-center gap-1.5" data-review-id="${review.id}" data-token="businessContact">
                                                        businessContact
                                                    </button>
                                                    <button class="insert-token-button py-1 px-2 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 text-sm transition-colors duration-200 rounded-md flex items-center gap-1.5" data-review-id="${review.id}" data-token="firstName">
                                                        firstName
                                                    </button>
                                                </div>
                                            </div>
                                            

                                            <div class="flex flex-wrap gap-2" id="editEmotions-${review.id}">                                                
                                                    <select id="editToneSelect-${review.id}" class="mt-1 block px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                        <option value="professional">Professional</option>
                                                        <option value="friendly">Friendly</option>
                                                        <option value="apologetic">Apologetic</option>
                                                        <option value="thankful">Thankful</option>
                                                    </select>
                                                    <select id="editLanguageSelect-${review.id}" class="mt-1 block px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                        <option value="same-as-review" selected>Same as review</option>
                                                        <option value="english">English</option>
                                                        <option value="spanish">Spanish</option>
                                                        <option value="french">French</option>
                                                        <option value="german">German</option>
                                                        <option value="italian">Italian</option>
                                                        <option value="portuguese">Portuguese</option>
                                                        <option value="dutch">Dutch</option>
                                                        <option value="russian">Russian</option>
                                                        <option value="chinese">Chinese (Simplified)</option>
                                                        <option value="japanese">Japanese</option>
                                                        <option value="korean">Korean</option>
                                                        <option value="arabic">Arabic</option>
                                                        <option value="hindi">Hindi</option>
                                                    </select>
                                                    <select id="editSentimentSelect-${review.id}" class="mt-1 block px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                        <option value="any">Any</option>
                                                        <option value="positive">Positive Only</option>
                                                        <option value="negative">Negative Only</option>
                                                        <option value="neutral">Neutral Only</option>
                                                    </select>
                                            </div>
                                        </div>

                                        <div class="bg-white flex justify-end p-4 gap-2 fixed md:relative bottom-0 left-0 border-t border-gray-200 w-full" id="editResponseGenerate-${review.id}">
                                            <button id="editGenerateButton-${review.id}" class="w-full md:w-auto inline-flex justify-center items-center gap-2 px-2 py-2 text-sm font-medium rounded-md text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-md transition-all duration-200 editGenerateReplyAI"
                                                data-review="${review.id}"
                                                data-reviewerName="${review.reviewer_name}"
                                                data-rating="${review.star_rating}"
                                                data-ai-model="gpt-4"
                                                data-custom-instruction="">
                                                <i class="fa-solid fa-wand-magic-sparkles"></i> Generate Response
                                            </button>
                                        </div>
                                        <div class="bg-white flex justify-end gap-2 w-full p-4 flex-wrap fixed md:relative bottom-0 left-0 border-t border-gray-200 hidden" id="editPostReplyButton-${review.id}">
                                            <button class="w-full md:w-auto px-3 py-2 flex-gap-1 text-md bg-green-600 text-white rounded-md hover:bg-green-700 editSmartPostReply"
                                                data-reply-id="${reply.id}"
                                                data-review-id="${review.id}"
                                                data-reviewId="${review.review_id}"
                                                data-location="${location_name}">
                                                <i class="fas fa-paper-plane"></i> Post Reply
                                            </button>                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            `;
    }
    function makeReplyModal(review) {
        let magicImage = document.getElementById("magicImage").value
        let templateOptions = templates
            .filter(template => review.star_rating_numeric == template.ratings || template.title === "Default Template")
            .map(template => {
                let isSelected = template.title === "Default Template" ? 'selected' : '';
                return `<option value="${template.id}" ${isSelected}>${template.title}</option>`;
            }).join('');
        return `
            <div id="replyModal-${review.id}" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm">
                <div class="bg-white md:rounded-lg shadow-xl w-full md:max-w-[750px] h-full md:h-auto overflow-y-auto">
                    <div class="p-4 border-b border-gray-200 flex-col justify-between items-center">                        
                        <div class="flex items-center justify-between gap-2">
                            <h3 class="text-md md:text-lg font-semibold">Reply to Review</h3>
                            <button class="close-modal bg-gray-200 flex-shrink-0 py-0 px-2 rounded-md text-gray-800 hover:text-gray-700 close-reply-modal" data-review-id="${review.id}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="flex items-center flex-wrap gap-2 mt-1">
                            <div class="w-[24px] h-[24px] rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                <img src="https://lh3.googleusercontent.com/a-/ALV-UjWZJf268PUiHEJNSJJ8lqUXI_Ylir2apIgbyF2Oyf0S1mkXH_fK=s120-c-rp-mo-ba4-br100" alt="Bhavik Dutt">
                            </div>
                            <span class="text-sm text-gray-600">${review.reviewer_name}</span>
                            <div class="text-xs text-gray-500">${review.created_at_google}</div>
                            <div class="flex items-center gap-1 w-full md:w-auto">
                                <span class="text-yellow-400">${starsHTML}</span>
                                
                            </div>
                        </div>
                        <!-- Tab Navigation -->
                        <div class="flex w-full bg-gray-100 rounded-full overflow-hidden p-1 mt-6">
                            <button id="manualReplyTab-${review.id}" class="tab-button w-1/2 py-2 text-sm font-medium flex items-center justify-center gap-1 rounded-full text-gray-500 hover:text-gray-700 bg-white shado active-tab">
                                <i class="fas fa-paper-plane"></i>
                                Manual Reply
                            </button>
                            <button id="generateWithAITab-${review.id}" class="tab-button w-1/2 py-2 text-sm font-medium flex items-center justify-center gap-1 rounded-full text-gray-500 hover:text-gray-700">
                                <i class="fa-solid fa-wand-magic-sparkles"></i> Smart Reply
                            </button>
                        </div>
                    </div>

                    <div class="">
                        <div class="grid ">
                            <!-- Left Column: Review Information and Tabs -->
                            <div class="flex flex-col">
                                <!-- Manual Reply Tab Content -->
                                <div id="manualReplyContent-${review.id}" class="tab-content-box h-[calc(100vh-340px)] md:h-auto overflow-y-auto gap-4 relative">
                                    <div class="p-4">
                                        <label class="block text-sm font-normal text-gray-700 mb-0">Write your reply manually in the text area below </label>
                                        <div class="relative mt-1">
                                            <button id="generateReplyButton-${review.id}" disabled class="w-[26px] h-[26px] bg-white z-10 absolute right-[10px] top-[18px] text-xs text-indigo-600 hover:text-indigo-800 rounded-md improveReply ring-1 ring-gray-200 flex items-center justify-center
                                            disabled:opacity-50 disabled:cursor-not-allowed
                                            "
                                                data-review="${review.id}"
                                                data-reviewerName="${review.reviewer_name}"
                                                data-rating="${review.star_rating}"
                                                data-ai-model="gpt-4"
                                                data-custom-instruction="">
                                                <img src="${magicImage}" alt="Magic Image"/>
                                            </button>
                                            <textarea id="manualReplyText-${review.id}" data-review-id="${review.id}" class="w-full h-60 md:h-32 p-2 border border-gray-200 rounded-lg text-sm resize-none focus:border-2 focus:border-indigo-500 manualReplyText" placeholder="Write your reply manually in the text area below"></textarea>
                                        </div>
                                    </div>
                                    <div class="bg-white flex justify-end p-4 gap-2 fixed md:relative bottom-0 left-0 border-t border-gray-200 w-full">
                                        <button id="manualPostReplyButton-${review.id}" class="w-full md:w-auto px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700 postReplyManual"
                                                data-review-id="${review.id}"
                                                data-reviewId="${review.review_id}"
                                                data-location="${location_name}">
                                            <i class="fas fa-paper-plane mr-1"></i> Post Reply
                                        </button>
                                    </div>

                                </div>

                                <!-- Generate with AI Tab Content -->
                                <div id="generateWithAIContent-${review.id}" class="tab-content-box hidden h-[calc(100vh-255px)] md:h-auto overflow-y-auto gap-4 relative">
                                    <div class="flex flex-col">
                                        <div class="p-4 flex flex-col gap-4">
                                            <div id="templateOptionDiv-${review.id}">
                                                <label class="block text-sm font-medium text-gray-700">Select Template</label>
                                                <select id="templateSelect-${review.id}" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                                data-review-id="${review.id}"
                                                data-reviewer-name="${review.reviewer_name}"
                                                data-rating="${review.star_rating}"
                                                data-review-date="${review.created_at_google}"
                                                data-review-text="${review.comment}">
                                                ${templateOptions}
                                                </select>
                                            </div>
                                            <div class="p-0 pt-0">
                                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                                    Specific Instructions
                                                    <span class="text-gray-500">(Prompt)</span>
                                                </label>
                                                <div class="relative border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                                    <span class="w-full p-2 flex-shrink-0 bg-white z-10 text-sm text-indigo-600 hover:text-indigo-800 flex items-center gap-1 rounded-tl-md rounded-tr-md hidden border-b  border-gray-300" id="generatedReplyId-${review.id}">
                                                        <img src="${magicImage}" alt="Magic Image"/> Generated Reply
                                                    </span>
                                                    <textarea id="specificInstructions-${review.id}" class="w-full h-44 md:h-32 block px-3 py-2 text-sm pt-2 focus:outline-none" placeholder="Add any specific instructions you want for the AI..."></textarea>
                                                </div>
                                            </div>
                                            
                                            <div class="flex items-center gap-4 hidden" id="buttonBundles-${review.id}">
                                                <button class="px-3 py-2 flex-gap-1 text-md bg-white ring-1 ring-gray-200 rounded-md hover:bg-gray-200 text-gray-800 generateReplyAI"
                                                    data-review="${review.id}"
                                                    data-reviewerName="${review.reviewer_name}"
                                                    data-rating="${review.star_rating}"
                                                    data-ai-model="gpt-4"
                                                    data-custom-instruction="">
                                                    <i class="fa-solid fa-rotate"></i> Generate Again
                                                </button>
                                                <button class="px-3 py-2 flex-gap-1 text-md bg-white ring-1 ring-gray-200 rounded-md hover:bg-gray-200 text-gray-800 modifyPromptData" 
                                                data-review="${review.id}"
                                                data-reviewerName="${review.reviewer_name}"
                                                data-rating="${review.star_rating}">
                                                    <i class="fa-solid fa-sliders"></i> Modify Prompt
                                                </button>
                                            </div>
                                            <div>
                                                <label class="block text-sm hidden font-normal text-gray-700 mb-0">
                                                    Click to Insert Variables
                                                </label>
                                                <div class="flex flex-wrap gap-2 mt-1" id="tagButtons">
                                                    <button class="insert-token-button py-1 px-2 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 text-sm transition-colors duration-200 rounded-md flex items-center gap-1.5" data-review-id="${review.id}" data-token="businessName">
                                                        businessName
                                                    </button> 
                                                    <button class="insert-token-button py-1 px-2 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 text-sm transition-colors duration-200 rounded-md flex items-center gap-1.5" data-review-id="${review.id}" data-token="businessContact">
                                                        businessContact
                                                    </button>
                                                    <button class="insert-token-button py-1 px-2 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 text-sm transition-colors duration-200 rounded-md flex items-center gap-1.5" data-review-id="${review.id}" data-token="firstName">
                                                        firstName
                                                    </button>
                                                </div>
                                            </div>
                                            

                                            <div class="flex flex-wrap gap-2" id="emotions-${review.id}">   
                                                    <div class="flex flex-col">
                                                        <label class="block text-sm font-normal text-gray-700 mb-1">
                                                            Tone
                                                        </label>
                                                        <select id="toneSelect-${review.id}" class="mt-1 block px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                            <option value="professional">Professional</option>
                                                            <option value="friendly">Friendly</option>
                                                            <option value="apologetic">Apologetic</option>
                                                            <option value="thankful">Thankful</option>
                                                        </select>
                                                    </div>
                                                    <div class="flex flex-col">
                                                        <label class="block text-sm font-normal text-gray-700 mb-1">
                                                            Language
                                                        </label>                                           
                                                        <select id="languageSelect-${review.id}" class="mt-1 block px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                            <option value="same-as-review" selected>Same as review</option>
                                                            <option value="english">English</option>
                                                            <option value="spanish">Spanish</option>
                                                            <option value="french">French</option>
                                                            <option value="german">German</option>
                                                            <option value="italian">Italian</option>
                                                            <option value="portuguese">Portuguese</option>
                                                            <option value="dutch">Dutch</option>
                                                            <option value="russian">Russian</option>
                                                            <option value="chinese">Chinese (Simplified)</option>
                                                            <option value="japanese">Japanese</option>
                                                            <option value="korean">Korean</option>
                                                            <option value="arabic">Arabic</option>
                                                            <option value="hindi">Hindi</option>
                                                        </select>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-normal text-gray-700 mb-1">
                                                            Sentiment
                                                        </label>
                                                        <select id="sentimentSelect-${review.id}" class="mt-1 block px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                            <option value="any">Any</option>
                                                            <option value="positive">Positive Only</option>
                                                            <option value="negative">Negative Only</option>
                                                            <option value="neutral">Neutral Only</option>
                                                        </select>
                                                    </div>
                                            </div>
                                        </div>

                                        <div class="bg-white flex justify-end p-4 gap-2 fixed md:relative bottom-0 left-0 border-t border-gray-200 w-full" id="responseGenerate-${review.id}">
                                            <button id="generateButton-${review.id}" class="w-full md:w-auto inline-flex justify-center items-center gap-2 px-2 py-2 text-sm font-medium rounded-md text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-md transition-all duration-200 generateReplyAI"
                                                data-review="${review.id}"
                                                data-reviewerName="${review.reviewer_name}"
                                                data-rating="${review.star_rating}"
                                                data-ai-model="gpt-4"
                                                data-custom-instruction="">
                                                <i class="fa-solid fa-wand-magic-sparkles"></i> Generate Response
                                            </button>
                                        </div>
                                        <div class="bg-white flex justify-end gap-2 w-full p-4 flex-wrap fixed md:relative bottom-0 left-0 border-t border-gray-200 hidden" id="postReplyButton-${review.id}">
                                            <button class="w-full md:w-auto px-3 py-2 flex-gap-1 text-md bg-green-600 text-white rounded-md hover:bg-green-700 smartPostReply"
                                                
                                                data-review-id="${review.id}"
                                                data-reviewId="${review.review_id}"
                                                data-location="${location_name}">
                                                <i class="fas fa-paper-plane"></i> Post Reply
                                            </button>                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            `;
            
    }

    // --- Main review card HTML ---
    reviewCard.innerHTML = `
        <div class="flex flex-col gap-1">
            <div class="flex gap-3">
                <div class="w-10 h-10 flex-shrink-0 overflow-hidden">
                    <img src="${review.reviewer_photo || ''}" alt="${review.reviewer_name}">
                </div>            
                <div class="flex items-start gap-4">
                    <div>
                        <div class="flex gap-2 md:gap-4 flex-wrap mb-2">
                            <h3 class="text-sm font-medium text-gray-900 capitalize">${review.reviewer_name}</h3>
                            <div class="stars-container flex text-yellow-400">
                                ${starsHTML}
                            </div>
                            <div id="sentimentDisplay-${review.id}" class="sentiment-container">
                                <span class="sentiment-badge ${sentimentClass}">${sentiment}</span>
                            </div>
                            <input type="hidden" id="starRating-${review.id}" value="${review.star_rating_numeric}">
                        </div>
                        <p class="text-xs text-gray-500">${review.created_at}</p>
                    </div>
                </div>
            </div>
            <div class="flex flex-col w-full">
                ${review.comment ? `
                    <p id="reviewText-${review.id}" class="text-sm text-gray-700 mt-2 line-clamp-3">
                        ${review.comment}
                        <button 
                            class="texttoggleBtn p-0 border-0 bg-transparent text-indigo-500 text-sm underline hidden" 
                            id="textToggleBtn-${review.id}">
                            Read more
                        </button>
                    </p>
                ` : ''}
                ${hasReply ? `
                <div class="mt-4 border-gray-200 pl-4 p-2 rounded-lg relative ${review.is_latest_reply ? 'bg-indigo-50' : 'bg-gray-100'}">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-xs font-bold text-white flex-shrink-0">
                                ${reply.reviewer_name.charAt(0).toUpperCase()}
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-semibold text-gray-800">${reply.reviewer_name}</h4>
                                <p class="text-xs text-gray-500">${reply.updated_at}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-3">
                            <button class="edit-reply-trigger py-0.5 px-1.5 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 transition-colors duration-200 rounded-md flex items-center gap-1.5"
                                data-review-id="${review.id}"
                                data-reply-id="${reply.id}"
                                data-reviewer-name="${review.reviewer_name}"
                                data-rating="${review.star_rating}"
                                data-review-date="${review.created_at_google}"
                                data-review-text="${review.comment}"
                                data-existing-reply="${reply.comment}">
                                <i class="fas fa-edit text-xs md:text-sm"></i>
                                
                            </button>
                            <button class="delete-reply-btn flex items-center gap-1.5 py-0.5 px-1.5 rounded-md bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-colors duration-200" data-review-id="${review.id}" data-reply-id="${reply.id}">
                                <i class="fas fa-trash text-xs md:text-sm"></i>
                                
                            </button>
                        </div>
                    </div>
                    ${ reply.reply_by !== null ? `                    
                    <div class="team-member-info flex items-center mt-1 mb-1 border-l-2 border-blue-400 px-2 py-1 gap-2">
                        ${ reply.reply_by === 0 ? `<div class="w-6 h-6 p-1 rounded-full bg-gray-200 flex items-center justify-center text-sm flex-shrink-0">
                            RM </div>  
                            <div class="text-xs text-gray-700">
                            <span class="text-xs text-gray-700 ml-1">Replied via <img src="${reviewIcon.value}" alt="ReviewMaster.biz" class="w-8 h-4 rounded-full inline"> 
                                    by <b> Smart Reply </b> </span>
                            <span class="text-xs text-gray-400 ml-2"> ${ reply.replied_template[0] ? reply.replied_template[0] : '' } </span>
                        </div>` : reply.reply_by === null ? "" :
                            `<div class="w-6 h-6 p-1 rounded-full bg-gray-200 flex items-center justify-center text-sm flex-shrink-0">
                            SJ </div>  
                            <div class="text-xs text-gray-700">
                            <span class="text-xs text-gray-700 ml-1">Replied via <img src="${reviewIcon.value}" alt="ReviewMaster.biz" class="w-8 h-4 rounded-full inline"> 
                                    by <b> ${reply.reply_by_name}</b>
                            </span>
                            <span class="text-xs text-gray-400 ml-2"> ${ reply.replied_template != null && reply.replied_template ? reply.replied_template : '' } </span>
                        </div>`
                    }
                    </div>` : ''}
                    <p class="text-sm text-gray-700 mt-2">${reply.comment || ''}</p>
                </div>
                ` : `
                <div class="inline-flex gap-4 items-center mt-4">
                    <button class="reply-btn relative overflow-hidden bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md text-xs py-1 px-3 before:absolute before:top-0 before:left-[-75%] before:h-full before:w-[50%] before:bg-gradient-to-r before:from-white before:to-transparent before:opacity-30 hover:before:left-[125%] before:transform before:skew-x-[-20deg] before:transition-all before:duration-500" data-review-id="${review.id}" data-business-id="${business_id.value}" data-reviewer-name="${review.reviewer_name}" data-rating="${review.star_rating}" data-review-date="${review.created_at_google}" data-review-text="${review.comment}">Reply</button>
                </div>
                `}
            </div>
        </div>`;

    // --- Append modals to body (not inside card) ---
    if (hasReply && reply) {
        if (!document.getElementById(`editReplyModal-${reply.id}`)) {
            const editModal = document.createElement('div');
            editModal.innerHTML = makeEditModal(reply, review);
            document.body.appendChild(editModal.firstElementChild);
        }
    } else {
        if (!document.getElementById(`replyModal-${review.id}`)) {
            const replyModal = document.createElement('div');
            replyModal.innerHTML = makeReplyModal(review);
            document.body.appendChild(replyModal.firstElementChild);
        }
    }

    return reviewCard;
}


// Initialize event handlers when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    initReviewEventListeners();

    const refreshButton = document.querySelector('.refreshFilters');

    if(refreshButton)
    {
        refreshButton.addEventListener('click', function (e) {
        const businessId = this.getAttribute('data-business');
    
        // Remove active styling from all filter buttons
        document.querySelectorAll('.getFilter').forEach(el => {
            el.classList.remove('bg-indigo-600', 'text-white', 'activated');
            localStorage.setItem('filterRatings', []);
            localStorage.setItem('data-page', 2);
    
            if (el.tagName === 'SELECT') {
                el.selectedIndex = 0;
            }
        });
    
        // Reset sort order to default if it exists
        const sortOrderFilter = document.getElementById('showOrderFilter');
        if (sortOrderFilter) {
            sortOrderFilter.selectedIndex = 0;
        }
    
        // document.getElementById('reviewList').innerHTML = "Loading...";
        
        document.getElementById('loader').style.display = 'flex';
        document.getElementById('reviewList').innerHTML = ''
        setTimeout(() => {
            document.getElementById('loader').style.display = 'none';
            initializeReviewsFilter(businessId, null, sortOrderFilter.value, null);
            body.style.overflow = 'hidden';
        }, 1000);
    });
    }
});

let count = [];
let ratingCounts = {}; // Store counts by rating value

// --- Attach filter event listeners ---
document.querySelectorAll('.getFilter').forEach(el => {
    const eventType = el.tagName === 'SELECT' ? 'change' : 'click';
    //['click', 'change'].forEach(eventType => {
        el.addEventListener(eventType, function (e) {
            e.preventDefault();
            const businessId = this.dataset.business;
            const locationId = this.dataset.location;
            const buttonElement = this;
            const visibleText = buttonElement.dataset.rating;
            const order = document.getElementById('showOrderFilter').value;
            document.body.classList.toggle('filter-sidebar-active');
            document.getElementById('loadMoreBtnFilter').style.display = 'block';
            
            initializeReviewsFilter(businessId, buttonElement, order, null);

            if (buttonElement.classList.contains('rating-btn')) {
                if (buttonElement.classList.contains('activated')) {
                    if (!ratingCounts.hasOwnProperty(visibleText)) {
                        fetch(`/countreviews/${locationId}/${visibleText}`)
                            .then(response => response.json())
                            .then(data => {
                                ratingCounts[visibleText] = data;
                                rebuildCountArray();
                            });
                    }
                } else {
                    if (ratingCounts.hasOwnProperty(visibleText)) {
                        delete ratingCounts[visibleText];
                        rebuildCountArray();
                    }
                }
            } else {
                fetch(`/countreviews/${locationId}/${visibleText}`)
                    .then(response => response.json())
                    .then(data => {
                        count = [data];
                        document.getElementById('totalDynamicReviews').textContent = localStorage.getItem('totalReviews');
                    });
            }

            const selectedRatings = [];
            document.querySelectorAll('.rating-btn.activated').forEach(btn => {
                selectedRatings.push(btn.dataset.rating);
            });

            localStorage.setItem('filterRatings', JSON.stringify(selectedRatings));
            document.getElementById('loadMoreBtnFilter').style.display = 'block';
            localStorage.setItem('data-page', 2);
        });
    //});
});

// Helper function to rebuild the count array and update total
function rebuildCountArray() {
    count = Object.values(ratingCounts);
    const total = count.reduce((sum, value) => sum + value, 0);
    console.log(localStorage.getItem('totalReviews').textContent, " check all total reviews")
    if(total > 0)
    {
        document.getElementById('totalDynamicReviews').textContent = total;
    }
    else
    {
        document.getElementById('totalDynamicReviews').textContent = localStorage.getItem('totalReviews');
    }
}

// Run sentiment analysis and apply filters when the page loads
document.addEventListener('DOMContentLoaded', function () {
    //analyzeSentimentForAllReviews();
    // Restore selected ratings from localStorage
    try {
        let totalReviews = document.getElementById('totalDynamicReviews')
        if(totalReviews)
        {
            totalReviews.textContent || 0;
            localStorage.setItem('totalReviews', totalReviews.textContent);
            localStorage.setItem('filterRatings', JSON.stringify([]));
    
            let storedRatings = localStorage.getItem('filterRatings') || [];
            if (storedRatings && storedRatings !== null) {
                const selectedRatings = JSON.parse(storedRatings);
                if (selectedRatings.length > 0) {
                    // Apply active class to stored ratings
                    document.querySelectorAll('.rating-btn').forEach(btn => {
                        if (selectedRatings.includes(btn.dataset.rating)) {
                            btn.classList.add('bg-indigo-600', 'text-white', 'activated');
                        }
                    });
                }
            }
            localStorage.setItem('data-page', 2)
            // Apply filters on page load

            const businessId = document.querySelector('.getFilter')?.dataset.business;
            if (businessId) {
                document.getElementById('loader').style.display = 'flex';
            
                setTimeout(() => {
                    initializeReviewsFilter(
                        businessId,
                        null,
                        document.getElementById('showOrderFilter').value,
                        null
                    );
                    document.getElementById('loadMoreBtnFilter').style.display = 'block';
                    document.getElementById('loader').style.display = 'none';
                    //body.style.overflow = 'hidden';
                }, 1000);
            }
        }
    } catch (e) {
        console.error('Error restoring ratings from localStorage:', e);
    }
});

// Also run sentiment analysis after a short delay to ensure all reviews are loaded
setTimeout(function () {
    analyzeSentimentForAllReviews();
}, 1500);

// --- Initial binding on page load ---
document.addEventListener('DOMContentLoaded', function () {
    try {
        bindReviewEvents();
        initLoadMoreButtonFilter();
    } catch (error) {
        console.error('Error in DOMContentLoaded:', error);
    }
});
window.initLoadMoreButtonFilter = initLoadMoreButtonFilter;


