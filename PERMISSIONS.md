# Team Permissions System Documentation

## Overview

The ReviewMaster AI application implements a comprehensive permissions system to control access to various features based on user roles and explicitly granted permissions. This document outlines how the permissions system works and how to use it in both backend and frontend code.

## Permission Types

The system defines the following permission types:

- `view_reviews`: Allows viewing of reviews
- `reply_to_reviews`: Allows replying to reviews
- `manage_templates`: Allows creating, editing, and deleting response templates
- `view_analytics`: Allows viewing analytics data
- `manage_settings`: Allows modifying application settings
- `manage_team`: Allows managing team members

## Backend Implementation

### Middleware

The `CheckPermission` middleware (`app/Http/Middleware/CheckPermission.php`) enforces permission checks on routes. It:

1. Checks if the user is the business account owner (full access)
2. Checks if the user is an admin team member (full access)
3. Checks if the user has the specific permission required for the route

### Models

- `TeamMember` model includes helper methods like `hasPermission()` and `isAdmin()`
- `Permission` model defines available permissions and their relationships

### Controllers

- `PermissionController` provides an API endpoint to get user permissions
- Other controllers use permission checks before allowing actions

## Frontend Implementation

### Permissions Handler

The `permissions-handler.js` utility provides frontend permission checking:

```javascript
// Load user permissions for a business account
permissionsHandler.loadUserPermissions(businessAccountId);

// Check if user has a specific permission
if (permissionsHandler.hasPermission('manage_templates')) {
  // Show UI elements or enable functionality
}

// Show permission denied message
permissionsHandler.showPermissionDeniedMessage('You do not have permission to perform this action');
```

### Integration with Components

The permissions system is integrated with all major components:

1. **Templates Manager**: Hides add/edit/delete buttons for users without `manage_templates` permission
2. **Settings Manager**: Disables form inputs for users without `manage_settings` permission
3. **Reviews Manager**: Hides reply buttons for users without `reply_to_reviews` permission
4. **Analytics Dashboard**: Hides analytics for users without `view_analytics` permission
5. **Team Management**: Restricts access to users without `manage_team` permission

## Usage Guidelines

### Adding Permission Checks to Routes

```php
// In routes/web.php
Route::middleware(['check.permission:manage_templates'])->group(function () {
    Route::get('/templates', [TemplateController::class, 'index']);
    // Other template routes
});
```

### Adding Permission Checks to Controllers

```php
public function update(Request $request, $id)
{
    $businessAccount = BusinessAccount::findOrFail($id);
    
    // Check if user has permission
    if (Auth::id() !== $businessAccount->user_id) {
        $teamMember = TeamMember::where('user_id', Auth::id())
            ->where('business_account_id', $id)
            ->where('status', 'active')
            ->first();
            
        if (!$teamMember || (!$teamMember->isAdmin() && !$teamMember->hasPermission('manage_settings'))) {
            return redirect()->back()->with('error', 'You do not have permission to update settings.');
        }
    }
    
    // Continue with update logic
}
```

### Adding Permission Checks to Frontend Components

```javascript
// Check permission before showing UI elements
const hasPermission = permissionsHandler.hasPermission('reply_to_reviews');
if (hasPermission) {
  // Show reply button
} else {
  // Show disabled button or hide completely
}

// Check permission before performing actions
submitButton.addEventListener('click', function() {
  if (!permissionsHandler.hasPermission('manage_templates')) {
    permissionsHandler.showPermissionDeniedMessage('You do not have permission to save templates');
    return;
  }
  
  // Continue with save logic
});
```

## Initialization

The permissions handler is automatically initialized in the main app layout for any page that has a business account context:

```html
@if(isset($businessAccount))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        permissionsHandler.loadUserPermissions("{{ $businessAccount->id }}");
    });
</script>
@endif
```

## Compiling the Permissions Handler

To update the permissions handler JavaScript file, run:

```bash
node build-permissions.js
```

This will compile the permissions handler and make it available to the application.
