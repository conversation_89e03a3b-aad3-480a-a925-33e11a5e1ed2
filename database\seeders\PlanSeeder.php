<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    public function run()
    {
        // Clear existing plans (delete instead of truncate to handle foreign keys)
        Plan::query()->delete();

        // INR Plans
        $this->createINRPlans();

        // USD Plans
        $this->createUSDPlans();
    }

    private function createINRPlans()
    {
        // Free Plan - INR
        Plan::create([
            'name' => 'Trial',
            'short_description' => 'Free Forever',
            'long_description' => '["Connect 1 Google Business location", "1 team member access", "100 AI-powered replies per month", "Basic analytics & smart replies included", "Email support only"]',
            'currency' => 'INR',
            'currency_symbol' => '₹',
            'price' => 0.00,
            'annual_price' => 0.00,
            'annual_discount_percentage' => 0,
            'tax_percentage' => 18,
            'duration_days' => 30,
            'features' => $this->createFeatures(1, 1, 100, 'email', false, false, 'none', 'basic', 'default', 'default', false, false),
            'is_active' => true,
        ]);

        // Business Plan - INR
        Plan::create([
            'name' => 'Business',
            'short_description' => 'Best for small to medium businesses',
            'long_description' => '["Connect up to 3 locations","2 team members with shared access","1000 AI replies/month with reply scheduling","Basic analytics & smart replies included","Custom templates & settings enabled","Email support with 10% annual discount"]',
            'currency' => 'INR',
            'currency_symbol' => '₹',
            'price' => 600.00,
            'annual_price' => 6480.00, // 10% discount
            'annual_discount_percentage' => 10,
            'tax_percentage' => 18,
            'duration_days' => 30,
            'features' => $this->createFeatures(3, 2, 1000, 'email', false, true, 'none', 'basic', 'default', 'custom', true, false),
            'is_active' => true,
        ]);

        // Enterprise Plan - INR
        Plan::create([
            'name' => 'Enterprise',
            'short_description' => 'For larger enterprises',
            'long_description' => '["Connect 10 Google Business locations", "Team access for up to 5 users", "10,000 replies/month + auto scheduling", "API access, data export & beta features", "Email + Phone support with 15% off annually"]',
            'currency' => 'INR',
            'currency_symbol' => '₹',
            'price' => 1500.00,
            'annual_price' => 15300.00, // 15% discount
            'annual_discount_percentage' => 15,
            'tax_percentage' => 18,
            'duration_days' => 30,
            'features' => $this->createFeatures(10, 5, 10000, 'email_phone', true, true, 'basic', 'basic', 'default', 'custom', true, true),
            'is_active' => true,
        ]);

        // Agency Plan - INR
        Plan::create([
            'name' => 'Agency',
            'short_description' => 'Best for marketing / social media agencies',
            'long_description' => '["Connect 25 Google Business locations", "Team access for up to 20 users", "Unlimited replies/month with auto scheduling", "Advanced API access, data export & beta features", "Dedicated account manager", "Email + Phone support with 20% off annually"]',
            'currency' => 'INR',
            'currency_symbol' => '₹',
            'price' => 3000.00,
            'annual_price' => 28800.00, // 20% discount
            'annual_discount_percentage' => 20,
            'duration_days' => 30,
            'features' => $this->createFeatures(25, 20, -1, 'dedicated_manager', true, true, 'advanced', 'advanced', 'custom_training', 'custom', true, true),
            'is_active' => true,
        ]);
    }

    private function createFeatures($businessLimit, $teamLimit, $replyLimit, $supportLevel, $dataExport, $scheduledReplies, $apiLevel, $analyticsLevel, $aiLevel, $templateAccess, $settingsAccess, $betaFeatures)
    {
        return [
            [
                'title' => '# of Google Business to connect',
                'key' => 'business_connections_limit',
                'value' => $businessLimit,
                'status' => 'enabled'
            ],
            [
                'title' => 'Team Access',
                'key' => 'team_members_limit',
                'value' => $teamLimit,
                'status' => 'enabled'
            ],
            [
                'title' => 'Reply Up to',
                'key' => 'monthly_reply_limit',
                'value' => $replyLimit === -1 ? 'Unlimited' : $replyLimit . ' / Month',
                'status' => 'enabled'
            ],
            [
                'title' => 'Smart Replies (AI generated response)',
                'key' => 'smart_replies_enabled',
                'value' => 'Yes',
                'status' => 'enabled'
            ],
            [
                'title' => 'Analytics',
                'key' => 'analytics_level',
                'value' => ucfirst($analyticsLevel),
                'status' => 'enabled'
            ],
            [
                'title' => 'AI Customization',
                'key' => 'ai_customization_level',
                'value' => $this->formatAiCustomizationValue($aiLevel),
                'status' => 'enabled'
            ],
            [
                'title' => 'Template',
                'key' => 'template_access',
                'value' => ucfirst($templateAccess),
                'status' => 'enabled'
            ],
            [
                'title' => 'Schedule Auto Replies via AI',
                'key' => 'scheduled_auto_replies_enabled',
                'value' => $scheduledReplies ? 'Yes' : 'No',
                'status' => $scheduledReplies ? 'enabled' : 'disabled'
            ],
            [
                'title' => 'Settings',
                'key' => 'settings_access_enabled',
                'value' => $settingsAccess ? 'Yes' : 'No',
                'status' => $settingsAccess ? 'enabled' : 'disabled'
            ],
            [
                'title' => 'Data export',
                'key' => 'data_export_enabled',
                'value' => $dataExport ? 'Yes' : 'No',
                'status' => $dataExport ? 'enabled' : 'disabled'
            ],
            [
                'title' => 'New Beta Features',
                'key' => 'beta_features_enabled',
                'value' => $betaFeatures ? 'Yes' : 'No',
                'status' => $betaFeatures ? 'enabled' : 'disabled'
            ],
            [
                'title' => 'API Access',
                'key' => 'api_access_level',
                'value' => $this->formatApiAccessValue($apiLevel),
                'status' => $apiLevel !== 'none' ? 'enabled' : 'disabled'
            ],
            [
                'title' => 'Support',
                'key' => 'support_level',
                'value' => $this->formatSupportValue($supportLevel),
                'status' => 'enabled'
            ]
        ];
    }

    private function formatSupportValue($level)
    {
        switch ($level) {
            case 'email':
                return 'Email';
            case 'email_phone':
                return 'Email + Phone';
            case 'dedicated_manager':
                return 'Dedicated Account Manager';
            default:
                return 'Email';
        }
    }

    private function formatApiAccessValue($level)
    {
        switch ($level) {
            case 'none':
                return 'No';
            case 'basic':
                return 'Basic';
            case 'advanced':
                return 'Advance';
            default:
                return 'No';
        }
    }

    private function formatAiCustomizationValue($level)
    {
        switch ($level) {
            case 'custom_training':
                return 'Custom AI training';
            case 'default':
            default:
                return 'Default';
        }
    }

    private function createUSDPlans()
    {
        // Free Plan - USD
        Plan::create([
            'name' => 'Trial',
            'short_description' => 'Free Forever',
            'long_description' => '["Connect 1 Google Business location", "1 team member access", "100 AI-powered replies per month", "Basic analytics & smart replies included", "Email support only"]     ',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'price' => 0.00,
            'annual_price' => 0.00,
            'annual_discount_percentage' => 0,
            'duration_days' => 7,
            'features' => $this->createFeatures(1, 1, 100, 'email', false, false, 'none', 'basic', 'default', 'default', false, false),
            'is_active' => true,
        ]);

        // Business Plan - USD
        Plan::create([
            'name' => 'Business',
            'short_description' => 'Best for small to medium businesses',
            'long_description' => '["Connect up to 3 locations","2 team members with shared access","1000 AI replies/month with reply scheduling","Basic analytics & smart replies included","Custom templates & settings enabled","Email support with 10% annual discount"]',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'price' => 15.00,
            'annual_price' => 162.00, // 10% discount
            'annual_discount_percentage' => 10,
            'duration_days' => 30,
            'features' => $this->createFeatures(3, 2, 1000, 'email', false, true, 'none', 'basic', 'default', 'custom', true, false),
            'is_active' => true,
        ]);

        // Enterprise Plan - USD
        Plan::create([
            'name' => 'Enterprise',
            'short_description' => 'For larger enterprises',
            'long_description' => '["Connect 10 Google Business locations", "Team access for up to 5 users", "10,000 replies/month + auto scheduling", "API access, data export & beta features", "Email + Phone support with 15% off annually"]',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'price' => 45.00,
            'annual_price' => 459.00, // 15% discount
            'annual_discount_percentage' => 15,
            'duration_days' => 30,
            'features' => $this->createFeatures(10, 5, 10000, 'email_phone', true, true, 'basic', 'basic', 'default', 'custom', true, true),
            'is_active' => true,
        ]);

        // Agency Plan - USD
        Plan::create([
            'name' => 'Agency',
            'short_description' => 'Best for marketing / social media agencies',
            'long_description' => '["Connect 25 Google Business locations", "Team access for up to 20 users", "Unlimited replies/month with auto scheduling", "Advanced API access, data export & beta features", "Dedicated account manager", "Email + Phone support with 20% off annually"]',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'price' => 95.00,
            'annual_price' => 912.00, // 20% discount
            'annual_discount_percentage' => 20,
            'duration_days' => 30,
            'features' => $this->createFeatures(25, 20, -1, 'dedicated_manager', true, true, 'advanced', 'advanced', 'custom_training', 'custom', true, true),
            'is_active' => true,
        ]);
    }
}
