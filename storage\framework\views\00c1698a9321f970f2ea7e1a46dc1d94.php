<!-- Reviews List -->
<div id="loader" class="loader-wrap absolute left-0 top-0 inset-0 flex items-center justify-center z-50 bg-white bg-opacity-10 backdrop-blur-sm">
    <div  class="loading"></div>
</div>
<div id="reviewList" class="flex-1 overflow-y-auto p-4 flex flex-col gap-4">
    <div id="reviewListIdDynamic"></div>
</div>
<input type="hidden" id="magicImage" value="<?php echo e(asset('ai-icon.svg')); ?>" />
<input type="hidden" id="reviewIcon" value="<?php echo e(asset('rm-icon.png')); ?>" />
<div class="flex justify-center items-center p-4 hidden" id="loadMoreDataButtonFilter">
    <button type="button" id="loadMoreBtnFilter"
        class="w-auto px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium"
        data-business-id="<?php echo e($business->id); ?>" data-account-id="<?php echo e(isset($businessId) ? $businessId : ''); ?>"
        data-page="2" data-has-more="<?php echo e(isset($reviews) ? 'true' : 'false'); ?>">
        Load More
        <span class="spinner hidden ml-2">
            <i class="fas fa-spinner fa-spin"></i>
        </span>
    </button>
</div>
<!-- Reply Modal Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        
        // DOM Elements
        const openGenerateModalBtn = document.getElementById('openGenerateModal');
        const generateReplyModal = document.getElementById('generateReplyModal');
        const closeModalBtn = document.querySelector('.close-modal');
        const generateBtn = document.getElementById('generateButton');
        const regenerateBtn = document.getElementById('regenerateButton');
        const copyBtn = document.getElementById('copyButton');
        const postReplyBtn = document.getElementById('postReplyButton');
        const responseTemplate = document.getElementById('responseTemplate');
        const specificInstructions = document.getElementById('specificInstructions');
        const manualReplyText = document.getElementById('manualReplyText');
        const generatedReplyContent = document.getElementById('generatedReplyContent');
        const generateReplyButton = document.getElementById('generateReplyButton');
        const manualPostReplyButton = document.getElementById('manualPostReplyButton');

        // Tab Elements
        const manualReplyTab = document.getElementById('manualReplyTab');
        const generateWithAITab = document.getElementById('generateWithAITab');
        const manualReplyContent = document.getElementById('manualReplyContent');
        const generateWithAIContent = document.getElementById('generateWithAIContent');

        // Modal Controls
        openGenerateModalBtn.addEventListener('click', function() {
            generateReplyModal.classList.remove('hidden');
        });

        closeModalBtn.addEventListener('click', function() {
            generateReplyModal.classList.add('hidden');
        });

        // Close modal when clicking outside of it
        generateReplyModal.addEventListener('click', function(e) {
            if (e.target === generateReplyModal) {
                generateReplyModal.classList.add('hidden');
            }
        });

        // Tab switching functionality
        manualReplyTab.addEventListener('click', function() {
            // Update tab buttons
            manualReplyTab.classList.add('active-tab');
            manualReplyTab.classList.add('bg-white');
            manualReplyTab.classList.add('shadow');
            generateWithAITab.classList.remove('active-tab');
            generateWithAITab.classList.remove('bg-white');
            generateWithAITab.classList.remove('shadow');

            // Show/hide content
            manualReplyContent.classList.remove('hidden');
            generateWithAIContent.classList.add('hidden');
        });

        generateWithAITab.addEventListener('click', function() {
            // Update tab buttons
            generateWithAITab.classList.add('active-tab');
            generateWithAITab.classList.add('bg-white');
            generateWithAITab.classList.add('shadow');
            manualReplyTab.classList.remove('active-tab');
            manualReplyTab.classList.remove('bg-white');
            manualReplyTab.classList.remove('shadow');

            // Show/hide content
            generateWithAIContent.classList.remove('hidden');
            manualReplyContent.classList.add('hidden');
        });

        // Simple Generate Reply
        generateReplyButton.addEventListener('click', function() {
            const template = responseTemplate.value;
            const instructions = specificInstructions.value;

            // Show loading state

            generateReplyButton.disabled = true;
            generateReplyButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Generating...';

            // Build the prompt based on the template
            let prompt;
            switch (template) {
                case 'positive':
                    prompt = "Generate a professional reply for a 5-star positive review. Make it warm, appreciative and welcoming.";
                    break;
                case 'neutral':
                    prompt = "Generate a professional reply for a 3-4 star review that has some positive aspects but also mentions improvements. Acknowledge both the positive feedback and address improvement areas.";
                    break;
                case 'negative':
                    prompt = "Generate a professional reply for a negative 1-2 star review. Be empathetic, apologetic, and offer a solution. Avoid being defensive.";
                    break;
                default:
                    prompt = "Generate a professional reply for a customer review.";
            }

            // Add custom instructions if provided
            if (instructions) {
                prompt += " " + instructions;
            }

            // Make the API request to generate reply
            fetch('/generate-reply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: 'gpt-3.5-turbo'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    generateReplyButton.disabled = false;
                    generateReplyButton.innerHTML = '<i class="fas fa-magic mr-1"></i> Generate Reply';

                    // Process and display the response
                    try {
                        const content = data.choices[0].message.content.trim();
                        manualReplyText.value = content;

                        // Enable post button
                        postReplyBtn.disabled = false;
                    } catch (error) {
                        manualReplyText.value = 'Error generating reply. Please try again.';
                        console.error('Error processing AI response:', error);
                    }
                })
                .catch(error => {
                    generateReplyButton.disabled = false;
                    generateReplyButton.innerHTML = '<i class="fas fa-magic mr-1"></i> Generate Reply';
                    manualReplyText.value = 'Failed to generate reply. Please try again.';
                    console.error('Error generating reply:', error);
                });
        });

        // Manual Post Reply
        manualPostReplyButton.addEventListener('click', function() {
            manualReplyText.value = '';
        });

        // Generate Reply With AI
        generateBtn.addEventListener('click', function() {
            const template = responseTemplate.value;
            const instructions = specificInstructions.value;

            // Show loading state

            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Generating...';

            // Build the prompt based on the template
            let prompt;
            switch (template) {
                case 'positive':
                    prompt = "Generate a professional reply for a 5-star positive review. Make it warm, appreciative and welcoming.";
                    break;
                case 'neutral':
                    prompt = "Generate a professional reply for a 3-4 star review that has some positive aspects but also mentions improvements. Acknowledge both the positive feedback and address improvement areas.";
                    break;
                case 'negative':
                    prompt = "Generate a professional reply for a negative 1-2 star review. Be empathetic, apologetic, and offer a solution. Avoid being defensive.";
                    break;
                default:
                    prompt = "Generate a professional reply for a customer review.";
            }

            // Add custom instructions if provided
            if (instructions) {
                prompt += " " + instructions;
            }

            // Make the API request to generate reply
            fetch('/generate-reply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: 'gpt-3.5-turbo'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    generateBtn.disabled = false;
                    generateBtn.innerHTML = '<i class="fas fa-magic mr-1"></i> Generate Reply';
                    console.log(data, "data json in reply")
                    // Process and display the response
                    try {
                        const content = data.choices[0].message.content.trim();
                        specificInstructions.value = content;

                        // Enable post button
                        postReplyBtn.disabled = false;
                    } catch (error) {
                        specificInstructions.value = 'Error generating reply. Please try again.';
                        console.error('Error processing AI response:', error);
                    }
                })
                .catch(error => {
                    generateBtn.disabled = false;
                    generateBtn.innerHTML = '<i class="fas fa-magic mr-1"></i> Generate Reply';
                    specificInstructions.value = 'Failed to generate reply. Please try again.';
                    console.error('Error generating reply:', error);
                });
        });

        // Regenerate Reply
        regenerateBtn.addEventListener('click', function() {
            generateBtn.click();
        });

        // Copy to Clipboard
        copyBtn.addEventListener('click', function() {
            const replyText = generatedReplyContent.textContent;
            if (replyText) {
                navigator.clipboard.writeText(replyText)
                    .then(() => {
                        // Show copied confirmation
                        const originalText = copyBtn.innerHTML;
                        copyBtn.innerHTML = '<i class="fas fa-check mr-1"></i> Copied!';
                        setTimeout(() => {
                            copyBtn.innerHTML = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy text: ', err);
                    });
            }
        });

        // Post Reply
        postReplyBtn.addEventListener('click', function() {
            const replyText = generatedReplyContent.textContent || manualReplyText.value;

            if (!replyText) {
                alert('Please generate or write a reply first');
                return;
            }

            // Here you could implement saving the reply or posting it to a review
            alert('Reply saved successfully!');

            // Add to saved replies
            const savedRepliesContainer = document.getElementById('savedReplies');

            // Clear placeholder if it exists
            if (savedRepliesContainer.querySelector('.text-center')) {
                savedRepliesContainer.innerHTML = '';
            }

            // Create and add new saved reply element
            const savedReplyElement = document.createElement('div');
            savedReplyElement.className = 'bg-gray-50 p-4 rounded-lg border border-gray-200';
            savedReplyElement.innerHTML = `
            <div class="flex justify-between items-start">
                <div class="flex-1 pr-4">
                    <p class="text-sm text-gray-700">${replyText.substring(0, 150)}${replyText.length > 150 ? '...' : ''}</p>
                </div>
                <div class="flex space-x-2">
                    <button class="text-xs bg-gray-200 px-2 py-1 rounded hover:bg-gray-300">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    <button class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded hover:bg-red-200">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

            savedRepliesContainer.prepend(savedReplyElement);

            // Close the modal
            generateReplyModal.classList.add('hidden');
        });



        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.querySelector('.fixed:not(.hidden)');
                if (modal) {
                    modal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            }
        });
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.reply-modal-tabs .btn');
        const tabContents = document.querySelectorAll('.tab-wrap > div');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // Remove 'active' from all buttons
                tabButtons.forEach(btn => btn.classList.remove('active', 'bg-white'));

                // Add 'active' and styling to clicked button
                button.classList.add('active', 'bg-white');

                // Hide all tab content
                tabContents.forEach(content => content.classList.add('hidden'));

                // Show the selected tab content
                const activeContent = document.getElementById(targetTab);
                if (activeContent) {
                    activeContent.classList.remove('hidden');
                }
            });
        });
    });
</script><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partial-reviews.blade.php ENDPATH**/ ?>