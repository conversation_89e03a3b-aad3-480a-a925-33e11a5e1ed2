<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Business Reviews</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/styles.css')); ?>" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body class="bg-white">
    <div class="flex flex-col lg:flex-row w-full bg-white overflow-hidden shadow-2xl h-auto lg:h-screen">
        <!-- Left Section -->
        <div class="flex-col justify-center w-2/2 lg:w-1/2 p-4 md:p-12 bg-[#0F1D36] text-white hidden lg:flex">
            <div class="max-w-[500px] mx-auto">
                <div class="flex items-center justify-center mb-0 md:mb-20 text-center">
                    <!-- <i class="fas fa-star-half-alt text-5xl text-white mr-4"></i>
                    <h1 class="text-4xl font-bold">ReviewMaster AI</h1> -->
                    <img src="<?php echo e(url('logo.png')); ?>" alt="ReviewMaster AI Logo" width="285" height="43" class="mr-2">
                </div>
                <h2 class="text-3xl font-bold mb-4 text-center hidden md:block">Welcome Back!</h2>
                <p class="text-purple-100 mb-12 text-center hidden md:block">Log in to your account to manage your reviews, respond to customers, and grow your business with AI-powered insights.</p>

                <!-- <div class="bg-purple-400/20 p-6 rounded-xl mb-8 hidden md:block">
                    <p class="text-sm text-purple-100"><i class="fas fa-clock mr-2"></i>We'll remember your device for 30 days.</p>
                </div> -->
            </div>
        </div>

        <!-- Right Section -->
        <div class="w-2/2 lg:w-1/2  flex flex-col justify-center h-screen">
            <div class="flex items-center lg:hidden justify-center p-4 text-center bg-[#0F1D36] absolute top-0 left-0 w-full z-10">
                <!-- <i class="fas fa-star-half-alt text-5xl text-white mr-4"></i>
                <h1 class="text-4xl font-bold">ReviewMaster AI</h1> -->
                <img src="<?php echo e(url('logo.png')); ?>" alt="ReviewMaster AI Logo" width="285" height="43" class="mr-2 w-[220px] h-auto md:w-[285px] md:h-[43px]">
            </div>
            <div class="max-w-full  h-[calc(100vh-65px)] md:h-auto overflow-auto pt-[65px] md:pt-[75px]">
                <div class="p-6 w-full max-w-[550px] mx-auto">
                    <h2 class="text-lg md:text-2xl font-bold text-gray-800 mb-2">Sign In</h2>
                    <p class="text-gray-600 mb-4 md:mb-8">Enter your credentials to access your account</p>
                    <form class="flex flex-col gap-4" method="POST" action="<?php echo e(route('login')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('auth.google')); ?>" type="button" class="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition flex items-center justify-center gap-2">
                                <i class="fa-brands fa-google"></i>
                                <span class="text-gray-700">Google</span>
                            </a>
                        </div>
                        <div class="relative py-2">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-300"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-white text-gray-500">Or continue with</span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2" for="email">Email Address</label>
                            <div class="relative">
                                <input type="email" id="email" name="email" placeholder="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                                <i class="fas fa-envelope absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            </div>
                            <?php if($errors->has('email')): ?>
                            <div class="bg-red-100 border border-red-200 text-red-700 px-4 py-1 mt-2 text-sm rounded relative">
                                <?php echo e($errors->first('email')); ?>

                            </div>
                            <?php endif; ?>
                        </div>

                        <div>
                            <div class="flex justify-between mb-2">
                                <label class="text-gray-700" for="password">Password</label>
                                <a href="<?php echo e(route('password.request')); ?>" class="text-[#006AFF] hover:text-purple-700 text-sm">Forgot password?</a>
                            </div>
                            <div class="relative">
                                <input type="password" id="password" name="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                                <i class="fas fa-eye absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer" id="togglePassword" onclick="togglePasswordVisibility()"></i>
                            </div>
                            <?php if($errors->has('password')): ?>
                            <div class="bg-red-100 border border-red-200 text-red-700 px-4 py-1 mt-2 text-sm rounded relative">
                                <?php echo e($errors->first('password')); ?>

                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="remember" name="remember" class="w-4 h-4 text-[#006AFF] border-gray-300 rounded focus:ring-purple-500">
                            <label for="remember" class="ml-2 text-gray-700">Remember me for 30 days</label>
                        </div>

                        <button type="submit" class="w-full bg-[#006AFF] hover:bg-blue-700  text-white font-semibold py-3 px-4 rounded-lg transition flex items-center justify-center gap-2">
                            Sign In
                            <i class="fas fa-arrow-right"></i>
                        </button>



                        <p class="text-center text-gray-600">
                            Don't have an account? <a href="<?php echo e(route('register')); ?>" class="text-[#006AFF] hover:text-purple-700 font-semibold">Sign up</a>
                        </p>

                        <div class="flex flex-wrap flex-col md:flex-row items-center text-center lg:text-inline justify-center gap-2 text-gray-500 text-sm">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-lock"></i>
                                <span>Secure Login</span>
                            </div>
                            <div class="w-1 h-1 bg-gray-400 rounded-full mx-1 hidden md:block"></div>
                            <span>Your connection to this site is encrypted and secure</span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const togglePassword = document.getElementById('togglePassword');
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                togglePassword.classList.add('fa-eye-slash');
                togglePassword.classList.remove('fa-eye');
            } else {
                passwordInput.type = 'password';
                togglePassword.classList.remove('fa-eye-slash');
                togglePassword.classList.add('fa-eye');
            }
        }
        var plan = '<?php echo e(isset($plan) ? $plan : 0); ?>';
        if (plan) {
            localStorage.setItem('plan', plan);
        }
    </script>
</body>

</html><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/auth/login.blade.php ENDPATH**/ ?>