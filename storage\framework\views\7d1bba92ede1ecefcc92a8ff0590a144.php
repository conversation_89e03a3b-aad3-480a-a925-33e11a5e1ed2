 

<?php $__env->startSection('title', 'Edit Email Template'); ?>
<?php $__env->startSection('page-title', 'Edit Email Template'); ?>

<?php $__env->startPush('styles'); ?>
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .variable-tag {
        @apply px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded cursor-pointer hover:bg-blue-200;
    }
    .ql-editor {
        min-height: 300px;
    }
    .preview-frame {
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        min-height: 400px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Email Template</h1>
            <p class="text-gray-600">Update the email template details and content</p>
        </div>
        <a href="<?php echo e(route('admin.email-templates.index')); ?>" 
           class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
            <i class="fas fa-arrow-left mr-2"></i>Back to Templates
        </a>
    </div>

    <form method="POST" action="<?php echo e(route('admin.email-templates.update', $emailTemplate)); ?>" id="templateForm">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Template Name *</label>
                                <input type="text" name="name" value="<?php echo e(old('name', $emailTemplate->name)); ?>" required
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Subject Line *</label>
                                <input type="text" name="subject" value="<?php echo e(old('subject', $emailTemplate->subject)); ?>" required
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea name="description" rows="2"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"</textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Email Content -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Email Content</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">HTML Content *</label>
                            <div id="html-editor" style="height: 300px;"><?php echo old('html_content', $emailTemplate->html_content); ?></div>
                            <input type="hidden" name="html_content" id="html_content">
                            <?php $__errorArgs = ['html_content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Plain Text Content</label>
                            <textarea id="editor" name="text_content" rows="4"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 <?php $__errorArgs = ['text_content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      placeholder="Plain text version of the email"><?php echo e(old('text_content', $emailTemplate->text_content)); ?></textarea>
                            <?php $__errorArgs = ['text_content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Variables -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Variables</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Available Variables</label>
                            <div id="available-variables" class="border border-gray-300 rounded-md p-3 min-h-[100px]">
                                <?php if($emailTemplate->available_variables && count($emailTemplate->available_variables) > 0): ?>
                                    <?php $__currentLoopData = $emailTemplate->available_variables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded mr-2 mb-2 cursor-pointer" 
                                              onclick="toggleRequired('<?php echo $variable; ?>')"><?php echo $variable; ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <p class="text-sm text-gray-500">No variables selected</p>
                                <?php endif; ?>
                            </div>
                            <input type="hidden" name="available_variables" id="available_variables_input" value="<?php echo e(json_encode($emailTemplate->available_variables)); ?>">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Variables</label>
                            <div id="required-variables" class="border border-gray-300 rounded-md p-3 min-h-[100px]">
                                <?php if($emailTemplate->required_variables && count($emailTemplate->required_variables) > 0): ?>
                                    <?php $__currentLoopData = $emailTemplate->required_variables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-block px-2 py-1 text-xs bg-red-100 text-red-800 rounded mr-2 mb-2 cursor-pointer" 
                                              onclick="toggleRequired('<?php echo e($variable); ?>')"><?php echo e($variable); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <p class="text-sm text-gray-500">No required variables</p>
                                <?php endif; ?>
                            </div>
                            <input type="hidden" name="required_variables" id="required_variables_input" value="<?php echo e(json_encode($emailTemplate->required_variables)); ?>">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Live Preview</h3>
                    </div>
                    <div class="p-6">
                        <iframe id="preview-frame preview" class="preview-frame w-full" src="about:blank"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.quilljs.com/1.3.6/quill.js"></script>
<script>
    // Initialize Quill editor
    var quill = new Quill('#html-editor', {
        theme: 'snow'
    });

    // Set initial content
    quill.root.innerHTML = <?php echo json_encode(old('html_content', $emailTemplate->html_content)); ?>;

    // Variable management
    let availableVariables = <?php echo json_encode($emailTemplate->available_variables); ?>;
    let requiredVariables = <?php echo json_encode($emailTemplate->required_variables); ?>;

    // Update variable display
    function updateVariableDisplay() {
        const availableDiv = document.getElementById('available-variables');
        const requiredDiv = document.getElementById('required-variables');

        // Update available variables
        if (availableVariables.length > 0) {
            availableDiv.innerHTML = availableVariables.map(variable => 
                `<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded mr-2 mb-2 cursor-pointer" 
                       onclick="toggleRequired('${variable}')"><?php echo $variable; ?></span>`
            ).join('');
        } else {
            availableDiv.innerHTML = '<p class="text-sm text-gray-500">No variables selected</p>';
        }

        // Update required variables
        if (requiredVariables.length > 0) {
            requiredDiv.innerHTML = requiredVariables.map(variable => 
                `<span class="inline-block px-2 py-1 text-xs bg-red-100 text-red-800 rounded mr-2 mb-2 cursor-pointer" 
                       onclick="toggleRequired('${variable}')"><?php echo $variable; ?></span>`
            ).join('');
        } else {
            requiredDiv.innerHTML = '<p class="text-sm text-gray-500">No required variables</p>';
        }

        // Update hidden inputs
        document.getElementById('available_variables_input').value = JSON.stringify(availableVariables);
        document.getElementById('required_variables_input').value = JSON.stringify(requiredVariables);
    }

    // Toggle required variable
    window.toggleRequired = function(variable) {
        const index = requiredVariables.indexOf(variable);
        if (index > -1) {
            requiredVariables.splice(index, 1);
        } else {
            requiredVariables.push(variable);
        }
        updateVariableDisplay();
    };

    // Form submission
    document.getElementById('templateForm').addEventListener('submit', function() {
        document.getElementById('html_content').value = quill.root.innerHTML;
    });

    // Preview functionality
    document.getElementById('preview-btn').addEventListener('click', function() {
        document.getElementById('html_content').value = quill.root.innerHTML;
        updatePreview();
    });

    function updatePreview() {
        const htmlContent = quill.root.innerHTML;
        const iframe = document.getElementById('preview-frame');
        const doc = iframe.contentDocument || iframe.contentWindow.document;
        
        doc.open();
        doc.write(htmlContent);
        doc.close();
    }
</script>
<?php $__env->stopPush(); ?>



<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/email-templates/edit.blade.php ENDPATH**/ ?>