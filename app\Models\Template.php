<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Template extends Model
{
    protected $table = 'templates';
    protected $fillable = ['business_id', 'title', 'description', 'template_text', 'ratings', 'sentiment', 'length'];

    /**
     * Get the business that owns the template
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }
}
