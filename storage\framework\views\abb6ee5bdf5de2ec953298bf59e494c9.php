<style>
    .error-border {
        border-color: #ef4444 !important;
        box-shadow: 0 0 0 1px #ef4444 !important;
    }

    /* Specific styling for different input types */
    input.error-border,
    select.error-border,
    textarea.error-border {
        border-color: #ef4444 !important;
        box-shadow: 0 0 0 1px #ef4444 !important;
    }

    /* Range slider error styling */
    input[type="range"].error-border {
        accent-color: #ef4444 !important;
    }

    /* Auto-reply block error styling */
    .auto-reply-block.has-error {
        background-color: #fef2f2 !important;
        border: 1px solid #fca5a5 !important;
    }

    .field-error {
        animation: fadeInError 0.3s ease-in;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .field-error::before {
        content: "⚠";
        font-size: 12px;
        color: #ef4444;
    }

    @keyframes fadeInError {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .global-error,
    .global-success {
        animation: slideInDown 0.4s ease-out;
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced error summary styling */
    .error-summary {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border-left: 4px solid #ef4444;
    }

    .success-summary {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-left: 4px solid #22c55e;
    }

    /* Template selection error highlighting */
    .template-error {
        border: 2px solid #ef4444 !important;
        background-color: #fef2f2 !important;
    }

    /* Validation state indicators */
    .validation-passed::after {
        content: "✓";
        color: #22c55e;
        font-weight: bold;
        margin-left: 8px;
    }

    .validation-failed::after {
        content: "✗";
        color: #ef4444;
        font-weight: bold;
        margin-left: 8px;
    }

    /* Countdown timer styling */
    #globalErrorCountdown,
    #globalSuccessCountdown {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        padding: 2px 6px;
        font-weight: 500;
        animation: countdownPulse 1s infinite;
    }

    @keyframes countdownPulse {

        0%,
        100% {
            transform: scale(1);
            opacity: 0.7;
        }

        50% {
            transform: scale(1.05);
            opacity: 1;
        }
    }

    /* Auto-remove fade animation */
    .fade-out {
        animation: fadeOut 0.5s ease-out forwards;
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
            transform: translateY(0);
        }

        to {
            opacity: 0;
            transform: translateY(-10px);
        }
    }

    /* Enhanced global message styling */
    .global-error {
        border-left: 4px solid #ef4444;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .global-success {
        border-left: 4px solid #22c55e;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
</style>

<div class="pb-6 w-full max-w-4xl mx auto">
    <!-- Settings Content -->
    <div id="settingContent" class="grid grid-cols-1 gap-8">
        <div class="custom-tabs" data-tabs>
            <!-- Tab Buttons -->
            <div class="tab-buttons flex w-full border border-gray-100 p-1 rounded-4xl">
                <button class="w-1/2 flex justify-center items-center custom-tab-btn px-3 py-2 md:p-3 rounded-4xl text-gray-500 text-xs md:text-sm lg:text-md font-medium" data-tab-target="aiConfiguration">
                    AI Configuration
                </button>
                <button class="w-1/2 flex justify-center items-center custom-tab-btn px-3 py-2 md:p-3 rounded-4xl text-gray-500 text-xs md:text-sm lg:text-md font-medium" data-tab-target="reviewChecking">
                    Review Checking
                </button>
            </div>

            <!-- Tab Content -->
            <div class="custom-tab-content">
                <div class="custom-tab-panel hidden pt-6" data-tab-id="aiConfiguration">
                    <!-- Left Column -->
                    <div>
                        <!-- AI Configuration Section -->
                        <div class="mb-6">
                            <!-- <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">AI Configuration</h4> -->

                            <div class="space-y-4">
                                <!-- AI Provider -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="aiProvider" class="block text-sm mb-1">AI Provider</label>
                                        <select id="aiProvider" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <option value="chatgpt">ChatGPT (OpenAI)</option>
                                        </select>
                                    </div>
                                    <!-- Model Version -->
                                    <div>
                                        <label for="aiModel" class="block text-sm mb-1">Model Version</label>
                                        <select id="aiModel" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                        </select>
                                    </div>
                                    <!-- Response Style Slider -->
                                    <div>
                                        <label for="responseStyle" class="block text-sm mb-1">Response Style</label>
                                        <div class="flex items-center">
                                            <span class="text-xs text-gray-500 w-16">Formal</span>
                                            <div class="flex-1 mx-2">
                                                <input
                                                    type="range"
                                                    id="responseStyle"
                                                    min="0"
                                                    max="100"
                                                    value="<?php echo e($business->setting->response_style ?? 50); ?>"
                                                    class="w-full"
                                                    style="accent-color: #6366f1;">
                                            </div>
                                            <span class="text-xs text-gray-500 w-16">Casual</span>
                                        </div>
                                        <div class="text-center text-xs text-gray-500 mt-1">
                                            <span id="responseStyleValue">Balanced (50%)</span>
                                        </div>
                                    </div>
                                    <!-- Response Length Slider -->
                                    <div>
                                        <label for="responseLength" class="block text-sm mb-1">Response Length</label>
                                        <div class="flex items-center">
                                            <span class="text-xs text-gray-500 w-16">Concise</span>
                                            <div class="flex-1 mx-2">
                                                <input type="range" id="responseLength" min="0" max="100" value="<?php echo e($business->setting->response_length ?? 50); ?>" class="w-full" style="accent-color: #6366f1;">
                                            </div>
                                            <span class="text-xs text-gray-500 w-16">Elaborative</span>
                                        </div>
                                        <div class="text-center text-xs text-gray-500 mt-1">
                                            <span id="responseLengthValue">Moderate (50%)</span>
                                        </div>
                                    </div>
                                    <!-- Sign-off Text -->
                                    <div>
                                        <label for="signOffText" class="block text-sm mb-1">Custom Sign-off Text</label>
                                        <input type="text" id="signOffText" placeholder="Thank you for your business!" value="<?php echo e($business->setting->custom_signoff_text); ?>" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                        <p class="text-xs text-gray-500 mt-1">This text will be added at the end of each reply</p>
                                    </div>
                                    <!-- Language Selection -->
                                    <div>
                                        <label for="aiLanguage" class="block text-sm mb-1">Response Language</label>
                                        <select id="aiLanguage" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <?php $__currentLoopData = languageOptionsAssoc(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($value); ?>" <?php echo e($value == $business->setting->response_language ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <!-- Custom Instructions -->
                                <div>
                                    <label for="customInstructions" class="block text-sm mb-1">Custom Instructions</label>
                                    <textarea id="customInstructions" rows="3" placeholder="Add any specific instructions for the AI..." class="w-full border border-gray-300  text-sm rounded-md px-3 py-2 resize-none">
                                    <?php echo e($business->setting->custom_instruction); ?>

                                    </textarea>
                                    <p class="text-xs text-gray-500 mt-1">These instructions will guide how the AI generates responses</p>
                                </div>
                            </div>
                        </div>

                        <!-- Business Configuration Section -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">Business Configuration</h4>

                            <div class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Timezone Selection -->
                                    <div>
                                        <label for="timezone" class="block text-sm mb-1">Business Timezone</label>
                                        <select id="timezone" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <option value="Pacific/Honolulu">(GMT-10:00) Hawaii</option>
                                            <option value="America/Anchorage">(GMT-09:00) Alaska</option>
                                            <option value="America/Los_Angeles">(GMT-08:00) Pacific Time (US & Canada)</option>
                                            <option value="America/Phoenix">(GMT-07:00) Arizona</option>
                                            <option value="America/Denver">(GMT-07:00) Mountain Time (US & Canada)</option>
                                            <option value="America/Chicago">(GMT-06:00) Central Time (US & Canada)</option>
                                            <option value="America/New_York" selected>(GMT-05:00) Eastern Time (US & Canada)</option>
                                            <option value="America/Sao_Paulo">(GMT-03:00) Sao Paulo</option>
                                            <option value="UTC">(GMT+00:00) UTC</option>
                                            <option value="Europe/London">(GMT+00:00) London</option>
                                            <option value="Europe/Paris">(GMT+01:00) Paris</option>
                                            <option value="Europe/Berlin">(GMT+01:00) Berlin</option>
                                            <option value="Europe/Athens">(GMT+02:00) Athens</option>
                                            <option value="Asia/Jerusalem">(GMT+02:00) Jerusalem</option>
                                            <option value="Asia/Riyadh">(GMT+03:00) Riyadh</option>
                                            <option value="Asia/Dubai">(GMT+04:00) Dubai</option>
                                            <option value="Asia/Kolkata">(GMT+05:30) Mumbai, New Delhi</option>
                                            <option value="Asia/Bangkok">(GMT+07:00) Bangkok</option>
                                            <option value="Asia/Shanghai">(GMT+08:00) Beijing</option>
                                            <option value="Asia/Tokyo">(GMT+09:00) Tokyo</option>
                                            <option value="Australia/Sydney">(GMT+10:00) Sydney</option>
                                            <option value="Pacific/Auckland">(GMT+12:00) Auckland</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">Used for scheduling and time-based features</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="custom-tab-panel hidden pt-6" data-tab-id="reviewChecking">
                    <!-- Right Column -->
                    <div class="disabled relative">
                        <!-- Review Checking Section -->
                        <div class="mb-6">
                            <!-- <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">Review Checking</h4> -->

                            <div class="space-y-4">

                                <div class="flex items-start">
                                    <input type="checkbox" id="reviewCheckingEnabled" name="auto_check_reviews" <?php echo e($business->setting->auto_check_reviews == 1 ? "checked" : ""); ?> class="relative top-1.5 mr-2">
                                    <label for="reviewCheckingEnabled">Automatically check for new reviews</label>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="reviewCheckingInterval" class="block text-sm mb-1">Check Interval (minutes)</label>
                                        <select id="reviewCheckingInterval" name="check_interval_minutes" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <option value="60" <?php echo e($business->setting->check_interval_minutes == 60 ? 'selected' : 'selected'); ?>>Every hour</option>
                                            <option value="180" <?php echo e($business->setting->check_interval_minutes == 180 ? 'selected' : ''); ?>>Every 3 hours</option>
                                            <option value="360" <?php echo e($business->setting->check_interval_minutes == 360 ? 'selected' : ''); ?>>Every 6 hours</option>
                                            <option value="720" <?php echo e($business->setting->check_interval_minutes == 720 ? 'selected' : ''); ?>>Every 12 hours</option>
                                            <option value="1440" <?php echo e($business->setting->check_interval_minutes == 1440 ? 'selected' : ''); ?>>Once a day</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">How often to check for new reviews</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Auto Reply Section -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between gap-4 flex-wrap mb-4 pb-2 border-b border-gray-200">
                                <h4 class="text-md font-medium">Auto Reply</h4>
                                <button class="w-auto flex-shrink-0 flex gap-1 items-center justify-center px-2 p-1 text-xs border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors sendAutoReplies"
                                    data-account-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>" data-business-id="<?php echo e($business['id']); ?>" title="Send Auto Replies" data-settings="1">

                                    <i class="fas fa-reply-all"></i>
                                    <span class="flex items-center"><span class="hidden md:block"> Preview Auto</span> Replies</span>
                                </button>
                            </div>
                            <div class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="auto_reply" id="autoReplyEnabled" class="mr-2" <?php echo e($business->setting->auto_reply == 1 ? 'checked' : ''); ?>>
                                        <label for="autoReplyEnabled">Automatically reply to new reviews</label>
                                    </div>

                                    <div class="flex items-center gap-1 flex-wrap justify-between">
                                        <label for="fromAutoReplyDate" class="block text-sm mb-1">From Auto Reply Date</label>
                                        <input type="date" id="fromAutoReplyDate" name="from_auto_reply_date" value="<?php echo e(optional($business->setting)->from_auto_reply_date ? \Carbon\Carbon::parse($business->setting->from_auto_reply_date)->format('Y-m-d') : ''); ?>" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="autoReplyConditionsLength" class="block text-sm mb-1">Review Length</label>
                                        <select id="autoReplyConditionsLength" name="review_length_filter" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <option value="any" <?php echo e($business->setting->sentiment_filter == 'any' ? 'selected' : ''); ?>>Any length</option>
                                            <option value="short" <?php echo e($business->setting->sentiment_filter == 'short' ? 'selected' : ''); ?>>Short reviews only</option>
                                            <option value="medium" <?php echo e($business->setting->sentiment_filter == 'medium' ? 'selected' : ''); ?>>Medium reviews only</option>
                                            <option value="long" <?php echo e($business->setting->sentiment_filter == 'long' ? 'selected' : ''); ?>>Long reviews only</option>
                                        </select>
                                    </div>
                                    <!-- Auto Reply Timing -->
                                    <div>
                                        <label for="autoReplyTimingMode" class="block text-sm mb-1">When to Send Auto Reply</label>
                                        <select id="autoReplyTimingMode" name="reply_timing" class="w-full border border-gray-300 text-sm rounded-md px-3 py-2">
                                            <option value="immediate" <?php echo e($business->setting->reply_timing == 'immediate' ? 'selected' : ''); ?>>Immediately</option>
                                            <option value="delay" <?php echo e($business->setting->reply_timing == 'delay' ? 'selected' : ''); ?>>After a delay</option>
                                            <option value="scheduled" <?php echo e($business->setting->reply_timing == 'scheduled' ? 'selected' : ''); ?>>At a scheduled time</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <?php
                                    $reviewItems = [
                                    ['rating' => 1, 'status' => 'on'],
                                    ['rating' => 2, 'status' => 'off'],
                                    ['rating' => 3, 'status' => 'on'],
                                    ['rating' => 4, 'status' => 'off'],
                                    ['rating' => 5, 'status' => 'on'],
                                    ];
                                    $savedAutoReplySettings = json_decode($business->setting->auto_reply_settings, true) ?? [];
                                    ?>
                                    <?php $__currentLoopData = $reviewItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                    $rating = $item['rating'];
                                    $radioName = "toggleOption-$rating";
                                    $templateName = "templateSelect-$rating";

                                    $saved = collect($savedAutoReplySettings)->firstWhere('reply_rating', $rating);
                                    $status = $saved['status'] ?? $item['status'];
                                    $selectedTemplate = $saved['template'] ?? '';

                                    ?>

                                    <div class="flex items-center gap-3 flex-wrap auto-reply-block rounded-md bg-gray-200 bg-opacity-50 p-4" data-rating="<?php echo e($rating); ?>">

                                        <div class="flex items-center gap-3 flex-wrap justify-between w-full">
                                            <!-- Star Rating -->
                                            <div class="flex items-center gap-1 order-2 md:order-none">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <button class="flex flex-col items-center justify-center p-2 border bg-white border-gray-200 rounded hover:bg-gray-50" title="star">
                                                    <?php echo $i <= $rating
                                                        ? '<i class="icon-star text-yellow-400"></i>'
                                                        : '<i class="icon-star text-gray-400"></i>'; ?>

                                                        </button>
                                                        <?php endfor; ?>
                                            </div>
                                            <!-- Toggle ON/OFF -->
                                            <div class="flex items-center flex-wrap gap-2 order-1 md:order-none">
                                                <label class="relative inline-flex items-center rounded-full cursor-pointer bg-gray-300 has-checked:bg-indigo-50 has-checked:text-green-900 has-checked:ring-indigo-200 dark:has-checked:bg-green-600 dark:has-checked:text-indigo-200 dark:has-checked:ring-green-900 ... ">
                                                    <input
                                                        class="sr-only peer"
                                                        type="checkbox"
                                                        name="<?php echo e($radioName); ?>"
                                                        value="on"
                                                        <?php echo e($status === 'on' ? 'checked' : ''); ?> >
                                                    <div class="peer text-xs rounded-full outline-none duration-100 after:duration-500 w-16 h-8 peer-focus:outline-none peer-focus:ring-4 after:content-['Off'] after:absolute after:outline-none after:rounded-full after:h-6 after:w-6 after:bg-white after:top-1 after:left-1 after:flex after:justify-center after:items-center after:font-bold peer-checked:after:translate-x-8 peer-checked:after:content-['On'] peer-checked:bg-green-600 peer-checked:text-green-600 peer-checked:after:border-white">
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        <?php
                                        $templatesForRating = $templatesListing[$rating] ?? [];
                                        ?>
                                        <!-- Template Select -->
                                        <div class="flex w-full flex-col">
                                            <select name="<?php echo e($templateName); ?>" class="w-full border border-gray-300 bg-white text-sm rounded-md px-3 py-1.5">
                                                <option value="">Select Template</option>
                                                <?php $__currentLoopData = $templatesForRating; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($template->title == 'Default Template'): ?>
                                                <?php else: ?>
                                                <option value="<?php echo e($template->id); ?>" <?php echo e($selectedTemplate == $template->id ? 'selected' : ''); ?>>
                                                    <?php echo e($template->title); ?>

                                                </option>
                                                <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Settings Button -->
    <div class="mt-4 flex items-center justify-between flex-wrap gap-4">
        <div class="flex gap-4 items-center w-full md:w-auto">
            <!-- <button id="exportSettingsBtn" class="w-full md:w-auto px-2 md:px-4 py-2 bg-indigo-500 text-white rounded-md text-xs md:text-sm font-medium hover:bg-indigo-700">
                <i class="fas fa-download mr-1"></i> Export Reviews
            </button> -->
            <button id="exportReviewBtn" class="w-full md:w-auto px-2 md:px-4 py-2 bg-indigo-500 text-white rounded-md text-xs md:text-sm font-medium hover:bg-indigo-700">
                <i class="fas fa-download mr-1"></i> Export Reviews
            </button>

            <button id="exportReviewAsPdf" class="w-full md:w-auto px-2 md:px-4 py-2 bg-indigo-500 text-white rounded-md text-xs md:text-sm font-medium hover:bg-indigo-700">
                <i class="fas fa-download mr-1"></i> Export Reviews PDF
            </button>
        </div>
        <div class="flex gap-4 items-center w-full md:w-auto border-t border-gray-200 md:border-t-0 pt-4 md:pt-0">
            <button id="saveSettingsBtn" class="w-full md:w-auto px-2 md:px-4 py-2 bg-green-600 text-white rounded-md text-xs md:text-sm  font-medium hover:bg-green-700">
                <i class="fas fa-save mr-1"></i> Save Settings
            </button>
        </div>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        document.querySelectorAll("[data-tabs]").forEach(tabsContainer => {
            const buttons = tabsContainer.querySelectorAll(".custom-tab-btn");
            const panels = tabsContainer.querySelectorAll(".custom-tab-panel");

            buttons.forEach(button => {
                button.addEventListener("click", () => {
                    const target = button.getAttribute("data-tab-target");

                    // Remove active styles from all
                    buttons.forEach(btn => btn.classList.remove("active", "text-indigo-500", "bg-indigo-500", "bg-opacity-10"));
                    panels.forEach(panel => panel.classList.add("hidden"));

                    // Activate current
                    button.classList.add("active", "text-indigo-500", "bg-indigo-500", "bg-opacity-10");
                    tabsContainer.querySelector(`[data-tab-id="${target}"]`).classList.remove("hidden");
                });
            });

            // Auto-activate first tab
            if (buttons.length > 0) {
                buttons[0].click();
            }
        });

        // Handle Response Style slider changes
        const responseStyleSlider = document.getElementById('responseStyle');
        const responseStyleValue = document.getElementById('responseStyleValue');

        if (responseStyleSlider && responseStyleValue) {
            function updateResponseStyleDisplay(value) {
                let displayText;
                if (value <= 20) {
                    displayText = `Very Formal (${value}%)`;
                } else if (value <= 40) {
                    displayText = `Formal (${value}%)`;
                } else if (value <= 60) {
                    displayText = `Balanced (${value}%)`;
                } else if (value <= 80) {
                    displayText = `Casual (${value}%)`;
                } else {
                    displayText = `Very Casual (${value}%)`;
                }
                responseStyleValue.textContent = displayText;
            }

            // Set initial value
            updateResponseStyleDisplay(responseStyleSlider.value);

            // Handle slider input
            responseStyleSlider.addEventListener('input', function() {
                updateResponseStyleDisplay(this.value);
            });
        }

        // Handle Response Length slider changes
        const responseLengthSlider = document.getElementById('responseLength');
        const responseLengthValue = document.getElementById('responseLengthValue');

        if (responseLengthSlider && responseLengthValue) {
            function updateResponseLengthDisplay(value) {
                let displayText;
                if (value <= 20) {
                    displayText = `Very Concise (${value}%)`;
                } else if (value <= 40) {
                    displayText = `Concise (${value}%)`;
                } else if (value <= 60) {
                    displayText = `Moderate (${value}%)`;
                } else if (value <= 80) {
                    displayText = `Detailed (${value}%)`;
                } else {
                    displayText = `Very Detailed (${value}%)`;
                }
                responseLengthValue.textContent = displayText;
            }

            // Set initial value
            updateResponseLengthDisplay(responseLengthSlider.value);

            // Handle slider input
            responseLengthSlider.addEventListener('input', function() {
                updateResponseLengthDisplay(this.value);
            });
        }

        // Save Settings button event listener
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Call the saveSettings function from manage-settings.js
                // which now includes validation
                if (typeof saveSettings === 'function') {
                    saveSettings();
                } else {
                    console.error('saveSettings function not found. Make sure manage-settings.js is loaded.');
                }
            });
        }
    });
</script><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partials/settings.blade.php ENDPATH**/ ?>