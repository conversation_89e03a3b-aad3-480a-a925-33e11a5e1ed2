@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
                <h3 class="text-lg font-medium text-gray-900">Analytics Overview</h3>
                <p class="mt-1 text-sm text-gray-600">Monitor your platform's key performance metrics</p>
            </div>
            <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
                <!-- Date Range Filter -->
                <select id="dateRange" onchange="updateFilters()" class="block w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="7_days" {{ $dateRange == '7_days' ? 'selected' : '' }}>Last 7 Days</option>
                    <option value="30_days" {{ $dateRange == '30_days' ? 'selected' : '' }}>Last 30 Days</option>
                    <option value="90_days" {{ $dateRange == '90_days' ? 'selected' : '' }}>Last 90 Days</option>
                    <option value="1_year" {{ $dateRange == '1_year' ? 'selected' : '' }}>Last Year</option>
                </select>

                <!-- Currency Filter -->
                <select id="currency" onchange="updateFilters()" class="block w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="all" {{ $currency == 'all' ? 'selected' : '' }}>All Currencies</option>
                    @foreach($filterOptions['currencies'] as $curr)
                        <option value="{{ $curr }}" {{ $currency == $curr ? 'selected' : '' }}>{{ strtoupper($curr) }}</option>
                    @endforeach
                </select>

                <!-- Plan Filter -->
                <select id="plan" onchange="updateFilters()" class="block w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="all" {{ $plan == 'all' ? 'selected' : '' }}>All Plans</option>
                    @foreach($filterOptions['plans'] as $planOption)
                        <option value="{{ $planOption->id }}" {{ $plan == $planOption->id ? 'selected' : '' }}>
                            {{ $planOption->name }} ({{ strtoupper($planOption->currency) }})
                        </option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Active Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-users text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($metrics['active_users']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-credit-card text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Subscriptions</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($metrics['active_subscriptions']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiring Subscriptions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($metrics['expiring_subscriptions']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Signups -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-plus text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">New Signups</dt>
                            <dd class="flex items-center">
                                <span class="text-lg font-medium text-gray-900">{{ number_format($metrics['new_signups']) }}</span>
                                @if($metrics['signup_growth'] > 0)
                                    <span class="ml-2 text-sm text-green-600">
                                        <i class="fas fa-arrow-up"></i> {{ $metrics['signup_growth'] }}%
                                    </span>
                                @elseif($metrics['signup_growth'] < 0)
                                    <span class="ml-2 text-sm text-red-600">
                                        <i class="fas fa-arrow-down"></i> {{ abs($metrics['signup_growth']) }}%
                                    </span>
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-money-bill-wave text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                            <dd class="flex items-center">
                                <span class="text-lg font-medium text-gray-900">₹{{ number_format($metrics['total_revenue'], 2) }}</span>
                                @if($metrics['revenue_growth'] > 0)
                                    <span class="ml-2 text-sm text-green-600">
                                        <i class="fas fa-arrow-up"></i> {{ $metrics['revenue_growth'] }}%
                                    </span>
                                @elseif($metrics['revenue_growth'] < 0)
                                    <span class="ml-2 text-sm text-red-600">
                                        <i class="fas fa-arrow-down"></i> {{ abs($metrics['revenue_growth']) }}%
                                    </span>
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Coupon Usage -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-pink-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-tags text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Coupon Usage</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($metrics['coupon_usage']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Businesses -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-building text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Businesses</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($metrics['total_businesses']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Team Members -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-teal-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-friends text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Team Members</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($metrics['active_team_members']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Revenue Per User -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">ARPU</dt>
                            <dd class="text-lg font-medium text-gray-900">₹{{ number_format($metrics['arpu'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
            <div class="h-64">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- Plan Distribution Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Distribution</h3>
            <div class="h-64">
                <canvas id="planChart"></canvas>
            </div>
        </div>

        <!-- User Growth Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
            <div class="h-64">
                <canvas id="userGrowthChart"></canvas>
            </div>
        </div>

        <!-- Subscription Status Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Status</h3>
            <div class="h-64">
                <canvas id="subscriptionStatusChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Additional Analytics Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Business Metrics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Business Analytics</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">New Businesses</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($businessMetrics['new_businesses']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Active Businesses</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($businessMetrics['active_businesses']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">With Teams</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($businessMetrics['businesses_with_teams']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Avg Team Size</span>
                    <span class="text-sm font-medium text-gray-900">{{ $businessMetrics['average_team_size'] }}</span>
                </div>
            </div>
        </div>

        <!-- Team Metrics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Team Analytics</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">New Members</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($teamMetrics['new_team_members']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Pending Invitations</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($teamMetrics['pending_invitations']) }}</span>
                </div>
                @foreach($teamMetrics['team_members_by_role'] as $role)
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">{{ ucfirst($role->role) }}</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($role->count) }}</span>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Activity Metrics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Activity Analytics</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Total Activities</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($activityMetrics['total_activities']) }}</span>
                </div>
                @foreach($activityMetrics['activities_by_category'] as $activity)
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">{{ ucfirst(str_replace('_', ' ', $activity->activity_category)) }}</span>
                    <span class="text-sm font-medium text-gray-900">{{ number_format($activity->count) }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Recent Activity Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Users -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Users</h3>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentUsers as $user)
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $user->name }}</p>
                                <p class="text-sm text-gray-500">{{ $user->email }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-4 text-center text-gray-500">
                        No recent users
                    </div>
                @endforelse
            </div>
            @if($recentUsers->count() > 0)
                <div class="px-6 py-3 bg-gray-50">
                    <a href="{{ route('admin.users.index') }}" class="text-sm text-indigo-600 hover:text-indigo-500">
                        View all users →
                    </a>
                </div>
            @endif
        </div>

        <!-- Recent Payments -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Payments</h3>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentPayments as $payment)
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $payment->user->name }}</p>
                                <p class="text-sm text-gray-500">{{ $payment->plan->name ?? 'N/A' }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">₹{{ number_format($payment->amount, 2) }}</p>
                                <p class="text-xs text-gray-500">{{ $payment->payment_date->diffForHumans() }}</p>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-4 text-center text-gray-500">
                        No recent payments
                    </div>
                @endforelse
            </div>
            @if($recentPayments->count() > 0)
                <div class="px-6 py-3 bg-gray-50">
                    <a href="{{ route('admin.payments.index') }}" class="text-sm text-indigo-600 hover:text-indigo-500">
                        View all payments →
                    </a>
                </div>
            @endif
        </div>

        <!-- Expiring Subscriptions -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Expiring Soon</h3>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($expiringSubscriptions as $subscription)
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $subscription->user->name }}</p>
                                <p class="text-sm text-gray-500">{{ $subscription->plan->name }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-red-600">
                                    {{ $subscription->expiry_date->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-4 text-center text-gray-500">
                        No expiring subscriptions
                    </div>
                @endforelse
            </div>
            @if($expiringSubscriptions->count() > 0)
                <div class="px-6 py-3 bg-gray-50">
                    <a href="{{ route('admin.subscriptions.index') }}" class="text-sm text-indigo-600 hover:text-indigo-500">
                        View all subscriptions →
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($chartData['revenue']->pluck('date')) !!},
            datasets: [{
                label: 'Revenue',
                data: {!! json_encode($chartData['revenue']->pluck('revenue')) !!},
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Plan Distribution Chart
    const planCtx = document.getElementById('planChart').getContext('2d');
    const planChart = new Chart(planCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode($chartData['plan_distribution']->pluck('name')) !!},
            datasets: [{
                data: {!! json_encode($chartData['plan_distribution']->pluck('count')) !!},
                backgroundColor: [
                    '#3B82F6',
                    '#10B981',
                    '#F59E0B',
                    '#EF4444',
                    '#8B5CF6'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    const userGrowthChart = new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($chartData['user_growth']->map(function($item) { return $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT); })) !!},
            datasets: [{
                label: 'New Users',
                data: {!! json_encode($chartData['user_growth']->pluck('users')) !!},
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Subscription Status Chart
    const subscriptionStatusCtx = document.getElementById('subscriptionStatusChart').getContext('2d');
    const subscriptionStatusChart = new Chart(subscriptionStatusCtx, {
        type: 'pie',
        data: {
            labels: {!! json_encode($chartData['subscription_status']->pluck('status')) !!},
            datasets: [{
                data: {!! json_encode($chartData['subscription_status']->pluck('count')) !!},
                backgroundColor: [
                    '#10B981', // Active - Green
                    '#F59E0B', // Expired - Yellow
                    '#EF4444', // Cancelled - Red
                    '#6B7280'  // Other - Gray
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Updated filter function
    function updateFilters() {
        const dateRange = document.getElementById('dateRange').value;
        const currency = document.getElementById('currency').value;
        const plan = document.getElementById('plan').value;

        const params = new URLSearchParams();
        params.append('date_range', dateRange);
        params.append('currency', currency);
        params.append('plan', plan);

        window.location.href = `{{ route('admin.dashboard') }}?${params.toString()}`;
    }

    // Legacy function for backward compatibility
    function updateDateRange() {
        updateFilters();
    }
</script>
@endpush
