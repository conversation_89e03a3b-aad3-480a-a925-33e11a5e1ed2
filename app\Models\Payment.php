<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    protected $fillable = [
        'user_id',
        'plan_id',
        'payment_type',
        'gateway_name',
        'transaction_id',
        'order_id',
        'coupon_code',
        'amount',
        'original_amount',
        'discount_amount',
        'tax_amount',
        'currency',
        'payment_status',
        'payment_method',
        'billing_cycle',
        'payment_date',
        'refund_id',
        'refund_date',
        'metadata'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'original_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'payment_date' => 'datetime',
        'refund_date' => 'datetime',
        'metadata' => 'array'
    ];

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }
}
