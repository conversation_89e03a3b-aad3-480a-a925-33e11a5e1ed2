<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AdminController extends Controller
{
    /**
     * Display a listing of admins
     */
    public function index(Request $request)
    {
        $query = Admin::with('creator');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        $admins = $query->orderBy('created_at', 'desc')->paginate(15);

        // Statistics
        $stats = [
            'total_admins' => Admin::count(),
            'active_admins' => Admin::where('is_active', true)->count(),
            'super_admins' => Admin::where('role', 'super_admin')->count(),
            'recent_logins' => Admin::where('last_login_at', '>=', now()->subDays(7))->count(),
        ];

        return view('admin.admins.index', compact('admins', 'stats'));
    }

    /**
     * Show the form for creating a new admin
     */
    public function create()
    {
        $roles = Admin::availableRoles();
        return view('admin.admins.create', compact('roles'));
    }

    /**
     * Store a newly created admin
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:admins',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|string|in:' . implode(',', array_keys(Admin::availableRoles())),
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            Admin::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'is_active' => $request->boolean('is_active', true),
                'created_by' => auth('admin')->id(),
            ]);

            return redirect()->route('admin.admins.index')
                           ->with('success', 'Admin created successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Error creating admin: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified admin
     */
    public function show(Admin $admin)
    {
        $admin->load(['creator', 'createdAdmins']);

        // Get admin activity stats
        $stats = [
            'created_admins' => $admin->createdAdmins()->count(),
            'last_login' => $admin->last_login_at,
            'account_age' => $admin->created_at->diffForHumans(),
            'permissions_count' => count($admin->getPermissions()),
        ];

        return view('admin.admins.show', compact('admin', 'stats'));
    }

    /**
     * Show the form for editing the specified admin
     */
    public function edit(Admin $admin)
    {
        // Prevent editing super admin by non-super admin
        if ($admin->isSuperAdmin() && !auth('admin')->user()->isSuperAdmin()) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'You cannot edit a super admin account');
        }

        $roles = Admin::availableRoles();
        return view('admin.admins.edit', compact('admin', 'roles'));
    }

    /**
     * Update the specified admin
     */
    public function update(Request $request, Admin $admin)
    {
        // Prevent editing super admin by non-super admin
        if ($admin->isSuperAdmin() && !auth('admin')->user()->isSuperAdmin()) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'You cannot edit a super admin account');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('admins')->ignore($admin->id),
            ],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|string|in:' . implode(',', array_keys(Admin::availableRoles())),
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        try {
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
                'is_active' => $request->boolean('is_active', true),
            ];

            // Only update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $admin->update($updateData);

            return redirect()->route('admin.admins.index')
                           ->with('success', 'Admin updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Error updating admin: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Toggle admin status
     */
    public function toggleStatus(Admin $admin)
    {
        // Prevent disabling super admin by non-super admin
        if ($admin->isSuperAdmin() && !auth('admin')->user()->isSuperAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot modify a super admin account'
            ], 403);
        }

        // Prevent admin from disabling themselves
        if ($admin->id === auth('admin')->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot disable your own account'
            ], 403);
        }

        try {
            $admin->update([
                'is_active' => !$admin->is_active
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Admin status updated successfully',
                'new_status' => $admin->is_active
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating admin status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete admin
     */
    public function destroy(Admin $admin)
    {
        // Prevent deleting super admin by non-super admin
        if ($admin->isSuperAdmin() && !auth('admin')->user()->isSuperAdmin()) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'You cannot delete a super admin account');
        }

        // Prevent admin from deleting themselves
        if ($admin->id === auth('admin')->id()) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'You cannot delete your own account');
        }

        try {
            $admin->delete();

            return redirect()->route('admin.admins.index')
                           ->with('success', 'Admin deleted successfully');
        } catch (\Exception $e) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'Error deleting admin: ' . $e->getMessage());
        }
    }

    /**
     * Show permissions page
     */
    public function permissions()
    {
        $roles = Admin::availableRoles();
        $permissions = $this->getAllPermissions();

        return view('admin.admins.permissions', compact('roles', 'permissions'));
    }

    /**
     * Export admins data
     */
    public function export(Request $request)
    {
        $query = Admin::with('creator');

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        $admins = $query->orderBy('created_at', 'desc')->get();

        $filename = 'admins_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($admins) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Role', 'Status', 'Last Login',
                'Created By', 'Created At'
            ]);

            // CSV data
            foreach ($admins as $admin) {
                fputcsv($file, [
                    $admin->id,
                    $admin->name,
                    $admin->email,
                    $admin->role,
                    $admin->is_active ? 'Active' : 'Inactive',
                    $admin->last_login_at ? $admin->last_login_at->format('Y-m-d H:i:s') : 'Never',
                    $admin->creator ? $admin->creator->name : 'System',
                    $admin->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get all available permissions
     */
    private function getAllPermissions()
    {
        return [
            'Dashboard' => [
                'dashboard.view' => 'View Dashboard'
            ],
            'Users' => [
                'users.view' => 'View Users',
                'users.create' => 'Create Users',
                'users.edit' => 'Edit Users',
                'users.delete' => 'Delete Users'
            ],
            'Subscriptions' => [
                'subscriptions.view' => 'View Subscriptions',
                'subscriptions.edit' => 'Edit Subscriptions'
            ],
            'Plans' => [
                'plans.view' => 'View Plans',
                'plans.create' => 'Create Plans',
                'plans.edit' => 'Edit Plans',
                'plans.delete' => 'Delete Plans'
            ],
            'Coupons' => [
                'coupons.view' => 'View Coupons',
                'coupons.create' => 'Create Coupons',
                'coupons.edit' => 'Edit Coupons',
                'coupons.delete' => 'Delete Coupons'
            ],
            'Payments' => [
                'payments.view' => 'View Payments',
                'payments.refund' => 'Process Refunds'
            ],
            'Analytics' => [
                'analytics.view' => 'View Analytics',
                'analytics.export' => 'Export Analytics'
            ],
            'Admin Management' => [
                'admins.view' => 'View Admins',
                'admins.create' => 'Create Admins',
                'admins.edit' => 'Edit Admins',
                'admins.delete' => 'Delete Admins'
            ],
            'Settings' => [
                'settings.view' => 'View Settings',
                'settings.edit' => 'Edit Settings'
            ],
            'Logs' => [
                'logs.view' => 'View API Logs',
                'logs.export' => 'Export Logs'
            ],
            'Email Templates' => [
                'email_templates.view' => 'View Email Templates',
                'email_templates.create' => 'Create Email Templates',
                'email_templates.edit' => 'Edit Email Templates',
                'email_templates.delete' => 'Delete Email Templates'
            ],
            'Site Settings' => [
                'site_settings.view' => 'View Site Settings',
                'site_settings.edit' => 'Edit Site Settings'
            ]
        ];
    }
}
