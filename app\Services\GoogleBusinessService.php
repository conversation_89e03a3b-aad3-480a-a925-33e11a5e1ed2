<?php

namespace App\Services;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;
use App\Models\BusinessAccount;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Services\ExternalApiLogger;

/**
 * GoogleBusinessService - Enhanced Token Management
 *
 * This service provides enhanced token validation and refresh functionality that supports:
 * 1. Session-based tokens for online users
 * 2. Database-based tokens for offline/scheduled jobs
 * 3. Automatic token expiration checking before API calls
 * 4. Dual storage (session + database) for refreshed tokens
 *
 * Usage Examples:
 *
 * // For online users (uses session tokens)
 * $service = new GoogleBusinessService($accessToken);
 * $isValid = $service->isTokenValid();
 * $newToken = $service->refreshAccessToken();
 *
 * // For scheduled jobs with specific BusinessAccount
 * $businessAccount = BusinessAccount::find($id);
 * $isValid = $service->isTokenValid(null, $businessAccount);
 * $newToken = $service->refreshAccessToken($businessAccount);
 *
 * // Comprehensive validation and refresh for scheduled jobs
 * $result = $service->validateAndRefreshBusinessToken($businessAccount);
 * if ($result['valid']) {
 *     $token = $result['token'];
 *     $wasRefreshed = $result['refreshed'];
 * }
 *
 * // Get BusinessAccount for external usage
 * $businessAccount = $service->getBusinessAccountById($id);
 * $businessAccount = $service->getBusinessAccountByUserId($userId);
 */
class GoogleBusinessService
{
    protected $accessToken;

    public function __construct($accessToken)
    {
        $this->accessToken = $accessToken;
    }

    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
    }

    /**
     * Check if a Google access token is valid
     *
     * @param string|null $accessToken The access token to validate (optional)
     * @param BusinessAccount|null $businessAccount The business account to check (optional)
     * @return bool True if token is valid, false otherwise
     */
    public function isTokenValid($accessToken = null, $businessAccount = null)
    {
        // Determine the access token to validate
        $tokenToValidate = $this->getAccessToken($accessToken, $businessAccount);

        if (!$tokenToValidate) {
            Log::warning('No access token available for validation');
            return false;
        }

        // Check if we have a business account and if token is expired based on expires_at
        if ($businessAccount && $businessAccount->token_expires_at) {
            if (Carbon::now()->greaterThan($businessAccount->token_expires_at)) {
                Log::info('Token expired based on expires_at', [
                    'business_account_id' => $businessAccount->id,
                    'expires_at' => $businessAccount->token_expires_at
                ]);
                return false;
            }
        }

        $start = microtime(true);
        $url = 'https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=' . $tokenToValidate;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                [],
                $response,
                $httpCode,
                'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }
        
        return $httpCode === 200;
    }

    /**
     * Get access token from various sources
     * Priority: provided token > session > business account database
     *
     * @param string|null $accessToken
     * @param BusinessAccount|null $businessAccount
     * @return string|null
     */
    private function getAccessToken($accessToken = null, $businessAccount = null)
    {
        // 1. Use provided access token if available
        if ($accessToken) {
            return $accessToken;
        }

        // 2. Check session for online users
        if (Session::has('business_google_token')) {
            return Session::get('business_google_token');
        }

        // 3. Use business account token from database
        if ($businessAccount && $businessAccount->business_google_token) {
            return $businessAccount->business_google_token;
        }

        // 4. Try to get business account from authenticated user
        if (Auth::check()) {
            $userBusinessAccount = BusinessAccount::where('user_id', Auth::id())->first();
            if ($userBusinessAccount && $userBusinessAccount->business_google_token) {
                return $userBusinessAccount->business_google_token;
            }
        }

        return null;
    }

    /**
     * Get refresh token from various sources
     * Priority: business account > session > authenticated user's business account
     *
     * @param BusinessAccount|null $businessAccount
     * @return string|null
     */
    private function getRefreshToken($businessAccount = null)
    {
        // 1. Use business account refresh token if provided
        if ($businessAccount && $businessAccount->business_refresh_token) {
            return $businessAccount->business_refresh_token;
        }

        // 2. Check session for online users
        if (Session::has('business_google_refresh_token')) {
            return Session::get('business_google_refresh_token');
        }

        // 3. Try to get business account from authenticated user
        if (Auth::check()) {
            $userBusinessAccount = BusinessAccount::where('user_id', Auth::id())->first();
            if ($userBusinessAccount && $userBusinessAccount->business_refresh_token) {
                return $userBusinessAccount->business_refresh_token;
            }
        }

        return null;
    }

    /**
     * Get business account for updating tokens
     *
     * @return BusinessAccount|null
     */
    private function getBusinessAccountForUpdate()
    {
        if (Auth::check()) {
            return BusinessAccount::where('user_id', Auth::id())->first();
        }

        return null;
    }

    /**
     * Get BusinessAccount by ID (for scheduled jobs and external usage)
     *
     * @param int $businessAccountId
     * @return BusinessAccount|null
     */
    public function getBusinessAccountById($businessAccountId)
    {
        return BusinessAccount::where('business_google_id', $businessAccountId)->orWhere('id', $businessAccountId)->first();
    }

    /**
     * Get BusinessAccount by user ID
     *
     * @param int $userId
     * @return BusinessAccount|null
     */
    public function getBusinessAccountByUserId($userId)
    {
        return BusinessAccount::where('user_id', $userId)->first();
    }

    /**
     * Validate and refresh token for a specific business account
     * This method is useful for scheduled jobs and external processes
     *
     * @param int|BusinessAccount $businessAccountOrId
     * @return array{valid: bool, token: string|null, refreshed: bool}
     */
    public function validateAndRefreshBusinessToken($businessAccountOrId)
    {
        $businessAccount = $businessAccountOrId instanceof BusinessAccount
            ? $businessAccountOrId
            : $this->getBusinessAccountById($businessAccountOrId);

        if (!$businessAccount) {
            return ['valid' => false, 'token' => null, 'refreshed' => false];
        }

        $isValid = $this->isTokenValid(null, $businessAccount);

        if ($isValid) {
            return [
                'valid' => true,
                'token' => $businessAccount->business_google_token,
                'refreshed' => false
            ];
        }

        // Token is invalid, try to refresh
        $newToken = $this->refreshAccessToken($businessAccount);

        if ($newToken) {
            return [
                'valid' => true,
                'token' => $newToken,
                'refreshed' => true
            ];
        }

        return ['valid' => false, 'token' => null, 'refreshed' => false];
    }

    /**
     * Refresh Google access token
     *
     * @param BusinessAccount|null $businessAccount The business account to refresh tokens for
     * @return string|false New access token on success, false on failure
     */
    public function refreshAccessToken($businessAccountOrId = null)
    {
        $businessAccount = $businessAccountOrId instanceof BusinessAccount
            ? $businessAccountOrId
            : $this->getBusinessAccountById($businessAccountOrId);
            
        // Get the refresh token from various sources
        $refreshToken = $this->getRefreshToken($businessAccount);

        if (!$refreshToken) {
            Log::error('No refresh token found for token refresh', [
                'business_account_id' => $businessAccount ? $businessAccount->id : null,
                'has_session_token' => Session::has('business_google_refresh_token'),
                'user_authenticated' => Auth::check()
            ]);
            return false;
        }

        // Get the business account to update
        $accountToUpdate = $businessAccount ?: $this->getBusinessAccountForUpdate();

        // Check if token is not yet expired (avoid unnecessary API calls)
        if ($accountToUpdate && $accountToUpdate->token_expires_at) {
            if (Carbon::now()->lessThan($accountToUpdate->token_expires_at->subMinutes(5))) {
                Log::info('Token still valid, skipping refresh', [
                    'business_account_id' => $accountToUpdate->id,
                    'expires_at' => $accountToUpdate->token_expires_at
                ]);
                return $accountToUpdate->business_google_token;
            }
        }

        $client = new \GuzzleHttp\Client();
        try {
            $response = $client->post('https://oauth2.googleapis.com/token', [
                'form_params' => [
                    'client_id' => env('GOOGLE_CLIENT_ID'),
                    'client_secret' => env('GOOGLE_CLIENT_SECRET'),
                    'refresh_token' => $refreshToken,
                    'grant_type' => 'refresh_token',
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if (!isset($data['access_token'])) {
                Log::error('Google API did not return an access token', [
                    'response' => $data,
                    'business_account_id' => $accountToUpdate ? $accountToUpdate->id : null
                ]);
                return false;
            }

            $newAccessToken = $data['access_token'];
            $newRefreshToken = $data['refresh_token'] ?? $refreshToken; // Keep existing if not provided
            $expiresIn = $data['expires_in'] ?? 3600;

            // Update session if user is online
            if (Auth::check()) {
                Session::put('business_google_token', $newAccessToken);
                Session::put('business_google_refresh_token', $newRefreshToken);
            }

            // Update database
            if ($accountToUpdate) {
                $accountToUpdate->update([
                    'business_google_token' => $newAccessToken,
                    'business_refresh_token' => $newRefreshToken,
                    'token_expires_at' => Carbon::now()->addSeconds($expiresIn)
                ]);

                Log::info('Google tokens refreshed successfully', [
                    'business_account_id' => $accountToUpdate->id,
                    'user_id' => $accountToUpdate->user_id,
                    'expires_in' => $expiresIn,
                    'new_expires_at' => $accountToUpdate->token_expires_at
                ]);
            }

            if (config("google.debug_mode")) {
                error_log("New access token generated: " . $newAccessToken);
            }

            return $newAccessToken;

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $errorMessage = $e->hasResponse()
                ? $e->getResponse()->getBody()->getContents()
                : $e->getMessage();
            Log::error('Failed to refresh access token', [
                'message' => $errorMessage,
                'code' => $e->getCode(),
                'business_account_id' => $accountToUpdate ? $accountToUpdate->id : null
            ]);
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to refresh access token', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'business_account_id' => $accountToUpdate ? $accountToUpdate->id : null
            ]);
            return false;
        }
    }

    public function getReviews($locationId, $businessId, $accessToken)
    {
        $result = $this->validateAndRefreshBusinessToken($businessId);        
        $accessToken = ($result['valid']) ?  $result['token'] : $accessToken;
        if(str_contains($locationId, '/')) {
            $locationId = explode("/", $locationId)[1];
        }
        $start = microtime(true);
        $url = config("google.google_mybusiness_api_v4") . "/accounts/{$businessId}/locations/{$locationId}/reviews";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                [],
                $response,
                $httpCode,
                $error ?: 'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call to fetch reviews:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        // if ($httpCode == 401) {
        //     $newAccessToken = $this->refreshAccessToken($businessId);
        //     if ($newAccessToken) {
        //         return $this->getReviews($locationId, $businessId, $newAccessToken);
        //     }
        // }

        return json_decode($response, true);
    }

    public function replyToReview($locationId, $businessId, $reviewId, $reply, $accessToken)
    {
        $start = microtime(true);
        $url = config("google.google_mybusiness_api_v4") . "/accounts/{$businessId}/locations/{$locationId}/reviews/{$reviewId}/reply";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'comment' => $reply
        ]));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                ['comment' => $reply],
                $response,
                $httpCode,
                $error ?: 'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        if ($httpCode == 401) {
            $newAccessToken = $this->refreshAccessToken();
            if ($newAccessToken) {
                return $this->replyToReview($locationId, $businessId, $reviewId, $reply, $newAccessToken);
            }
        }

        return json_decode($response, true);
    }

    public function deleteReviewReply($locationId, $businessId, $reviewId, $accessToken)
    {
        $start = microtime(true);
        $url = config("google.google_mybusiness_api_v4") . "/accounts/{$businessId}/locations/{$locationId}/reviews/{$reviewId}/reply";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                [],
                $response,
                $httpCode,
                $error ?: 'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        if ($httpCode == 401) {
            $newAccessToken = $this->refreshAccessToken();
            if ($newAccessToken) {
                return $this->deleteReviewReply($locationId, $businessId, $reviewId, $newAccessToken);
            }
        }

        return json_decode($response, true);
    }

    public function getReviewReplies($locationId, $businessId, $reviewId, $accessToken)
    {
        $start = microtime(true);
        $url = config("google.google_mybusiness_api_v4") . "/accounts/{$businessId}/locations/{$locationId}/reviews/{$reviewId}/replies";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                [],
                $response,
                $httpCode,
                $error ?: 'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        if ($httpCode == 401) {
            $newAccessToken = $this->refreshAccessToken();
            if ($newAccessToken) {
                return $this->getReviewReplies($locationId, $businessId, $reviewId, $newAccessToken);
            }
        }

        return json_decode($response, true);
    }

    public function updateReviewReply($locationId, $businessId, $reviewId, $replyId, $reply, $accessToken)
    {
        $start = microtime(true);
        $url = config("google.google_mybusiness_api_v4") . "/accounts/{$businessId}/locations/{$locationId}/reviews/{$reviewId}/replies/{$replyId}";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'comment' => $reply
        ]));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                ['comment' => $reply],
                $response,
                $httpCode,
                $error ?: 'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        if ($httpCode == 401) {
            $newAccessToken = $this->refreshAccessToken();
            if ($newAccessToken) {
                return $this->updateReviewReply($locationId, $businessId, $reviewId, $replyId, $reply, $newAccessToken);
            }
        }

        return json_decode($response, true);
    }

    /**
     * Make a generic request to Google API
     */
    public function makeRequest($url, $accessToken, $method = 'GET', $data = null)
    {
        $start = microtime(true);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json"
        ]);

        if ($method === 'POST' || $method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        try {
            ExternalApiLogger::logGoogleApi(
                $url,
                $data,
                $response,
                $httpCode,
                $error ?: 'success',
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $url,
                'error' => $e->getMessage()
            ]);
        }

        if (config("google.debug_mode")) {
            error_log("API response code: $httpCode");
            error_log("API response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));
            if ($error) {
                error_log("cURL error: $error");
            }
        }

        if ($httpCode == 401) {
            $newAccessToken = $this->refreshAccessToken();
            if ($newAccessToken) {
                return $this->makeRequest($url, $newAccessToken, $method, $data);
            } else {
                throw new \Exception("Unable to refresh access token");
            }
        }

        if ($error) {
            throw new \Exception("cURL Error: $error");
        }

        $responseData = json_decode($response, true);

        if ($httpCode >= 400) {
            $errorMessage = isset($responseData['error']['message'])
                ? $responseData['error']['message']
                : "API Error: HTTP Code $httpCode";
            throw new \Exception($errorMessage);
        }

        return $responseData;
    }

    /**
     * Get Google Business accounts
     */
    public function getAccounts($accessToken)
    {
        return $this->makeRequest(config('google.accounts_api'), $accessToken, 'GET');
    }

    /**
     * Get business locations for an account
     */
    public function getLocations($accountId, $accessToken, $readMask = null)
    {
        $url = str_replace('{accountId}', $accountId, config('google.google_location_api'));

        if ($readMask) {
            $url .= '?read_mask=' . $readMask;
        }

        return $this->makeRequest($url, $accessToken, 'GET');
    }

    /**
     * Get reviews with pagination support
     */
    public function getReviewsWithPagination($locationId, $businessId, $accessToken, $pageToken = null, $pageSize = 50)
    {
        $this->isTokenValid($accessToken);
        $url = config("google.google_mybusiness_api_v4") . "/accounts/{$businessId}/locations/{$locationId}/reviews";

        $params = [];
        if ($pageToken) {
            $params['pageToken'] = $pageToken;
        }
        if ($pageSize) {
            $params['pageSize'] = $pageSize;
        }

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $this->makeRequest($url, $accessToken, 'GET');
    }
}
