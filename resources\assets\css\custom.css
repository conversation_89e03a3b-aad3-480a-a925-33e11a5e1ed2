@import "tailwindcss";

/* ReviewMaster AI - Custom Styles */

body{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Font Setup */
.font-poppins {
  font-family: 'Poppins', sans-serif;
}

/* Custom Extension Sizing */
.popup-container {
  /* min-width: 800px; */
  min-height: 600px;
}
@media screen and (max-width: 992px)  {
  .popup-container{
    min-width: auto;
  }
}
/* Tab Navigation */
.tab-btn {
  position: relative;
  transition: all 0.3s ease;
  bottom: 0;
}

.tab-btn.active {
  color: rgba(79, 70, 229,1);
  border-bottom: 2px solid rgba(79, 70, 229,1) !important;
}

.tab-btn:not(.active) {
  border-bottom: 2px solid transparent;
}

.tab-btn:hover:not(.active) {
  border-bottom: 2px solid #E5E7EB;
}

/* Tab Content */
.tab-content {
  display: none;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

#tabScrollContainer .tab-btn.active{
    color: rgba(79, 70, 229,1);
    border-bottom: none ;
}
#tabScrollContainer .tab-btn:hover:not(.active) {
  border-bottom: none;
}
/* Business Card Styling */
.business-card {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.business-card:hover {
  background-color: #F3F4F6;
}

.business-card.selected {
  background-color: #EEF2FF;
  border-left: 3px solid rgba(79, 70, 229,1);
}

/* Review Card Styling */
.review-card {
  transition: all 0.2s ease;
}

.review-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.review-card.expanded {
  border-color: rgba(79, 70, 229,1);
}

/* Star Ratings */
.stars-container {
  display: inline-flex;
}

.star-rating-1 .fa-star:nth-child(-n+1),
.star-rating-2 .fa-star:nth-child(-n+2),
.star-rating-3 .fa-star:nth-child(-n+3),
.star-rating-4 .fa-star:nth-child(-n+4),
.star-rating-5 .fa-star:nth-child(-n+5) {
  color: #FBBF24;
}

/* Reply Generator */
.reply-generator {
  background-color: #F9FAFB;
  border-top: 1px solid #E5E7EB;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #F3F4F6;
}

::-webkit-scrollbar-thumb {
  background: #9CA3AF;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Skeleton Loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom Button Styles */
.btn-primary {
  background: linear-gradient(to right, rgba(79, 70, 229,1), #7C3AED);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: white;
  color: rgba(79, 70, 229,1);
  border: 1px solid rgba(79, 70, 229,1);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: #EEF2FF;
}

/* Badge Styles */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.badge-warning {
  background-color: rgba(251, 191, 36, 0.1);
  color: #FBBF24;
}

.badge-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}

.badge-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
}

/* Sentiment Badge Styles */
.sentiment-container {
  display: inline-flex;
  align-items: center;
}

.sentiment-badge {
  display: inline-block;
  padding: 0.15rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.65rem;
  font-weight: 500;
  text-transform: capitalize;
}

.sentiment-badge.positive {
  background-color: rgba(16, 185, 129, 0.15);
  color: #10B981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.sentiment-badge.negative {
  background-color: rgba(239, 68, 68, 0.15);
  color: #EF4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.sentiment-badge.neutral {
  background-color: rgba(107, 114, 128, 0.15);
  color: #6B7280;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.analyzing {
  font-size: 0.65rem;
  color: #6B7280;
  font-style: italic;
}

/* Glassmorphism Elements */
.glass {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Chart Containers */
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.header-scrolled {
  background-color: rgba(79, 70, 229, 0.8);
  backdrop-filter: blur(8px);
}

/* Hide scrollbar */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE 10+ */
  scrollbar-width: none;     /* Firefox */
}

.reply-modal-tabs .btn.active{
  background-color: #FFF;
}



.gradient-text {
  background-image: linear-gradient(90deg, #9035EB 0%, #2662EB 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

.iti{
  width: 100%;
}






.overlay{
  content: '';
  position: fixed;
  width: 100%;
  height: calc(100vh - 62px);
  top: 62px;
  background-color: rgba(0, 0, 0, 0.15);
  z-index: 0;
  transform: translateX(-100%);
  opacity: 0;
}
body.active .overlay, body.active .overlay{
  transform: translateX(0); 
  opacity: 1; 
}

/* Loader Style */
.loading {
  height: 0;
  width: 0;
  padding: 15px;
  border: 6px solid rgb(238, 242, 255);
  border-right-color: #4f46e5;
  border-radius: 22px;
  -webkit-animation: rotate 1s infinite linear;
  position: absolute;
  /* left: 50%;
  top: 50%; */
  z-index: 2;
}

@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}

/* hamburger icon*/

.three{
  padding: 80px 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #2c3e50;
  color: #ecf0f1;
  text-align: center;
}

.hamburger .line{
  width: 20px;
  height: 2px;
  background-color: #ecf0f1;
  display: block;
  margin: 5px auto;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.hamburger:hover{
  cursor: pointer;
}

body.active .hamburger .line:nth-child(2), body.menu-active .hamburger .line:nth-child(2){
  opacity: 0;
}

body.active .hamburger .line:nth-child(1), body.menu-active .hamburger .line:nth-child(1){
  -webkit-transform: translateY(5px) rotate(45deg);
  -ms-transform: translateY(5px) rotate(45deg);
  -o-transform: translateY(5px) rotate(45deg);
  transform: translateY(5px) rotate(45deg);
}

body.active .hamburger .line:nth-child(3), body.menu-active .hamburger .line:nth-child(3){
  -webkit-transform: translateY(-9px) rotate(-45deg);
  -ms-transform: translateY(-9px) rotate(-45deg);
  -o-transform: translateY(-9px) rotate(-45deg);
  transform: translateY(-9px) rotate(-45deg);
}

/*backdrop*/
.backdrop{
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
body.active .backdrop, body.menu-active .backdrop, body.filter-sidebar-active .backdrop{
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  z-index: 1;
}
body.active{
  overflow: hidden;
}
/* Responsive style */
@media screen and (max-width: 992px)  {
  /* info-sidebar */
  .info-sidebar {
    transform: translateX(-100%);
    z-index: 1000;
  }
  
  body.active .info-sidebar{
    transform: translateX(0);
    left: 0;
  }
  
}

/* Safe Area Insets */

.pt-safe {
    padding-top: env(safe-area-inset-top);
  }

  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .h-safe {
    height: calc(100vh - 156px - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }
@media screen and (max-width: 991.98px) {
  
}
@media screen and (max-width: 767.98px) {
  /* #sidebar {
    transform: translateX(-100%);
    height: calc(100vh - 62px);
    top: 62px;
    z-index: 1000;
  }

  body.sidebar-active #sidebar{
    transform: translateX(0);
  }  */
  .btn-primary{
    padding: 4px 10px;
  }
  .tab-btn{
    bottom: 0;
  }
  .tab-btn.active,  .tab-btn:not(.active){
    border-bottom: none !important;
  }
  .sidemenubar {
    height: calc(100% - 46px);
    top: 46px;
    bottom: 0;
  }
  #tabScrollContainer .tab-btn.active{
    color: rgba(79, 70, 229,1);
    border-bottom: none !important;
  }
  /* Side menu bar */
  .sidemenubar, #sidebar {
    transform: translateX(-100%);
    z-index: 1000;
  }

  body.menu-active .sidemenubar, body.filter-sidebar-active #sidebar{
    transform: translateX(0);
  }  
}
 

.tab-button {
  transition: all 0.3s ease;
  border-bottom-color: transparent;
  color: #6B7280;
}

.tab-button:hover {
  color: #4F46E5;
}

.tab-button.active-tab {
  border-bottom-color: #4F46E5;
  color: #4F46E5;
}

.tab-content {
  transition: opacity 0.3s ease;
}

/* Add this to your CSS file or <style> tag */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}