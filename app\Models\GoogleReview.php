<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GoogleReview extends Model
{
    protected $table = 'google_reviews';



    protected $fillable = [
        'review_id',
        'location_id',
        'account_id',
        'parent_id',
        'reviewer_name',
        'reviewer_photo',
        'star_rating',
        'comment',
        'created_at_google',
        'updated_at_google',
        'review_photos',
        'reviewType',
        'averageRating',
        'nextPageToken',
        'reply_comment',
        'reply_by',
        'reply_edited_by',
        'reply_template_id'
    ];

    public function getFormattedCreatedAtAttribute()
    {
        return \Carbon\Carbon::parse($this->created_at)->format('j F Y');
    }

    public function getReplyByNameAttribute()
    {
        if (is_null($this->reply_by)) {
            return null;
        }

        if ($this->reply_by == 0) {
            return 'Smart Reply System';
        }

        return $this->user->name ?? 'Unknown User';
    }

    protected $casts = [
        'review_photos' => 'array' // Cast the review_photos field as an array
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'reply_by')->select('id', 'name');
    }

    public function parent()
    {
        return $this->belongsTo(GoogleReview::class, 'parent_id');
    }

    public function selectedParentField()
    {
        return $this->belongsTo(GoogleReview::class, 'parent_id')->select(['id', 'reviewer_name']);
    }

    public function children()
    {
        return $this->hasMany(GoogleReview::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(GoogleReview::class, 'parent_id')->whereNotNull('parent_id');
    }

    public function repliesWithUser()
    {
        return $this->hasMany(GoogleReview::class, 'parent_id')
            ->whereNotNull('parent_id')
            ->with('user');
    }

    public function selectedReply()
    {
        return $this->hasMany(GoogleReview::class, 'parent_id');
    }

    /**
     * Get total reviews count optimized
     *
     * @param string $locationId
     * @param string $accountId
     * @return int
     */
    public static function totalReviews(string $locationId, string $accountId): int
    {
        $count = self::where([
            'location_id' => $locationId,
            'account_id' => $accountId
        ])
            ->whereNull('parent_id')
            ->count();

        Log::debug('Total reviews counted', [
            'location_id' => $locationId,
            'account_id' => $accountId,
            'count' => $count
        ]);

        return $count;
    }

    /**
     * Get the numeric value of a text rating
     *
     * @param string $rating
     * @return int
     */
    public static function getNumericRating(string $rating): int
    {
        return match (strtoupper($rating)) {
            'FIVE' => 5,
            'FOUR' => 4,
            'THREE' => 3,
            'TWO' => 2,
            'ONE' => 1,
            default => 0,
        };
    }

    /**
     * Calculate rating statistics for a business location with optimized queries
     *
     * @param string $locationId
     * @param string $accountId
     * @return array
     */
    public static function getRatingStats(string $locationId, string $accountId): array
    {
        Log::debug('Calculating rating statistics', [
            'location_id' => $locationId,
            'account_id' => $accountId
        ]);

        // Use database aggregation for better performance
        $ratingData = self::where([
            'location_id' => $locationId,
            'account_id' => $accountId
        ])
            ->whereNull('parent_id')
            ->whereNotNull('star_rating')
            ->selectRaw('
                star_rating,
                COUNT(*) as count,
                SUM(CASE
                    WHEN UPPER(star_rating) = "FIVE" THEN 5
                    WHEN UPPER(star_rating) = "FOUR" THEN 4
                    WHEN UPPER(star_rating) = "THREE" THEN 3
                    WHEN UPPER(star_rating) = "TWO" THEN 2
                    WHEN UPPER(star_rating) = "ONE" THEN 1
                    ELSE 0
                END) as total_numeric_rating
            ')
            ->groupBy('star_rating')
            ->get();

        $totalRatings = 0;
        $sumRatings = 0;
        $ratingCounts = [
            'FIVE' => 0,
            'FOUR' => 0,
            'THREE' => 0,
            'TWO' => 0,
            'ONE' => 0
        ];

        foreach ($ratingData as $data) {
            $rating = strtoupper(trim($data->star_rating));
            if (isset($ratingCounts[$rating])) {
                $ratingCounts[$rating] = (int) $data->count;
                $totalRatings += (int) $data->count;
                $sumRatings += (int) $data->total_numeric_rating;
            }
        }

        $avgRating = $totalRatings > 0 ? number_format($sumRatings / $totalRatings, 1) : 0;

        $result = [
            'avgRating' => $avgRating,
            'totalRatings' => $totalRatings,
            'ratingCounts' => $ratingCounts,
            'sumRatings' => $sumRatings
        ];

        Log::debug('Rating statistics calculated', $result);
        return $result;
    }

    /**
     * Calculate response rate and average response time with optimized queries
     *
     * @param string $locationId
     * @param string $accountId
     * @return array
     */
    public static function getResponseMetrics(string $locationId, string $accountId): array
    {
        Log::debug('Calculating response metrics', [
            'location_id' => $locationId,
            'account_id' => $accountId
        ]);

        // Get total reviews count efficiently
        $totalReviews = self::where([
            'location_id' => $locationId,
            'account_id' => $accountId
        ])
            ->whereNull('parent_id')
            ->count();

        if ($totalReviews === 0) {
            return [
                'responseRate' => 0,
                'avgResponseTime' => 0,
                'reviewsWithReplies' => 0,
                'totalReviews' => 0
            ];
        }

        // Get reviews with replies using optimized query with joins
        $reviewsWithRepliesData = DB::table('google_reviews as parent')
            ->join('google_reviews as reply', 'parent.id', '=', 'reply.parent_id')
            ->where('parent.location_id', $locationId)
            ->where('parent.account_id', $accountId)
            ->whereNull('parent.parent_id')
            ->whereNotNull('reply.parent_id')
            ->whereNotNull('parent.created_at_google')
            ->whereNotNull('reply.updated_at_google')
            ->select([
                'parent.id',
                'parent.created_at_google',
                'reply.updated_at_google'
            ])
            ->get();

        $reviewsWithReplies = $reviewsWithRepliesData->count();
        $totalResponseTime = 0;
        $validResponseCount = 0;

        // Calculate response times efficiently
        foreach ($reviewsWithRepliesData as $data) {
            try {
                $reviewDate = Carbon::parse($data->created_at_google);
                $replyDate = Carbon::parse($data->updated_at_google);

                // Only count positive response times (reply after review)
                if ($replyDate->greaterThan($reviewDate)) {
                    $responseTime = $reviewDate->diffInHours($replyDate);
                    $totalResponseTime += $responseTime;
                    $validResponseCount++;
                }
            } catch (\Exception $e) {
                Log::warning('Failed to parse dates for response time calculation', [
                    'review_id' => $data->id,
                    'created_at_google' => $data->created_at_google,
                    'updated_at_google' => $data->updated_at_google,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $responseRate = round(($reviewsWithReplies / $totalReviews) * 100);
        $avgResponseTime = $validResponseCount > 0 ? round($totalResponseTime / $validResponseCount) : 0;

        $result = [
            'responseRate' => $responseRate,
            'avgResponseTime' => $avgResponseTime,
            'reviewsWithReplies' => $reviewsWithReplies,
            'totalReviews' => $totalReviews,
            'validResponseCount' => $validResponseCount
        ];

        Log::debug('Response metrics calculated', $result);
        return $result;
    }

    /**
     * Get comprehensive review statistics optimized
     *
     * @param string $locationId
     * @param string $accountId
     * @return array
     */
    public static function getComprehensiveStats(string $locationId, string $accountId): array
    {
        Log::debug('Calculating comprehensive review statistics', [
            'location_id' => $locationId,
            'account_id' => $accountId
        ]);

        $totalReviews = self::totalReviews($locationId, $accountId);
        $ratingStats = self::getRatingStats($locationId, $accountId);
        $responseMetrics = self::getResponseMetrics($locationId, $accountId);

        $result = [
            'totalReviews' => $totalReviews,
            'avgRating' => $ratingStats['avgRating'],
            'ratingCounts' => $ratingStats['ratingCounts'],
            'responseRate' => $responseMetrics['responseRate'],
            'avgResponseTime' => $responseMetrics['avgResponseTime'],
            'reviewsWithReplies' => $responseMetrics['reviewsWithReplies'],
            'generatedAt' => now()->toISOString()
        ];

        Log::debug('Comprehensive statistics calculated', $result);
        return $result;
    }
}
