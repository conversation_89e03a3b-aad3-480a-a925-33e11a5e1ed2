<?php

namespace App\Services;

use App\Models\GoogleReview;
use Illuminate\Support\Carbon;

class ReviewService
{
    /**
     * Send replay to review
     *
     * @param int $reviewId
     * @return void
     */
    public static function generateReplay($data)
    {
        // Get all global templates (without business_id)
        // $existingReply = GoogleReview::where('parent_id', $data['parent_id'])->first();

        //     if ($existingReply) {
        //     return response()->json(['error' => 'A reply for this review already exists.'], 409);
        //     }

        GoogleReview::create([
            'review_id'         => NULL,
            'location_id'       => $data['location_id'],
            'account_id'        => $data['account_id'],
            'parent_id'         => $data['parent_id'],
            'reviewer_name'     => 'Admin',
            'reviewer_photo'    => NULL,
            'star_rating'       => NULL,
            'comment'           => $data['comment'],
            'reply_by'          => $data['reply_by'],
            'reply_edited_by'   => NULL,
            'reply_template_id' => $data['reply_template_id'],
            'created_at_google' => now(),
            'updated_at_google' => now(),
        ]);
    }
}
