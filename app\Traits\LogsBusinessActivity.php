<?php

namespace App\Traits;

use App\Services\BusinessActivityLogger;
use Illuminate\Support\Facades\Log;

trait LogsBusinessActivity
{
    /**
     * Log business activity with error handling
     * 
     * @param array $data Activity data
     * @param bool $showToast Whether to show toast message on failure
     * @return bool Success status
     */
    protected function logBusinessActivity(array $data, bool $showToast = false): bool
    {
        try {
            $result = BusinessActivityLogger::log($data);
            
            if (!$result && $showToast) {
                $this->addToastMessage('Activity logging failed, but your action was completed successfully.', 'warning');
            }
            
            return $result !== null;
            
        } catch (\Exception $e) {
            Log::error('LogsBusinessActivity: Failed to log activity', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            
            if ($showToast) {
                $this->addToastMessage('Activity logging failed, but your action was completed successfully.', 'warning');
            }
            
            return false;
        }
    }

    /**
     * Log business connection with toast message
     */
    protected function logBusinessConnectionActivity(int $businessId, int $userId, array $metadata = [], bool $showToast = true): bool
    {
        $result = BusinessActivityLogger::logBusinessConnection($businessId, $userId, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Business connected successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log business disconnection with toast message
     */
    protected function logBusinessDisconnectionActivity(int $businessId, int $userId, array $metadata = [], bool $showToast = true): bool
    {
        $result = BusinessActivityLogger::logBusinessDisconnection($businessId, $userId, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Business disconnected successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log reply sent activity with toast message
     */
    protected function logReplySentActivity(int $businessId, int $userId, array $metadata = [], bool $showToast = true): bool
    {
        $result = BusinessActivityLogger::logReplySent($businessId, $userId, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Reply sent successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log reviews fetch activity
     */
    protected function logReviewsFetchActivity(int $businessId, int $userId, int $reviewCount = 0, array $metadata = [], bool $showToast = false): bool
    {
        $result = BusinessActivityLogger::logReviewsFetch($businessId, $userId, $reviewCount, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Reviews fetched successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log template activity
     */
    protected function logTemplateActivity(string $action, int $businessId, int $userId, array $metadata = [], bool $showToast = false): bool
    {
        $result = BusinessActivityLogger::logTemplateActivity($action, $businessId, $userId, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Template action completed successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log team member activity
     */
    protected function logTeamMemberActivity(string $action, int $businessId, int $userId, array $metadata = [], bool $showToast = false): bool
    {
        $result = BusinessActivityLogger::logTeamMemberActivity($action, $businessId, $userId, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Team member action completed successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log API usage activity
     */
    protected function logApiUsageActivity(int $businessId, int $userId, string $endpoint, array $metadata = [], bool $showToast = false): bool
    {
        $result = BusinessActivityLogger::logApiUsage($businessId, $userId, $endpoint, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('API call completed successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log data export activity
     */
    protected function logDataExportActivity(int $businessId, int $userId, string $exportType, array $metadata = [], bool $showToast = false): bool
    {
        $result = BusinessActivityLogger::logDataExport($businessId, $userId, $exportType, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('Data exported successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Log system activity
     */
    protected function logSystemActivity(int $businessId, int $userId, string $activityType, string $description, array $metadata = [], bool $showToast = false): bool
    {
        $result = BusinessActivityLogger::logSystemActivity($businessId, $userId, $activityType, $description, $metadata);
        
        if (!$result && $showToast) {
            $this->addToastMessage('System action completed successfully, but activity logging failed.', 'warning');
        }
        
        return $result !== null;
    }

    /**
     * Add toast message to session
     * This method should be overridden in controllers that use different toast systems
     */
    protected function addToastMessage(string $message, string $type = 'info'): void
    {
        // Default implementation using Laravel session flash
        session()->flash('toast_message', $message);
        session()->flash('toast_type', $type);
        
        // You can also use other toast systems like:
        // toastr()->warning($message);
        // or add to a custom toast array in session
    }

    /**
     * Helper method to safely get business ID from request or route
     */
    protected function getBusinessIdFromRequest(): ?int
    {
        $request = request();
        
        // Try to get from route parameter
        if ($request->route('business_id')) {
            return (int) $request->route('business_id');
        }
        
        // Try to get from request input
        if ($request->input('business_id')) {
            return (int) $request->input('business_id');
        }
        
        // Try to get from route parameter with different name
        if ($request->route('id')) {
            return (int) $request->route('id');
        }
        
        return null;
    }

    /**
     * Helper method to safely get user ID
     */
    protected function getUserIdForLogging(): ?int
    {
        return auth()->id();
    }

    /**
     * Helper method to create metadata array with common request data
     */
    protected function createActivityMetadata(array $additional = []): array
    {
        $request = request();
        
        $metadata = [
            'route_name' => $request->route()?->getName(),
            'method' => $request->method(),
            'url' => $request->url(),
            'timestamp' => now()->toISOString()
        ];
        
        return array_merge($metadata, $additional);
    }
}
