<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('businesses', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            // This assumes your users table has a primary key `id`

            $table->string('location_name');
            $table->text('next_token')->nullable();
            $table->string('title');
            $table->enum('status', ['active', 'inactive'])->default('active');

            $table->string('primary_phone')->nullable();
            $table->json('additional_phones')->nullable();

            $table->string('region_code')->nullable();
            $table->string('language_code')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('administrative_area')->nullable();
            $table->string('locality')->nullable();
            $table->json('address_lines')->nullable();

            $table->string('website')->nullable();

            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();

            $table->text('profile_description')->nullable();
            $table->decimal('average_rating', 3, 2)->nullable();
            $table->unsignedInteger('total_reviews')->default(0);

            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('businesses');
    }
};
