<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->dropColumn(['min_rating', 'max_rating']);
            $table->tinyInteger('ratings')->unsigned()->after('template_text');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('templates', function (Blueprint $table) {
            $table->tinyInteger('min_rating')->unsigned()->nullable()->after('template_text');
            $table->tinyInteger('max_rating')->unsigned()->nullable()->after('min_rating');
            $table->dropColumn('ratings');
        });
    }
};
