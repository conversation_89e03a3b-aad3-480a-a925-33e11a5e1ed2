@extends('admin.layouts.app')

@section('title', 'Subscription Details')

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Subscription Details</h1>
            <p class="text-gray-600 mt-1">View and manage subscription information</p>
        </div>
        <div class="flex gap-3 mt-4 sm:mt-0">
            @if($subscription->status === 'active')
                <button onclick="extendSubscription()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>Extend
                </button>
                <button onclick="cancelSubscription()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-times mr-2"></i>Cancel
                </button>
            @endif
            <a href="{{ route('admin.subscriptions.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Subscriptions
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Subscription Overview -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Subscription Overview</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Subscription Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            @php
                                $statusClass = match($subscription->status) {
                                    'active' => 'bg-green-100 text-green-800',
                                    'expired' => 'bg-red-100 text-red-800',
                                    'cancelled' => 'bg-gray-100 text-gray-800',
                                    default => 'bg-yellow-100 text-yellow-800'
                                };
                            @endphp
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $statusClass }}">
                                {{ ucfirst($subscription->status) }}
                            </span>
                        </div>

                        <!-- Plan Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Plan</label>
                            <p class="text-sm text-gray-900">{{ $subscription->plan->name }}</p>
                        </div>

                        <!-- Start Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                            <p class="text-sm text-gray-900">{{ $subscription->created_at->format('M d, Y H:i') }}</p>
                        </div>

                        <!-- Expiry Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Expiry Date</label>
                            <p class="text-sm text-gray-900">
                                {{ $subscription->expires_at ? $subscription->expires_at->format('M d, Y H:i') : 'Never' }}
                            </p>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Amount Paid</label>
                            <p class="text-sm text-gray-900">
                                {{ $subscription->plan->currency_symbol }}{{ number_format($subscription->amount ?? $subscription->plan->price, 2) }}
                            </p>
                        </div>

                        <!-- Duration -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                            <p class="text-sm text-gray-900">{{ $subscription->plan->duration_days }} days</p>
                        </div>
                    </div>

                    @if($subscription->notes)
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                            <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{{ $subscription->notes }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Plan Features -->
            <div class="bg-white rounded-lg shadow mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Plan Features</h3>
                </div>
                <div class="p-6">
                    @if($subscription->plan->features)
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($subscription->plan->features as $feature)
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check text-green-500"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ $feature['title'] }}</p>
                                        <p class="text-sm text-gray-600">{{ $feature['value'] }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500">No features defined for this plan.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- User Information -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">User Information</h3>
                </div>
                <div class="p-6">
                    <div class="text-center mb-6">
                        <div class="mx-auto h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center mb-4">
                            <i class="fas fa-user text-2xl text-gray-600"></i>
                        </div>
                        <h4 class="text-lg font-medium text-gray-900">{{ $subscription->user->name }}</h4>
                        <p class="text-sm text-gray-600">{{ $subscription->user->email }}</p>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                            <p class="text-sm text-gray-900">#{{ $subscription->user->id }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Joined</label>
                            <p class="text-sm text-gray-900">{{ $subscription->user->created_at->format('M d, Y') }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Subscriptions</label>
                            <p class="text-sm text-gray-900">{{ $subscription->user->subscriptions()->count() }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $subscription->user->email_verified_at ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $subscription->user->email_verified_at ? 'Verified' : 'Unverified' }}
                            </span>
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <a href="{{ route('admin.users.show', $subscription->user) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors text-center block">
                            <i class="fas fa-user mr-2"></i>View User Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if($subscription->payment)
                <div class="bg-white rounded-lg shadow mt-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Payment ID</label>
                                <p class="text-sm text-gray-900">#{{ $subscription->payment->id }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                                <p class="text-sm text-gray-900">
                                    {{ $subscription->payment->currency_symbol }}{{ number_format($subscription->payment->amount, 2) }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                                <p class="text-sm text-gray-900">{{ $subscription->payment->created_at->format('M d, Y H:i') }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ ucfirst($subscription->payment->status) }}
                                </span>
                            </div>
                        </div>

                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <a href="{{ route('admin.payments.show', $subscription->payment) }}" 
                               class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors text-center block">
                                <i class="fas fa-credit-card mr-2"></i>View Payment Details
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Extend Subscription Modal -->
<div id="extendModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Extend Subscription</h3>
            </div>
            <form id="extendForm" class="p-6">
                <div class="mb-4">
                    <label for="extend_days" class="block text-sm font-medium text-gray-700 mb-2">Extend by (days)</label>
                    <input type="number" id="extend_days" name="extend_days" min="1" value="30" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mb-4">
                    <label for="extend_reason" class="block text-sm font-medium text-gray-700 mb-2">Reason (optional)</label>
                    <textarea id="extend_reason" name="extend_reason" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Reason for extending subscription..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeExtendModal()" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Extend Subscription
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function extendSubscription() {
    document.getElementById('extendModal').classList.remove('hidden');
}

function closeExtendModal() {
    document.getElementById('extendModal').classList.add('hidden');
}

function cancelSubscription() {
    if (confirm('Are you sure you want to cancel this subscription?')) {
        fetch(`/admin/subscriptions/{{ $subscription->id }}/cancel`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error cancelling subscription');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error cancelling subscription');
        });
    }
}

document.getElementById('extendForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        extend_days: formData.get('extend_days'),
        reason: formData.get('extend_reason')
    };
    
    fetch(`/admin/subscriptions/{{ $subscription->id }}/extend`, {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error extending subscription');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error extending subscription');
    });
});
</script>
@endpush
