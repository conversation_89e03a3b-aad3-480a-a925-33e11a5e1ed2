<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Plan;
use Illuminate\Container\Attributes\Log;
use Illuminate\Support\Facades\Cache;

class PlanController extends Controller
{
    public function getAllPlans(Request $request)
    {
        try {
            $currency = $request->get('currency', 'INR'); // Default to INR
            $type = $request->get('type', 'all'); // Default to all (all, free, paid)

            if(Cache::has('all_plans_'.$currency.'_'.$type)) {
                return Cache::get('all_plans_'.$currency.'_'.$type);
            }
            
            $plans = Plan::where('currency', $currency)
                        ->where('is_active', true)
                        ->orderBy('price')
                        ->get();
            foreach($plans as &$plan) {            
                $plan->features = $plan->long_description;            
            }
                    
            if ($plans) {
                $responseData = [
                    'status' => true,
                    'plans' => $plans,
                    'code' => 200
                ];
                Cache::put('all_plans_'.$currency.'_'.$type, $responseData, now()->addHours(1));
                return response()->json($responseData);
            } else {
                return response()->json(['status' => false, 'plans' => 'Not Found', 'code' => 404]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to retrieve all plans', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve all plans',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
                'code' => 500
            ], 500);
        }
    }

    public function getComparePlans(Request $request)
    {
        try {
            $currency = $request->get('currency', 'INR'); // Default to INR

            if(Cache::has('compare_plans_'.$currency)) {
                return Cache::get('compare_plans_'.$currency);
            }

            // Get plans for the specified currency, ordered by tier
            $plans = Plan::where('currency', $currency)
                        ->where('is_active', true)
                        ->orderBy('price')
                        ->get();

            if ($plans->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => 'No plans found for the specified currency',
                    'code' => 404
                ]);
            }

            // Build the comparison structure
            $comparison = [
                'currency' => $currency,
                'plans' => [],
                'features' => []
            ];

            // First, collect all unique feature keys to ensure consistent ordering
            $allFeatureKeys = [];
            foreach ($plans as $plan) {
                if ($plan->features && is_array($plan->features)) {
                    foreach ($plan->features as $feature) {
                        if (isset($feature['key']) && !in_array($feature['key'], $allFeatureKeys)) {
                            $allFeatureKeys[] = $feature['key'];
                        }
                    }
                }
            }

            // Define the desired feature order based on your table
            $featureOrder = [
                'business_connections_limit',
                'team_members_limit',
                'monthly_reply_limit',
                'smart_replies_enabled',
                'analytics_level',
                'ai_customization_level',
                'template_access',
                'scheduled_auto_replies_enabled',
                'settings_access_enabled',
                'data_export_enabled',
                'beta_features_enabled',
                'api_access_level',
                'support_level'
            ];

            // Build plan data
            foreach ($plans as $plan) {
                $planData = [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'short_description' => $plan->short_description,
                    'pricing' => [
                        'monthly' => [
                            'price' => $plan->price,
                            'formatted' => $plan->price == 0 ? 'Free' : $plan->currency_symbol . number_format($plan->price, 0) . ($currency === 'INR' ? ' +tax' : ''),
                            'display' => $plan->price == 0 ? 'free' : $plan->currency_symbol . number_format($plan->price, 0)
                        ],
                        'annual' => [
                            'discount_percentage' => $plan->annual_discount_percentage,
                            'discount_display' => $plan->annual_discount_percentage > 0 ? $plan->annual_discount_percentage . '%' : '-'
                        ]
                    ],
                    'features' => []
                ];

                // Add features in the specified order
                foreach ($featureOrder as $featureKey) {
                    $feature = $this->findFeatureByKey($plan->features, $featureKey);
                    if ($feature) {
                        $planData['features'][$featureKey] = [
                            'title' => $feature['title'],
                            'value' => $feature['value'],
                            'status' => $feature['status'] ?? 'enabled'
                        ];
                    }
                }

                $comparison['plans'][] = $planData;
            }

            // Build feature rows for easy table rendering
            foreach ($featureOrder as $featureKey) {
                $featureRow = [
                    'key' => $featureKey,
                    'title' => '',
                    'values' => []
                ];

                foreach ($plans as $plan) {
                    $feature = $this->findFeatureByKey($plan->features, $featureKey);
                    if ($feature) {
                        if (empty($featureRow['title'])) {
                            $featureRow['title'] = $feature['title'];
                        }
                        $featureRow['values'][] = [
                            'plan_name' => $plan->name,
                            'value' => $feature['value'],
                            'status' => $feature['status'] ?? 'enabled'
                        ];
                    }
                }

                if (!empty($featureRow['values'])) {
                    $comparison['features'][] = $featureRow;
                }
            }

            $responseData = [
                'status' => true,
                'data' => $comparison,
                'code' => 200
            ];
            Cache::put('compare_plans_'.$currency, $responseData, now()->addHours(1));
            return response()->json($responseData);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve plans for comparison', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve plans for comparison',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
                'code' => 500
            ], 500);
        }
    }

    /**
     * Helper method to find a feature by key in the features array
     */
    private function findFeatureByKey($features, $key)
    {
        if (!$features || !is_array($features)) {
            return null;
        }

        foreach ($features as $feature) {
            if (isset($feature['key']) && $feature['key'] === $key) {
                return $feature;
            }
        }

        return null;
    }
}
