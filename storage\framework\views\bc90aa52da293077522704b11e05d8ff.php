<?php $__env->startSection('title', 'Email Template Details'); ?>
<?php $__env->startSection('page-title', 'Email Template Details'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .status-badge {
        @apply px-2 py-1 text-xs font-medium rounded-full;
    }
    .status-active { @apply bg-green-100 text-green-800; }
    .status-inactive { @apply bg-red-100 text-red-800; }
    .category-badge {
        @apply px-2 py-1 text-xs font-medium rounded;
    }
    .category-system { @apply bg-blue-100 text-blue-800; }
    .category-custom { @apply bg-purple-100 text-purple-800; }
    .type-badge {
        @apply px-2 py-1 text-xs font-medium rounded bg-gray-100 text-gray-800;
    }
    .variable-tag {
        @apply px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded;
    }
    .preview-frame {
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        min-height: 400px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900"><?php echo e($emailTemplate->name); ?></h1>
            <div class="flex items-center space-x-2 mt-1">
                <span class="category-badge <?php echo e($emailTemplate->category === 'system' ? 'category-system' : 'category-custom'); ?>">
                    <?php echo e(ucfirst($emailTemplate->category)); ?>

                </span>
                <span class="type-badge">
                    <?php echo e(ucfirst(str_replace('_', ' ', $emailTemplate->type))); ?>

                </span>
                <span class="status-badge <?php echo e($emailTemplate->is_active ? 'status-active' : 'status-inactive'); ?>">
                    <?php echo e($emailTemplate->is_active ? 'Active' : 'Inactive'); ?>

                </span>
                <?php if($emailTemplate->is_default): ?>
                    <span class="px-2 py-1 text-xs font-medium rounded bg-yellow-100 text-yellow-800">
                        Default
                    </span>
                <?php endif; ?>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.email-templates.index')); ?>" 
               class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                <i class="fas fa-arrow-left mr-2"></i>Back to Templates
            </a>
            
            <?php if(auth('admin')->user()->hasPermission('email_templates.edit')): ?>
                <a href="<?php echo e(route('admin.email-templates.edit', $emailTemplate)); ?>" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-edit mr-2"></i>Edit Template
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <?php if($emailTemplate->description): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <p class="text-sm text-gray-900"><?php echo e($emailTemplate->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject Line</label>
                        <p class="text-sm text-gray-900"><?php echo e($emailTemplate->subject); ?></p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Created By</label>
                            <p class="text-sm text-gray-900">
                                <?php echo e($emailTemplate->creator ? $emailTemplate->creator->name : 'System'); ?>

                                <span class="text-gray-500 text-xs">
                                    (<?php echo e($emailTemplate->created_at->format('M j, Y g:i A')); ?>)
                                </span>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated By</label>
                            <p class="text-sm text-gray-900">
                                <?php echo e($emailTemplate->updater ? $emailTemplate->updater->name : 'System'); ?>

                                <span class="text-gray-500 text-xs">
                                    (<?php echo e($emailTemplate->updated_at->format('M j, Y g:i A')); ?>)
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Content -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Email Content</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <label class="block text-sm font-medium text-gray-700">HTML Content</label>
                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('admin.email-templates.preview', $emailTemplate)); ?>?format=html" 
                                   target="_blank"
                                   class="text-sm text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-external-link-alt mr-1"></i>Open in New Tab
                                </a>
                            </div>
                        </div>
                        <div class="border border-gray-300 rounded-md overflow-hidden">
                            <iframe id="html-preview" class="w-full h-96" srcdoc="<?php echo e($emailTemplate->html_content); ?>"></iframe>
                        </div>
                    </div>

                    <?php if($emailTemplate->text_content): ?>
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <label class="block text-sm font-medium text-gray-700">Plain Text Content</label>
                                <a href="<?php echo e(route('admin.email-templates.preview', $emailTemplate)); ?>?format=text" 
                                   target="_blank"
                                   class="text-sm text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-external-link-alt mr-1"></i>Open in New Tab
                                </a>
                            </div>
                            <div class="bg-gray-50 border border-gray-300 rounded-md p-4 font-mono text-sm whitespace-pre-wrap">
                                <?php echo e($emailTemplate->text_content); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Test Email -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Send Test Email</h3>
                </div>
                <div class="p-6">
                    <form id="test-email-form" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                            <input type="email" id="test_email" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="Enter email address to send test">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Custom Variables (Optional)</label>
                            <textarea id="test_variables" rows="3"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                      placeholder='{"user_name": "John Doe", "custom_value": "Test Value"}'></textarea>
                            <p class="text-xs text-gray-500 mt-1">Enter JSON format variables to use in the test email</p>
                        </div>

                        <div>
                            <button type="submit" id="send-test-btn"
                                    class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <i class="fas fa-paper-plane mr-2"></i>Send Test Email
                            </button>
                        </div>

                        <div id="test-result" class="hidden p-4 rounded-md mt-4"></div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Variables -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Template Variables</h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Available Variables</label>
                        <?php if($emailTemplate->available_variables && count($emailTemplate->available_variables) > 0): ?>
                            <div class="flex flex-wrap gap-2">
                                <?php $__currentLoopData = $emailTemplate->available_variables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="variable-tag"><?php echo $variable; ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-sm text-gray-500">No variables defined</p>
                        <?php endif; ?>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Required Variables</label>
                        <?php if($emailTemplate->required_variables && count($emailTemplate->required_variables) > 0): ?>
                            <div class="flex flex-wrap gap-2">
                                <?php $__currentLoopData = $emailTemplate->required_variables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                                        <?php echo $variable; ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-sm text-gray-500">No required variables</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Preview Options -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Preview Options</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Device Preview</label>
                        <div class="flex space-x-2">
                            <button type="button" id="preview-web" 
                                    class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm">
                                Web View
                            </button>
                            <button type="button" id="preview-mobile" 
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md text-sm">
                                Mobile View
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <?php if(auth('admin')->user()->hasPermission('email_templates.edit')): ?>
                        <a href="<?php echo e(route('admin.email-templates.edit', $emailTemplate)); ?>" 
                           class="block w-full bg-blue-600 text-white text-center px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-edit mr-2"></i>Edit Template
                        </a>
                    <?php endif; ?>

                    <?php if(auth('admin')->user()->hasPermission('email_templates.delete') && $emailTemplate->category !== 'system'): ?>
                        <form method="POST" action="<?php echo e(route('admin.email-templates.destroy', $emailTemplate)); ?>" 
                              onsubmit="return confirm('Are you sure you want to delete this template?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="block w-full bg-red-600 text-white text-center px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                <i class="fas fa-trash mr-2"></i>Delete Template
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile/Web preview toggle
    document.getElementById('preview-web').addEventListener('click', function() {
        this.classList.add('bg-indigo-600', 'text-white');
        this.classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-mobile').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-mobile').classList.remove('bg-indigo-600', 'text-white');
        document.getElementById('html-preview').style.width = '100%';
    });

    document.getElementById('preview-mobile').addEventListener('click', function() {
        this.classList.add('bg-indigo-600', 'text-white');
        this.classList.remove('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-web').classList.add('bg-gray-300', 'text-gray-700');
        document.getElementById('preview-web').classList.remove('bg-indigo-600', 'text-white');
        document.getElementById('html-preview').style.width = '375px';
    });

    // Test email form
    document.getElementById('test-email-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('test_email').value;
        let variables = {};
        
        try {
            const variablesText = document.getElementById('test_variables').value.trim();
            if (variablesText) {
                variables = JSON.parse(variablesText);
            }
        } catch (error) {
            showTestResult('error', 'Invalid JSON format for variables');
            return;
        }
        
        const sendBtn = document.getElementById('send-test-btn');
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
        
        fetch('<?php echo e(route('admin.email-templates.test', $emailTemplate)); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                test_email: email,
                variables: variables
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTestResult('success', data.message);
            } else {
                showTestResult('error', data.message || 'Failed to send test email');
            }
        })
        .catch(error => {
            showTestResult('error', 'An error occurred: ' + error.message);
        })
        .finally(() => {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Send Test Email';
        });
    });

    function showTestResult(type, message) {
        const resultDiv = document.getElementById('test-result');
        resultDiv.classList.remove('hidden', 'bg-green-100', 'text-green-800', 'bg-red-100', 'text-red-800');
        
        if (type === 'success') {
            resultDiv.classList.add('bg-green-100', 'text-green-800');
        } else {
            resultDiv.classList.add('bg-red-100', 'text-red-800');
        }
        
        resultDiv.innerHTML = message;
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/email-templates/show.blade.php ENDPATH**/ ?>