/**
 * ReviewMaster AI - Templates Manager
 * Handles the template management functionality
 */


/**
 * Initialize the templates manager
 */
function initializeTemplatesManager() {
  // Load templates from storage
  loadTemplates();
  
  // Set up event listeners for templates form
  setupTemplatesEventListeners();
  
  // Render templates list
  renderTemplatesList();

  // Check permissions for template management
  checkTemplatePermissions();
}

document.addEventListener('DOMContentLoaded', function () {
  const addTemplateBtn = document.getElementById('addTemplateBtn');
  if (addTemplateBtn) {
    addTemplateBtn.addEventListener('click', () => {
      // Check if user has permission to manage templates
      if (typeof permissionsHandler !== 'undefined' && !permissionsHandler.hasPermission('manage_templates')) {
        permissionsHandler.showPermissionDeniedMessage('You do not have permission to add templates');
        return;
      }
      validateSubscriptionAndUpgrade('template_access').then(validation => {
        if (validation.allowed) {
          // Original functionality - called only if validation passes
          if (typeof showAddTemplateModal === 'function') {
            showAddTemplateModal();
          }
        }
      });      
    });
  }
  initializeTemplatesManager();
})

/**
 * Load templates from storage
 */
async function loadTemplates() {
  try {
    // For the prototype, we'll just use localStorage
    const savedTemplates = localStorage.getItem('reviewMasterTemplates');
    
    let editButton = document.querySelectorAll('.editTemplateBtn')
      editButton.forEach(button => {
      button.addEventListener('click', () => {
        let indexVal = button.getAttribute('data-index');
        showEditTemplateModal(indexVal);
      });
    });


    let deleteButton = document.querySelectorAll('.deleteTemplateBtn')
      deleteButton.forEach(button => {
        button.addEventListener('click', () => {
          let indexVal = button.getAttribute('data-index');
          deleteTemplate(indexVal);
        });
      });

    // If no templates found, use defaults
    if (!savedTemplates) {
      // window.appTemplates = defaultTemplates;
      saveTemplates();
      return;
    }
    if(savedTemplates !== 'undefined')
    {
      // Parse saved templates
      window.appTemplates = JSON.parse(savedTemplates);
      
      console.log('Templates loaded:', window.appTemplates);
    }
  } catch (error) {
    console.error('Error loading templates:', error);
    
    // Use defaults if there's an error
    saveTemplates();
  }
}

/**
 * Save templates to storage
 */
function saveTemplates() {
  try {
    // For the prototype, we'll just use localStorage
    // In a real extension, this would use chrome.storage.sync
    localStorage.setItem('reviewMasterTemplates', JSON.stringify(window.appTemplates));
  } catch (error) {
    console.error('Error saving templates:', error);
  }
}

/**
 * Render the templates list
 */
function renderTemplatesList() {
  const templatesList = document.getElementById('templatesList');
  if (!templatesList) return;
  
  // Clear the list
  templatesList.innerHTML = '';
  
  // Add each template to the list
  window.appTemplates.forEach((template, index) => {
    const templateCard = document.createElement('div');
    templateCard.className = 'bg-white rounded-lg shadow-sm p-4 mb-4 border border-gray-200 hover:border-indigo-300 transition-colors';
    templateCard.dataset.templateId = template.id;
    
    // Get condition text
    const ratingText = template.conditions.rating.min === template.conditions.rating.max 
      ? `${template.conditions.rating.min} stars` 
      : `${template.conditions.rating.min}-${template.conditions.rating.max} stars`;
    
    const lengthText = template.conditions.length === 'any' 
      ? 'Any length' 
      : template.conditions.length.charAt(0).toUpperCase() + template.conditions.length.slice(1);
    
    const sentimentText = template.conditions.sentiment.charAt(0).toUpperCase() + template.conditions.sentiment.slice(1);
    
    // Check if user has permission to edit/delete templates
    const hasTemplatePermission = typeof permissionsHandler === 'undefined' || permissionsHandler.hasPermission('manage_templates');
    
    templateCard.innerHTML = `
      <div class="flex justify-between items-start mb-2">
        <h3 class="font-medium text-gray-900">${template.title}</h3>
        <div class="flex space-x-2">
          ${hasTemplatePermission ? `
            <button class="edit-template-btn text-indigo-600 hover:text-indigo-800" data-index="${index}" data-requires-permission="manage_templates">
              <i class="fas fa-edit"></i>
            </button>
            ${template.isDefault ? '' : `<button class="delete-template-btn text-red-600 hover:text-red-800" data-index="${index}" data-requires-permission="manage_templates">
              <i class="fas fa-trash-alt"></i>
            </button>`}
          ` : ''}
        </div>
      </div>
      <p class="text-sm text-gray-600 mb-2">${template.description}</p>
      <div class="text-xs text-gray-500 mb-2">
        <div class="flex flex-wrap gap-2 mb-1">
          <span class="inline-block bg-blue-100 text-blue-800 rounded px-2 py-1">
            <i class="fas fa-star mr-1"></i> ${ratingText}
          </span>
          <span class="inline-block bg-purple-100 text-purple-800 rounded px-2 py-1">
            <i class="fas fa-comment mr-1"></i> ${sentimentText}
          </span>
          <span class="inline-block bg-green-100 text-green-800 rounded px-2 py-1">
            <i class="fas fa-text-height mr-1"></i> ${lengthText}
          </span>
        </div>
      </div>
      <div class="bg-gray-50 p-2 rounded text-sm text-gray-700 mb-2 whitespace-pre-line">
        ${template.text}
      </div>
      
    `;
    
    // Add event listeners if user has permission
    if (hasTemplatePermission) {
      const editBtn = templateCard.querySelector('.edit-template-btn');
      if (editBtn) {
        editBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          showEditTemplateModal(index);
        });
      }
      
      if (!template.isDefault) {
        const deleteBtn = templateCard.querySelector('.delete-template-btn');
        if (deleteBtn) {
          deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            deleteTemplate(index);
          });
        }
      }
    }
    
    // Add to list
    templatesList.appendChild(templateCard);
  });
  
  // Add "Add New Template" button only if user has permission
  const hasTemplatePermission = typeof permissionsHandler === 'undefined' || permissionsHandler.hasPermission('manage_templates');
  if (hasTemplatePermission) {
    const addButtonContainer = document.createElement('div');
    addButtonContainer.className = 'text-center py-4';
    addButtonContainer.innerHTML = `
      <button id="addTemplateBtn" class="px-2 md:px-4 py-1 md:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors" data-requires-permission="manage_templates">
        <i class="fas fa-plus mr-2"></i> Add New Template
      </button>
    `;
    templatesList.appendChild(addButtonContainer);
    
    // Add event listener for add button
    const addBtn = addButtonContainer.querySelector('#addTemplateBtn');
    if (addBtn) {
      addBtn.addEventListener('click', () => {
        validateSubscriptionAndUpgrade('template_access').then(validation => {
          if (validation.allowed) {
            // Original functionality - called only if validation passes
            if (typeof showAddTemplateModal === 'function') {
              showAddTemplateModal();
            }
          }
        });
      });
    }
  }
}

/**
 * Show the add template modal
 */
function showAddTemplateModal() {  

  // Check if user has permission to manage templates
  if (typeof permissionsHandler !== 'undefined' && !permissionsHandler.hasPermission('manage_templates')) {
    permissionsHandler.showPermissionDeniedMessage('You do not have permission to add templates');
    return;
  }
  
  // Create modal if it doesn't exist
  let modal = document.getElementById('templateModal');
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'templateModal';
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm';
    modal.innerHTML = `
      <div class="absolute md:relative top-0 bg-white md:rounded-2xl shadow-lg max-w-3xl w-full h-full md:h-auto md:min-h-[400px] md:max-h-[90vh] flex flex-col">
        <form id="templateForm" class="flex flex-col h-full">
          <div class="flex justify-between items-center p-4 border-b border-gray-200">
            <h3 class="text-md md:text-lg font-semibold" id="modalTitle">Add New Template</h3>
            <button id="closeTemplateModal" class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="max-h-[calc(100vh-118px)] md:max-h-[calc(100vh-218px)] flex flex-col md:h-auto overflow-y-auto gap-4 p-4">
            <input type="hidden" id="templateId" value="">
            <input type="hidden" id="templateIndex" value="-1">
            
            <div>
              <label for="templateTitle" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
              <input type="text" id="templateTitle" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
            </div>
            
            <div>
              <label for="templateDescription" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <input type="text" id="templateDescription" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
            </div>
            
            <div>
              <label for="templateText" class="block text-sm font-medium text-gray-700 mb-1">Template Text</label>
              <textarea id="templateText" rows="5" class="w-full border border-gray-300 rounded-md px-3 py-2 resize-none" required></textarea>

              <div id="showPreviewText" class="mt-2 bg-gray-100 p-2 rounded text-sm text-gray-700 whitespace-pre-line hidden""></div>
              <button id="previewTextBtn" class="preview-button mt-2" type="button">Preview Template Text</button>
              
              <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Variables</label>
              <div class="flex flex-wrap gap-2 mb-2">
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="businessName">
                  {{businessName}}
                </button>
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="businessContact">
                  {{businessContact}}
                </button>
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="reviewerFirstName">
                  {{reviewerFirstName}}
                </button>
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="rating">
                  {{rating}}
                </button>
              </div>
              <p class="text-xs text-gray-500">Click on a variable to insert it into your template text</p>
            </div>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Rating Condition</label>
                <div class="flex items-center space-x-2">
                  <select id="ratingMin" class="border border-gray-300 rounded-md px-3 py-2">
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                  </select>
                  <span>stars</span>
                </div>
              </div>
              
              <div>
                <label for="sentiment" class="block text-sm font-medium text-gray-700 mb-1">Sentiment</label>
                <select id="sentiment" class="w-full border border-gray-300 rounded-md px-3 py-2">
                  <option value="positive">Positive</option>
                  <option value="neutral">Neutral</option>
                  <option value="negative">Negative</option>
                  <option value="any">Any</option>
                </select>
              </div>
            </div>
            
            <div>
              <label for="reviewLength" class="block text-sm font-medium text-gray-700 mb-1">Review Length</label>
              <select id="reviewLength" class="w-full border border-gray-300 rounded-md px-3 py-2">
                <option value="any">Any length</option>
                <option value="short">Short</option>
                <option value="medium">Medium</option>
                <option value="long">Long</option>
              </select>
            </div>
            
            </div>
            <div class="modal-footer flex justify-end border-t border-gray-200 p-4 gap-2">
              <button type="button" id="cancelTemplateBtn" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                Cancel
              </button>
              <button type="submit" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                Save Template
              </button>
            </div>
          </div>
        </form>
    `;
    document.body.appendChild(modal);
    
    // Add event listeners
    document.getElementById('closeTemplateModal').addEventListener('click', hideTemplateModal);
    document.getElementById('cancelTemplateBtn').addEventListener('click', hideTemplateModal);
    document.getElementById('templateForm').addEventListener('submit', saveTemplateForm);
    
    // Add event listeners for variable insert buttons
    const variableInsertBtns = document.querySelectorAll('.variable-insert-btn');
    variableInsertBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const templateText = document.getElementById('templateText');
        const variable = btn.dataset.variable;
        templateText.value += `{{${variable}}}`;
        templateText.focus();
      });
    });

    document.getElementById('previewTextBtn').addEventListener('click', () => {
      const previewDiv = document.getElementById('showPreviewText');
      const templateText = document.getElementById('templateText').value;

      if (!previewDiv.classList.contains('hidden')) {
        previewDiv.classList.add('hidden');
        previewDiv.innerHTML = '';
        return;
      }

      // Define the values to replace
      const replacements = {
        businessName: "World Class Business",
        businessContact: "8899665522",
        reviewerFirstName: "John Doe",
        rating: "5"
      };

      // Replace placeholders like {{businessName}} with actual values
      let finalText = templateText.replace(/{{(.*?)}}/g, (match, p1) => {
        const key = p1.trim();
        return replacements[key] ?? match;
      });

      
      previewDiv.innerHTML = `
        <div class="border border-gray-300 rounded-md p-3 mt-2 bg-gray-100 text-sm text-gray-800">
          ${finalText.replace(/\n/g, "<br>")}
        </div>
      `;
      previewDiv.classList.remove('hidden');
    });
  }
  
  // Reset form
  document.getElementById('modalTitle').textContent = 'Add New Template';
  document.getElementById('templateId').value = 'temp' + (new Date().getTime());
  document.getElementById('templateIndex').value = '-1';
  document.getElementById('templateTitle').value = '';
  document.getElementById('templateDescription').value = '';
  document.getElementById('templateText').value = '';
  document.getElementById('ratingMin').value = '1';
  document.getElementById('sentiment').value = 'any';
  document.getElementById('reviewLength').value = 'any';
  
  // Show modal
  modal.classList.remove('hidden');
}

function capitalizeEachWord(text) {
  return text
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}


function showEditTemplateModal(templateId) {

  editTemplateModal(templateId);
  fetch(`/templates/${templateId}`)
    .then(response => response.json())
    .then(data => {
      if (data.success && data.template) {
        const template = data.template;


        console.log(template, capitalizeEachWord(template.sentiment), capitalizeEachWord(template.length))


        document.getElementById('editTemplateId').value = template.id;
        document.getElementById('editTemplateIndex').value = templateId;
        document.getElementById('editTemplateTitle').value = template.title;
        document.getElementById('editTemplateDescription').value = template.description;
        document.getElementById('editTemplateText').value = template.template_text;
        document.getElementById('editRatingMin').value = template.ratings;
        document.getElementById('editSentiment').value = template.sentiment.toLowerCase();;
        document.getElementById('editReviewLength').value = template.length.toLowerCase();

        // Show the modal
        const modal = document.getElementById('editTemplateModal');
        if (modal) {
          modal.classList.remove('hidden');
        } else {
          console.error("Modal not found in DOM");
        }

      } else {
        alert('Template data could not be loaded.');
      }
    })
    .catch(error => {
      console.error('Error fetching template data:', error);
    });
}

/**
 * Show the edit template modal
 */

function editTemplateModal(index) {
  // Show the modal first
  let modal = document.getElementById('editTemplateModal');
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'editTemplateModal';
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm';
    modal.innerHTML = `
      <div class="absolute md:relative top-0 bg-white md:rounded-2xl shadow-lg max-w-3xl w-full h-full md:h-auto md:min-h-[400px] md:max-h-[90vh] flex flex-col">
        <form id="editTemplateForm">
          <div class="flex justify-between items-center p-4 border-b border-gray-200">
            <h3 class="text-md md:text-lg font-semibold" id="editModalTitle">Edit Template</h3>
            <button id="editCancelTemplateBtn" class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="max-h-[calc(100vh-118px)] flex flex-col md:h-auto overflow-y-auto gap-4 p-4">
            <input type="hidden" id="editTemplateId" value="">
            <input type="hidden" id="editTemplateIndex" value="-1">
            
            <div>
              <label for="templateTitle" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
              <input type="text" id="editTemplateTitle" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
            </div>
            
            <div>
              <label for="templateDescription" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <input type="text" id="editTemplateDescription" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
            </div>
            
            <div>
              <label for="templateText" class="block text-sm font-medium text-gray-700 mb-1">Template Text</label>
              <textarea id="editTemplateText" rows="5" class="w-full border border-gray-300 rounded-md px-3 py-2 resize-none" required></textarea>

              <div id="showEditPreviewText" class="mt-2 bg-gray-100 p-2 rounded text-sm text-gray-700 whitespace-pre-line hidden""></div>
              <button id="previewEditTextBtn" class="preview-button mt-2" type="button">Preview Template Text</button>
              
              <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Variables</label>
              <div class="flex flex-wrap gap-2 mb-2">
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="businessName">
                  {{businessName}}
                </button>
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="businessContact">
                  {{businessContact}}
                </button>
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="reviewerFirstName">
                  {{reviewerFirstName}}
                </button>
                <button type="button" class="variable-insert-btn px-2 py-1 bg-gray-200 text-xs rounded hover:bg-gray-300" data-variable="rating">
                  {{rating}}
                </button>
              </div>
              <p class="text-xs text-gray-500">Click on a variable to insert it into your template text</p>
            </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Rating Condition</label>
                <div class="flex items-center space-x-2">
                  <select id="editRatingMin" class="border border-gray-300 rounded-md px-3 py-2">
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                  </select>
                  <span>stars</span>
                </div>
              </div>
              
              <div>
                <label for="sentiment" class="block text-sm font-medium text-gray-700 mb-1">Sentiment</label>
                <select id="editSentiment" class="w-full border border-gray-300 rounded-md px-3 py-2">
                  <option value="positive">Positive</option>
                  <option value="neutral">Neutral</option>
                  <option value="negative">Negative</option>
                  <option value="any">Any</option>
                </select>
              </div>
            </div>
            
            <div>
              <label for="reviewLength" class="block text-sm font-medium text-gray-700 mb-1">Review Length</label>
              <select id="editReviewLength" class="w-full border border-gray-300 rounded-md px-3 py-2">
                <option value="any">Any length</option>
                <option value="short">Short</option>
                <option value="medium">Medium</option>
                <option value="long">Long</option>
              </select>
            </div>
            
            </div>
            <div class="modal-footer flex justify-end border-t border-gray-200 p-4 gap-2">
              <button type="button" id="editCancelTemplateBtn1" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                Cancel
              </button>
              <button type="submit" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                Save Template
              </button>
            </div>
        </div>
      </form>
    `;
    document.body.appendChild(modal);
    
    // Add event listeners
    document.getElementById('closeTemplateModal')?.addEventListener('click', () => hideEditTemplateModal(index));
    document.getElementById('editCancelTemplateBtn')?.addEventListener('click', () => hideEditTemplateModal(index));
    document.getElementById('editCancelTemplateBtn1')?.addEventListener('click', () => hideEditTemplateModal(index));
    document.getElementById('editTemplateForm')?.addEventListener('submit', (e) => {
      e.preventDefault(); 

      const editTemplate = {
        id: document.getElementById('editTemplateId').value,
        index: document.getElementById('editTemplateIndex').value,
        title: document.getElementById('editTemplateTitle').value,
        description: document.getElementById('editTemplateDescription').value,
        text: document.getElementById('editTemplateText').value,
        ratings: document.getElementById('editRatingMin').value,
        sentiment: document.getElementById('editSentiment').value,
        length: document.getElementById('editReviewLength').value
      };
    
      updateTemplateForm(editTemplate);
    });
    
    // Add event listeners for variable insert buttons
    const variableInsertBtns = document.querySelectorAll('.variable-insert-btn');
    const templateText = document.getElementById('editTemplateText');
    variableInsertBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        
        const variable = btn.dataset.variable;
        templateText.value += `{{${variable}}}`;
        templateText.focus();
      });
    });

    document.getElementById('previewEditTextBtn').addEventListener('click', () => {
      const previewDiv = document.getElementById('showEditPreviewText');
      
      if (!previewDiv.classList.contains('hidden')) {
        previewDiv.classList.add('hidden');
        previewDiv.innerHTML = '';
        return;
      }

      // Define the values to replace
      const replacements = {
        businessName: "World Class Business",
        businessContact: "8899665522",
        reviewerFirstName: "John Doe",
        rating: "5"
      };

      // Replace placeholders like {{businessName}} with actual values
      let finalText = templateText.value.replace(/{{(.*?)}}/g, (match, p1) => {
        const key = p1.trim();
        return replacements[key] ?? match;
      });

      
      previewDiv.innerHTML = `
        <div class="border border-gray-300 rounded-md p-3 mt-2 bg-gray-100 text-sm text-gray-800">
          ${finalText.replace(/\n/g, "<br>")}
        </div>
      `;
      previewDiv.classList.remove('hidden');
    });


  }
  
  // Reset form
  
  
  // Show modal
  modal.classList.remove('hidden');
}


function updateTemplateForm(data)
{
  fetch('/templates/' + data.id, { method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
    },
    body: JSON.stringify({ title: data.title, description: data.description, template_text: data.text, ratings: data.ratings, length: data.length, sentiment: data.sentiment }),
  })
    .then(response => response.json())
    .then(data => {
      if (data.success === true) {
        hideEditTemplateModal(data.id);
      } else {
        // console.error('Failed to update template');
      }
    })
    .catch(error => console.error('Error:', error));
}

/**
 * Hide the template modal
 */
function hideTemplateModal() {
  const modal = document.getElementById('templateModal');
  if (modal) {
    modal.classList.add('hidden');
  }
}

function hideEditTemplateModal(index) {
  console.log(index)
  const modal = document.getElementById('editTemplateModal');
  if (modal) {
    modal.classList.add('hidden');
  }
}

/**
 * Save the template form
 */
function saveTemplateForm(e, editId=null) {
  e.preventDefault();
  console.log(editId, ' editId')
  const id = document.getElementById('templateId').value;
  const index = parseInt(document.getElementById('templateIndex').value);
  const title = document.getElementById('templateTitle').value;
  const description = document.getElementById('templateDescription').value;
  const text = document.getElementById('templateText').value;
  const ratingMin = parseInt(document.getElementById('ratingMin').value);
  const sentiment = document.getElementById('sentiment').value;
  const length = document.getElementById('reviewLength').value;
  
  const variables = [];
  const variableInsertBtns = document.querySelectorAll('.variable-insert-btn');
  variableInsertBtns.forEach(btn => {
    if (text.includes(`{{${btn.dataset.variable}}}`)) {
      variables.push(btn.dataset.variable);
    }
  });
  
  const template = {
    id,
    title,
    description,
    text,
    variables,
    conditions: {
      rating: { min: ratingMin, max: ratingMax },
      sentiment,
      length
    }
  };
    
  fetch('/templates', { method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
    },
    body: JSON.stringify({ title, description, template_text: text, ratings: ratings, sentiment }),
  })
    .then(response => response.json())
    .then(data => {
      if (data.success  === true) {
        console.log('Template saved successfully');
        hideTemplateModal();
        window.location.reload();
      } else {
        console.error('Failed to save template');
      }
    })
    .catch(error => console.error('Error:', error));
  
}

/**
 * Delete a template
 */
function deleteTemplate(index) {
  // Check if user has permission to manage templates
  if (typeof permissionsHandler !== 'undefined' && !permissionsHandler.hasPermission('manage_templates')) {
    permissionsHandler.showPermissionDeniedMessage('You do not have permission to delete templates');
    return;
  }
  // Get the template to check if it's the default template
  if (confirm('Are you sure you want to delete this template?')) {
  fetch('/templates/' + index, { method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
    }
  })
    .then(response => response.json())
    .then(data => {
      if (data.success  === true) {
        console.log('Template Deleted successfully');
        hideTemplateModal();
        window.location.reload();
      } else {
        console.error('Failed to deleet template');
      }
    })
    .catch(error => console.error('Error:', error));
  }
}

/**
 * Get the best template for a review
 */
function getBestTemplateForReview(review) {
  console.log('Finding best template for review:', review);
  
  // Step 1: Filter templates by rating
  let matchingTemplates = window.appTemplates.filter(template => {
    return template.conditions.rating.min <= review.rating && 
           template.conditions.rating.max >= review.rating;
  });
  
  console.log('Templates matching rating criteria:', matchingTemplates.length);
  
  // If no templates match the rating, return the default template
  if (matchingTemplates.length === 0) {
    console.log('No templates match rating, using default template');
    return window.appTemplates.find(template => template.isDefault) || window.appTemplates[0];
  }
  
  // Step 2: Filter by sentiment if specified
  const sentimentFiltered = matchingTemplates.filter(template => {
    return template.conditions.sentiment === 'any' || 
           template.conditions.sentiment === review.sentiment;
  });
  
  if (sentimentFiltered.length > 0) {
    matchingTemplates = sentimentFiltered;
    console.log('Templates matching sentiment criteria:', matchingTemplates.length);
  }
  
  // Step 3: Filter by length if specified
  const reviewLength = getReviewLengthCategory(review.text);
  const lengthFiltered = matchingTemplates.filter(template => {
    return template.conditions.length === 'any' || 
           template.conditions.length === reviewLength;
  });
  
  if (lengthFiltered.length > 0) {
    matchingTemplates = lengthFiltered;
    console.log('Templates matching length criteria:', matchingTemplates.length);
  }
  
  // Return the first matching template or the default template if none match
  if (matchingTemplates.length > 0) {
    console.log('Using matching template:', matchingTemplates[0].title);
    return matchingTemplates[0];
  } else {
    console.log('No matching templates found, using default template');
    return window.appTemplates.find(template => template.isDefault) || window.appTemplates[0];
  }
}

/**
 * Determine the length category of a review
 * @param {string} text - The review text
 * @returns {string} The length category (short, medium, long)
 */
function getReviewLengthCategory(text) {
  const wordCount = text.split(/\s+/).length;
  
  if (wordCount < 20) {
    return 'short';
  } else if (wordCount < 100) {
    return 'medium';
  } else {
    return 'long';
  }
}

/**
 * Get all templates
 */
function getAllTemplates() {
  return window.appTemplates || [];
}

/**
 * Get a template by ID
 */
function getTemplateById(id) {
  return window.appTemplates.find(t => t.id === id) || null;
}

/**
 * Set up event listeners for the templates tab
 */
function setupTemplatesEventListeners() {
  // Tab button click
  const templatesTabBtn = document.getElementById('templatesTabBtn');
  if (templatesTabBtn) {
    templatesTabBtn.addEventListener('click', () => {
      // Refresh templates list when tab is clicked
      renderTemplatesList();
    });
  }
}

/**
 * Check if the user has permission to manage templates and update UI accordingly
 */
function checkTemplatePermissions() {
  // If permissionsHandler is not defined or not initialized, assume user has permission
  if (typeof permissionsHandler === 'undefined' || !permissionsHandler.initialized) {
    return;
  }
  
  // Check if user has permission to manage templates
  const hasTemplatePermission = permissionsHandler.hasPermission('manage_templates');
  
  // Update UI based on permissions
  if (!hasTemplatePermission) {
    // Hide add button
    const addBtn = document.getElementById('addTemplateBtn');
    if (addBtn) {
      addBtn.style.display = 'none';
    }
    
    // Hide edit and delete buttons
    document.querySelectorAll('.edit-template-btn, .delete-template-btn').forEach(btn => {
      btn.style.display = 'none';
    });
    
    // Add a message indicating view-only access
    const templatesList = document.getElementById('templatesList');
    if (templatesList && templatesList.children.length > 0) {
      const permissionMessage = document.createElement('div');
      permissionMessage.className = 'bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4';
      permissionMessage.innerHTML = `
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              You have view-only access to templates. Contact an administrator to make changes.
            </p>
          </div>
        </div>
      `;
      templatesList.insertBefore(permissionMessage, templatesList.firstChild);
    }
  }
}

// Export functions to global scope
window.initializeTemplatesManager = initializeTemplatesManager;
window.getBestTemplateForReview = getBestTemplateForReview;
window.getAllTemplates = getAllTemplates;
window.checkTemplatePermissions = checkTemplatePermissions;
window.getTemplateById = getTemplateById;
