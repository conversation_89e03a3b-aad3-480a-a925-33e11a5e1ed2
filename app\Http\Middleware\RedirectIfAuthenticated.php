<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): \Symfony\Component\HttpFoundation\Response
    {
        // Get the current route name
        $routeName = $request->route() ? $request->route()->getName() : null;

        // Only redirect authenticated users if they're trying to access guest-only routes
        // This prevents redirect loops
        if (Auth::check() && in_array($routeName, [            
            'login',
            'register',
            'home',
            'password.request',
            'password.reset',
            'password.email',
            'password.update',
            'auth.google'                        
        ])) {
            return redirect()->route('business.dashboard');
        }

        return $next($request);
    }
}
