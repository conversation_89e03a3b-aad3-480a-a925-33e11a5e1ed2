<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class WelcomeUserMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $password;
    public $token;

    public function __construct(User $user, $password, $token)
    {
        $this->user = $user;
        $this->password = $password;
        $this->token = $token;
    }

    public function build()
    {
        return $this->view('emails.welcome_user')
            ->with([
                'user' => $this->user,
                'password' => $this->password,
                'token' => $this->token,
            ]);
    }
}
