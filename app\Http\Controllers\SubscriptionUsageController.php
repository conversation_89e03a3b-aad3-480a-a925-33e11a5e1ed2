<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\SubscriptionUsageTracker;
use App\Services\SubscriptionService;

class SubscriptionUsageController extends Controller
{
    protected $usageTracker;
    protected $subscriptionService;

    public function __construct(SubscriptionUsageTracker $usageTracker, SubscriptionService $subscriptionService)
    {
        $this->usageTracker = $usageTracker;
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Get real-time subscription usage data
     */
    public function getUsageData(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $forceRefresh = $request->boolean('refresh', false);
            $usageData = $this->usageTracker->getRealTimeUsage($user->id, $forceRefresh);
            
            return response()->json([
                'success' => true,
                'data' => $usageData,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get usage data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if user can perform a specific action
     */
    public function checkActionLimit(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'action' => 'required|string|in:connect_business,invite_team_member,send_reply,data_export,api_call,template_access'
        ]);

        try {
            $action = $request->input('action');
            $params = $request->input('params', []);
            
            $validation = $this->subscriptionService->requiresUpgrade($user->id, $action, $params);
            //$usageData = $this->usageTracker->getRealTimeUsage($user->id);
            
            return response()->json([
                'success' => true,
                'allowed' => $validation['allowed'],
                'message' => $validation['message'],
            //    'usage_data' => $usageData,
                'action' => $action,
                'upgrade_required' => !$validation['allowed']
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to check action limit',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get usage summary for dashboard widgets
     */
    public function getUsageSummary(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $usageData = $this->usageTracker->getRealTimeUsage($user->id);
            
            // Extract key metrics for dashboard
            $summary = [
                'subscription' => $usageData['subscription_info'],
                'limits' => [
                    'business_connections' => [
                        'current' => $usageData['business_connections']['current'],
                        'limit' => $usageData['business_connections']['limit'],
                        'percentage' => $usageData['business_connections']['percentage'],
                        'status' => $usageData['business_connections']['status']
                    ],
                    'team_members' => [
                        'current' => $usageData['team_members']['current'],
                        'limit' => $usageData['team_members']['limit'],
                        'percentage' => $usageData['team_members']['percentage'],
                        'status' => $usageData['team_members']['status']
                    ],
                    'monthly_replies' => [
                        'current' => $usageData['monthly_replies']['current'],
                        'limit' => $usageData['monthly_replies']['limit'],
                        'percentage' => $usageData['monthly_replies']['percentage'],
                        'status' => $usageData['monthly_replies']['status'],
                        'unlimited' => $usageData['monthly_replies']['unlimited']
                    ]
                ],
                'alerts' => $usageData['upgrade_alerts'],
                'limits_status' => $usageData['limits_status']
            ];
            
            return response()->json([
                'success' => true,
                'summary' => $summary,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get usage summary',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get upgrade suggestions based on current usage
     */
    public function getUpgradeSuggestions(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $usageData = $this->usageTracker->getRealTimeUsage($user->id);
            $alerts = $usageData['upgrade_alerts'];
            
            // Get available plans for upgrade suggestions
            $currentSubscription = $this->subscriptionService->getActiveSubscription($user->id);
            $suggestions = [];
            
            if ($currentSubscription) {
                $suggestions = $this->generateUpgradeSuggestions($usageData, $currentSubscription);
            }
            
            return response()->json([
                'success' => true,
                'alerts' => $alerts,
                'suggestions' => $suggestions,
                'current_plan' => $currentSubscription ? $currentSubscription->plan->name : null
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get upgrade suggestions',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh usage cache
     */
    public function refreshUsage(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $usageData = $this->usageTracker->refreshUsageCache($user->id);
            
            return response()->json([
                'success' => true,
                'message' => 'Usage data refreshed successfully',
                'data' => $usageData,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to refresh usage data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate upgrade suggestions based on usage patterns
     */
    private function generateUpgradeSuggestions(array $usageData, $currentSubscription): array
    {
        $suggestions = [];
        
        // Business connection suggestions
        if ($usageData['business_connections']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'business_connections',
                'title' => 'Increase Business Connections',
                'description' => 'Upgrade to connect more business locations',
                'current_limit' => $usageData['business_connections']['limit'],
                'suggested_limit' => $usageData['business_connections']['limit'] * 2,
                'urgency' => $usageData['business_connections']['percentage'] > 95 ? 'high' : 'medium'
            ];
        }
        
        // Reply limit suggestions
        if (!$usageData['monthly_replies']['unlimited'] && $usageData['monthly_replies']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'monthly_replies',
                'title' => 'Increase Reply Limit',
                'description' => 'Upgrade for more monthly replies or unlimited access',
                'current_limit' => $usageData['monthly_replies']['limit'],
                'suggested_limit' => 'Unlimited',
                'urgency' => $usageData['monthly_replies']['percentage'] > 95 ? 'high' : 'medium'
            ];
        }
        
        // Team member suggestions
        if ($usageData['team_members']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'team_members',
                'title' => 'Increase Team Size',
                'description' => 'Upgrade to invite more team members',
                'current_limit' => $usageData['team_members']['limit'],
                'suggested_limit' => $usageData['team_members']['limit'] + 5,
                'urgency' => $usageData['team_members']['percentage'] > 95 ? 'high' : 'medium'
            ];
        }
        
        return $suggestions;
    }

    /**
     * Get usage trends over time
     */
    public function getUsageTrends(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'period' => 'string|in:7_days,30_days,90_days',
            'metric' => 'string|in:replies,connections,activities'
        ]);

        try {
            $period = $request->input('period', '30_days');
            $metric = $request->input('metric', 'activities');

            $trends = $this->calculateUsageTrends($user->id, $period, $metric);

            return response()->json([
                'success' => true,
                'trends' => $trends,
                'period' => $period,
                'metric' => $metric
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get usage trends',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate subscription limits for frontend triggers
     */
    public function validateLimit(Request $request)
    {        
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'limit_type' => 'required|string|in:template_access,team_member_invite,business_connection,monthly_reply_limit,feature_access',
            'business_id' => 'nullable|integer',
            'feature' => 'nullable|string'
        ]);

        try {
            $limitType = $request->input('limit_type');
            $businessId = $request->input('business_id');
            $feature = $request->input('feature');

            // Determine validation parameters based on limit type
            $params = ['user_id' => $user->id];
            if ($businessId) {
                $params['business_id'] = $businessId;
            }
            if ($feature) {
                $params['feature'] = $feature;
            }

            // Perform validation based on limit type
            $validation = $this->performLimitValidation($limitType, $params);

            return response()->json([
                'success' => true,
                'allowed' => $validation['allowed'],
                'message' => $validation['message'],
                'limit_type' => $limitType,
                'upgrade_required' => !$validation['allowed'],
                'current_count' => $validation['current_count'] ?? null,
                'limit' => $validation['limit'] ?? null,
                'metadata' => $validation['metadata'] ?? []
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to validate limit',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Perform limit validation based on type
     */
    private function performLimitValidation(string $limitType, array $params): array
    {
        $userId = $params['user_id'];

        switch ($limitType) {
            case 'template_access':
                return $this->subscriptionService->hasFeatureAccess($userId, 'template_access');
            
            case 'monthly_reply_limit':                
                return $this->subscriptionService->canSendReply($userId);

            case 'team_member_invite':
                $businessId = $params['business_id'] ?? 0;
                return $this->subscriptionService->canInviteTeamMember($userId, $businessId);

            case 'business_connection':
                return $this->subscriptionService->canConnectBusiness($userId);

            case 'feature_access':
                $feature = $params['feature'] ?? '';
                return $this->subscriptionService->hasFeatureAccess($userId, $feature);

            default:
                return [
                    'allowed' => false,
                    'message' => 'Unknown limit type specified.'
                ];
        }
    }

    /**
     * Calculate usage trends for specified period and metric
     */
    private function calculateUsageTrends(int $userId, string $period, string $metric): array
    {
        // This would implement trend calculation logic
        // For now, return placeholder data
        return [
            'data_points' => [],
            'trend_direction' => 'stable',
            'percentage_change' => 0
        ];
    }
}
