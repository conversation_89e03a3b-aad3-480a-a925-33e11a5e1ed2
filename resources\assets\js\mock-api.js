/**
 * ReviewMaster AI - Mock API Service
 * Simulates API calls to a backend service for the extension prototype
 */

class MockAPI {
  constructor() {
    // Initialize with dummy data
    this.businesses = [
      {
        id: "bus123",
        name: "Coffee Supreme",
        address: "123 Main St, Seattle, WA",
        rating: 4.7,
        reviewCount: 182,
        thumbnail: "coffee_shop.png"
      },
      {
        id: "bus124",
        name: "Seattle Bakery",
        address: "456 Pike St, Seattle, WA",
        rating: 4.2,
        reviewCount: 143,
        thumbnail: "bakery.png"
      },
      {
        id: "bus125",
        name: "Downtown Deli",
        address: "789 Olive Way, Seattle, WA",
        rating: 3.9,
        reviewCount: 98,
        thumbnail: "deli.png"
      },
      {
        id: "bus126",
        name: "Harbor Seafood",
        address: "321 Waterfront Ave, Seattle, WA",
        rating: 4.5,
        reviewCount: 156,
        thumbnail: "seafood.png"
      },
      {
        id: "bus127",
        name: "Green Leaf Cafe",
        address: "567 University St, Seattle, WA",
        rating: 4.1,
        reviewCount: 112,
        thumbnail: "cafe.png"
      }
    ];

    this.reviews = [
      {
        id: "rev456",
        businessId: "bus123",
        customerName: "<PERSON>",
        customerPhoto: "placeholder.png",
        rating: 4,
        date: "2025-03-12T14:32:00",
        text: "Great coffee and atmosphere. The staff was very friendly!",
        photos: ["placeholder.jpg"],
        replied: true,
        replyText: "Thank you for your kind words, Jane! We're glad you enjoyed your experience.",
        replyDate: "2025-03-13T09:15:00"
      },
      {
        id: "rev457",
        businessId: "bus123",
        customerName: "Michael Johnson",
        customerPhoto: "placeholder.png",
        rating: 2,
        date: "2025-03-10T09:45:00",
        text: "Coffee was cold and service was slow. Disappointed with my experience today.",
        photos: [],
        replied: false,
        replyText: "",
        replyDate: null
      },
      {
        id: "rev458",
        businessId: "bus123",
        customerName: "Emily Davis",
        customerPhoto: "placeholder.png",
        rating: 5,
        date: "2025-03-08T16:22:00",
        text: "Best coffee in town! Love the new seasonal blend and the barista was super helpful explaining the different options.",
        photos: ["review_photo2.jpg", "review_photo3.jpg"],
        replied: true,
        replyText: "Thanks for the amazing review, Emily! We're thrilled you enjoyed our seasonal blend. Can't wait to serve you again soon!",
        replyDate: "2025-03-08T18:05:00"
      },
      {
        id: "rev459",
        businessId: "bus123",
        customerName: "David Wilson",
        customerPhoto: "placeholder.png",
        rating: 3,
        date: "2025-03-05T11:10:00",
        text: "Decent coffee but a bit overpriced compared to other places in the area. The ambiance is nice though.",
        photos: [],
        replied: false,
        replyText: "",
        replyDate: null
      },
      {
        id: "rev460",
        businessId: "bus123",
        customerName: "Sarah Thompson",
        customerPhoto: "placeholder.png",
        rating: 5,
        date: "2025-03-01T15:45:00",
        text: "I love this place! The pastries are amazing and the coffee is always perfect. My favorite spot to work from.",
        photos: ["review_photo4.jpg"],
        replied: true,
        replyText: "Thank you for the wonderful review, Sarah! We're happy to be your favorite work spot. See you again soon!",
        replyDate: "2025-03-02T10:20:00"
      },
      {
        id: "rev461",
        businessId: "bus124",
        customerName: "Robert Brown",
        customerPhoto: "placeholder.png",
        rating: 4,
        date: "2025-03-14T08:30:00",
        text: "Great bakery with amazing bread. The sourdough is exceptional!",
        photos: [],
        replied: true,
        replyText: "Thank you, Robert! We're proud of our sourdough recipe and glad you enjoyed it.",
        replyDate: "2025-03-14T12:15:00"
      },
      {
        id: "rev462",
        businessId: "bus124",
        customerName: "Lisa Garcia",
        customerPhoto: "placeholder.png",
        rating: 2,
        date: "2025-03-11T16:40:00",
        text: "Ordered a birthday cake that was supposed to be ready at 4pm, but it wasn't ready until 5:30pm. Made me late for the party.",
        photos: [],
        replied: false,
        replyText: "",
        replyDate: null
      }
    ];

    this.templates = [
      {
        id: "temp1",
        title: "Thank You Response",
        description: "A warm response for positive reviews",
        text: "Thank you for your kind words, {{customerName}}! We're delighted to hear that you enjoyed {{specificMention}}. Your feedback means a lot to us at {{businessName}}, and we hope to see you again soon!",
        variables: ["customerName", "specificMention", "businessName"],
        toneOptions: ["friendly", "professional", "thankful"]
      },
      {
        id: "temp2",
        title: "Apology Response",
        description: "A sincere apology for negative experiences",
        text: "We're truly sorry to hear about your experience, {{customerName}}. We strive to provide excellent service, and we clearly missed the mark with {{specificIssue}}. We'd like to make this right - please contact our manager at {{contactInfo}} so we can address this personally.",
        variables: ["customerName", "specificIssue", "contactInfo"],
        toneOptions: ["apologetic", "professional"]
      },
      {
        id: "temp3",
        title: "Neutral Response",
        description: "A balanced response for mixed reviews",
        text: "Thank you for your feedback, {{customerName}}. We appreciate you taking the time to share your thoughts about {{specificMention}}. We're always working to improve our service and your input helps us do that. If you have any additional suggestions, please let us know!",
        variables: ["customerName", "specificMention"],
        toneOptions: ["professional", "friendly"]
      }
    ];

    this.analytics = {
      overallRating: 4.3,
      totalReviews: 182,
      responseRate: 87,
      averageResponseTime: "14 hours",
      sentimentTrend: [
        { period: "Jan 2025", sentiment: 4.1 },
        { period: "Feb 2025", sentiment: 4.3 },
        { period: "Mar 2025", sentiment: 4.5 },
        { period: "Apr 2025", sentiment: 4.4 }
      ],
      reviewActivityData: [
        { date: "Apr 1", newReviews: 3, responded: 2 },
        { date: "Apr 2", newReviews: 2, responded: 2 },
        { date: "Apr 3", newReviews: 1, responded: 1 },
        { date: "Apr 4", newReviews: 0, responded: 0 },
        { date: "Apr 5", newReviews: 2, responded: 2 },
        { date: "Apr 6", newReviews: 4, responded: 3 },
        { date: "Apr 7", newReviews: 1, responded: 1 },
        { date: "Apr 8", newReviews: 2, responded: 2 },
        { date: "Apr 9", newReviews: 3, responded: 3 },
        { date: "Apr 10", newReviews: 2, responded: 1 },
        { date: "Apr 11", newReviews: 0, responded: 0 },
        { date: "Apr 12", newReviews: 1, responded: 1 },
        { date: "Apr 13", newReviews: 2, responded: 2 },
        { date: "Apr 14", newReviews: 3, responded: 2 },
        { date: "Apr 15", newReviews: 4, responded: 4 },
        { date: "Apr 16", newReviews: 2, responded: 2 },
        { date: "Apr 17", newReviews: 1, responded: 1 },
        { date: "Apr 18", newReviews: 0, responded: 0 },
        { date: "Apr 19", newReviews: 2, responded: 1 },
        { date: "Apr 20", newReviews: 3, responded: 3 },
        { date: "Apr 21", newReviews: 2, responded: 2 },
        { date: "Apr 22", newReviews: 1, responded: 1 },
        { date: "Apr 23", newReviews: 2, responded: 2 },
        { date: "Apr 24", newReviews: 3, responded: 2 },
        { date: "Apr 25", newReviews: 4, responded: 3 },
        { date: "Apr 26", newReviews: 2, responded: 2 },
        { date: "Apr 27", newReviews: 1, responded: 1 },
        { date: "Apr 28", newReviews: 3, responded: 3 },
        { date: "Apr 29", newReviews: 2, responded: 1 },
        { date: "Apr 30", newReviews: 4, responded: 2 }
      ],
      topTopics: [
        { topic: "Service", count: 87, sentiment: 4.6 },
        { topic: "Quality", count: 65, sentiment: 4.2 },
        { topic: "Atmosphere", count: 42, sentiment: 4.7 },
        { topic: "Price", count: 38, sentiment: 3.5 },
        { topic: "Location", count: 25, sentiment: 4.8 }
      ]
    };

    this.competitiveBenchmark = [
      { business: "Your Business", rating: 4.3 },
      { business: "Competitor A", rating: 4.0 },
      { business: "Competitor B", rating: 4.5 },
      { business: "Competitor C", rating: 3.8 }
    ];
  }

  // Simulate API delay
  async _delay(ms = 100) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get all businesses
  async getBusinesses() {
    await this._delay();
    return { success: true, data: this.businesses };
  }

  // Get a specific business by ID
  async getBusinessById(id) {
    await this._delay();
    const business = this.businesses.find(b => b.id === id);
    
    if (business) {
      return { success: true, data: business };
    } else {
      return { success: false, error: "Business not found" };
    }
  }

  // Get reviews for a specific business
  async getReviewsForBusiness(businessId, filters = {}) {
    await this._delay(200); // Slightly longer delay for reviews
    
    console.log('Getting reviews for business ID:', businessId);
    console.log('All reviews:', this.reviews);
    
    // Ensure businessId is treated as a string for comparison
    const businessIdStr = String(businessId);
    
    let filteredReviews = this.reviews.filter(r => String(r.businessId) === businessIdStr);
    console.log('Filtered reviews by business ID:', filteredReviews);
    
    // Apply filters if provided
    if (filters && filters.rating) {
      console.log('Filtering by rating:', filters.rating);
      // Convert to number for comparison
      const ratingNum = Number(filters.rating);
      filteredReviews = filteredReviews.filter(r => r.rating === ratingNum);
    }
    
    if (filters && filters.replied !== undefined && filters.replied !== null) {
      console.log('Filtering by replied status:', filters.replied);
      // Convert to boolean for comparison
      const repliedBool = Boolean(filters.replied);
      filteredReviews = filteredReviews.filter(r => Boolean(r.replied) === repliedBool);
    }
    
    if (filters && filters.withPhotos) {
      console.log('Filtering by photos presence');
      filteredReviews = filteredReviews.filter(r => r.photos && r.photos.length > 0);
    }
    
    // Sort by date (newest first)
    filteredReviews.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    console.log('Final filtered reviews:', filteredReviews);
    return { success: true, data: filteredReviews };
  }

  // Get reply templates
  async getReplyTemplates() {
    await this._delay();
    
    // Use templates from the template manager if available
    if (window.appTemplates && Array.isArray(window.appTemplates)) {
      return { success: true, data: window.appTemplates };
    }
    
    // Fallback to built-in templates
    return { success: true, data: this.templates };
  }

  // Generate AI reply (simulated)
  async generateReply(reviewId, templateId, tone, language = "english") {
    await this._delay(300); // Longer delay to simulate AI processing
    
    const review = this.reviews.find(r => r.id === reviewId);
    
    // If templateId is 'auto', find the best template for this review
    if (templateId === 'auto' && typeof window.getBestTemplateForReview === 'function') {
      const bestTemplate = window.getBestTemplateForReview(review);
      if (bestTemplate) {
        templateId = bestTemplate.id;
      }
    }
    
    // Get the template - first try from the template manager, then fall back to built-in templates
    let template;
    if (typeof window.getTemplateById === 'function') {
      template = window.getTemplateById(templateId);
    }
    
    if (!template) {
      // If template not found, try to get the default template
      if (typeof window.appTemplates !== 'undefined') {
        template = window.appTemplates.find(t => t.isDefault);
      }
      
      // If still no template, fall back to built-in templates
      if (!template) {
        template = this.templates.find(t => t.id === templateId);
        
        // If specific template not found, use the first available template
        if (!template && this.templates.length > 0) {
          template = this.templates[0];
        }
      }
    }
    
    if (!review) {
      return { success: false, error: "Review not found" };
    }
    
    if (!template) {
      return { success: false, error: "No template available" };
    }
    
    // Start with the template text
    let replyText = template.text;
    
    // Get the business
    const business = this.businesses.find(b => b.id === review.businessId);
    
    // Extract first and last name
    const nameParts = review.customerName.split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : '';
    
    // Replace variables with defaults if they exist in the template
    const variables = {
      businessName: business?.name || "our business",
      businessContact: `info@${business?.name?.toLowerCase().replace(/\s+/g, '')}.com` || "<EMAIL>",
      reviewerFirstName: firstName,
      reviewerLastName: lastName,
      specificMention: "our products and service",
      specificIssue: "the issue you experienced",
      contactInfo: "our customer service <NAME_EMAIL>"
    };
    
    // Replace variables
    for (const [key, value] of Object.entries(variables)) {
      replyText = replyText.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }
    
    // Get AI configuration settings if available
    const aiConfig = window.appSettings?.aiConfig || {
      provider: 'chatgpt',
      model: 'gpt-4',
      responseStyle: 50,
      responseLength: 50,
      signOffText: 'Thank you for your business!',
      language: 'same-as-review',
      customInstructions: ''
    };
    
    // Simulate tone adjustments based on response style setting
    if (tone === "friendly") {
      // More casual for higher response style values
      if (aiConfig.responseStyle > 66) {
        replyText += " 😊 We really appreciate you!";
      } else if (aiConfig.responseStyle > 33) {
        replyText += " 😊";
      } else {
        replyText += " We appreciate your feedback.";
      }
    } else if (tone === "apologetic") {
      // Formality based on response style
      if (aiConfig.responseStyle > 66) {
        replyText = "We're really sorry about that! " + replyText;
      } else if (aiConfig.responseStyle > 33) {
        replyText = "We sincerely apologize. " + replyText;
      } else {
        replyText = "Please accept our formal apology. " + replyText;
      }
    } else if (tone === "thankful") {
      // Formality based on response style
      if (aiConfig.responseStyle > 66) {
        replyText = "Wow! Thanks so much! " + replyText;
      } else if (aiConfig.responseStyle > 33) {
        replyText = "We're incredibly grateful! " + replyText;
      } else {
        replyText = "We extend our sincere gratitude. " + replyText;
      }
    }
    
    // Adjust response length based on settings
    if (aiConfig.responseLength < 33) {
      // Make it more concise
      replyText = replyText.replace(/\b(we are|we're)\b/gi, "we're")
                          .replace(/\b(you are|you're)\b/gi, "you're")
                          .replace(/\b(it is|it's)\b/gi, "it's")
                          .replace(/\b(that is|that's)\b/gi, "that's")
                          .replace(/\b(cannot)\b/gi, "can't");
                          
      // Remove some common phrases to make it shorter
      replyText = replyText.replace(/\b(please feel free to|don't hesitate to)\b/gi, "please")
                          .replace(/\b(we would like to|we'd like to)\b/gi, "we want to")
                          .replace(/\b(in order to)\b/gi, "to");
    } else if (aiConfig.responseLength > 66) {
      // Make it more elaborate
      if (!replyText.includes("If you have any further questions")) {
        replyText += " If you have any further questions or concerns, please don't hesitate to reach out to us directly.";
      }
      
      if (!replyText.includes("We value your feedback")) {
        replyText += " We value your feedback and are committed to providing the best experience possible.";
      }
    }
    
    // Add sign-off text if available
    if (aiConfig.signOffText && aiConfig.signOffText.trim() !== '') {
      replyText += `\n\n${aiConfig.signOffText}`;
    }
    
    // Simulate language translation (just a placeholder)
    // Use aiConfig.language if it's not set to 'same-as-review', otherwise use the passed language parameter
    const responseLanguage = aiConfig.language === 'same-as-review' ? language : aiConfig.language;
    if (responseLanguage !== "english") {
      replyText += ` [Translated to ${responseLanguage}]`;
    }
    
    // Add model information for demonstration purposes
    const modelInfo = this._getModelInfo(aiConfig.provider, aiConfig.model);
    
    return { 
      success: true, 
      data: { 
        replyText,
        tone,
        language: responseLanguage,
        aiProvider: aiConfig.provider,
        aiModel: aiConfig.model,
        modelInfo,
        templateId
      } 
    };
  }
  
  // Helper method to get model information
  _getModelInfo(provider, model) {
    const providers = {
      'chatgpt': 'OpenAI',
      'claude': 'Anthropic',
      'gemini': 'Google'
    };
    
    const models = {
      // OpenAI models
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'gpt-4': 'GPT-4',
      'gpt-4-turbo': 'GPT-4 Turbo',
      
      // Anthropic models
      'claude-2': 'Claude 2',
      'claude-3-opus': 'Claude 3 Opus',
      'claude-3-sonnet': 'Claude 3 Sonnet',
      'claude-3-haiku': 'Claude 3 Haiku',
      
      // Google models
      'gemini-pro': 'Gemini Pro',
      'gemini-ultra': 'Gemini Ultra'
    };
    
    return {
      providerName: providers[provider] || provider,
      modelName: models[model] || model
    };
  }

  // Post a reply to a review
  async postReply(reviewId, replyText) {
    await this._delay();
    
    const reviewIndex = this.reviews.findIndex(r => r.id === reviewId);
    
    if (reviewIndex === -1) {
      return { success: false, error: "Review not found" };
    }
    
    // Update the review with the reply
    this.reviews[reviewIndex].replied = true;
    this.reviews[reviewIndex].replyText = replyText;
    this.reviews[reviewIndex].replyDate = new Date().toISOString();
    
    return { 
      success: true, 
      data: this.reviews[reviewIndex]
    };
  }

  // Get analytics data
  async getAnalytics(businessId) {
    await this._delay(400);
    
    // In a real implementation, this would filter by businessId
    return { success: true, data: this.analytics };
  }
}

// Create a global instance
const mockAPI = new MockAPI();
export default mockAPI;