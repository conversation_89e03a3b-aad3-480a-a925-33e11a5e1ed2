<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BusinessActivityLog extends Model
{
    protected $table = 'business_activity_logs';

    protected $fillable = [
        'user_id',
        'business_id',
        'subscription_id',
        'activity_type',
        'activity_category',
        'activity_description',
        'performed_by_type',
        'performed_by_user_id',
        'performed_by_name',
        'counts_toward_limit',
        'activity_count',
        'ip_address',
        'user_agent',
        'metadata',
        'status',
        'error_message'
    ];

    protected $casts = [
        'counts_toward_limit' => 'boolean',
        'activity_count' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Activity Types Constants
    const ACTIVITY_TYPES = [
        'connect_business' => 'Business Connected',
        'disconnect_business' => 'Business Disconnected',
        'send_reply' => 'Reply Sent',
        'fetch_reviews' => 'Reviews Fetched',
        'create_template' => 'Template Created',
        'update_template' => 'Template Updated',
        'delete_template' => 'Template Deleted',
        'invite_team_member' => 'Team Member Invited',
        'remove_team_member' => 'Team Member Removed',
        'update_business_settings' => 'Business Settings Updated',
        'export_data' => 'Data Exported',
        'api_call' => 'API Call Made',
        'auto_reply_sent' => 'Auto Reply Sent',
        'scheduled_reply_sent' => 'Scheduled Reply Sent'
    ];

    // Activity Categories Constants
    const ACTIVITY_CATEGORIES = [
        'business_management' => 'Business Management',
        'review_management' => 'Review Management',
        'template_management' => 'Template Management',
        'team_management' => 'Team Management',
        'api_usage' => 'API Usage',
        'data_export' => 'Data Export',
        'settings' => 'Settings',
        'automation' => 'Automation'
    ];

    // Performed By Types Constants
    const PERFORMED_BY_TYPES = [
        'owner' => 'Business Owner',
        'member' => 'Team Member',
        'system' => 'System/Automated'
    ];

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function performedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by_user_id');
    }

    /**
     * Scopes
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForBusiness($query, int $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeByActivityType($query, string $activityType)
    {
        return $query->where('activity_type', $activityType);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('activity_category', $category);
    }

    public function scopeByPerformedBy($query, string $performedByType)
    {
        return $query->where('performed_by_type', $performedByType);
    }

    public function scopeCountsTowardLimit($query, bool $counts = true)
    {
        return $query->where('counts_toward_limit', $counts);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    public function scopeInDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeThisMonth($query)
    {
        $now = Carbon::now();
        return $query->whereMonth('created_at', $now->month)
                    ->whereYear('created_at', $now->year);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::today());
    }

    /**
     * Helper Methods
     */
    public function getActivityTypeDisplayAttribute(): string
    {
        return self::ACTIVITY_TYPES[$this->activity_type] ?? ucfirst(str_replace('_', ' ', $this->activity_type));
    }

    public function getActivityCategoryDisplayAttribute(): string
    {
        return self::ACTIVITY_CATEGORIES[$this->activity_category] ?? ucfirst(str_replace('_', ' ', $this->activity_category));
    }

    public function getPerformedByTypeDisplayAttribute(): string
    {
        return self::PERFORMED_BY_TYPES[$this->performed_by_type] ?? ucfirst($this->performed_by_type);
    }

    public function getPerformedByDisplayNameAttribute(): string
    {
        if ($this->performed_by_name) {
            return $this->performed_by_name;
        }

        if ($this->performedByUser) {
            return $this->performedByUser->name;
        }

        return $this->getPerformedByTypeDisplayAttribute();
    }

    /**
     * Static helper methods for activity counting
     */
    public static function getActivityCount(int $userId, string $activityType, Carbon $startDate = null, Carbon $endDate = null): int
    {
        $query = self::forUser($userId)
                    ->byActivityType($activityType)
                    ->successful()
                    ->countsTowardLimit();

        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }

        return $query->sum('activity_count');
    }

    public static function getMonthlyActivityCount(int $userId, string $activityType): int
    {
        return self::forUser($userId)
                  ->byActivityType($activityType)
                  ->successful()
                  ->countsTowardLimit()
                  ->thisMonth()
                  ->sum('activity_count');
    }

    public static function getTotalActivityCount(int $userId, string $activityType): int
    {
        return self::forUser($userId)
                  ->byActivityType($activityType)
                  ->successful()
                  ->countsTowardLimit()
                  ->sum('activity_count');
    }
}
