/**
 * Review Filters and Load More Functionality
 * Handles filtering, pagination, and loading more reviews
 */

// Global state for filters and pagination
let reviewFilters = {
    date_range: 'all',
    rating: null,
    type: 'all',
    order: 'desc'
};

let paginationState = {
    currentPage: 1,
    hasMorePages: true,
    isLoading: false,
    totalReviews: 0
};

let currentBusinessId = null;
let currentAccountId = null;
let currentLocationName = null;

/**
 * Initialize review filters and load more functionality
 */
function initializeReviewFilters() {

    // Get business data from the page
    const businessIdElement = document.getElementById('businessId');
    const googleBusinessIdElement = document.getElementById('googleBusinessId');
    const locationNameElement = document.getElementById('locationName');

    if (businessIdElement) {
        currentBusinessId = businessIdElement.value;
    }

    if (googleBusinessIdElement) {
        currentAccountId = googleBusinessIdElement.value;
    }

    if (locationNameElement) {
        currentLocationName = locationNameElement.value;
    }

    // Fallback: Get IDs from load more button if not found above
    if (!currentBusinessId || !currentAccountId) {
        const loadMoreElement = document.getElementById('loadMoreBtn');

        if (loadMoreElement) {
            if (!currentBusinessId && loadMoreElement.dataset.businessId) {
                currentBusinessId = loadMoreElement.dataset.businessId;
                console.log('Business ID from load more element:', currentBusinessId);
            }
            if (!currentAccountId && loadMoreElement.dataset.accountId) {
                currentAccountId = loadMoreElement.dataset.accountId;
                console.log('Account ID from load more element:', currentAccountId);
            }
        }
    }

    // Set up filter event listeners
    setupFilterEventListeners();

    // Set up load more functionality
    setupLoadMoreFunctionality();

    // Set up order filter
    setupOrderFilter();

    // === AJAX Review Actions: Fetch/Sync ===
    // Helper for showing toast
    function showToast(message, type = 'success', duration = 3500, containerId = 'reviewsActionToast') {
        const toast = document.createElement('div');
        toast.className = `mb-2 px-4 py-2 rounded shadow-lg text-white font-semibold transition-all toast-${type}`;
        toast.style.background = type === 'success' ? '#22c55e' : (type === 'error' ? '#ef4444' : '#6366f1');
        toast.style.opacity = 0.95;
        toast.innerHTML = message;
        const container = document.getElementById(containerId);
        if (container) {
            container.appendChild(toast);
            setTimeout(() => {
                toast.style.opacity = 0;
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }
    }

    // Animate icon
    function animateIcon(icon, spin = true) {
        if (!icon) return;
        if (spin) {
            icon.classList.add('fa-spin');
            icon.style.opacity = 0.6;
        } else {
            icon.classList.remove('fa-spin');
            icon.style.opacity = 1;
        }
    }

    // Fetch New Reviews (MOBILE)
    const fetchBtn = document.getElementById('fetchReviewsBtn');
    if (fetchBtn) {
        fetchBtn.addEventListener('click', function (e) {
            e.preventDefault();
            if (fetchBtn.disabled) return;
            const icon = document.getElementById('fetchReviewsIcon');
            requestAnimationFrame(() => {
                animateIcon(icon, true);
                fetchBtn.disabled = true;
                fetchBtn.classList.add('opacity-60');
            });
            const businessId = fetchBtn.dataset.businessId;
            const location = fetchBtn.dataset.location;
            const csrf = document.querySelector('meta[name="csrf-token"]')?.content || document.querySelector('input[name="_token"]')?.value;
            fetch('/business/reviews/fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrf
                },
                body: JSON.stringify({ businessId, location })
            })
                .then(res => res.json())
                .then(data => {
                    animateIcon(icon, false);
                    fetchBtn.disabled = false;
                    fetchBtn.classList.remove('opacity-60');
                    showToast(
                        data.success || data.status ?
                        ('Fetched new reviews! ' + (data.message || '')) :
                        ('Failed to fetch reviews. ' + (data.message || '')),
                        data.success || data.status ? 'success' : 'error',
                        3500,
                        'reviewsActionToast'
                    );
                })
                .catch(err => {
                    animateIcon(icon, false);
                    fetchBtn.disabled = false;
                    fetchBtn.classList.remove('opacity-60');
                    showToast('Network error: Could not fetch reviews.', 'error', 3500, 'reviewsActionToast');
                });
        });
    }

    // Sync Reviews (MOBILE)
    const syncBtn = document.getElementById('syncReviewsBtn');
    if (syncBtn) {
        syncBtn.addEventListener('click', function (e) {
            e.preventDefault();
            if (syncBtn.disabled) return;
            const icon = document.getElementById('syncReviewsIcon');
            requestAnimationFrame(() => {
                animateIcon(icon, true);
                syncBtn.disabled = true;
                syncBtn.classList.add('opacity-60');
            });
            const businessId = syncBtn.dataset.businessId;
            const location = syncBtn.dataset.location;
            const csrf = document.querySelector('meta[name="csrf-token"]')?.content || document.querySelector('input[name="_token"]')?.value;
            fetch('/business/reviews/fetch-next', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrf
                },
                body: JSON.stringify({ businessId, location })
            })
                .then(res => res.json())
                .then(data => {
                    animateIcon(icon, false);
                    syncBtn.disabled = false;
                    syncBtn.classList.remove('opacity-60');
                    showToast(
                        data.success || data.status ?
                        ('Sync complete! ' + (data.message || '')) :
                        ('Sync failed. ' + (data.message || '')),
                        data.success || data.status ? 'success' : 'error',
                        3500,
                        'reviewsActionToast'
                    );
                })
                .catch(err => {
                    animateIcon(icon, false);
                    syncBtn.disabled = false;
                    syncBtn.classList.remove('opacity-60');
                    showToast('Network error: Could not sync reviews.', 'error', 3500, 'reviewsActionToast');
                });
        });
    }

    // Fetch New Reviews (DESKTOP)
    const fetchBtnDesktop = document.getElementById('fetchReviewsBtnDesktop');
    if (fetchBtnDesktop) {
        fetchBtnDesktop.addEventListener('click', function (e) {
            e.preventDefault();
            if (fetchBtnDesktop.disabled) return;fetchReviewsIconDesktop
            const icon = document.getElementById('fetchReviewsIconDesktop');
            requestAnimationFrame(() => {
                animateIcon(icon, true);
                fetchBtnDesktop.disabled = true;
                fetchBtnDesktop.classList.add('opacity-60');
            });
            const businessId = fetchBtnDesktop.dataset.businessId;
            const location = fetchBtnDesktop.dataset.location;
            const csrf = document.querySelector('meta[name="csrf-token"]')?.content || document.querySelector('input[name="_token"]')?.value;
            fetch('/business/reviews/fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrf,
                    'Accept': 'application/json' 
                },
                body: JSON.stringify({ businessId, location })
            })
                .then(res => res.json())
                .then(data => {
                    animateIcon(icon, false);
                    fetchBtnDesktop.disabled = false;
                    fetchBtnDesktop.classList.remove('opacity-60');
                    showToast(
                        data.success || data.status ?
                        ('Fetched new reviews! ' + (data.message || '')) :
                        ('Failed to fetch reviews. ' + (data.message || '')),
                        data.success || data.status ? 'success' : 'error',
                        5000,
                        'reviewsActionToastDesktop'
                    );
                })
                .catch(err => {
                    animateIcon(icon, false);
                    fetchBtnDesktop.disabled = false;
                    fetchBtnDesktop.classList.remove('opacity-60');
                    showToast('Network error: Could not fetch reviews.', 'error', 3500, 'reviewsActionToastDesktop');
                });
        });
    }

    // Sync Reviews (DESKTOP)
    const syncBtnDesktop = document.getElementById('syncReviewsBtnDesktop');
    if (syncBtnDesktop) {
        syncBtnDesktop.addEventListener('click', function (e) {
            e.preventDefault();
            if (syncBtnDesktop.disabled) return;
            const icon = document.getElementById('syncReviewsIconDesktop');
            requestAnimationFrame(() => {
                animateIcon(icon, true);
                syncBtnDesktop.disabled = true;
                syncBtnDesktop.classList.add('opacity-60');
            });
            const businessId = syncBtnDesktop.dataset.businessId;
            const location = syncBtnDesktop.dataset.location;
            const csrf = document.querySelector('meta[name="csrf-token"]')?.content || document.querySelector('input[name="_token"]')?.value;
            fetch('/business/reviews/fetch-next', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrf
                },
                body: JSON.stringify({ businessId, location })
            })
                .then(res => res.json())
                .then(data => {
                    animateIcon(icon, false);
                    syncBtnDesktop.disabled = false;
                    syncBtnDesktop.classList.remove('opacity-60');
                    showToast(
                        data.success || data.status ?
                        ('Sync complete! ' + (data.message || '')) :
                        ('Sync failed. ' + (data.message || '')),
                        data.success || data.status ? 'success' : 'error',
                        3500,
                        'reviewsActionToastDesktop'
                    );
                })
                .catch(err => {
                    animateIcon(icon, false);
                    syncBtnDesktop.disabled = false;
                    syncBtnDesktop.classList.remove('opacity-60');
                    showToast('Network error: Could not sync reviews.', 'error', 3500, 'reviewsActionToastDesktop');
                });
        });
    }
}


/**
 * Set up event listeners for all filter controls
 */
function setupFilterEventListeners() {
    // Date range filter
    const dateRangeFilter = document.getElementById('dateRangeFilter');
    if (dateRangeFilter) {
        dateRangeFilter.addEventListener('change', function() {
            reviewFilters.date_range = this.value;
            console.log('Date range changed to:', this.value);
            applyFilters();
        });
    }
    
    // Rating filter buttons
    const ratingButtons = document.querySelectorAll('.btnRating');
    ratingButtons.forEach(button => {
        button.addEventListener('click', function() {
            const rating = this.dataset.rating;
            console.log('Rating button clicked:', rating);

            // Toggle rating filter
            if (reviewFilters.rating === rating) {
                reviewFilters.rating = null;
                this.classList.remove('bg-indigo-100', 'border-indigo-500');
            } else {
                // Remove active state from all rating buttons
                ratingButtons.forEach(btn => {
                    btn.classList.remove('bg-indigo-100', 'border-indigo-500');
                });

                // Add active state to clicked button
                reviewFilters.rating = rating;
                this.classList.add('bg-indigo-100', 'border-indigo-500');
            }

            applyFilters();
        });
    });
    
    // Type filter
    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            reviewFilters.type = this.value;
            console.log('Type filter changed to:', this.value);
            applyFilters();
        });
    }
}

/**
 * Set up order filter functionality
 */
function setupOrderFilter() {
    const orderFilter = document.getElementById('showOrderFilter');
    if (orderFilter) {
        orderFilter.addEventListener('change', function() {
            reviewFilters.order = this.value;
            console.log('Order changed to:', this.value);
            applyFilters();
        });
    }
}

/**
 * Apply current filters and reload reviews
 */
function applyFilters() {
    console.log('Applying filters:', reviewFilters);
    
    // Reset pagination
    paginationState.currentPage = 1;
    paginationState.hasMorePages = true;
    
    // Show loading state
    showFilterLoading();
    
    // Make API request
    //fetchFilteredReviews(true); // true = replace existing reviews
}

/**
 * Fetch filtered reviews from the backend
 */
async function fetchFilteredReviews(replaceExisting = false) {
    if (paginationState.isLoading) {
        console.log('Already loading, skipping request');
        return;
    }
    
    if (!currentBusinessId) {
        console.error('No business ID available');
        return;
    }
    
    paginationState.isLoading = true;
    
    try {
        // Prepare filter parameters
        const params = new URLSearchParams({
            business_id: currentBusinessId,
            account_id: currentAccountId || '',
            page: paginationState.currentPage,
            per_page: 10,
            date_range: reviewFilters.date_range,
            type: reviewFilters.type,
            order: reviewFilters.order
        });
        
        // Add rating filter if selected
        if (reviewFilters.rating) {
            params.append('rating', reviewFilters.rating);
        }
        
        console.log('Fetching reviews with params:', params.toString());
        
        // Make API request
        const response = await fetch(`/businesses/${currentBusinessId}/reviews/${reviewFilters.order}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(filters)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Received filtered reviews:', data);
        
        // Update pagination state
        paginationState.hasMorePages = data.current_page < data.last_page;
        paginationState.totalReviews = data.total;
        
        // Render reviews
        renderFilteredReviews(data.reviews, data.templates, replaceExisting);
        
        // Update load more button
        updateLoadMoreButton();
        
    } catch (error) {
        console.error('Error fetching filtered reviews:', error);
        showFilterError('Failed to load reviews. Please try again.');
    } finally {
        paginationState.isLoading = false;
        hideFilterLoading();
    }
}

/**
 * Render filtered reviews in the UI
 */
function renderFilteredReviews(reviews, templates, replaceExisting = false) {
    const reviewsContainer = document.getElementById('reviewListIdDynamic');
    
    if (!reviewsContainer) {
        console.error('Reviews container not found');
        return;
    }
    
    if (replaceExisting) {
        reviewsContainer.innerHTML = '';
    }
    
    if (!reviews || reviews.length === 0) {
        if (replaceExisting) {
            reviewsContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-gray-500 mb-4">
                        <i class="fas fa-search text-4xl mb-2"></i>
                        <p class="text-lg">No reviews found</p>
                        <p class="text-sm">Try adjusting your filters</p>
                    </div>
                    <button onclick="clearAllFilters()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                        Clear Filters
                    </button>
                </div>
            `;
        }
        return;
    }
    
    // Generate HTML for each review
    reviews.forEach(review => {
        const reviewHtml = generateReviewHtml(review, templates);
        reviewsContainer.insertAdjacentHTML('beforeend', reviewHtml);
    });
    
    // Attach event listeners to new review elements
    attachReviewEventListeners();
}

/**
 * Generate HTML for a single review
 */
function generateReviewHtml(review, templates) {
    const stars = generateStarsHtml(review.star_rating_numeric);
    const hasReplies = review.replies && review.replies.length > 0;
    
    return `
        <div class="review-item bg-white border border-gray-200 rounded-lg p-4 mb-4">
            <div class="flex items-start space-x-3">
                <img src="${review.reviewer_photo || '/images/default-avatar.png'}" 
                     alt="${review.reviewer_name}" 
                     class="w-10 h-10 rounded-full object-cover">
                <div class="flex-1">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">${review.reviewer_name}</h4>
                            <div class="flex items-center space-x-2">
                                <div class="flex text-yellow-400">${stars}</div>
                                <span class="text-sm text-gray-500">${review.created_at}</span>
                            </div>
                        </div>
                        ${!hasReplies ? `
                            <button class="reply-btn bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700"
                                    data-review-id="${review.id}">
                                Reply
                            </button>
                        ` : ''}
                    </div>
                    <p class="mt-2 text-gray-700">${review.comment || 'No comment provided'}</p>
                    
                    ${hasReplies ? `
                        <div class="mt-3 pl-4 border-l-2 border-gray-200">
                            ${review.replies.map(reply => `
                                <div class="bg-gray-50 p-3 rounded">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-sm">${reply.reviewer_name}</span>
                                        <span class="text-xs text-gray-500">${reply.created_at}</span>
                                    </div>
                                    <p class="text-sm text-gray-700">${reply.comment}</p>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

/**
 * Generate stars HTML for rating
 */
function generateStarsHtml(rating) {
    let starsHtml = '';
    for (let i = 1; i <= 5; i++) {
        const starClass = i <= rating ? 'fas fa-star' : 'far fa-star';
        starsHtml += `<i class="${starClass}"></i>`;
    }
    return starsHtml;
}

/**
 * Set up load more functionality
 */
function setupLoadMoreFunctionality() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const loadMoreBtnFilter = document.getElementById('loadMoreBtnFilter');

    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            if (!paginationState.isLoading && paginationState.hasMorePages) {
                paginationState.currentPage++;
                //fetchFilteredReviews(false); // false = append to existing reviews
            }
        });
    }

    if (loadMoreBtnFilter) {
        loadMoreBtnFilter.addEventListener('click', function() {
            if (!paginationState.isLoading && paginationState.hasMorePages) {
                paginationState.currentPage++;
                //fetchFilteredReviews(false); // false = append to existing reviews
            }
        });
    }
}

/**
 * Update load more button state
 */
function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const loadMoreBtnFilter = document.getElementById('loadMoreBtnFilter');
    const loadMoreContainer = document.getElementById('loadMoreDataButton');
    const loadMoreFilterContainer = document.getElementById('loadMoreDataButtonFilter');

    // Determine which button to show based on whether filters are active
    const hasActiveFilters = reviewFilters.date_range !== 'all' ||
                            reviewFilters.rating !== null ||
                            reviewFilters.type !== 'all';

    console.log('Updating load more buttons. Has active filters:', hasActiveFilters, 'Has more pages:', paginationState.hasMorePages);
    
    if (hasActiveFilters) {
        // Show filter load more button
        if (loadMoreContainer) loadMoreContainer.classList.add('hidden');
        if (loadMoreFilterContainer) {
            // Remove hidden class if there are more pages
            if (paginationState.hasMorePages) {
                loadMoreFilterContainer.classList.remove('hidden');
            } else {
                loadMoreFilterContainer.classList.add('hidden');
            }
        }

        if (loadMoreBtnFilter) {
            loadMoreBtnFilter.disabled = paginationState.isLoading;
            // Update data-page attribute to reflect current page
            loadMoreBtnFilter.dataset.page = paginationState.currentPage + 1;
            // Update data-has-more attribute
            loadMoreBtnFilter.dataset.hasMore = paginationState.hasMorePages ? 'true' : 'false';
            
            const spinner = loadMoreBtnFilter.querySelector('.spinner');
            if (spinner) {
                spinner.classList.toggle('hidden', !paginationState.isLoading);
            }
        }
    } else {
        // Show regular load more button
        if (loadMoreFilterContainer) loadMoreFilterContainer.classList.add('hidden');
        if (loadMoreContainer) {
            if (paginationState.hasMorePages) {
                loadMoreContainer.classList.remove('hidden');
            } else {
                loadMoreContainer.classList.add('hidden');
            }
        }

        if (loadMoreBtn) {
            loadMoreBtn.disabled = paginationState.isLoading;
            // Update data-page attribute to reflect current page
            loadMoreBtn.dataset.page = paginationState.currentPage + 1;
            // Update data-has-more attribute
            loadMoreBtn.dataset.hasMore = paginationState.hasMorePages ? 'true' : 'false';
            
            const spinner = loadMoreBtn.querySelector('.spinner');
            if (spinner) {
                spinner.classList.toggle('hidden', !paginationState.isLoading);
            }
        }
    }
}

/**
 * Clear all filters and reload reviews
 */
function clearAllFilters() {
    console.log('Clearing all filters');

    // Reset filter state
    reviewFilters = {
        date_range: 'all',
        rating: null,
        type: 'all',
        order: 'desc'
    };

    // Reset UI elements
    const dateRangeFilter = document.getElementById('dateRangeFilter');
    if (dateRangeFilter) dateRangeFilter.value = 'all';

    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) typeFilter.value = 'all';

    const orderFilter = document.getElementById('showOrderFilter');
    if (orderFilter) orderFilter.value = 'desc';

    // Remove active state from rating buttons
    document.querySelectorAll('.btnRating').forEach(btn => {
        btn.classList.remove('bg-indigo-100', 'border-indigo-500');
    });

    // Reload reviews
    applyFilters();
}

/**
 * Show loading state for filters
 */
function showFilterLoading() {
    const reviewsContainer = document.getElementById('reviewListIdDynamic');
    if (reviewsContainer) {
        reviewsContainer.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-2"></i>
                <p class="text-gray-600">Loading reviews...</p>
            </div>
        `;
    }
}

/**
 * Hide loading state for filters
 */
function hideFilterLoading() {
    // Loading state is replaced by actual content in renderFilteredReviews
}

/**
 * Show error message for filters
 */
function showFilterError(message) {
    const reviewsContainer = document.getElementById('reviewListIdDynamic');
    if (reviewsContainer) {
        reviewsContainer.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-exclamation-triangle text-2xl text-red-600 mb-2"></i>
                <p class="text-red-600 mb-4">${message}</p>
                <button onclick="applyFilters()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                    Try Again
                </button>
            </div>
        `;
    }
}

/**
 * Attach event listeners to review elements
 */
function attachReviewEventListeners() {
    // Attach reply button listeners
    document.querySelectorAll('.reply-btn').forEach(button => {
        if (!button.hasAttribute('data-listener-attached')) {
            button.addEventListener('click', function() {
                const reviewId = this.dataset.reviewId;
                console.log('Reply button clicked for review:', reviewId);

                // Call reply generator if available
                if (typeof window.showReplyGenerator === 'function') {
                    window.showReplyGenerator(reviewId);
                } else {
                    console.error('Reply generator function not available');
                }
            });
            button.setAttribute('data-listener-attached', 'true');
        }
    });
}

// Make functions globally available
window.initializeReviewFilters = initializeReviewFilters;
window.applyFilters = applyFilters;
window.clearAllFilters = clearAllFilters;
// window.fetchFilteredReviews = fetchFilteredReviews;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {

    // Add a small delay to ensure all elements are rendered
    setTimeout(() => {
        initializeReviewFilters();
    }, 500);
});
