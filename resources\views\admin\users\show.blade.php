@extends('admin.layouts.app')

@section('title', 'User Details - ' . ($user->name ?? $user->email))
@section('page-title', 'User Details')

@section('content')
<div class="space-y-6">
    <!-- User Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($user->image)
                            <img class="h-16 w-16 rounded-full" src="{{ $user->image }}" alt="{{ $user->name }}">
                        @else
                            <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-xl font-medium text-gray-700">
                                    {{ substr($user->name ?? $user->email, 0, 1) }}
                                </span>
                            </div>
                        @endif
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-gray-900">{{ $user->name ?? 'No Name' }}</h1>
                        <p class="text-sm text-gray-500">{{ $user->email }}</p>
                        @if($user->phone_number)
                            <p class="text-sm text-gray-500">{{ $user->country_code }}{{ $user->phone_number }}</p>
                        @endif
                        <div class="flex items-center mt-2">
                            @if($user->hasActiveSubscription())
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Active Subscription
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    No Active Subscription
                                </span>
                            @endif
                            @if($user->google_id)
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fab fa-google mr-1"></i>
                                    Google Account
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button onclick="openResetPasswordModal()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-key mr-2"></i>
                        Reset Password
                    </button>
                    <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}" class="inline">
                        @csrf
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                            <i class="fas fa-user-slash mr-2"></i>
                            Toggle Status
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-building text-blue-500 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Owned Businesses</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $ownedBusinesses->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-handshake text-green-500 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Associated Businesses</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $associatedBusinesses->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-purple-500 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Team Memberships</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $teamMemberships->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-credit-card text-yellow-500 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Payments</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $payments->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-6">
            <!-- User Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">User Information</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $user->name ?? 'Not provided' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $user->email }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ $user->phone_number ? $user->country_code . $user->phone_number : 'Not provided' }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Registration Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('M d, Y g:i A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $user->updated_at->format('M d, Y g:i A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Google ID</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $user->google_id ?? 'Not connected' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Stripe Customer ID</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $user->stripe_customer_id ?? 'Not available' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">First Login Popup</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ $user->first_login_popup_shown ? 'Shown' : 'Not shown' }}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Owned Businesses -->
            @if($ownedBusinesses->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Owned Businesses</h3>
                    <div class="space-y-4">
                        @foreach($ownedBusinesses as $business)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $business->title }}</h4>
                                        <p class="text-sm text-gray-500">{{ $business->location_name }}</p>
                                        @if($business->primary_phone)
                                            <p class="text-sm text-gray-500">{{ $business->primary_phone }}</p>
                                        @endif
                                        <div class="flex items-center mt-2 space-x-4">
                                            @if($business->average_rating)
                                                <span class="text-sm text-gray-500">
                                                    <i class="fas fa-star text-yellow-400"></i>
                                                    {{ number_format($business->average_rating, 1) }}
                                                </span>
                                            @endif
                                            <span class="text-sm text-gray-500">
                                                <i class="fas fa-comments"></i>
                                                {{ $business->total_reviews }} reviews
                                            </span>
                                            <span class="text-sm text-gray-500">
                                                <i class="fas fa-users"></i>
                                                {{ $business->teamMembers->count() }} team members
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Team Memberships -->
            @if($teamMemberships->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Team Memberships</h3>
                    <div class="space-y-4">
                        @foreach($teamMemberships as $membership)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $membership->business->title }}</h4>
                                        <p class="text-sm text-gray-500">{{ $membership->business->location_name }}</p>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ ucfirst($membership->role) }}
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                {{ $membership->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                {{ ucfirst($membership->status) }}
                                            </span>
                                            @if($membership->invitedBy)
                                                <span class="text-sm text-gray-500">
                                                    Invited by {{ $membership->invitedBy->name }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Associated Businesses -->
            @if($associatedBusinesses->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Associated Businesses</h3>
                    <div class="space-y-4">
                        @foreach($associatedBusinesses as $business)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-900">{{ $business->title }}</h4>
                                <p class="text-sm text-gray-500">{{ $business->location_name }}</p>
                                @if($business->primary_phone)
                                    <p class="text-sm text-gray-500">{{ $business->primary_phone }}</p>
                                @endif
                                <div class="flex items-center mt-2 space-x-4">
                                    @if($business->average_rating)
                                        <span class="text-sm text-gray-500">
                                            <i class="fas fa-star text-yellow-400"></i>
                                            {{ number_format($business->average_rating, 1) }}
                                        </span>
                                    @endif
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-comments"></i>
                                        {{ $business->total_reviews }} reviews
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Current Subscription -->
            @if($user->subscriptions->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Current Subscription</h3>
                    @php $activeSubscription = $user->subscriptions->where('status', 'ACTIVE')->first(); @endphp
                    @if($activeSubscription)
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Plan:</span>
                                <span class="ml-2 text-sm text-gray-900">{{ $activeSubscription->plan->name }}</span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Status:</span>
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ $activeSubscription->status }}
                                </span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Start Date:</span>
                                <span class="ml-2 text-sm text-gray-900">{{ $activeSubscription->start_date->format('M d, Y') }}</span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Expiry Date:</span>
                                <span class="ml-2 text-sm text-gray-900">{{ $activeSubscription->expiry_date->format('M d, Y') }}</span>
                            </div>
                        </div>
                    @else
                        <p class="text-sm text-gray-500">No active subscription</p>
                    @endif
                    <div class="mt-4">
                        <a href="{{ route('admin.users.subscriptions', $user) }}" 
                           class="text-sm text-indigo-600 hover:text-indigo-500">
                            View all subscriptions →
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <!-- Subscription Utilization -->
            @if($subscriptionUtilization)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Subscription Utilization</h3>
                    <div class="space-y-4">
                        @foreach($subscriptionUtilization as $key => $util)
                            <div>
                                <div class="flex justify-between text-sm">
                                    <span class="font-medium text-gray-700">{{ $util['title'] }}</span>
                                    <span class="text-gray-500">{{ $util['used'] }}/{{ $util['limit'] }}</span>
                                </div>
                                <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" 
                                         style="width: {{ $util['limit'] > 0 ? ($util['used'] / $util['limit']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Recent Activity -->
            @if($recentActivity->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div class="space-y-3">
                        @foreach($recentActivity->take(5) as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm text-gray-900">{{ $activity->activity_description }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ $activity->created_at->diffForHumans() }}
                                        @if($activity->business)
                                            • {{ $activity->business->title }}
                                        @endif
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('admin.users.activity', $user) }}" 
                           class="text-sm text-indigo-600 hover:text-indigo-500">
                            View all activity →
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <!-- Recent Payments -->
            @if($payments->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Payments</h3>
                    <div class="space-y-3">
                        @foreach($payments->take(5) as $payment)
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $payment->plan->name ?? 'N/A' }}</p>
                                    <p class="text-xs text-gray-500">{{ $payment->payment_date->format('M d, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">
                                        {{ $payment->currency }} {{ number_format($payment->amount, 2) }}
                                    </p>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium 
                                        {{ $payment->payment_status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ ucfirst($payment->payment_status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('admin.users.payments', $user) }}" 
                           class="text-sm text-indigo-600 hover:text-indigo-500">
                            View all payments →
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('admin.users.subscriptions', $user) }}" 
                           class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                            <i class="fas fa-credit-card mr-2"></i>
                            View Subscriptions
                        </a>
                        <a href="{{ route('admin.users.payments', $user) }}" 
                           class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                            <i class="fas fa-receipt mr-2"></i>
                            View Payments
                        </a>
                        <a href="{{ route('admin.users.activity', $user) }}" 
                           class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                            <i class="fas fa-history mr-2"></i>
                            View Activity Log
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Reset User Password</h3>
            <form method="POST" action="{{ route('admin.users.reset-password', $user) }}">
                @csrf
                <div class="mb-4">
                    <label for="new_password" class="block text-sm font-medium text-gray-700">New Password</label>
                    <input type="password" 
                           name="new_password" 
                           id="new_password"
                           required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="mb-4">
                    <label for="new_password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input type="password" 
                           name="new_password_confirmation" 
                           id="new_password_confirmation"
                           required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" 
                            onclick="closeResetPasswordModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700">
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openResetPasswordModal() {
    document.getElementById('resetPasswordModal').classList.remove('hidden');
}

function closeResetPasswordModal() {
    document.getElementById('resetPasswordModal').classList.add('hidden');
}
</script>
@endsection
