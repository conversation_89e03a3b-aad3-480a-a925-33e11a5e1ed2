<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>

<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <h2 class="text-center">Pay with Razorpay</h2>
                <form>
                    <input type="hidden" id="razorpay_payment_id" name="razorpay_payment_id">
                    <button type="button" id="rzp-button" class="btn btn-primary w-100">Pay ₹{{ $amount / 100 }}</button>
                </form>
            </div>
        </div>
    </div>
    <script>
        var options = {
            "key": "<?php echo $razorpay_key ?>",
            "amount": "<?php echo $amount ?>",
            "currency": "INR",
            "name": "Business Reviews",
            "description": "Payment for Subscription",
            "order_id": "{{ $order_id }}",
            "handler": function(response) {
                document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
                window.location.href = "{{ route('payment.success') }}?razorpay_payment_id=" + response.razorpay_payment_id +
                    "&payment_id={{ $payment_id }}&business_id={{ $business_id }}&plan_id={{ $plan_id }}";
            },
            "prefill": {
                "name": "{{ $name }}",
                "email": "{{ $email }}",
            },
            "theme": {
                "color": "#3399cc"
            }
        }

        console.log(options);
        var rzp1 = new Razorpay(options);

        // Open Razorpay checkout on button click
        document.getElementById('rzp-button').onclick = function(e) {
            rzp1.open();
            e.preventDefault();
        };
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>