<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    public function manageSetting(Request $request)
    {
        $data = $request->json()->all();
        //return response()->json(['success' => false, 'message' => 'all data', 'data' => $data]);
        if (!$data) {
            return response()->json(['success' => false, 'message' => 'No data provided', 'data' => []]);
        }

        $setting = null;

        if (!$setting && !empty($data['business_id'])) {
            $setting = Setting::where('business_id', $data['business_id'])->first();
        }

        $attributes = [
            'business_id' => $data['business_id'],
            'ai_provider' => $data['aiProvider'],
            'model_version' => $data['aiModel'],
            'response_style' => $data['responseStyle'],
            'response_length' => $data['responseLength'],
            'custom_signoff_text' => $data['signOffText'],
            'custom_instruction' => $data['customInstructions'],
            'response_language' => $data['aiLanguage'],
            'auto_check_reviews' => $data['reviewCheckingEnabled'],
            'check_interval_minutes' => $data['reviewCheckingInterval'],
            'auto_reply' => $data['autoReplyEnabled'],
            'review_length_filter' => $data['autoReplyConditionsLength'],
            'reply_timing' => $data['autoReplyTimingMode'],
            'timezone' => $data['timezone'],
            'auto_reply_settings' => $data['auto_reply_settings'],
            'from_auto_reply_date' => $data['from_auto_reply_date']
        ];

        if ($setting) {
            $setting->update($attributes);
            $message = 'Settings updated successfully';
        } else {
            Setting::create($attributes);
            $message = 'Settings created successfully';
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $attributes
        ]);
    }


    public function getSettings(Request $request, $id)
    {
        $setting = Setting::where('business_id', $id)->first();
        if ($setting) {
            return response()->json(['success' => true, 'message' => 'Settings found', 'data' => $setting]);
        } else {
            return response()->json(['success' => false, 'message' => 'Settings not found', 'data' => []]);
        }
    }
}
