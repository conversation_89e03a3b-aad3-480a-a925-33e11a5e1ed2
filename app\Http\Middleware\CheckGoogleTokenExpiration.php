<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\GoogleBusinessService;
use Illuminate\Support\Facades\Auth;

class CheckGoogleTokenExpiration
{
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        if ($user) {
            // Check and refresh business account tokens if expired using enhanced GoogleBusinessService
            if ($user->businessAccounts && $user->businessAccounts->count() > 0) {
                $googleBusinessService = new GoogleBusinessService(null);

                foreach ($user->businessAccounts as $account) {
                    if ($account->business_refresh_token) {
                        // Use the enhanced validateAndRefreshBusinessToken method
                        $result = $googleBusinessService->validateAndRefreshBusinessToken($account);

                        if ($result['refreshed']) {
                            \Illuminate\Support\Facades\Log::info('Token refreshed in middleware', [
                                'business_account_id' => $account->id,
                                'user_id' => $user->id
                            ]);
                        }
                    }
                }
            }
        }

        return $next($request);
    }
}
