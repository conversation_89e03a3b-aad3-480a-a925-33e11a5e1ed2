<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('external_api_logs', function (Blueprint $table) {
            // Add new columns for better admin viewing
            $table->string('method', 10)->default('GET')->after('endpoint'); // HTTP method
            $table->json('request_headers')->nullable()->after('request_payload'); // Request headers
            $table->json('response_headers')->nullable()->after('response_payload'); // Response headers
            $table->string('ip_address', 45)->nullable()->after('user_id'); // Client IP address
            $table->text('user_agent')->nullable()->after('ip_address'); // User agent
            $table->string('session_id', 100)->nullable()->after('user_agent'); // Session identifier
            $table->text('error_message')->nullable()->after('status_message'); // Detailed error message
            $table->boolean('is_retry')->default(false)->after('error_message'); // Is this a retry attempt
            $table->unsignedInteger('retry_count')->default(0)->after('is_retry'); // Number of retries
            
            // Add indexes for better performance in admin queries
            $table->index(['status_code', 'created_at']);
            //$table->index(['business_id', 'created_at']);
            $table->index(['is_retry', 'retry_count']);
            $table->index('ip_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('external_api_logs', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['status_code', 'created_at']);
            $table->dropIndex(['business_id', 'created_at']);
            $table->dropIndex(['is_retry', 'retry_count']);
            $table->dropIndex(['ip_address']);
            
            // Drop columns
            $table->dropColumn([
                'method', 'request_headers', 'response_headers', 
                'ip_address', 'user_agent', 'session_id', 
                'error_message', 'is_retry', 'retry_count'
            ]);
        });
    }
};
