@extends('admin.layouts.app')

@section('title', 'User Activity - ' . ($user->name ?? $user->email))
@section('page-title', 'User Activity')

@section('content')
<div class="space-y-6">
    <!-- User Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($user->image)
                            <img class="h-12 w-12 rounded-full" src="{{ $user->image }}" alt="{{ $user->name }}">
                        @else
                            <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-lg font-medium text-gray-700">
                                    {{ substr($user->name ?? $user->email, 0, 1) }}
                                </span>
                            </div>
                        @endif
                    </div>
                    <div class="ml-4">
                        <h1 class="text-xl font-bold text-gray-900">{{ $user->name ?? 'No Name' }}</h1>
                        <p class="text-sm text-gray-500">{{ $user->email }}</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.users.show', $user) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    @if($activities->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Activity Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $activities->total() }}</div>
                    <div class="text-sm text-gray-500">Total Activities</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ $activities->where('activity_category', 'review_management')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Review Management</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ $activities->where('activity_category', 'business_management')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Business Management</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ $activities->where('activity_category', 'team_management')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Team Management</div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Activity Timeline -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Activity Timeline</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Chronological list of user activities</p>
        </div>
        
        @if($activities->count() > 0)
            <div class="flow-root">
                <ul class="-mb-8">
                    @foreach($activities as $index => $activity)
                        <li>
                            <div class="relative pb-8">
                                @if(!$loop->last)
                                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                @endif
                                <div class="relative flex space-x-3 px-4 py-4">
                                    <div>
                                        <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white
                                            {{ $activity->activity_category === 'review_management' ? 'bg-blue-500' : 
                                               ($activity->activity_category === 'business_management' ? 'bg-green-500' : 
                                               ($activity->activity_category === 'team_management' ? 'bg-purple-500' : 
                                               ($activity->activity_category === 'template_management' ? 'bg-yellow-500' : 
                                               ($activity->activity_category === 'settings' ? 'bg-gray-500' : 'bg-indigo-500')))) }}">
                                            @switch($activity->activity_category)
                                                @case('review_management')
                                                    <i class="fas fa-star text-white text-xs"></i>
                                                    @break
                                                @case('business_management')
                                                    <i class="fas fa-building text-white text-xs"></i>
                                                    @break
                                                @case('team_management')
                                                    <i class="fas fa-users text-white text-xs"></i>
                                                    @break
                                                @case('template_management')
                                                    <i class="fas fa-file-alt text-white text-xs"></i>
                                                    @break
                                                @case('settings')
                                                    <i class="fas fa-cog text-white text-xs"></i>
                                                    @break
                                                @case('api_usage')
                                                    <i class="fas fa-code text-white text-xs"></i>
                                                    @break
                                                @case('data_export')
                                                    <i class="fas fa-download text-white text-xs"></i>
                                                    @break
                                                @case('automation')
                                                    <i class="fas fa-robot text-white text-xs"></i>
                                                    @break
                                                @default
                                                    <i class="fas fa-circle text-white text-xs"></i>
                                            @endswitch
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <p class="font-medium text-gray-900">{{ $activity->activity_description }}</p>
                                            </div>
                                            <div class="mt-1 flex items-center space-x-4">
                                                <p class="text-sm text-gray-500">
                                                    <time datetime="{{ $activity->created_at->toISOString() }}">
                                                        {{ $activity->created_at->format('M d, Y g:i A') }}
                                                    </time>
                                                </p>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    {{ $activity->activity_category === 'review_management' ? 'bg-blue-100 text-blue-800' : 
                                                       ($activity->activity_category === 'business_management' ? 'bg-green-100 text-green-800' : 
                                                       ($activity->activity_category === 'team_management' ? 'bg-purple-100 text-purple-800' : 
                                                       ($activity->activity_category === 'template_management' ? 'bg-yellow-100 text-yellow-800' : 
                                                       ($activity->activity_category === 'settings' ? 'bg-gray-100 text-gray-800' : 'bg-indigo-100 text-indigo-800')))) }}">
                                                    {{ ucfirst(str_replace('_', ' ', $activity->activity_category)) }}
                                                </span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    {{ ucfirst($activity->performed_by_type) }}
                                                </span>
                                                @if($activity->business)
                                                    <div class="flex items-center text-sm text-gray-500">
                                                        <i class="fas fa-building mr-1"></i>
                                                        {{ $activity->business->title }}
                                                    </div>
                                                @endif
                                            </div>
                                            @if($activity->activity_details)
                                                <div class="mt-2">
                                                    <button onclick="toggleActivityDetails('{{ $activity->id }}')" 
                                                            class="text-sm text-indigo-600 hover:text-indigo-500">
                                                        <i class="fas fa-chevron-down mr-1" id="chevron-{{ $activity->id }}"></i>
                                                        View Details
                                                    </button>
                                                    <div id="details-{{ $activity->id }}" class="hidden mt-2 p-3 bg-gray-50 rounded-md">
                                                        @if(is_array($activity->activity_details))
                                                            <dl class="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                                                                @foreach($activity->activity_details as $key => $value)
                                                                    <div>
                                                                        <dt class="text-xs font-medium text-gray-500">{{ ucfirst(str_replace('_', ' ', $key)) }}</dt>
                                                                        <dd class="text-xs text-gray-900">{{ is_array($value) ? json_encode($value) : $value }}</dd>
                                                                    </div>
                                                                @endforeach
                                                            </dl>
                                                        @else
                                                            <p class="text-sm text-gray-700">{{ $activity->activity_details }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
            
            <!-- Pagination -->
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                {{ $activities->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No activity found</h3>
                <p class="text-gray-500">This user has no recorded activity.</p>
            </div>
        @endif
    </div>
</div>

<script>
function toggleActivityDetails(activityId) {
    const details = document.getElementById('details-' + activityId);
    const chevron = document.getElementById('chevron-' + activityId);
    
    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        chevron.classList.remove('fa-chevron-down');
        chevron.classList.add('fa-chevron-up');
    } else {
        details.classList.add('hidden');
        chevron.classList.remove('fa-chevron-up');
        chevron.classList.add('fa-chevron-down');
    }
}
</script>
@endsection
