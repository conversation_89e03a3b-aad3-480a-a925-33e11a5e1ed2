<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $fillable = ['user_id', 'plan_id', 'business_id', 'payment_id', 'status', 'start_date', 'expiry_date', 'features'];

    protected $casts = [
        'start_date' => 'datetime',
        'expiry_date' => 'datetime',
        'features' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id', 'id');
    }

    public function business()
    {
        return $this->belongsTo(Business::class, 'business_id', 'id');
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'payment_id', 'id');
    }

    /**
     * Get feature value from subscription features
     */
    public function getFeatureValue(string $key)
    {
        if (!$this->features || !is_array($this->features)) {
            return null;
        }

        foreach ($this->features as $feature) {
            if (isset($feature['key']) && $feature['key'] === $key) {
                return $feature['value'] ?? null;
            }
        }

        return null;
    }

    /**
     * Check if subscription has specific feature enabled
     */
    public function hasFeature(string $key): bool
    {
        $value = $this->getFeatureValue($key);

        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'enabled', 'custom']);
        }

        if (is_numeric($value)) {
            return $value > 0;
        }

        return false;
    }
}
