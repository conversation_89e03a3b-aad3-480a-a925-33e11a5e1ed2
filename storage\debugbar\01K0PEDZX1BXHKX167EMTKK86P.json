{"__meta": {"id": "01K0PEDZX1BXHKX167EMTKK86P", "datetime": "2025-07-21 12:13:12", "utime": **********.994503, "method": "GET", "uri": "/business-dashboard", "ip": "127.0.0.1"}, "messages": {"count": 9, "messages": [{"message": "[12:13:12] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php on line 61", "message_html": null, "is_string": false, "label": "warning", "time": **********.175641, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.warning: Optional parameter $reviewText declared before required parameter $starRating is implicitly treated as a required parameter in C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php on line 290", "message_html": null, "is_string": false, "label": "warning", "time": **********.182182, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.info: Token still valid, skipping refresh {\n    \"business_account_id\": 1,\n    \"expires_at\": \"2025-07-21T12:35:31.000000Z\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.219716, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php on line 61", "message_html": null, "is_string": false, "label": "warning", "time": **********.77283, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.debug: Total reviews counted {\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\",\n    \"count\": 15\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.865634, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.debug: Calculating rating statistics {\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.866797, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.debug: Rating statistics calculated {\n    \"avgRating\": \"4.7\",\n    \"totalRatings\": 15,\n    \"ratingCounts\": {\n        \"FIVE\": 12,\n        \"FOUR\": 2,\n        \"THREE\": 0,\n        \"TWO\": 1,\n        \"ONE\": 0\n    },\n    \"sumRatings\": 70\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.87667, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.debug: Calculating response metrics {\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.877131, "xdebug_link": null, "collector": "log"}, {"message": "[12:13:12] LOG.debug: Response metrics calculated {\n    \"responseRate\": 40,\n    \"avgResponseTime\": 24445,\n    \"reviewsWithReplies\": 6,\n    \"totalReviews\": 15,\n    \"validResponseCount\": 6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.89703, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.794264, "end": **********.994537, "duration": 2.***************, "duration_str": "2.2s", "measures": [{"label": "Booting", "start": **********.794264, "relative_start": 0, "end": **********.264017, "relative_end": **********.264017, "duration": 0.****************, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.264029, "relative_start": 0.*****************, "end": **********.994539, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.297103, "relative_start": 0.****************, "end": **********.304269, "relative_end": **********.304269, "duration": 0.007166147232055664, "duration_str": "7.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.938712, "relative_start": 2.****************, "end": **********.991474, "relative_end": **********.991474, "duration": 0.*****************, "duration_str": "52.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: business-dashboard", "start": **********.942808, "relative_start": 2.****************, "end": **********.942808, "relative_end": **********.942808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.header-nav", "start": **********.964355, "relative_start": 2.***************, "end": **********.964355, "relative_end": **********.964355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.upgrade-subscription-modal", "start": **********.967118, "relative_start": 2.172853946685791, "end": **********.967118, "relative_end": **********.967118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.reviews-filter", "start": **********.971753, "relative_start": 2.1774888038635254, "end": **********.971753, "relative_end": **********.971753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partial-reviews", "start": **********.973576, "relative_start": 2.179311990737915, "end": **********.973576, "relative_end": **********.973576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.analytics", "start": **********.975345, "relative_start": 2.1810808181762695, "end": **********.975345, "relative_end": **********.975345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.templates", "start": **********.976955, "relative_start": 2.1826908588409424, "end": **********.976955, "relative_end": **********.976955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.settings", "start": **********.979078, "relative_start": 2.184813976287842, "end": **********.979078, "relative_end": **********.979078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.sidebar", "start": **********.9882, "relative_start": 2.1939358711242676, "end": **********.9882, "relative_end": **********.9882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 41636192, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "business-dashboard", "param_count": null, "params": [], "start": **********.942708, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/business-dashboard.blade.phpbusiness-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fbusiness-dashboard.blade.php&line=1", "ajax": false, "filename": "business-dashboard.blade.php", "line": "?"}}, {"name": "partials.header-nav", "param_count": null, "params": [], "start": **********.964241, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/header-nav.blade.phppartials.header-nav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fheader-nav.blade.php&line=1", "ajax": false, "filename": "header-nav.blade.php", "line": "?"}}, {"name": "components.upgrade-subscription-modal", "param_count": null, "params": [], "start": **********.966845, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/components/upgrade-subscription-modal.blade.phpcomponents.upgrade-subscription-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fcomponents%2Fupgrade-subscription-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-subscription-modal.blade.php", "line": "?"}}, {"name": "partials.reviews-filter", "param_count": null, "params": [], "start": **********.971668, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/reviews-filter.blade.phppartials.reviews-filter", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Freviews-filter.blade.php&line=1", "ajax": false, "filename": "reviews-filter.blade.php", "line": "?"}}, {"name": "partial-reviews", "param_count": null, "params": [], "start": **********.973481, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partial-reviews.blade.phppartial-reviews", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartial-reviews.blade.php&line=1", "ajax": false, "filename": "partial-reviews.blade.php", "line": "?"}}, {"name": "partials.analytics", "param_count": null, "params": [], "start": **********.975244, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/analytics.blade.phppartials.analytics", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "partials.templates", "param_count": null, "params": [], "start": **********.976872, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/templates.blade.phppartials.templates", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Ftemplates.blade.php&line=1", "ajax": false, "filename": "templates.blade.php", "line": "?"}}, {"name": "partials.settings", "param_count": null, "params": [], "start": **********.978977, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/settings.blade.phppartials.settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fsettings.blade.php&line=1", "ajax": false, "filename": "settings.blade.php", "line": "?"}}, {"name": "partials.sidebar", "param_count": null, "params": [], "start": **********.988103, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/sidebar.blade.phppartials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}]}, "queries": {"count": 38, "nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04134000000000001, "accumulated_duration_str": "41.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt' limit 1", "type": "query", "params": [], "bindings": ["R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.339494, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "review_master", "explain": null, "start_percent": 0, "width_percent": 4.354}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.363976, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 4.354, "width_percent": 3.895}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` = 1 and `business_accounts`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.376702, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "CheckGoogleTokenExpiration.php:18", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FMiddleware%2FCheckGoogleTokenExpiration.php&line=18", "ajax": false, "filename": "CheckGoogleTokenExpiration.php", "line": "18"}, "connection": "review_master", "explain": null, "start_percent": 8.249, "width_percent": 3.435}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************', '[]', '{\\\"azp\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"aud\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"sub\\\":\\\"110486499747300774507\\\",\\\"scope\\\":\\\"https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/business.manage https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.email https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.profile openid\\\",\\\"exp\\\":\\\"1753101336\\\",\\\"expires_in\\\":\\\"1340\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_verified\\\":\\\"true\\\",\\\"access_type\\\":\\\"offline\\\"}', 200, 'success', 130, null, 1, '2025-07-21 12:13:11', '2025-07-21 12:13:11')", "type": "query", "params": [], "bindings": ["google_api", "https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************", "[]", "{\"azp\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"aud\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"sub\":\"110486499747300774507\",\"scope\":\"https:\\/\\/www.googleapis.com\\/auth\\/business.manage https:\\/\\/www.googleapis.com\\/auth\\/userinfo.email https:\\/\\/www.googleapis.com\\/auth\\/userinfo.profile openid\",\"exp\":\"1753101336\",\"expires_in\":\"1340\",\"email\":\"<EMAIL>\",\"email_verified\":\"true\",\"access_type\":\"offline\"}", 200, "success", 130, null, 1, "2025-07-21 12:13:11", "2025-07-21 12:13:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 99}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 236}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 24}], "start": **********.515289, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 11.684, "width_percent": 2.201}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.5260708, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:441", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=441", "ajax": false, "filename": "BusinessController.php", "line": "441"}, "connection": "review_master", "explain": null, "start_percent": 13.885, "width_percent": 1.476}, {"sql": "select * from `team_members` where `team_members`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.5307362, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:441", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=441", "ajax": false, "filename": "BusinessController.php", "line": "441"}, "connection": "review_master", "explain": null, "start_percent": 15.36, "width_percent": 1.524}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.536503, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:441", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=441", "ajax": false, "filename": "BusinessController.php", "line": "441"}, "connection": "review_master", "explain": null, "start_percent": 16.884, "width_percent": 2.395}, {"sql": "select exists(select * from `team_members` where `user_id` = 1) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 450}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.540989, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:450", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 450}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=450", "ajax": false, "filename": "BusinessController.php", "line": "450"}, "connection": "review_master", "explain": null, "start_percent": 19.279, "width_percent": 1.306}, {"sql": "select * from `businesses` where (`user_id` = 1 and `status` = 'active') limit 1", "type": "query", "params": [], "bindings": [1, "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.545635, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:465", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=465", "ajax": false, "filename": "BusinessController.php", "line": "465"}, "connection": "review_master", "explain": null, "start_percent": 20.585, "width_percent": 3.749}, {"sql": "select * from `settings` where `settings`.`business_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.558393, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:465", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=465", "ajax": false, "filename": "BusinessController.php", "line": "465"}, "connection": "review_master", "explain": null, "start_percent": 24.335, "width_percent": 2.806}, {"sql": "select * from `subscriptions` where `subscriptions`.`user_id` = 1 and `subscriptions`.`user_id` is not null and `status` = 'ACTIVE'", "type": "query", "params": [], "bindings": [1, "ACTIVE"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 480}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.565769, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:480", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 480}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=480", "ajax": false, "filename": "BusinessController.php", "line": "480"}, "connection": "review_master", "explain": null, "start_percent": 27.141, "width_percent": 2.709}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 493}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.572558, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:493", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 493}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=493", "ajax": false, "filename": "BusinessController.php", "line": "493"}, "connection": "review_master", "explain": null, "start_percent": 29.85, "width_percent": 1.451}, {"sql": "select * from `cache` where `key` in ('reviewmasterai_cache_fetched_reviews_110486499747300774507_locations/13522179532217756997')", "type": "query", "params": [], "bindings": ["reviewmasterai_cache_fetched_reviews_110486499747300774507_locations/13522179532217756997"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.576893, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "review_master", "explain": null, "start_percent": 31.301, "width_percent": 1.137}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng', null, '{\\\"locations\\\":[{\\\"name\\\":\\\"locations\\\\/13522179532217756997\\\",\\\"title\\\":\\\"IndiaNIC Infotech Limited (Udaipur)\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"079 6191 6000\\\",\\\"additionalPhones\\\":[\\\"0294 252 7111\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"313001\\\",\\\"administrativeArea\\\":\\\"Rajasthan\\\",\\\"locality\\\":\\\"Udaipur\\\",\\\"addressLines\\\":[\\\"24, <PERSON><PERSON><PERSON>\\\",\\\"Shakti Nagar\\\",\\\"above Andhra Bank\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.indianic.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":24.5848982,\\\"longitude\\\":73.6984058},\\\"profile\\\":{\\\"description\\\":\\\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\\\n\\\\nThis place represents the Udaipur Development Center.\\\"}},{\\\"name\\\":\\\"locations\\\\/2713736367053130525\\\",\\\"title\\\":\\\"TopSpin Club\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"099306 63000\\\"},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"380015\\\",\\\"administrativeArea\\\":\\\"Gujarat\\\",\\\"locality\\\":\\\"Ahmedabad\\\",\\\"addressLines\\\":[\\\"101\\\\/3, DevArc Mall\\\",\\\"SG Highway\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.portray.work\\\\/topspinclub\\\",\\\"latlng\\\":{\\\"latitude\\\":23.0257952,\\\"longitude\\\":72.5075995},\\\"profile\\\":{\\\"description\\\":\\\"Announcing TopSpin Club. Ahmedabad\\'s first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\\\n\\\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad\\'s first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\\\n\\\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\\\n\\\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\\\"}},{\\\"name\\\":\\\"locations\\\\/3612416279359725233\\\",\\\"title\\\":\\\"NIC Gulf Software House LLC\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"04 834 2243\\\",\\\"additionalPhones\\\":[\\\"************\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"AE\\\",\\\"languageCode\\\":\\\"ar-Latn\\\",\\\"administrativeArea\\\":\\\"Dubai\\\",\\\"addressLines\\\":[\\\"Westburry office towers, Marasi Dr, Business Bay\\\",\\\"Suite 604\\\"]},\\\"websiteUri\\\":\\\"http:\\\\/\\\\/www.nicgulf.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":25.1858249,\\\"longitude\\\":55.2748255},\\\"profile\\\":{\\\"description\\\":\\\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\\\"}}]}', 200, 'success', 558, null, 1, '2025-07-21 12:13:12', '2025-07-21 12:13:12')", "type": "query", "params": [], "bindings": ["google_api", "https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng", null, "{\"locations\":[{\"name\":\"locations\\/13522179532217756997\",\"title\":\"IndiaNIC Infotech Limited (Udaipur)\",\"phoneNumbers\":{\"primaryPhone\":\"079 6191 6000\",\"additionalPhones\":[\"0294 252 7111\"]},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"313001\",\"administrativeArea\":\"Rajasthan\",\"locality\":\"Udaipur\",\"addressLines\":[\"24, <PERSON><PERSON><PERSON> Bhawan\",\"Shakti Nagar\",\"above Andhra Bank\"]},\"websiteUri\":\"https:\\/\\/www.indianic.com\\/\",\"latlng\":{\"latitude\":24.5848982,\"longitude\":73.6984058},\"profile\":{\"description\":\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\n\\nThis place represents the Udaipur Development Center.\"}},{\"name\":\"locations\\/2713736367053130525\",\"title\":\"TopSpin Club\",\"phoneNumbers\":{\"primaryPhone\":\"099306 63000\"},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"380015\",\"administrativeArea\":\"Gujarat\",\"locality\":\"Ahmedabad\",\"addressLines\":[\"101\\/3, DevArc Mall\",\"SG Highway\"]},\"websiteUri\":\"https:\\/\\/www.portray.work\\/topspinclub\",\"latlng\":{\"latitude\":23.0257952,\"longitude\":72.5075995},\"profile\":{\"description\":\"Announcing TopSpin Club. Ahmedabad's first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\n\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad's first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\n\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\n\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\"}},{\"name\":\"locations\\/3612416279359725233\",\"title\":\"NIC Gulf Software House LLC\",\"phoneNumbers\":{\"primaryPhone\":\"04 834 2243\",\"additionalPhones\":[\"************\"]},\"storefrontAddress\":{\"regionCode\":\"AE\",\"languageCode\":\"ar-Latn\",\"administrativeArea\":\"Dubai\",\"addressLines\":[\"Westburry office towers, Marasi Dr, Business Bay\",\"Suite 604\"]},\"websiteUri\":\"http:\\/\\/www.nicgulf.com\\/\",\"latlng\":{\"latitude\":25.1858249,\"longitude\":55.2748255},\"profile\":{\"description\":\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\"}}]}", 200, "success", 558, null, 1, "2025-07-21 12:13:12", "2025-07-21 12:13:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 680}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 511}], "start": **********.176929, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 32.438, "width_percent": 1.863}, {"sql": "select * from `businesses` where (`location_name` = 'locations/13522179532217756997' and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["locations/13522179532217756997", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 512}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.184022, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "ReviewHelper.php:411", "source": {"index": 20, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHelpers%2FReviewHelper.php&line=411", "ajax": false, "filename": "ReviewHelper.php", "line": "411"}, "connection": "review_master", "explain": null, "start_percent": 34.301, "width_percent": 6.362}, {"sql": "select count(*) as aggregate from `templates` where `business_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 434}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 512}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.193657, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ReviewHelper.php:434", "source": {"index": 16, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHelpers%2FReviewHelper.php&line=434", "ajax": false, "filename": "ReviewHelper.php", "line": "434"}, "connection": "review_master", "explain": null, "start_percent": 40.663, "width_percent": 2.25}, {"sql": "select * from `settings` where `business_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\SettingService.php", "line": 19}, {"index": 17, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 438}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 512}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.198148, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SettingService.php:19", "source": {"index": 16, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\SettingService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FSettingService.php&line=19", "ajax": false, "filename": "SettingService.php", "line": "19"}, "connection": "review_master", "explain": null, "start_percent": 42.912, "width_percent": 1.5}, {"sql": "select * from `businesses` where `user_id` != 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 2124}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 514}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.204952, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:2124", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 2124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=2124", "ajax": false, "filename": "BusinessController.php", "line": "2124"}, "connection": "review_master", "explain": null, "start_percent": 44.412, "width_percent": 2.225}, {"sql": "select * from `business_accounts` where `business_google_id` is null or `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 205}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 270}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 752}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.209367, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:205", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=205", "ajax": false, "filename": "GoogleBusinessService.php", "line": "205"}, "connection": "review_master", "explain": null, "start_percent": 46.638, "width_percent": 1.234}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 191}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 285}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 752}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.213255, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:191", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=191", "ajax": false, "filename": "GoogleBusinessService.php", "line": "191"}, "connection": "review_master", "explain": null, "start_percent": 47.871, "width_percent": 1.403}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng', null, '{\\\"locations\\\":[{\\\"name\\\":\\\"locations\\\\/13522179532217756997\\\",\\\"title\\\":\\\"IndiaNIC Infotech Limited (Udaipur)\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"079 6191 6000\\\",\\\"additionalPhones\\\":[\\\"0294 252 7111\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"313001\\\",\\\"administrativeArea\\\":\\\"Rajasthan\\\",\\\"locality\\\":\\\"Udaipur\\\",\\\"addressLines\\\":[\\\"24, <PERSON><PERSON><PERSON>\\\",\\\"Shakti Nagar\\\",\\\"above Andhra Bank\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.indianic.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":24.5848982,\\\"longitude\\\":73.6984058},\\\"profile\\\":{\\\"description\\\":\\\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\\\n\\\\nThis place represents the Udaipur Development Center.\\\"}},{\\\"name\\\":\\\"locations\\\\/2713736367053130525\\\",\\\"title\\\":\\\"TopSpin Club\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"099306 63000\\\"},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"380015\\\",\\\"administrativeArea\\\":\\\"Gujarat\\\",\\\"locality\\\":\\\"Ahmedabad\\\",\\\"addressLines\\\":[\\\"101\\\\/3, DevArc Mall\\\",\\\"SG Highway\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.portray.work\\\\/topspinclub\\\",\\\"latlng\\\":{\\\"latitude\\\":23.0257952,\\\"longitude\\\":72.5075995},\\\"profile\\\":{\\\"description\\\":\\\"Announcing TopSpin Club. Ahmedabad\\'s first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\\\n\\\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad\\'s first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\\\n\\\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\\\n\\\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\\\"}},{\\\"name\\\":\\\"locations\\\\/3612416279359725233\\\",\\\"title\\\":\\\"NIC Gulf Software House LLC\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"04 834 2243\\\",\\\"additionalPhones\\\":[\\\"************\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"AE\\\",\\\"languageCode\\\":\\\"ar-Latn\\\",\\\"administrativeArea\\\":\\\"Dubai\\\",\\\"addressLines\\\":[\\\"Westburry office towers, Marasi Dr, Business Bay\\\",\\\"Suite 604\\\"]},\\\"websiteUri\\\":\\\"http:\\\\/\\\\/www.nicgulf.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":25.1858249,\\\"longitude\\\":55.2748255},\\\"profile\\\":{\\\"description\\\":\\\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\\\"}}]}', 200, 'success', 547, null, 1, '2025-07-21 12:13:12', '2025-07-21 12:13:12')", "type": "query", "params": [], "bindings": ["google_api", "https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng", null, "{\"locations\":[{\"name\":\"locations\\/13522179532217756997\",\"title\":\"IndiaNIC Infotech Limited (Udaipur)\",\"phoneNumbers\":{\"primaryPhone\":\"079 6191 6000\",\"additionalPhones\":[\"0294 252 7111\"]},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"313001\",\"administrativeArea\":\"Rajasthan\",\"locality\":\"Udaipur\",\"addressLines\":[\"24, <PERSON><PERSON><PERSON> Bhawan\",\"Shakti Nagar\",\"above Andhra Bank\"]},\"websiteUri\":\"https:\\/\\/www.indianic.com\\/\",\"latlng\":{\"latitude\":24.5848982,\"longitude\":73.6984058},\"profile\":{\"description\":\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\n\\nThis place represents the Udaipur Development Center.\"}},{\"name\":\"locations\\/2713736367053130525\",\"title\":\"TopSpin Club\",\"phoneNumbers\":{\"primaryPhone\":\"099306 63000\"},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"380015\",\"administrativeArea\":\"Gujarat\",\"locality\":\"Ahmedabad\",\"addressLines\":[\"101\\/3, DevArc Mall\",\"SG Highway\"]},\"websiteUri\":\"https:\\/\\/www.portray.work\\/topspinclub\",\"latlng\":{\"latitude\":23.0257952,\"longitude\":72.5075995},\"profile\":{\"description\":\"Announcing TopSpin Club. Ahmedabad's first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\n\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad's first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\n\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\n\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\"}},{\"name\":\"locations\\/3612416279359725233\",\"title\":\"NIC Gulf Software House LLC\",\"phoneNumbers\":{\"primaryPhone\":\"04 834 2243\",\"additionalPhones\":[\"************\"]},\"storefrontAddress\":{\"regionCode\":\"AE\",\"languageCode\":\"ar-Latn\",\"administrativeArea\":\"Dubai\",\"addressLines\":[\"Westburry office towers, Marasi Dr, Business Bay\",\"Suite 604\"]},\"websiteUri\":\"http:\\/\\/www.nicgulf.com\\/\",\"latlng\":{\"latitude\":25.1858249,\"longitude\":55.2748255},\"profile\":{\"description\":\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\"}}]}", 200, "success", 547, null, 1, "2025-07-21 12:13:12", "2025-07-21 12:13:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 680}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 755}], "start": **********.779088, "duration": 0.00297, "duration_str": "2.97ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 49.274, "width_percent": 7.184}, {"sql": "select `location_name` from `associate_businesses` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1697}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 757}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.808047, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1697", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1697}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1697", "ajax": false, "filename": "BusinessController.php", "line": "1697"}, "connection": "review_master", "explain": null, "start_percent": 56.459, "width_percent": 5.902}, {"sql": "select * from `associate_businesses` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 555}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.821507, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:555", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=555", "ajax": false, "filename": "BusinessController.php", "line": "555"}, "connection": "review_master", "explain": null, "start_percent": 62.361, "width_percent": 2.322}, {"sql": "select * from `businesses` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 556}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.828182, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:556", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 556}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=556", "ajax": false, "filename": "BusinessController.php", "line": "556"}, "connection": "review_master", "explain": null, "start_percent": 64.683, "width_percent": 1.693}, {"sql": "select * from `businesses` where (`user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8340242, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:566", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=566", "ajax": false, "filename": "BusinessController.php", "line": "566"}, "connection": "review_master", "explain": null, "start_percent": 66.376, "width_percent": 4.91}, {"sql": "select * from `settings` where `settings`.`business_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.839875, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:566", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=566", "ajax": false, "filename": "BusinessController.php", "line": "566"}, "connection": "review_master", "explain": null, "start_percent": 71.287, "width_percent": 1.33}, {"sql": "select * from `businesses` where (`id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.843662, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:569", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=569", "ajax": false, "filename": "BusinessController.php", "line": "569"}, "connection": "review_master", "explain": null, "start_percent": 72.617, "width_percent": 2.177}, {"sql": "select * from `settings` where `settings`.`business_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.848161, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:569", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=569", "ajax": false, "filename": "BusinessController.php", "line": "569"}, "connection": "review_master", "explain": null, "start_percent": 74.794, "width_percent": 1.355}, {"sql": "select * from `templates` where `business_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1383}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 573}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.853851, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1383", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1383}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1383", "ajax": false, "filename": "BusinessController.php", "line": "1383"}, "connection": "review_master", "explain": null, "start_percent": 76.149, "width_percent": 1.548}, {"sql": "select count(*) as aggregate from `google_reviews` where (`location_id` = '13522179532217756997' and `account_id` = '110486499747300774507') and `parent_id` is null", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 582}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.859593, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:110", "source": {"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=110", "ajax": false, "filename": "GoogleReview.php", "line": "110"}, "connection": "review_master", "explain": null, "start_percent": 77.697, "width_percent": 2.129}, {"sql": "select\nstar_rating,\nCOUNT(*) as count,\nSUM(CASE\nWHEN UPPER(star_rating) = \"FIVE\" THEN 5\nWHEN UPPER(star_rating) = \"FOUR\" THEN 4\nWHEN UPPER(star_rating) = \"THREE\" THEN 3\nWHEN UPPER(star_rating) = \"TWO\" THEN 2\nWHEN UPPER(star_rating) = \"ONE\" THEN 1\nELSE 0\nEND) as total_numeric_rating\nfrom `google_reviews` where (`location_id` = '13522179532217756997' and `account_id` = '110486499747300774507') and `parent_id` is null and `star_rating` is not null group by `star_rating`", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 173}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 589}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.86959, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:173", "source": {"index": 15, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=173", "ajax": false, "filename": "GoogleReview.php", "line": "173"}, "connection": "review_master", "explain": null, "start_percent": 79.826, "width_percent": 4.354}, {"sql": "select count(*) as aggregate from `google_reviews` where (`location_id` = '13522179532217756997' and `account_id` = '110486499747300774507') and `parent_id` is null", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 227}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 592}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.878333, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:227", "source": {"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=227", "ajax": false, "filename": "GoogleReview.php", "line": "227"}, "connection": "review_master", "explain": null, "start_percent": 84.18, "width_percent": 2.08}, {"sql": "select `parent`.`id`, `parent`.`created_at_google`, `reply`.`updated_at_google` from `google_reviews` as `parent` inner join `google_reviews` as `reply` on `parent`.`id` = `reply`.`parent_id` where `parent`.`location_id` = '13522179532217756997' and `parent`.`account_id` = '110486499747300774507' and `parent`.`parent_id` is null and `reply`.`parent_id` is not null and `parent`.`created_at_google` is not null and `reply`.`updated_at_google` is not null", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 252}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 592}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.889034, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:252", "source": {"index": 13, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=252", "ajax": false, "filename": "GoogleReview.php", "line": "252"}, "connection": "review_master", "explain": null, "start_percent": 86.26, "width_percent": 3.532}, {"sql": "select * from `google_reviews` where `location_id` = '13522179532217756997' and `parent_id` is null and `updated_at_google` between '2015-01-01 00:00:00' and '2025-07-31 23:59:59' order by `updated_at_google` asc", "type": "query", "params": [], "bindings": ["13522179532217756997", "2015-01-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1176}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 603}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.898886, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1176", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1176", "ajax": false, "filename": "BusinessController.php", "line": "1176"}, "connection": "review_master", "explain": null, "start_percent": 89.792, "width_percent": 2.225}, {"sql": "select * from `google_reviews` where `location_id` = '13522179532217756997' and `parent_id` is null and `updated_at_google` between '2015-01-01 00:00:00' and '2025-07-31 23:59:59' order by `updated_at_google` asc", "type": "query", "params": [], "bindings": ["13522179532217756997", "2015-01-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1266}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 604}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.908798, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1266", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1266}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1266", "ajax": false, "filename": "BusinessController.php", "line": "1266"}, "connection": "review_master", "explain": null, "start_percent": 92.017, "width_percent": 2.709}, {"sql": "select * from `google_reviews` where `location_id` = '13522179532217756997' and `parent_id` is not null and `updated_at_google` between '2015-01-01 00:00:00' and '2025-07-31 23:59:59' order by `updated_at_google` asc", "type": "query", "params": [], "bindings": ["13522179532217756997", "2015-01-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1273}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 604}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.914331, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1273", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1273}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1273", "ajax": false, "filename": "BusinessController.php", "line": "1273"}, "connection": "review_master", "explain": null, "start_percent": 94.727, "width_percent": 1.742}, {"sql": "select * from `templates` where `business_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 608}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.92401, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:608", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 608}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=608", "ajax": false, "filename": "BusinessController.php", "line": "608"}, "connection": "review_master", "explain": null, "start_percent": 96.468, "width_percent": 1.597}, {"sql": "select exists(select * from `subscriptions` where `subscriptions`.`user_id` = 1 and `subscriptions`.`user_id` is not null and `status` = 'ACTIVE' and `expiry_date` >= '2025-07-21 12:13:12') as `exists`", "type": "query", "params": [], "bindings": [1, "ACTIVE", "2025-07-21 12:13:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\User.php", "line": 74}, {"index": 15, "namespace": "view", "name": "business-dashboard", "file": "C:\\wamp64\\www\\reviewbiz\\resources\\views/business-dashboard.blade.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.958769, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:74", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\User.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=74", "ajax": false, "filename": "User.php", "line": "74"}, "connection": "review_master", "explain": null, "start_percent": 98.065, "width_percent": 1.935}]}, "models": {"data": {"App\\Models\\GoogleReview": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=1", "ajax": false, "filename": "GoogleReview.php", "line": "?"}}, "App\\Models\\Template": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}, "App\\Models\\Business": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusiness.php&line=1", "ajax": false, "filename": "Business.php", "line": "?"}}, "App\\Models\\BusinessAccount": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusinessAccount.php&line=1", "ajax": false, "filename": "BusinessAccount.php", "line": "?"}}, "App\\Models\\Setting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\AssociateBusiness": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FAssociateBusiness.php&line=1", "ajax": false, "filename": "AssociateBusiness.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}}, "count": 70, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/business-dashboard", "action_name": "business.dashboard", "controller_action": "App\\Http\\Controllers\\BusinessController@businessDashboard", "uri": "GET business-dashboard", "controller": "App\\Http\\Controllers\\BusinessController@businessDashboard<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=438\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=438\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BusinessController.php:438-643</a>", "middleware": "web, auth", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f718a3b-e476-48d6-903a-07f3316c64c6\" target=\"_blank\">View in Telescope</a>", "duration": "2.22s", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1025258645 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1025258645\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-154524620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-154524620\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">http://localhost:8000/admin/email-templates/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1694 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im1ySEFHV2hQVDlGcjRqUSs4bHlUclE9PSIsInZhbHVlIjoibDlUcW14ZXYzYU04Sk5hNkhNc3FsTjNvbmVvdzI3ZndKb1RmQzl5dHhjUnB2N1N2ek5PZDhVNUtsVmFSR3NsS3VPcTAwYlhIRlc1YkhIVlllUUE3K1VlSEZEWGd1RG9oU2c2Q0xpcS9kZE5ScHpaZlpaM3FMVjI0dlFKQWRWTTMzdEliSHhVaXB2L0p2OTNLWVNodEpRN1hiNmowQ0RQNkloTExmdlAvM1poS051Qm1oTVAvSDJpcFI2LzJ6eWMxWEpVQU1uTlgyVFJ4eWw4OWpROURaMkx3MmN2TE5id0RRZi9oYnJOZ2hOZz0iLCJtYWMiOiIzNGFiOWI5ZjdiYzFjZWJmZjZlMDI2NzFjY2FhYjhlZDIzOTQyNjNjZjAzNWU0YjBlY2VkYjVhZDkxMmFjOTMxIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InhsS1ErNFB2VC9UOVE1R1ovemFtVmc9PSIsInZhbHVlIjoieStNUXZDUDNZU3RqRHdvKzFUT25jSHg4dUVCSjFwUmllU25qNXFiV0F1OW84OTBCNU9CdTE2MTVMNUFvVHB2TVlTS2NSQjJMTkduZE0wU3I0cTJ4Tmp0UisxU1VqNlRqR0F2cnJEM3ZNZGQzSTFUMEtaUWFqUDNMQkNqTkE5MWpoRUN1YkhGOEpDWmthUTJ4RWlVeUl3PT0iLCJtYWMiOiIzZTI3OTliODc5NGUyZTBhMzcwYzdmZjA1MjM1MDVjY2RhNjcyNzMyZTk2NjRjYTczYjE4YmZhMGMwNTQ3YzNlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ims4Z25JTDBSZmovdDdLRUpGdVBUZXc9PSIsInZhbHVlIjoielZxZEV2S0tmN2RvQVF2ZjJSaXI2dkE0bHA0OVFpQkZmR0pMMktHVTQzdGYvcTBBVG1PRVJBSHZjaDg1WXJtaU96bW1XdjBlKzJRU0FqMVNNa010N0luV3lBaEE5UUF3eG5qalpLdWJwZlk4WUVIcGttN2U0NE1IbkNxMnVpUE8iLCJtYWMiOiJhMjczZGEyZjMzMmI2ZGQ3OWIwOTIwNDUxZTQ3Y2ZlYzBkZWU3YjAwM2YxYzAyYjE3NmY5NWIyODhmNTZlNjY5IiwidGFnIjoiIn0%3D; reviewmasterai_session=eyJpdiI6ImdDTUdBajZCeXpTQ3ZiSlBmdlhTVFE9PSIsInZhbHVlIjoiTFNJR2pRSlltdjNnMCtPZ3d2VDJGVGxDaGZDOG1BcUFpZWFIY0ZWSXdjUUVOKytLTTB4c3k3M2hMbzlZNzJ3NkovSE1xNmxqK1V3VTJaOVlPMXp5NlZXUDhPeFNtZEtPZlFzcG9mRVo2T0pIYU1DclUvWklySUoya2pIVkNNeW8iLCJtYWMiOiJmZTQ2ZDIyY2M1Njc2N2YyMTEwZGQ1ZmMxYjcyYjkyNTk0ZTNkZjE5OTk0Y2Y5ZGQzZmY1OTlkZDk5MjczM2I2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1281289889 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|pmk0ZPcwNvdcyZh8txRDWRjQhRBItD6bQKyiIIwPOGZVYyePFjEhKDNxjvoa|$2y$12$quO4cLolCFDH2wXsSke/u.BarK4ZwLV6ACtlaLyMBFl5.eec7Yrdi</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|d4gsFumgVybRUrjg0Rh5kBgW54R5vfpkeMJcrWrT7FkGNX8F4D9Tf62Ue7fx|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  \"<span class=sf-dump-key>reviewmasterai_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281289889\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-991184802 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 12:13:12 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991184802\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1231167186 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>business_google_token</span>\" => \"<span class=sf-dump-str title=\"224 characters\">********************************************************************************************************************************************************************************************************************************</span>\"\n  \"<span class=sf-dump-key>business_google_refresh_token</span>\" => \"<span class=sf-dump-str title=\"103 characters\">1//0gTNtsu4DcUdMCgYIARAAGBASNwF-L9IrdXV9dXsoYipZErrwYsSEb4XPiKiODRngXtCKIqm0BDgTYJem9CDOhq2TaCAHNMGEzQM</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231167186\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/business-dashboard", "action_name": "business.dashboard", "controller_action": "App\\Http\\Controllers\\BusinessController@businessDashboard"}, "badge": null}}