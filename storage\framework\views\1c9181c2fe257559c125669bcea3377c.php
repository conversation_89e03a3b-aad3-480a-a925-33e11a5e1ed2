<?php $__env->startSection('title', 'Site Settings'); ?>
<?php $__env->startSection('page-title', 'Site Settings'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .setting-card {
        transition: all 0.2s ease;
    }
    .setting-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .category-icon {
        @apply w-8 h-8 rounded-lg flex items-center justify-center text-white;
    }
    .category-ai { @apply bg-purple-500; }
    .category-google_api { @apply bg-blue-500; }
    .category-email { @apply bg-green-500; }
    .category-application { @apply bg-orange-500; }
    .category-general { @apply bg-gray-500; }
    .access-badge {
        @apply px-2 py-1 text-xs font-medium rounded;
    }
    .access-super_admin { @apply bg-red-100 text-red-800; }
    .access-admin { @apply bg-blue-100 text-blue-800; }
    .access-moderator { @apply bg-green-100 text-green-800; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Site Settings</h1>
            <p class="text-gray-600">Manage global application settings and configuration</p>
        </div>
        <div class="flex items-center space-x-3">
            <?php if(auth('admin')->user()->hasPermission('site_settings.edit')): ?>
                <form method="POST" action="<?php echo e(route('admin.site-settings.clear-cache')); ?>" class="inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" 
                            class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        <i class="fas fa-sync mr-2"></i>Clear Cache
                    </button>
                </form>
                
                <a href="<?php echo e(route('admin.site-settings.export')); ?>" 
                   class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <i class="fas fa-download mr-2"></i>Export Settings
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Category Filter -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filter by Category</h3>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-3">
                <a href="<?php echo e(route('admin.site-settings.index')); ?>" 
                   class="px-4 py-2 rounded-md <?php echo e(!request('category') ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                    All Categories
                </a>
                <?php $__currentLoopData = $settings->keys(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('admin.site-settings.index', ['category' => $category])); ?>" 
                       class="px-4 py-2 rounded-md <?php echo e(request('category') == $category ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                        <?php echo e(ucfirst(str_replace('_', ' ', $category))); ?>

                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Settings by Category -->
    <?php $__empty_1 = true; $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryName => $categoryGroups): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="mb-8">
            <!-- Category Header -->
            <div class="flex items-center mb-4">
                <div class="category-icon category-<?php echo e($categoryName); ?>">
                    <?php switch($categoryName):
                        case ('ai'): ?>
                            <i class="fas fa-brain"></i>
                            <?php break; ?>
                        <?php case ('google_api'): ?>
                            <i class="fab fa-google"></i>
                            <?php break; ?>
                        <?php case ('email'): ?>
                            <i class="fas fa-envelope"></i>
                            <?php break; ?>
                        <?php case ('application'): ?>
                            <i class="fas fa-cog"></i>
                            <?php break; ?>
                        <?php default: ?>
                            <i class="fas fa-sliders-h"></i>
                    <?php endswitch; ?>
                </div>
                <div class="ml-3">
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo e(ucfirst(str_replace('_', ' ', $categoryName))); ?> Settings
                    </h2>
                    <p class="text-sm text-gray-600">
                        <?php switch($categoryName):
                            case ('ai'): ?>
                                Configure AI provider settings and parameters
                                <?php break; ?>
                            <?php case ('google_api'): ?>
                                Manage Google API credentials and limits
                                <?php break; ?>
                            <?php case ('email'): ?>
                                Email configuration and SMTP settings
                                <?php break; ?>
                            <?php case ('application'): ?>
                                General application settings and limits
                                <?php break; ?>
                            <?php default: ?>
                                General configuration settings
                        <?php endswitch; ?>
                    </p>
                </div>
                <?php if(auth('admin')->user()->hasPermission('site_settings.edit')): ?>
                    <div class="ml-auto">
                        <a href="<?php echo e(route('admin.site-settings.edit', ['category' => $categoryName])); ?>" 
                           class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-edit mr-2"></i>Edit <?php echo e(ucfirst($categoryName)); ?>

                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Settings Groups -->
            <?php $__currentLoopData = $categoryGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupName => $groupSettings): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow mb-4">
                    <?php if($groupName): ?>
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">
                                <?php echo e(ucfirst(str_replace('_', ' ', $groupName))); ?>

                            </h3>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                            <?php $__currentLoopData = $groupSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="setting-card border border-gray-200 rounded-lg p-3 sm:p-4">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900 text-sm"><?php echo e($setting->name); ?></h4>
                                        <span class="access-badge access-<?php echo e($setting->access_level); ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $setting->access_level))); ?>

                                        </span>
                                    </div>
                                    
                                    <?php if($setting->description): ?>
                                        <p class="text-xs text-gray-600 mb-3"><?php echo e($setting->description); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="mb-2">
                                        <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            Current Value
                                        </label>
                                        <div class="text-sm text-gray-900">
                                            <?php if($setting->type === 'boolean'): ?>
                                                <span class="px-2 py-1 text-xs rounded <?php echo e($setting->getTypedValue() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                    <?php echo e($setting->getTypedValue() ? 'Enabled' : 'Disabled'); ?>

                                                </span>
                                            <?php elseif($setting->is_encrypted && $setting->value): ?>
                                                <span class="text-gray-500 font-mono">••••••••</span>
                                            <?php elseif($setting->type === 'json'): ?>
                                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">JSON</code>
                                            <?php else: ?>
                                                <span class="font-mono"><?php echo e($setting->getTypedValue() ?: 'Not set'); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <span><?php echo e(ucfirst($setting->type)); ?></span>
                                        <?php if($setting->is_required): ?>
                                            <span class="text-red-600">Required</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if($setting->help_text): ?>
                                        <div class="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
                                            <i class="fas fa-info-circle mr-1"></i><?php echo e($setting->help_text); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="text-center py-12">
            <i class="fas fa-cog text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No settings found</h3>
            <p class="text-gray-600">No settings are available for your access level.</p>
        </div>
    <?php endif; ?>

    <!-- Quick Stats -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-4 sm:p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Settings Overview</h3>
        </div>
        <div class="p-4 sm:p-6">
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">
                        <?php echo e($settings->flatten()->count()); ?>

                    </div>
                    <div class="text-sm text-gray-600">Total Settings</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        <?php echo e($settings->flatten()->where('is_required', true)->count()); ?>

                    </div>
                    <div class="text-sm text-gray-600">Required Settings</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        <?php echo e($settings->flatten()->where('is_encrypted', true)->count()); ?>

                    </div>
                    <div class="text-sm text-gray-600">Encrypted Settings</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        <?php echo e($settings->count()); ?>

                    </div>
                    <div class="text-sm text-gray-600">Categories</div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/site-settings/index.blade.php ENDPATH**/ ?>