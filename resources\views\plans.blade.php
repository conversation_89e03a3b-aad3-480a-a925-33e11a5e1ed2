<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReviewMaster AI</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="assets/css/styles.css" rel="stylesheet">
    <style>
        /* Custom toggle switch styles */
        .toggle-checkbox {
            right: 0;
            z-index: 5;
            opacity: 0;
            top: 0;
        }

        .toggle-label {
            display: block;
            overflow: hidden;
            cursor: pointer;
            border: 0 solid #bbb;
            border-radius: 20px;
            margin: 0;
        }

        .toggle-checkbox:checked+.toggle-label {
            background-color: #4F46E5;
        }

        .toggle-checkbox:checked+.toggle-label:before {
            right: 0px;
        }

        .toggle-checkbox+.toggle-label:before {
            position: absolute;
            display: block;
            top: 0;
            bottom: 0;
            right: 4px;
            content: "";
            width: 16px;
            height: 16px;
            background-color: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            margin: auto 0;
            left: 4px;
        }

        .toggle-checkbox:checked+.toggle-label:before {
            transform: translateX(14px);
        }

        /* Custom width classes for 30-70 split */
        .w-3\/10 {
            width: 30%;
        }

        .w-7\/10 {
            width: 70%;
        }
    </style>
</head>

<body class="font-poppins bg-gray-100 text-gray-900">
    <div class="popup-container max-w-5xl flex flex-col m-auto bg-white">
        <!-- Header - Made sticky -->
        <header class="bg-gray-700 text-white p-4 flex justify-between items-center sticky top-0 z-20">
            <img src="assets/images/rm-logo-dark.png" height="90" width="192" alt="ReviewMaster AI Logo" class="mr-2">
            <div class="flex items-center space-x-4">
                <!-- Business Selector -->
                <div class="relative flex">
                    <div class="relative flex items-center">
                        <select id="headerBusinessSelector" class="bg-white bg-opacity-20 border border-white border-opacity-30 text-white rounded-md pl-3 pr-8 py-1 appearance-none focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                            <option value="">Select a business</option>
                            <!-- Business options will be populated by JavaScript -->
                            <option value="bus123">Coffee Supreme</option>
                            <option value="bus124">Seattle Bakery</option>
                            <option value="bus125">Downtown Deli</option>
                            <option value="bus126">Harbor Seafood</option>
                            <option value="bus127">Green Leaf Cafe</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                            <i class="fas fa-chevron-down text-white text-opacity-70"></i>
                        </div>
                    </div>
                    <button id="connectBusinessBtn" class="ml-2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-md px-2 py-1 text-sm flex items-center">
                        <i class="fas fa-plus mr-1"></i> Connect
                    </button>
                </div>

                <!-- User Profile -->
                <div class="flex items-center relative">
                    <div id="userProfileContainer" class="user-profile flex items-center cursor-pointer">
                        <img src="assets/images/user-avatar.png" alt="User" class="w-8 h-8 rounded-full">
                        <span class="ml-2 text-sm">John Smith</span>
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </div>

                    <!-- User Menu (Hidden by default) -->
                    <div id="userMenu" class="absolute right-0 top-full mt-1 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden">
                        <a href="#" id="accountBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-tab-trigger="accountTab">
                            <i class="fas fa-user-cog mr-2"></i> Account
                        </a>
                        <a href="#" id="editProfileBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user-edit mr-2"></i> Edit Profile
                        </a>
                        <a href="#" id="planBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-crown mr-2"></i> My Plan
                        </a>
                        <div class="border-t border-gray-100"></div>
                        <a href="#" id="logoutBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-sign-out-alt mr-2"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex flex-1 overflow-hidden">
            <main class="w-full flex flex-col overflow-hidden">
                <!-- Tabs - Made sticky -->
                <div id="tabbar" class="bg-white border-b border-gray-200 sticky top-0 z-10 hidden">
                    <nav class="flex">
                        <button id="reviewsTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500" data-tab="reviewsTab">
                            <i class="fas fa-star mr-1"></i> Reviews <span class="text-xs rounded-full bg-red-600 text-white px-2 py-1">2</span>
                        </button>
                        <button id="analyticsTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="analyticsTab">
                            <i class="fas fa-chart-line mr-1"></i> Analytics
                        </button>
                        <button id="templatesTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="templatesTab">
                            <i class="fas fa-file-alt mr-1"></i> Templates
                        </button>
                        <button id="settingsTabBtn2" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="settingsTab">
                            <i class="fas fa-cog mr-1"></i> Settings
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="flex-1 overflow-hidden">
                    <!-- Reviews Tab (Default) -->
                    <div id="reviewsTab" class="tab-content h-full flex flex-col hidden">
                        <!-- Filter Bar -->
                        <div class="bg-white p-4 border-b border-gray-200">
                            <div class="flex flex-wrap items-center gap-4">
                                <div class="filter-group">
                                    <label class="text-xs text-gray-500 block mb-1">Date Range</label>
                                    <select id="dateRangeFilter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <option value="last_week">Last week</option>
                                        <option value="last_month">Last month</option>
                                        <option value="last_3_months">Last 3 months</option>
                                        <option value="custom">Custom</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="text-xs text-gray-500 block mb-1">Rating</label>
                                    <div class="flex">
                                        <button class="rating-btn px-2 py-1 border border-gray-300 rounded-l-md hover:bg-gray-100">
                                            <i class="icon-star text-yellow-400"></i>
                                            <span>1</span>
                                        </button>
                                        <button class="rating-btn px-2 py-1 border-t border-b border-gray-300 hover:bg-gray-100">
                                            <i class="icon-star text-yellow-400"></i>
                                            <span>2</span>
                                        </button>
                                        <button class="rating-btn px-2 py-1 border-t border-b border-gray-300 hover:bg-gray-100">
                                            <i class="icon-star text-yellow-400"></i>
                                            <span>3</span>
                                        </button>
                                        <button class="rating-btn px-2 py-1 border-t border-b border-gray-300 hover:bg-gray-100">
                                            <i class="icon-star text-yellow-400"></i>
                                            <span>4</span>
                                        </button>
                                        <button class="rating-btn px-2 py-1 border border-gray-300 rounded-r-md hover:bg-gray-100">
                                            <i class="icon-star text-yellow-400"></i>
                                            <span>5</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="filter-group">
                                    <label class="text-xs text-gray-500 block mb-1">Type</label>
                                    <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <option value="all">All</option>
                                        <option value="replied">Replied</option>
                                        <option value="not_replied">Not Replied</option>
                                        <option value="with_photos">With Photos</option>
                                    </select>
                                </div>
                                <div id="fetchNewReviewsNav" class="mb-4">
                                    <label class="text-xs text-gray-500 block mb-2">Last fetched 1d ago</label>
                                    <button id="fetchNewReviewsBtn" class="flex items-center px-3 py-1 text-sm bg-white-600 text-indigo-600 border ring-2 ring-indigo-600 rounded-md font-medium hover:text-white hover:bg-indigo-600">
                                        <i class="fas fa-sync mr-2"></i> Fetch New Reviews
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Reviews List -->
                        <div id="reviewsList" class="flex-1 overflow-y-auto p-4">
                            <div class="review-card bg-white rounded-xl shadow-sm p-4 mb-4" data-review-id="rev456">
                                <div class="flex">
                                    <div class="w-10 h-10 flex-shrink-0 overflow-hidden">
                                        <img src="assets/images/placeholder.png" alt="Jane Smith" onerror="this.src='assets/images/default-user.png'">
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="text-sm font-medium text-gray-900">Jane Smith</h3>
                                                <p class="text-xs text-gray-500">Mar 12, 2025</p>
                                            </div>
                                            <div class="stars-container text-gray-300">
                                                <i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-gray-300"></i>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">Great coffee and atmosphere. The staff was very friendly!</p>

                                        <div class="review-photos flex mt-2 space-x-2">

                                            <div class="w-12 h-12 rounded overflow-hidden">
                                                <img src="assets/images/placeholder.jpg" alt="Review photo" class="w-full h-full object-cover" onerror="this.src='assets/images/placeholder.png'">
                                            </div>

                                        </div>


                                        <div class="review-reply mt-3 pl-3 border-l-2 border-indigo-200">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full bg-indigo-100 flex-shrink-0 overflow-hidden">
                                                    <img src="assets/images/business-avatar.png" alt="Coffee Supreme" onerror="this.src='assets/images/default-business.png'">
                                                </div>
                                                <div class="ml-2">
                                                    <p class="text-xs font-medium">Coffee Supreme</p>
                                                    <p class="text-xs text-gray-500">Mar 13</p>
                                                </div>
                                            </div>

                                            <div class="team-member-info flex items-center mt-1 mb-1 border-l-4 border-blue-400 px-2 py-1 flex-shrink-0">
                                                <div class="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                    SJ
                                                </div>
                                                <span class="text-xs text-gray-700 ml-1">Replied via <img src="assets/images/rm-icon.png" alt="ReviewMaster.biz" class="w-8 h-4 rounded-full inline"> by Sarah Johnson</span>
                                                <span class="text-xs text-gray-400 ml-2">(Used template)</span>

                                            </div>

                                            <p class="text-sm text-gray-700 mt-1">Thank you for your kind words, Jane! We're glad you enjoyed your experience.</p>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="review-card bg-white rounded-xl shadow-sm p-4 mb-4" data-review-id="rev457">
                                <div class="flex">
                                    <div class="w-10 h-10 flex-shrink-0 overflow-hidden">
                                        <img src="assets/images/placeholder.png" alt="Michael Johnson" onerror="this.src='assets/images/default-user.png'">
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="text-sm font-medium text-gray-900">Michael Johnson</h3>
                                                <p class="text-xs text-gray-500">Mar 10, 2025</p>
                                            </div>
                                            <div class="stars-container text-gray-300">
                                                <i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-gray-300"></i><i class="icon-star text-gray-300"></i><i class="icon-star text-gray-300"></i>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">Coffee was cold and service was slow. Disappointed with my experience today.</p>


                                        <div class="mt-3">
                                            <button class="reply-btn btn-primary text-sm py-1 px-3" data-review-id="rev457">
                                                <i class="fas fa-reply mr-1"></i> Reply
                                            </button>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="review-card bg-white rounded-xl shadow-sm p-4 mb-4" data-review-id="rev458">
                                <div class="flex">
                                    <div class="w-10 h-10 flex-shrink-0 overflow-hidden">
                                        <img src="assets/images/placeholder.png" alt="Emily Davis" onerror="this.src='assets/images/default-user.png'">
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="text-sm font-medium text-gray-900">Emily Davis</h3>
                                                <p class="text-xs text-gray-500">Mar 8, 2025</p>
                                            </div>
                                            <div class="stars-container text-gray-300">
                                                <i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">Best coffee in town! Love the new seasonal blend and the barista was super helpful explaining the different options.</p>

                                        <div class="review-photos flex mt-2 space-x-2">

                                            <div class="w-12 h-12 rounded overflow-hidden">
                                                <img src="assets/images/review_photo2.jpg" alt="Review photo" class="w-full h-full object-cover" onerror="this.src='assets/images/placeholder.png'">
                                            </div>

                                            <div class="w-12 h-12 rounded overflow-hidden">
                                                <img src="assets/images/review_photo3.jpg" alt="Review photo" class="w-full h-full object-cover" onerror="this.src='assets/images/placeholder.png'">
                                            </div>

                                        </div>


                                        <div class="review-reply mt-3 pl-3 border-l-2 border-indigo-200">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full bg-indigo-100 flex-shrink-0 overflow-hidden">
                                                    <img src="assets/images/business-avatar.png" alt="Coffee Supreme" onerror="this.src='assets/images/default-business.png'">
                                                </div>
                                                <div class="ml-2">
                                                    <p class="text-xs font-medium">Coffee Supreme</p>
                                                    <p class="text-xs text-gray-500">Mar 8</p>
                                                </div>
                                            </div>

                                            <div class="team-member-info flex items-center mt-1 mb-1 border-l-4 border-blue-400 px-2 py-1 flex-shrink-0">
                                                <div class="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                    JS
                                                </div>
                                                <span class="text-xs text-gray-700 ml-1">Replied via <img src="assets/images/rm-icon.png" alt="ReviewMaster.biz" class="w-8 h-4 rounded-full inline"> by John Smith</span>
                                                <span class="text-xs text-gray-400 ml-2">(Used template)</span>
                                                <span class="text-xs text-gray-400 ml-2">(Edited)</span>
                                            </div>

                                            <p class="text-sm text-gray-700 mt-1">Thanks for the amazing review, Emily! We're thrilled you enjoyed our seasonal blend. Can't wait to serve you again soon!</p>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="review-card bg-white rounded-xl shadow-sm p-4 mb-4" data-review-id="rev459">
                                <div class="flex">
                                    <div class="w-10 h-10 flex-shrink-0 overflow-hidden">
                                        <img src="assets/images/placeholder.png" alt="David Wilson" onerror="this.src='assets/images/default-user.png'">
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="text-sm font-medium text-gray-900">David Wilson</h3>
                                                <p class="text-xs text-gray-500">Mar 5, 2025</p>
                                            </div>
                                            <div class="stars-container text-gray-300">
                                                <i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-gray-300"></i><i class="icon-star text-gray-300"></i>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">Decent coffee but a bit overpriced compared to other places in the area. The ambiance is nice though.</p>


                                        <div class="mt-3">
                                            <button class="reply-btn btn-primary text-sm py-1 px-3" data-review-id="rev459">
                                                <i class="fas fa-reply mr-1"></i> Reply
                                            </button>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="review-card bg-white rounded-xl shadow-sm p-4 mb-4" data-review-id="rev460">
                                <div class="flex">
                                    <div class="w-10 h-10  flex-shrink-0 overflow-hidden">
                                        <img src="assets/images/placeholder.png" alt="Sarah Thompson" onerror="this.src='assets/images/default-user.png'">
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="text-sm font-medium text-gray-900">Sarah Thompson</h3>
                                                <p class="text-xs text-gray-500">Mar 1, 2025</p>
                                            </div>
                                            <div class="stars-container text-gray-300">
                                                <i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i><i class="icon-star text-yellow-400"></i>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">I love this place! The pastries are amazing and the coffee is always perfect. My favorite spot to work from.</p>

                                        <div class="review-photos flex mt-2 space-x-2">

                                            <div class="w-12 h-12 rounded overflow-hidden">
                                                <img src="assets/images/review_photo4.jpg" alt="Review photo" class="w-full h-full object-cover" onerror="this.src='assets/images/placeholder.png'">
                                            </div>

                                        </div>


                                        <div class="review-reply mt-3 pl-3 border-l-2 border-indigo-200">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full bg-indigo-100 flex-shrink-0 overflow-hidden">
                                                    <img src="assets/images/business-avatar.png" alt="Coffee Supreme" onerror="this.src='assets/images/default-business.png'">
                                                </div>
                                                <div class="ml-2">
                                                    <p class="text-xs font-medium">Coffee Supreme</p>
                                                    <p class="text-xs text-gray-500">Mar 2</p>
                                                </div>
                                            </div>

                                            <p class="text-sm text-gray-700 mt-1">Thank you for the wonderful review, Sarah! We're happy to be your favorite work spot. See you again soon!</p>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div id="analyticsTab" class="tab-content hidden h-full overflow-y-auto p-4">
                        <!-- Analytics Summary -->
                        <div class="grid grid-cols-4 gap-4 mb-6">
                            <div class="bg-white rounded-xl shadow-sm p-4">
                                <div class="text-sm text-gray-500 mb-1">Overall Rating</div>
                                <div class="flex items-center">
                                    <span class="text-2xl font-semibold mr-2">4.3</span>
                                    <div class="flex text-yellow-400">
                                        <i class="icon-star"></i>
                                        <i class="icon-star"></i>
                                        <i class="icon-star"></i>
                                        <i class="icon-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl shadow-sm p-4">
                                <div class="text-sm text-gray-500 mb-1">Total Reviews</div>
                                <div class="text-2xl font-semibold">182</div>
                            </div>
                            <div class="bg-white rounded-xl shadow-sm p-4">
                                <div class="text-sm text-gray-500 mb-1">Response Rate</div>
                                <div class="text-2xl font-semibold">87%</div>
                            </div>
                            <div class="bg-white rounded-xl shadow-sm p-4">
                                <div class="text-sm text-gray-500 mb-1">Avg. Response Time</div>
                                <div class="text-2xl font-semibold">14 hours</div>
                            </div>
                        </div>

                        <!-- Sentiment Trend Analysis -->
                        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-chart-line text-indigo-600 mr-2"></i>
                                <h3 class="text-lg font-semibold">Sentiment Trend Analysis</h3>
                            </div>
                            <p class="text-sm text-gray-600 mb-4">Track sentiment changes over time to identify improvement areas</p>
                            <div id="sentimentChart" class="h-64"><canvas height="0" style="display: block; box-sizing: border-box; height: 0px; width: 0px;" width="0"></canvas></div>
                        </div>

                        <!-- Review Activity Chart -->
                        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-chart-bar text-indigo-600 mr-2"></i>
                                <h3 class="text-lg font-semibold">Review Activity (Last 30 Days)</h3>
                            </div>
                            <p class="text-sm text-gray-600 mb-4">Track new reviews and responses over the past month</p>
                            <div id="reviewActivityChart" class="h-64"><canvas height="0" style="display: block; box-sizing: border-box; height: 0px; width: 0px;" width="0"></canvas></div>
                        </div>

                        <!-- Topic Discovery -->
                        <div class="bg-white rounded-xl shadow-sm p-4">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-search text-indigo-600 mr-2"></i>
                                <h3 class="text-lg font-semibold">Topic Discovery</h3>
                            </div>
                            <p class="text-sm text-gray-600 mb-4">Identify common themes and topics from customer reviews</p>
                            <div id="topicVisualization" class="h-64"><canvas height="0" style="display: block; box-sizing: border-box; height: 0px; width: 0px;" width="0"></canvas></div>
                        </div>
                    </div>

                    <!-- Templates Tab -->
                    <div id="templatesTab" class="tab-content hidden h-full overflow-y-auto p-4">
                        <div class="pt-2 pb-4">
                            <div class="flex justify-between items-center mb-4 flex-wrap">
                                <h3 class="text-lg font-semibold">Response Templates</h3>
                                <div class="text-sm text-gray-500">
                                    Templates are automatically selected based on review rating, sentiment, and length
                                </div>
                            </div>

                            <div class="mb-4">
                                <p class="text-sm text-gray-600">
                                    Create and manage templates for responding to different types of reviews.
                                    You can use variables like <code>businessName</code>, <code>businessContact</code>,
                                    <code>reviewerFirstName</code>, and <code>reviewerLastName</code> that will be
                                    automatically replaced with actual values.
                                </p>
                            </div>

                            <div id="templatesList" class="mt-4">
                                <div class="template-card bg-white border border-gray-200 rounded-lg p-4 mb-4" data-template-id="system-default">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-md font-semibold">
                                            System Default Template
                                            <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">System Default</span>
                                        </h4>
                                        <div class="flex space-x-2">
                                            <button class="edit-template-btn text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="delete-template-btn text-red-600 hover:text-red-800" disabled="" style="opacity: 0.5; cursor: not-allowed;">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">This is the system default template that cannot be deleted. It will be used when no other templates match.</p>
                                    <div class="text-xs text-gray-500 mb-2">
                                        <span class="mr-2"><i class="icon-star"></i> 1-5</span>
                                        <span class="mr-2"><i class="fas fa-heart"></i> any</span>
                                        <span><i class="fas fa-text-height"></i> any</span>
                                    </div>


                                    <div class="bg-gray-50 p-2 rounded text-sm mt-2">Thank you for your feedback about businessName. We appreciate you taking the time to share your thoughts. Your opinion matters to us and helps us improve our service. If you have any questions or need further assistance, please don't hesitate to contact us at businessContact.</div>
                                </div>
                                <div class="template-card bg-white border border-gray-200 rounded-lg p-4 mb-4" data-template-id="temp1">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-md font-semibold">
                                            Thank You Response

                                        </h4>
                                        <div class="flex space-x-2">
                                            <button class="edit-template-btn text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="delete-template-btn text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">A warm response for positive reviews</p>
                                    <div class="text-xs text-gray-500 mb-2">
                                        <span class="mr-2"><i class="icon-star"></i> 4-5</span>
                                        <span class="mr-2"><i class="fas fa-heart"></i> positive</span>
                                        <span><i class="fas fa-text-height"></i> any</span>
                                    </div>


                                    <div class="bg-gray-50 p-2 rounded text-sm mt-2">Thank you for your kind words, reviewerFirstName! We're delighted to hear that you enjoyed our service. Your feedback means a lot to us at businessName, and we hope to see you again soon!</div>
                                </div>
                                <div class="template-card bg-white border border-gray-200 rounded-lg p-4 mb-4" data-template-id="temp2">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-md font-semibold">
                                            Apology Response

                                        </h4>
                                        <div class="flex space-x-2">
                                            <button class="edit-template-btn text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="delete-template-btn text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">A sincere apology for negative experiences</p>
                                    <div class="text-xs text-gray-500 mb-2">
                                        <span class="mr-2"><i class="icon-star"></i> 1-2</span>
                                        <span class="mr-2"><i class="fas fa-heart"></i> negative</span>
                                        <span><i class="fas fa-text-height"></i> any</span>
                                    </div>


                                    <div class="bg-gray-50 p-2 rounded text-sm mt-2">We're truly sorry to hear about your experience, reviewerFirstName. We strive to provide excellent service, and we clearly missed the mark. We'd like to make this right - please contact us at businessContact so we can address this personally.</div>
                                </div>
                                <div class="template-card bg-white border border-gray-200 rounded-lg p-4 mb-4" data-template-id="temp3">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-md font-semibold">
                                            Neutral Response

                                        </h4>
                                        <div class="flex space-x-2">
                                            <button class="edit-template-btn text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="delete-template-btn text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">A balanced response for mixed reviews</p>
                                    <div class="text-xs text-gray-500 mb-2">
                                        <span class="mr-2"><i class="icon-star"></i> 3-3</span>
                                        <span class="mr-2"><i class="fas fa-heart"></i> neutral</span>
                                        <span><i class="fas fa-text-height"></i> any</span>
                                    </div>


                                    <div class="bg-gray-50 p-2 rounded text-sm mt-2">Thank you for your feedback, reviewerFirstName reviewerLastName. We appreciate you taking the time to share your thoughts. We're always working to improve our service at businessName and your input helps us do that. If you have any additional suggestions, please let us know!</div>
                                </div>
                                <div class="template-card bg-white border border-gray-200 rounded-lg p-4 mb-4" data-template-id="temp4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-md font-semibold">
                                            Detailed Appreciation

                                        </h4>
                                        <div class="flex space-x-2">
                                            <button class="edit-template-btn text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="delete-template-btn text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">A comprehensive thank you for detailed positive reviews</p>
                                    <div class="text-xs text-gray-500 mb-2">
                                        <span class="mr-2"><i class="icon-star"></i> 4-5</span>
                                        <span class="mr-2"><i class="fas fa-heart"></i> positive</span>
                                        <span><i class="fas fa-text-height"></i> long</span>
                                    </div>


                                    <div class="bg-gray-50 p-2 rounded text-sm mt-2">Thank you for taking the time to write such a detailed review, reviewerFirstName! We're thrilled that you had a positive experience with us at businessName. Your specific feedback helps us understand what we're doing right and motivates our team to maintain our high standards. We look forward to serving you again soon!</div>
                                </div>
                                <div class="template-card bg-white border border-gray-200 rounded-lg p-4 mb-4" data-template-id="temp5">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-md font-semibold">
                                            Detailed Apology

                                        </h4>
                                        <div class="flex space-x-2">
                                            <button class="edit-template-btn text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="delete-template-btn text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">A comprehensive apology for detailed negative reviews</p>
                                    <div class="text-xs text-gray-500 mb-2">
                                        <span class="mr-2"><i class="icon-star"></i> 1-2</span>
                                        <span class="mr-2"><i class="fas fa-heart"></i> negative</span>
                                        <span><i class="fas fa-text-height"></i> long</span>
                                    </div>


                                    <div class="bg-gray-50 p-2 rounded text-sm mt-2">We sincerely apologize for your disappointing experience, reviewerFirstName reviewerLastName. We take your detailed feedback very seriously and will be addressing each point you've raised. Our team at businessName is committed to improving, and we would appreciate the opportunity to make things right. Please contact us directly at businessContact to discuss how we can resolve these issues.</div>
                                </div><button class="add-template-btn w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 rounded-lg flex items-center justify-center"><i class="fas fa-plus mr-2"></i> Add New Template</button>
                            </div>
                        </div>
                    </div>

                    <!-- Account Management Tab -->
                    <div id="accountTab" class="tab-content hidden h-full overflow-y-auto p-4">
                        <!-- Back to Business Navigation -->
                        <div id="backToBusinessNav" class="hidden mb-4">
                            <button id="backToBusinessBtn" class="flex items-center px-4 py-2 bg-white-600 text-indigo-600 border ring-2 ring-indigo-600 rounded-md text-sm font-medium hover:text-white hover:bg-indigo-600">
                                <i class="fas fa-arrow-left mr-2"></i> Back to Business
                            </button>
                        </div>
                        <!-- Business Selector -->
                        <div class="mb-4 p-3 bg-gray-50 rounded-md">

                            <p class="text-xs text-gray-500 mt-1">Team members are assigned roles for each business separately</p>
                        </div>
                        <div class="bg-white rounded-xl shadow-sm p-4 md:p-6 mb-4">
                            <div class="flex flex-wrap justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Team Management</h3>
                                <div class="flex gap-2 justify-end">
                                    <button id="addTeamMemberToBusinessBtn" class="flex items-center gap-1 px-3 py-2 bg-indigo-100 text-indigo-700 rounded-md text-sm font-medium hover:bg-indigo-200">
                                        <i class="fas fa-user-plus"></i> <span class="hidden md:block">Add to Business</span>
                                    </button>
                                    <button id="inviteTeamMemberBtn" class="flex items-center gap-1 px-3 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
                                        <i class="fas fa-envelope"></i> <span class="hidden md:block">Invite New</span>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4">
                                <p class="text-sm text-gray-600">
                                    Manage your team members and their permissions. Team members can be assigned different roles for each business separately.
                                </p>
                            </div>



                            <!-- Team Members List -->
                            <div class="mt-6">
                                <h4 class="text-md font-medium mb-3">Team Members</h4>

                                <div class="bg-gray-50 rounded-md p-3 mb-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-800 font-semibold mr-3">
                                                JS
                                            </div>
                                            <div>
                                                <div class="font-medium">John Smith (You)</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs font-medium">Owner</span>
                                        </div>
                                    </div>
                                </div>

                                <div id="teamMembersList">
                                    <div class="bg-gray-50 rounded-md p-3 mb-4" data-member-id="tm1">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-800 font-semibold mr-3">
                                                    SJ
                                                </div>
                                                <div>
                                                    <div class="font-medium">Sarah Johnson</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                    <div class="flex items-center mt-1">
                                                        <div class="text-xs text-gray-400">Last login: 5/15/2025 10:30 AM</div>
                                                        <div class="text-xs text-gray-400 ml-3">Added: 5/1/2025</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3">Owner</span>
                                                <div class="dropdown relative">
                                                    <button class="text-gray-500 hover:text-gray-700 focus:outline-none">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                                                        <button class="edit-member-role-btn block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                            <i class="fas fa-user-tag mr-2"></i> Change Role
                                                        </button>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 rounded-md p-3 mb-4" data-member-id="tm2">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-800 font-semibold mr-3">
                                                    MC
                                                </div>
                                                <div>
                                                    <div class="font-medium">Michael Chen</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                    <div class="flex items-center mt-1">
                                                        <div class="text-xs text-gray-400">Last login: 5/14/2025 03:45 PM</div>
                                                        <div class="text-xs text-gray-400 ml-3">Added: 5/5/2025</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs font-medium mr-3">Viewer</span>
                                                <div class="dropdown relative">
                                                    <button class="text-gray-500 hover:text-gray-700 focus:outline-none">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                                                        <button class="edit-member-role-btn block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                            <i class="fas fa-user-tag mr-2"></i> Change Role
                                                        </button>

                                                        <button class="remove-from-business-btn block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                                            <i class="fas fa-user-minus mr-2"></i> Remove from Business
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Pending Invitations -->
                                <div class="mt-8">
                                    <h4 class="text-md font-medium mb-3">Pending Invitations</h4>
                                    <div id="pendingInvitationsList">
                                        <div class="text-sm text-gray-500 italic">No pending invitations for this business</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Connected Businesses Section -->
                        <div class="pt-2 pb-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Connected Businesses</h3>
                                <button id="connectNewBusinessBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
                                    <i class="fas fa-plus mr-1"></i> Connect New Business
                                </button>
                            </div>

                            <div class="mb-4">
                                <p class="text-sm text-gray-600">
                                    View and manage all businesses connected to your account. You can disconnect businesses that you no longer want to manage.
                                </p>
                            </div>

                            <div id="connectedBusinessesContainer" class="mt-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="connected-business-card bg-white rounded-lg shadow-sm p-4" data-business-id="bus123">
                                        <div class="flex items-start">
                                            <div class="business-thumbnail w-12 h-12 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                                <img src="assets/images/coffee_shop.png" alt="Coffee Supreme" onerror="this.src='assets/images/default-business.png'">
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between items-start">
                                                    <h3 class="text-md font-medium text-gray-900">Coffee Supreme</h3>
                                                    <button class="disconnect-business-btn text-red-600 hover:text-red-800 text-sm">
                                                        <i class="fas fa-unlink"></i> Disconnect
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-500">123 Main St, Seattle, WA</p>
                                                <div class="flex items-center mt-1">
                                                    <div class="flex text-yellow-400 text-xs mr-1">
                                                        <i class="icon-star"></i>
                                                    </div>
                                                    <span class="text-xs font-medium">4.7</span>
                                                    <span class="text-xs text-gray-500 ml-2">(182 reviews)</span>
                                                </div>

                                                <div class="connected-by flex items-center mt-2">
                                                    <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                        JS
                                                    </div>
                                                    <span class="text-xs text-gray-500 ml-1">Connected by John Smith</span>
                                                    <span class="text-xs text-gray-400 ml-2">4/15/2025</span>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="connected-business-card bg-white rounded-lg shadow-sm p-4" data-business-id="bus124">
                                        <div class="flex items-start">
                                            <div class="business-thumbnail w-12 h-12 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                                <img src="assets/images/bakery.png" alt="Seattle Bakery" onerror="this.src='assets/images/default-business.png'">
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between items-start">
                                                    <h3 class="text-md font-medium text-gray-900">Seattle Bakery</h3>
                                                    <button class="disconnect-business-btn text-red-600 hover:text-red-800 text-sm">
                                                        <i class="fas fa-unlink"></i> Disconnect
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-500">456 Pike St, Seattle, WA</p>
                                                <div class="flex items-center mt-1">
                                                    <div class="flex text-yellow-400 text-xs mr-1">
                                                        <i class="icon-star"></i>
                                                    </div>
                                                    <span class="text-xs font-medium">4.2</span>
                                                    <span class="text-xs text-gray-500 ml-2">(143 reviews)</span>
                                                </div>

                                                <div class="connected-by flex items-center mt-2">
                                                    <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                        SJ
                                                    </div>
                                                    <span class="text-xs text-gray-500 ml-1">Connected by Sarah Johnson</span>
                                                    <span class="text-xs text-gray-400 ml-2">4/18/2025</span>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="connected-business-card bg-white rounded-lg shadow-sm p-4" data-business-id="bus125">
                                        <div class="flex items-start">
                                            <div class="business-thumbnail w-12 h-12 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                                <img src="assets/images/deli.png" alt="Downtown Deli" onerror="this.src='assets/images/default-business.png'">
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between items-start">
                                                    <h3 class="text-md font-medium text-gray-900">Downtown Deli</h3>
                                                    <button class="disconnect-business-btn text-red-600 hover:text-red-800 text-sm">
                                                        <i class="fas fa-unlink"></i> Disconnect
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-500">789 Olive Way, Seattle, WA</p>
                                                <div class="flex items-center mt-1">
                                                    <div class="flex text-yellow-400 text-xs mr-1">
                                                        <i class="icon-star"></i>
                                                    </div>
                                                    <span class="text-xs font-medium">3.9</span>
                                                    <span class="text-xs text-gray-500 ml-2">(98 reviews)</span>
                                                </div>

                                                <div class="connected-by flex items-center mt-2">
                                                    <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                        JS
                                                    </div>
                                                    <span class="text-xs text-gray-500 ml-1">Connected by John Smith</span>
                                                    <span class="text-xs text-gray-400 ml-2">4/22/2025</span>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="connected-business-card bg-white rounded-lg shadow-sm p-4" data-business-id="bus126">
                                        <div class="flex items-start">
                                            <div class="business-thumbnail w-12 h-12 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                                <img src="assets/images/seafood.png" alt="Harbor Seafood" onerror="this.src='assets/images/default-business.png'">
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between items-start">
                                                    <h3 class="text-md font-medium text-gray-900">Harbor Seafood</h3>
                                                    <button class="disconnect-business-btn text-red-600 hover:text-red-800 text-sm">
                                                        <i class="fas fa-unlink"></i> Disconnect
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-500">321 Waterfront Ave, Seattle, WA</p>
                                                <div class="flex items-center mt-1">
                                                    <div class="flex text-yellow-400 text-xs mr-1">
                                                        <i class="icon-star"></i>
                                                    </div>
                                                    <span class="text-xs font-medium">4.5</span>
                                                    <span class="text-xs text-gray-500 ml-2">(156 reviews)</span>
                                                </div>

                                                <div class="connected-by flex items-center mt-2">
                                                    <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                        MC
                                                    </div>
                                                    <span class="text-xs text-gray-500 ml-1">Connected by Michael Chen</span>
                                                    <span class="text-xs text-gray-400 ml-2">5/1/2025</span>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="connected-business-card bg-white rounded-lg shadow-sm p-4" data-business-id="bus127">
                                        <div class="flex items-start">
                                            <div class="business-thumbnail w-12 h-12 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
                                                <img src="assets/images/cafe.png" alt="Green Leaf Cafe" onerror="this.src='assets/images/default-business.png'">
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <div class="flex justify-between items-start">
                                                    <h3 class="text-md font-medium text-gray-900">Green Leaf Cafe</h3>
                                                    <button class="disconnect-business-btn text-red-600 hover:text-red-800 text-sm">
                                                        <i class="fas fa-unlink"></i> Disconnect
                                                    </button>
                                                </div>
                                                <p class="text-sm text-gray-500">567 University St, Seattle, WA</p>
                                                <div class="flex items-center mt-1">
                                                    <div class="flex text-yellow-400 text-xs mr-1">
                                                        <i class="icon-star"></i>
                                                    </div>
                                                    <span class="text-xs font-medium">4.1</span>
                                                    <span class="text-xs text-gray-500 ml-2">(112 reviews)</span>
                                                </div>

                                                <div class="connected-by flex items-center mt-2">
                                                    <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                                                        SJ
                                                    </div>
                                                    <span class="text-xs text-gray-500 ml-1">Connected by Sarah Johnson</span>
                                                    <span class="text-xs text-gray-400 ml-2">5/5/2025</span>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>




                        </div>
                    </div>

                    <!-- Plan Management Tab -->
                    <div id="planTab" class="tab-content h-full overflow-y-auto p-4 active">
                        <!-- Back to Business Navigation -->
                        <div id="backToPlanNav" class="mb-4">
                            <button id="backFromPlanBtn" class="flex items-center px-4 py-2 bg-white-600 text-indigo-600 border ring-2 ring-indigo-600 rounded-md text-sm font-medium hover:text-white hover:bg-indigo-600">
                                <i class="fas fa-arrow-left mr-2"></i> Back to Business
                            </button>
                        </div>

                        <!-- Current Plan Section -->
                        <div class="bg-white rounded-xl shadow-sm p-4 md:p-6 mb-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Current Subscription</h3>
                            </div>

                            <div class="flex mb-6">
                                <!-- Left side (30%) - Plan info -->
                                <div class="w-3/10 pr-6 border-r border-gray-200">
                                    <div class="mb-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 id="currentPlanName" class="text-xl font-bold text-indigo-700">Professional Plan</h4>
                                            <button id="planInfoBtn" class="text-indigo-600 hover:text-indigo-800 text-sm">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                        </div>
                                        <p id="currentPlanPrice" class="text-gray-600">$29.99/month</p>
                                    </div>

                                    <div class="mb-4">
                                        <span id="planStatus" class="px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                                    </div>

                                    <div class="mt-6">
                                        <button id="upgradePlanBtn" class="w-full px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
                                            <i class="fas fa-arrow-up mr-1"></i> Upgrade Plan
                                        </button>
                                        <a href="#" id="cancelSubscriptionBtn" class="block text-center mt-3 text-xs text-red-500 hover:text-red-700">
                                            Cancel subscription
                                        </a>
                                    </div>
                                </div>

                                <!-- Right side (70%) - Billing details -->
                                <div class="w-7/10 pl-6">
                                    <h5 class="text-sm font-medium text-gray-700 mb-3">Billing Information</h5>
                                    <div class="p-4 bg-gray-50 rounded-md mb-4">
                                        <div class="flex justify-between items-center mb-3">
                                            <span class="text-sm text-gray-600">Auto-renew</span>
                                            <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                                <input type="checkbox" id="autoRenewToggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked="">
                                                <label for="autoRenewToggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                                            </div>
                                        </div>
                                        <div class="flex justify-between mb-3">
                                            <span class="text-sm text-gray-600">Next billing date</span>
                                            <span id="nextBillingDate" class="text-sm font-medium">June 19, 2025</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Next billing amount</span>
                                            <span id="nextBillingAmount" class="text-sm font-medium">$29.99</span>
                                        </div>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <h5 class="text-sm font-medium text-gray-700">Payment Method</h5>
                                    </div>
                                    <div id="paymentMethodContainer" class="mt-2 p-3 border rounded-md bg-gray-50">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fab fa-cc-visa text-xl text-blue-800 mr-3"></i>
                                                <div>
                                                    <p class="text-sm font-medium">Visa ending in 4242</p>
                                                    <p class="text-xs text-gray-500">Expires 12/2026</p>
                                                </div>
                                            </div>
                                            <button id="updatePaymentMethodBtn" class="text-indigo-600 hover:text-indigo-800 text-xs font-medium">
                                                <i class="fas fa-pen mr-1"></i> Update
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Billing Information Section -->
                        <div class="bg-white rounded-xl shadow-sm p-4 md:p-6 mb-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Billing Information</h3>
                                <button id="editBillingInfoBtn" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                    <i class="fas fa-pen mr-1"></i> Edit
                                </button>
                            </div>

                            <div id="billingInfoContainer" class="mb-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Name</p>
                                        <p id="billingName" class="font-medium">John Smith</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Email</p>
                                        <p id="billingEmail" class="font-medium"><EMAIL></p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">Company</p>
                                        <p id="billingCompany" class="font-medium">Acme Inc.</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 mb-1">VAT/Tax ID</p>
                                        <p id="billingTaxId" class="font-medium">US123456789</p>
                                    </div>
                                    <div class="col-span-2">
                                        <p class="text-sm text-gray-500 mb-1">Address</p>
                                        <p id="billingAddress" class="font-medium">123 Main St, Suite 100, San Francisco, CA 94105, USA</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice History Section -->
                        <div class="pt-2 pb-4">
                            <h3 class="text-lg font-semibold mb-4">Invoice History</h3>

                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead>
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200" id="invoiceTableBody">
                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">May 19, 2025</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">INV-2025-0519</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">$29.99</td>
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Paid
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">Invoice</a><a href="#" class="text-indigo-600 hover:text-indigo-900">Receipt</a>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">Apr 19, 2025</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">INV-2025-0419</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">$29.99</td>
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    Pending
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">Invoice</a><button class="text-indigo-600 hover:text-indigo-900">Pay Now</button>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">Mar 19, 2025</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">INV-2025-0319</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">$29.99</td>
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Failed
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">Invoice</a><button class="text-indigo-600 hover:text-indigo-900">Retry Payment</button>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">Feb 19, 2025</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">INV-2025-0219</td>
                                            <td class="px-4 py-3 whitespace-nowrap text-sm">$29.99</td>
                                            <td class="px-4 py-3 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    Canceled on Feb 19, 2025
                                                </span>
                                            </td>
                                            <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">Invoice</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Tab -->
                    <div id="settingsTab" class="tab-content hidden h-full overflow-y-auto p-4">
                        <div class="pt-2 pb-4">
                            <h3 class="text-lg font-semibold mb-6">Settings</h3>
                            

                            <!-- No Business Selected Message -->
                            <div id="noBusinessSelected" class="hidden">
                                <div class="text-center py-8">
                                    <div class="text-gray-400 mb-2">
                                        <i class="fas fa-building text-4xl"></i>
                                    </div>
                                    <h4 class="text-lg font-medium text-gray-700 mb-2">No Business Selected</h4>
                                    <p class="text-sm text-gray-500 mb-4">Please select a business from the header dropdown to configure its settings.</p>
                                </div>
                            </div>

                            <!-- Settings Content -->
                            <div id="settingsContent" class="grid grid-cols-2 gap-8">
                                <!-- Left Column -->
                                <div>
                                    <!-- AI Configuration Section -->
                                    <div class="mb-8">
                                        <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">AI Configuration</h4>

                                        <div class="space-y-4">
                                            <!-- AI Provider -->
                                            <div>
                                                <label for="aiProvider" class="block text-sm mb-1">AI Provider</label>
                                                <select id="aiProvider" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="chatgpt">ChatGPT (OpenAI)</option>
                                                    <option value="claude">Claude (Anthropic)</option>
                                                    <option value="gemini">Gemini (Google)</option>
                                                </select>
                                            </div>

                                            <!-- Model Version -->
                                            <div>
                                                <label for="aiModel" class="block text-sm mb-1">Model Version</label>
                                                <select id="aiModel" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                                    <option value="gpt-4">GPT-4</option>
                                                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                                </select>
                                            </div>

                                            <!-- Response Style Slider -->
                                            <div>
                                                <label for="responseStyle" class="block text-sm mb-1">Response Style</label>
                                                <div class="flex items-center">
                                                    <span class="text-xs text-gray-500 w-16">Formal</span>
                                                    <div class="flex-1 mx-2">
                                                        <input type="range" id="responseStyle" min="0" max="100" value="50" class="w-full">
                                                    </div>
                                                    <span class="text-xs text-gray-500 w-16">Casual</span>
                                                </div>
                                                <div class="text-center text-xs text-gray-500 mt-1">
                                                    <span id="responseStyleValue">Balanced (50%)</span>
                                                </div>
                                            </div>

                                            <!-- Response Length Slider -->
                                            <div>
                                                <label for="responseLength" class="block text-sm mb-1">Response Length</label>
                                                <div class="flex items-center">
                                                    <span class="text-xs text-gray-500 w-16">Concise</span>
                                                    <div class="flex-1 mx-2">
                                                        <input type="range" id="responseLength" min="0" max="100" value="50" class="w-full">
                                                    </div>
                                                    <span class="text-xs text-gray-500 w-16">Elaborative</span>
                                                </div>
                                                <div class="text-center text-xs text-gray-500 mt-1">
                                                    <span id="responseLengthValue">Moderate (50%)</span>
                                                </div>
                                            </div>

                                            <!-- Sign-off Text -->
                                            <div>
                                                <label for="signOffText" class="block text-sm mb-1">Custom Sign-off Text</label>
                                                <input type="text" id="signOffText" placeholder="Thank you for your business!" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                <p class="text-xs text-gray-500 mt-1">This text will be added at the end of each reply</p>
                                            </div>

                                            <!-- Language Selection -->
                                            <div>
                                                <label for="aiLanguage" class="block text-sm mb-1">Response Language</label>
                                                <select id="aiLanguage" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    @foreach (languageOptionsAssoc() as $value => $label)
                                                    <option value="{{ $value }}" {{ $value == 'same-as-review' ? 'selected' : '' }}>{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                            </div>

                                            <!-- Custom Instructions -->
                                            <div>
                                                <label for="customInstructions" class="block text-sm mb-1">Custom Instructions</label>
                                                <textarea id="customInstructions" rows="3" placeholder="Add any specific instructions for the AI..." class="w-full border border-gray-300 rounded-md px-3 py-2 resize-none"></textarea>
                                                <p class="text-xs text-gray-500 mt-1">These instructions will guide how the AI generates responses</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Business Configuration Section -->
                                    <div class="mb-8">
                                        <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">Business Configuration</h4>

                                        <div class="space-y-4">
                                            <!-- Timezone Selection -->
                                            <div>
                                                <label for="timezone" class="block text-sm mb-1">Business Timezone</label>
                                                <select id="timezone" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="Pacific/Honolulu">(GMT-10:00) Hawaii</option>
                                                    <option value="America/Anchorage">(GMT-09:00) Alaska</option>
                                                    <option value="America/Los_Angeles">(GMT-08:00) Pacific Time (US &amp; Canada)</option>
                                                    <option value="America/Phoenix">(GMT-07:00) Arizona</option>
                                                    <option value="America/Denver">(GMT-07:00) Mountain Time (US &amp; Canada)</option>
                                                    <option value="America/Chicago">(GMT-06:00) Central Time (US &amp; Canada)</option>
                                                    <option value="America/New_York" selected="">(GMT-05:00) Eastern Time (US &amp; Canada)</option>
                                                    <option value="America/Sao_Paulo">(GMT-03:00) Sao Paulo</option>
                                                    <option value="UTC">(GMT+00:00) UTC</option>
                                                    <option value="Europe/London">(GMT+00:00) London</option>
                                                    <option value="Europe/Paris">(GMT+01:00) Paris</option>
                                                    <option value="Europe/Berlin">(GMT+01:00) Berlin</option>
                                                    <option value="Europe/Athens">(GMT+02:00) Athens</option>
                                                    <option value="Asia/Jerusalem">(GMT+02:00) Jerusalem</option>
                                                    <option value="Asia/Riyadh">(GMT+03:00) Riyadh</option>
                                                    <option value="Asia/Dubai">(GMT+04:00) Dubai</option>
                                                    <option value="Asia/Kolkata">(GMT+05:30) Mumbai, New Delhi</option>
                                                    <option value="Asia/Bangkok">(GMT+07:00) Bangkok</option>
                                                    <option value="Asia/Shanghai">(GMT+08:00) Beijing</option>
                                                    <option value="Asia/Tokyo">(GMT+09:00) Tokyo</option>
                                                    <option value="Australia/Sydney">(GMT+10:00) Sydney</option>
                                                    <option value="Pacific/Auckland">(GMT+12:00) Auckland</option>
                                                </select>
                                                <p class="text-xs text-gray-500 mt-1">Used for scheduling and time-based features</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div>
                                    <!-- Review Checking Section -->
                                    <div class="mb-8">
                                        <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">Review Checking</h4>

                                        <div class="space-y-4">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="reviewCheckingEnabled" class="mr-2" checked="">
                                                <label for="reviewCheckingEnabled">Automatically check for new reviews</label>
                                            </div>

                                            <div>
                                                <label for="reviewCheckingInterval" class="block text-sm mb-1">Check Interval (minutes)</label>
                                                <select id="reviewCheckingInterval" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="5">Every 5 minutes</option>
                                                    <option value="15">Every 15 minutes</option>
                                                    <option value="30" selected="">Every 30 minutes</option>
                                                    <option value="60">Every hour</option>
                                                    <option value="120">Every 2 hours</option>
                                                    <option value="360">Every 6 hours</option>
                                                    <option value="720">Every 12 hours</option>
                                                    <option value="1440">Once a day</option>
                                                </select>
                                                <p class="text-xs text-gray-500 mt-1">How often to check for new reviews</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Auto Reply Section -->
                                    <div class="mb-8">
                                        <h4 class="text-md font-medium mb-4 pb-2 border-b border-gray-200">Auto Reply</h4>

                                        <div class="space-y-4">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="autoReplyEnabled" class="mr-2">
                                                <label for="autoReplyEnabled">Automatically reply to new reviews</label>
                                            </div>

                                            <div>
                                                <label class="block text-sm mb-1">Reply to reviews with rating:</label>
                                                <div class="flex items-center space-x-2">
                                                    <select id="autoReplyConditionsRatingMin" class="border border-gray-300 rounded-md px-3 py-2">
                                                        <option value="1">1</option>
                                                        <option value="2">2</option>
                                                        <option value="3">3</option>
                                                        <option value="4">4</option>
                                                        <option value="5">5</option>
                                                    </select>
                                                    <span>to</span>
                                                    <select id="autoReplyConditionsRatingMax" class="border border-gray-300 rounded-md px-3 py-2">
                                                        <option value="1">1</option>
                                                        <option value="2">2</option>
                                                        <option value="3">3</option>
                                                        <option value="4">4</option>
                                                        <option value="5" selected="">5</option>
                                                    </select>
                                                    <span>stars</span>
                                                </div>
                                            </div>

                                            <div>
                                                <label for="autoReplyConditionsSentiment" class="block text-sm mb-1">Sentiment</label>
                                                <select id="autoReplyConditionsSentiment" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    @foreach (sentimentOptionsAssoc() as $value => $label)
                                                    <option value="{{ $value }}" {{ $value == 'any' ? 'selected' : '' }}>{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                            </div>

                                            <div>
                                                <label for="autoReplyConditionsLength" class="block text-sm mb-1">Review Length</label>
                                                <select id="autoReplyConditionsLength" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="any" selected="">Any length</option>
                                                    <option value="short">Short reviews only</option>
                                                    <option value="medium">Medium reviews only</option>
                                                    <option value="long">Long reviews only</option>
                                                </select>
                                            </div>

                                            <div>
                                                <label for="autoReplyTemplateId" class="block text-sm mb-1">Template to Use</label>
                                                <select id="autoReplyTemplateId" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="auto" selected="">Auto-select best template</option>
                                                    <!-- Other templates will be populated by JavaScript -->
                                                    <option value="system-default">System Default Template</option>
                                                </select>
                                                <p class="text-xs text-gray-500 mt-1">Default template will be used if no matching template is found</p>
                                            </div>

                                            <div>
                                                <label for="autoReplyTone" class="block text-sm mb-1">Reply Tone</label>
                                                <select id="autoReplyTone" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    @foreach (toneOptionsAssoc() as $value => $label)
                                                    <option value="{{ $value }}" {{ $value == 'professional' ? 'selected' : '' }}>{{ $label }}</option>
                                                    @endforeach
                                                </select>
                                            </div>

                                            <!-- Auto Reply Timing -->
                                            <div>
                                                <label for="autoReplyTimingMode" class="block text-sm mb-1">When to Send Auto Reply</label>
                                                <select id="autoReplyTimingMode" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="immediate" selected="">Immediately</option>
                                                    <option value="delay">After a delay</option>
                                                    <option value="scheduled">At a scheduled time</option>
                                                </select>
                                            </div>

                                            <!-- Delay Minutes (shown when mode is "delay") -->
                                            <div id="autoReplyDelayContainer" class="hidden">
                                                <label for="autoReplyTimingDelayMinutes" class="block text-sm mb-1">Delay (minutes)</label>
                                                <select id="autoReplyTimingDelayMinutes" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                    <option value="5">5 minutes</option>
                                                    <option value="15" selected="">15 minutes</option>
                                                    <option value="30">30 minutes</option>
                                                    <option value="60">1 hour</option>
                                                    <option value="120">2 hours</option>
                                                    <option value="240">4 hours</option>
                                                    <option value="480">8 hours</option>
                                                </select>
                                                <p class="text-xs text-gray-500 mt-1">Wait this long after review is posted, before sending the auto-reply</p>
                                            </div>

                                            <!-- Scheduled Time (shown when mode is "scheduled") -->
                                            <div id="autoReplyScheduledContainer" class="hidden">
                                                <label for="autoReplyTimingScheduledTime" class="block text-sm mb-1">Time to Send Replies</label>
                                                <input type="time" id="autoReplyTimingScheduledTime" value="09:00" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                                <p class="text-xs text-gray-500 mt-1">All replies will be queued and sent at this time (business timezone)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Reset Settings Button -->
                            <div class="mt-6 flex justify-between">
                                <div>
                                    <button id="exportSettingsBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                                        <i class="fas fa-download mr-1"></i> Export Settings
                                    </button>
                                </div>
                                <div class="space-x-2">
                                    <button id="saveSettingsBtn" class="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
                                        <i class="fas fa-save mr-1"></i> Save Settings
                                    </button>
                                    <button id="resetSettingsBtn" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium">
                                        Reset to Default Settings
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Billing Information Modal (Hidden by default) -->
    <div id="billingInfoModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div class="flex justify-between items-center border-b px-6 py-4">
                <h3 class="text-lg font-semibold">Edit Billing Information</h3>
                <button id="closeBillingInfoModalBtn" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <form id="billingInfoForm">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                        <div class="col-span-2">
                            <label for="billingNameInput" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                            <input type="text" id="billingNameInput" class="w-full border border-gray-300 rounded-md px-3 py-2" value="John Smith">
                        </div>
                        <div class="col-span-2">
                            <label for="billingEmailInput" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                            <input type="email" id="billingEmailInput" class="w-full border border-gray-300 rounded-md px-3 py-2" value="<EMAIL>">
                        </div>
                        <div class="col-span-2">
                            <label for="billingCompanyInput" class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                            <input type="text" id="billingCompanyInput" class="w-full border border-gray-300 rounded-md px-3 py-2" value="Acme Inc.">
                        </div>
                        <div class="col-span-2">
                            <label for="billingTaxIdInput" class="block text-sm font-medium text-gray-700 mb-1">VAT/Tax ID</label>
                            <input type="text" id="billingTaxIdInput" class="w-full border border-gray-300 rounded-md px-3 py-2" value="US123456789">
                        </div>
                        <div class="col-span-2">
                            <label for="billingAddressInput" class="block text-sm font-medium text-gray-700 mb-1">Billing Address</label>
                            <textarea id="billingAddressInput" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2">123 Main St, Suite 100, San Francisco, CA 94105, USA</textarea>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="cancelBillingInfoBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
                        <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Plan Info Modal (Hidden by default) -->
    <div id="planInfoModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div class="flex justify-between items-center border-b px-6 py-4">
                <h3 class="text-lg font-semibold">Professional Plan Details</h3>
                <button id="closePlanInfoModalBtn" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-500">Monthly Price</span>
                        <span class="font-semibold">$29.99</span>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-500">Billing Cycle</span>
                        <span>Monthly</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-500">Renewal Date</span>
                        <span>June 19, 2025</span>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-4 mt-4">
                    <h4 class="font-medium mb-2">Plan Features</h4>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>Unlimited review monitoring</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>AI-powered response generation</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>Advanced analytics and reporting</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>Up to 5 team members</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>Connect up to 10 business locations</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>Email and chat support</span>
                        </li>
                    </ul>
                </div>

                <div class="mt-6 text-center">
                    <button id="upgradePlanModalBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
                        Upgrade to Business Plan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    @vite(['resources/assets/js/app.js'])

    <div id="editProfileModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden backdrop-blur-sm">
        <div class="bg-white md:rounded-lg shadow-xl w-[500px] max-w-full h-full md:h-auto md:max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">Edit Profile 123</h3>
                    <button id="closeProfileModalBtn" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="profileForm" class="space-y-4">
                    <!-- Profile Picture -->
                    <div class="flex justify-center mb-6">
                        <div class="relative">
                            <img src="assets/images/user-avatar.png" alt="Profile" class="w-24 h-24 rounded-full object-cover border-4 border-gray-200">
                            <button type="button" class="absolute bottom-0 right-0 bg-indigo-600 text-white rounded-full p-2 shadow-md hover:bg-indigo-700">
                                <i class="fas fa-camera text-xs"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Name -->
                    <div>
                        <label for="profileName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <input type="text" id="profileName" value="John Smith" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="profileEmail" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input type="email" id="profileEmail" value="<EMAIL>" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>

                    <!-- Phone -->
                    <div class="w-full">
                        <label for="profilePhone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" id="profilePhone" placeholder="Enter your phone number" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>

                    <!-- Google Account -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Google Account</label>
                        <div class="flex items-center justify-between border border-gray-300 rounded-md px-3 py-2 bg-gray-50">
                            <div class="flex items-center">
                                <i class="fab fa-google text-red-500 mr-2"></i>
                                <span><NAME_EMAIL></span>
                            </div>
                            <button type="button" class="text-sm text-indigo-600 hover:text-indigo-800">Disconnect</button>
                        </div>
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="profilePassword" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <button type="button" id="changePasswordBtn" class="text-sm text-indigo-600 hover:text-indigo-800">Change Password</button>
                        <div id="passwordFields" class="hidden space-y-3 mt-3">
                            <input type="password" id="currentPassword" placeholder="Current password" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <input type="password" id="newPassword" placeholder="New password" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <input type="password" id="confirmPassword" placeholder="Confirm new password" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        </div>
                    </div>

                    <div class="flex justify-end pt-4">
                        <button type="button" id="cancelProfileBtn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 mr-2">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div id="highlighter--hover-tools" style="display: none;">
        <div id="highlighter--hover-tools--container">
            <div class="highlighter--icon highlighter--icon-copy" title="Copy"></div>
            <div class="highlighter--icon highlighter--icon-change-color" title="Change Color"></div>
            <div class="highlighter--icon highlighter--icon-delete" title="Delete"></div>
        </div>
    </div>
</body>

</html>