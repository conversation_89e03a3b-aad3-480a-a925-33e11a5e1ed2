/**
 * Build script for permissions-handler.js
 * This script compiles the permissions handler JavaScript and copies it to the public directory
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination paths
const sourcePath = path.join(__dirname, 'resources/assets/js/permissions-handler.js');
const destPath = path.join(__dirname, 'public/js/permissions-handler.js');

// Ensure the destination directory exists
const destDir = path.dirname(destPath);
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
}

try {
  // Read the source file
  const data = fs.readFileSync(sourcePath, 'utf8');
  
  // Write to destination
  fs.writeFileSync(destPath, data, 'utf8');
  console.log(`Successfully compiled permissions handler to ${destPath}`);
} catch (err) {
  console.error('Error:', err);
  process.exit(1);
}
