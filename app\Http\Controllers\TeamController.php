<?php

namespace App\Http\Controllers;

use App\Models\BusinessAccount;
use App\Models\Permission;
use App\Models\TeamMember;
use App\Models\User;
use App\Mail\TeamInvitation;
use App\Mail\WelcomeUserMail;
use App\Models\AssociateBusiness;
use App\Models\Business;
use App\Services\SubscriptionService;
use App\Traits\ValidatesSubscriptionUsage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Carbon\Carbon;
use Illuminate\Support\Facades\Password;

class TeamController extends Controller
{
    use ValidatesSubscriptionUsage;
    /**
     * Display a listing of team members for a specific business account.
     *
     * @param int $locationId
     * @return \Illuminate\Http\Response
     */
    public function index($businessAccountId, $businessId = null)
    {
        $user = User::with(['invitedTeamMembers.user', 'invitedTeamMembers.permissions'])->find(Auth::id());

        $associateBusinesses = AssociateBusiness::where('user_id', $user->id)->get();
        $connectedBusinesses = Business::where('user_id', $user->id)->get();
        $connectedBusinessIds = $connectedBusinesses->pluck('location_name')->toArray();
        $business = Business::where('location_name', 'locations/' . $businessAccountId)->with('user')->first();

        $businessId = $businessId ? $businessId : $business->id;
        $teamUser = $user->invitedTeamMembers->where('business_id', $businessId)->where('status', 'active');
        $pendingInvitations = $this->pendingInvitations($user->id, $businessId);

        // if (Auth::id() !== $business->user_id) {
        //     $teamMembers = TeamMember::where('user_id', Auth::id())
        //         ->where('status', 'active')
        //         ->first();

        //     if (!$teamMember || (!$teamMember->isAdmin() && !$teamMember->hasPermission('manage_team'))) {
        //         return redirect()->back()->with('error', 'You do not have permission to manage team members.');
        //     }
        // }
        $teamMembers = TeamMember::where('user_id', $user->id)
            ->with(['business', 'invitedBy', 'permissions'])
            ->get();
        // $teamMembers = $business->teamMembers()->with(['user', 'permissions'])->get();
        return view('team.index', [
            'locationId' => $businessAccountId,
            'business' => $business,
            'teamMembers' => $teamMembers,
            'availablePermissions' => Permission::availablePermissions(),
            'connectedBusinesses' => $connectedBusinesses,
            'user' => $user,
            'teamUser' => $teamUser,
            'associateBusinesses' => $associateBusinesses,
            'connectedBusinessIds' => $connectedBusinessIds,
            'pendingInvitations' => $pendingInvitations
        ]);
    }

    public function pendingInvitations($userId, $businessId)
    {
        $teamData = TeamMember::where('invited_by', $userId)->where('status', 'pending')->where('business_id', $businessId)->get();
        $users = [];
        foreach ($teamData as $team) {
            $user = User::find($team->user_id);
            $users[] = $user;
        }
        return $users;
    }


    /**
     * Show the form for inviting a new team member.
     *
     * @param int $locationId
     * @return \Illuminate\Http\Response
     */
    public function create($locationId)
    {
        $business = Business::where('location_name', 'locations/' . $locationId)->with('user')->first();

        // Check if user owns this business account or is a team member with manage_team permission
        if (Auth::id() !== $business->user_id) {
            $teamMember = TeamMember::where('user_id', Auth::id())
                ->where('business_id', $business->id)
                ->where('status', 'active')
                ->first();

            if (!$teamMember || (!$teamMember->isAdmin() && !$teamMember->hasPermission('manage_team'))) {
                return redirect()->back()->with('error', 'You do not have permission to invite team members.');
            }
        }

        return view('team.invite', [
            'business' => $business,
            'locationId' => $locationId,
            'availablePermissions' => Permission::availablePermissions()
        ]);
    }

    /**
     * Send an invitation to a new team member.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $locationId
     * @return \Illuminate\Http\Response
     */
    public function invite(Request $request, $locationId)
    {

        $business = Business::where('location_name', 'locations/' . $locationId)->with('user')->first();
        // Check if user owns this business account or is a team member with manage_team permission
        if (Auth::id() !== $business->user_id) {
            $teamMember = TeamMember::where('user_id', Auth::id())
                ->where('business_id', $business->id)
                ->where('status', 'active')
                ->first();

            if (!$teamMember || (!$teamMember->isAdmin() && !$teamMember->hasPermission('manage_team'))) {
                return redirect()->back()->with('error', 'You do not have permission to invite team members.');
            }
        }

        // Check subscription limits for team member invitations
        $subscriptionService = new SubscriptionService();
        $validation = $subscriptionService->canInviteTeamMember($business->user_id, $business->id);
        if (!$validation['allowed']) {
            return redirect()->back()
                ->with('error', $validation['message'])
                ->with('upgrade_required', true)
                ->with('validation_data', $validation);
        }

        $request->validate([
            'email' => 'required|email',
            'role' => ['required', Rule::in(['viewer', 'member'])],
            'permission.*' => ['string', Rule::in(array_keys(Permission::availablePermissions()))]
        ]);
        // Check if user already exists
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            $temporaryPassword = 'password123';
            // Create a new user with a temporary password
            $user = User::create([
                'email' => $request->email,
                'name' => explode('@', $request->email)[0], // Use part of email as temporary name
                'password' => Hash::make($temporaryPassword),
            ]);

            // Mail::to($user->email)->send(new WelcomeUserMail($user, $temporaryPassword, $token));
        }

        // Check if user is already a member of this business account
        $existingMember = TeamMember::where('user_id', $user->id)
            ->where('business_id', $business->id)
            ->first();

        if ($existingMember) {
            if ($existingMember->status === 'active') {
                return redirect()->back()->with('error', 'This user is already a member of this business account.');
            } else {
                // Update the existing pending invitation
                $existingMember->update([
                    'role' => $request->role,
                    'invited_by' => Auth::id(),
                    'invitation_token' => Str::random(32),
                    'invitation_sent_at' => now(),
                    'status' => 'pending'
                ]);

                $teamMember = $existingMember;
            }
        } else {
            // Create a new team member
            $teamMember = TeamMember::create([
                'user_id' => $user->id,
                'business_id' => $business->id,
                'invited_by' => Auth::id(),
                'role' => $request->role,
                'status' => 'pending',
                'invitation_token' => Str::random(32),
                'invitation_sent_at' => now()
            ]);
        }

        // Assign permissions
        if ($request->has('permission')) {
            Permission::updateOrCreate(
                ['team_member_id' => $teamMember->id, 'permission_name' => $request->permission],
                ['is_granted' => true]
            );
        }
        $token = Password::createToken($user);
        // Send invitation email
        Mail::to($user->email)->send(new TeamInvitation($teamMember, $business, $token, $user));

        return redirect()->route('team.index', $locationId)
            ->with('success', 'Invitation sent successfully.');
    }

    /**
     * Accept a team invitation.
     *
     * @param string $token
     * @return \Illuminate\Http\Response
     */
    public function acceptInvitation($token)
    {
        $teamMember = TeamMember::where('invitation_token', $token)
            ->where('status', 'pending')
            ->firstOrFail();

        if (Auth::id() !== $teamMember->user_id) {
            return redirect()->route('login')->with('error', 'You must be logged in as the invited user to accept this invitation.');
        }

        $teamMember->update([
            'status' => 'active',
            'invitation_accepted_at' => now(),
            'invitation_token' => null
        ]);

        return redirect()->route('business.dashboard')
            ->with('success', 'You have successfully joined the team.');
    }


    /**
     * Decline a team invitation.
     *
     * @param string $token
     * @return \Illuminate\Http\Response
     */
    public function declineInvitation($token)
    {
        $teamMember = TeamMember::where('invitation_token', $token)
            ->where('status', 'pending')
            ->firstOrFail();

        if (Auth::id() !== $teamMember->user_id) {
            return redirect()->route('login')->with('error', 'You must be logged in as the invited user to accept this invitation.');
        }

        $teamMember->update([
            'status' => 'declined',
            'invitation_token' => null
        ]);

        return redirect()->back()
            ->with('success', 'You have successfully declined the invitation.');
    }

    /**
     * Show the form for editing team member permissions.
     *
     * @param int $teamMemberId
     * @return \Illuminate\Http\Response
     */
    public function edit($teamMemberId)
    {

        $teamMember = TeamMember::with(['user', 'permissions', 'invitedBy'])->findOrFail($teamMemberId);
        // Check if user owns this business account or is a team member with manage_team permission
        if (Auth::id() !== $teamMember->invitedBy->id) {
            $currentMember = TeamMember::where('user_id', Auth::id())
                ->where('business_account_id', $teamMember->business_account_id)
                ->where('status', 'active')
                ->first();

            if (!$currentMember || (!$currentMember->isAdmin() && !$currentMember->hasPermission('manage_team'))) {
                return redirect()->back()->with('error', 'You do not have permission to edit team members.');
            }
        }

        return view('team.edit', [
            'teamMember' => $teamMember,
            'availablePermissions' => Permission::availablePermissions()
        ]);
    }

    /**
     * Update the specified team member.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $teamMemberId
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $teamMemberId)
    {
        $teamMember = TeamMember::with('invitedBy')->findOrFail($teamMemberId);

        // Check if user owns this business account or is a team member with manage_team permission
        if (Auth::id() !== $teamMember->invitedBy->id) {
            $currentMember = TeamMember::where('user_id', Auth::id())
                ->where('business_account_id', $teamMember->business_account_id)
                ->where('status', 'active')
                ->first();

            if (!$currentMember || (!$currentMember->isAdmin() && !$currentMember->hasPermission('manage_team'))) {
                return redirect()->back()->with('error', 'You do not have permission to update team members.');
            }
        }

        $request->validate([
            'role' => ['required', Rule::in(['admin', 'member'])],
            'permissions' => 'array',
            'permissions.*' => ['string', Rule::in(array_keys(Permission::availablePermissions()))]
        ]);

        $teamMember->update([
            'role' => $request->role
        ]);

        // Update permissions
        foreach (array_keys(Permission::availablePermissions()) as $permName) {
            Permission::updateOrCreate(
                ['team_member_id' => $teamMember->id, 'permission_name' => $permName],
                ['is_granted' => in_array($permName, $request->permissions ?? [], true)]
            );
        }

        return redirect()->route('business.dashboard')
            ->with('success', 'Team member updated successfully.');
    }

    /**
     * Remove the specified team member.
     *
     * @param int $teamMemberId
     * @return \Illuminate\Http\Response
     */
    public function destroy($teamMemberId)
    {
        // Find the team member with eager loading of the business account relationship
        $teamMember = TeamMember::with('businessAccount')->find($teamMemberId);

        // Check if team member exists
        if (!$teamMember) {
            return redirect()->back()->with('error', 'Team member not found.');
        }

        // Check if business account relationship exists
        if (!$teamMember->businessAccount) {
            // Log the error for debugging purposes
            \Illuminate\Support\Facades\Log::error('Business account not found for team member ID: ' . $teamMemberId);

            // Get the business account ID directly from the team member if possible
            $businessAccountId = $teamMember->business_account_id;
            $teamMember->delete();

            if ($businessAccountId) {
                return redirect()->route('team.index', $businessAccountId)
                    ->with('success', 'Team member removed successfully.');
            } else {
                return redirect()->route('business.dashboard')
                    ->with('success', 'Team member removed successfully.');
            }
        }

        // Check if user owns this business account or is a team member with manage_team permission
        if (Auth::id() !== $teamMember->businessAccount->user_id) {
            $currentMember = TeamMember::where('user_id', Auth::id())
                ->where('business_account_id', $teamMember->business_account_id)
                ->where('status', 'active')
                ->first();

            if (!$currentMember || (!$currentMember->isAdmin() && !$currentMember->hasPermission('manage_team'))) {
                return redirect()->back()->with('error', 'You do not have permission to remove team members.');
            }
        }

        // Prevent removing yourself
        if ($teamMember->user_id === Auth::id()) {
            return redirect()->back()->with('error', 'You cannot remove yourself from the team.');
        }

        $userId = $teamMember->user_id;
        $businessAccountId = $teamMember->business_account_id;
        $businessId = $teamMember->business_id;

        // Update the status to 'removed' instead of hard deleting
        $teamMember->status = 'removed';
        $teamMember->save();

        // Also create an audit trail in the database for compliance
        $actionData = [
            'user_id' => Auth::id(),
            'action' => 'team_member_removed',
            'target_id' => $userId,
            'business_id' => $businessId,
            'business_account_id' => $businessAccountId,
            'timestamp' => now()->toDateTimeString(),
            'ip_address' => request()->ip()
        ];

        // Log the removal for audit purposes
        \Illuminate\Support\Facades\Log::info('Team member removed', $actionData);

        // If this was done through an API request, we can't terminate their session directly
        // Instead, we'll rely on our session validation in AuthController and BusinessController

        return redirect()->route('team.index', $businessAccountId)
            ->with('success', 'Team member removed successfully.');
    }


    public function teamRequest()
    {
        $teamMember = TeamMember::where('user_id', Auth::id())->where('status', 'pending')->with(['business', 'invitedBy', 'permissions'])->get();
        return view('team.teamrequest', compact('teamMember'));
    }

    public function editPermissions($teamMemberId)
    {
        $teamMember = TeamMember::findOrFail($teamMemberId)->with('user', 'permission')->first();
        return response()->json($teamMember);
    }

    public function updatePermissions($teamMemberId, Request $request)
    {
        $teamMember = Permission::where('team_member_id', $teamMemberId)->first();
        if ($teamMember) {
            $teamMember->update([
                'permission_name' => $request->newRole
            ]);
            return response()->json(['success' => true, 'data' => $teamMember], 200);
        }
        return response()->json(['error' => 'Team member not found'], 404);
    }

    public function removeMember($teamMemberId)
    {
        $teamMember = TeamMember::find($teamMemberId);

        if (!$teamMember) {
            return response()->json(['success' => false, 'message' => 'Team member not found.'], 404);
        }
        $teamMember->delete();
        return response()->json(['success' => true, 'message' => 'Team member removed successfully.'], 200);
    }

    /**
     * Get team member analytics for a business
     */
    public function getTeamAnalytics(Request $request, $businessId)
    {
        $user = Auth::user();
        $business = Business::findOrFail($businessId);

        // Check if user owns this business or is a team member with analytics permission
        if ($user->id !== $business->user_id) {
            $teamMember = TeamMember::where('user_id', $user->id)
                ->where('business_id', $business->id)
                ->where('status', 'active')
                ->first();

            if (!$teamMember || (!$teamMember->isAdmin() && !$teamMember->hasPermission('view_analytics'))) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        }

        try {
            // Get date range
            $dateRange = $request->input('date_range', '30_days');
            $startDate = $this->getStartDateFromRange($dateRange);
            $endDate = Carbon::now();

            // Get team members
            $teamMembers = TeamMember::where('business_id', $businessId)
                ->with('user')
                ->get();

            // Get team activities
            $teamActivities = \App\Models\BusinessActivityLog::forBusiness($businessId)
                ->inDateRange($startDate, $endDate)
                ->successful()
                ->with('performedByUser')
                ->get()
                ->groupBy('performed_by_user_id');

            // Calculate team statistics
            $teamStats = [];
            $totalActivities = 0;

            // Add owner statistics
            $ownerActivities = $teamActivities->get($business->user_id, collect());
            $ownerActivityCount = $ownerActivities->count();
            $totalActivities += $ownerActivityCount;

            $teamStats[] = [
                'user_id' => $business->user_id,
                'name' => $business->user->name,
                'email' => $business->user->email,
                'role' => 'owner',
                'status' => 'active',
                'activity_count' => $ownerActivityCount,
                'activity_breakdown' => $ownerActivities->groupBy('activity_type')->map->count()->toArray(),
                'last_activity' => $ownerActivities->max('created_at'),
                'is_owner' => true
            ];

            // Add team member statistics
            foreach ($teamMembers as $member) {
                if ($member->user) {
                    $memberActivities = $teamActivities->get($member->user_id, collect());
                    $memberActivityCount = $memberActivities->count();
                    $totalActivities += $memberActivityCount;

                    $teamStats[] = [
                        'user_id' => $member->user_id,
                        'name' => $member->user->name,
                        'email' => $member->user->email,
                        'role' => $member->role,
                        'status' => $member->status,
                        'activity_count' => $memberActivityCount,
                        'activity_breakdown' => $memberActivities->groupBy('activity_type')->map->count()->toArray(),
                        'last_activity' => $memberActivities->max('created_at'),
                        'is_owner' => false,
                        'invited_at' => $member->created_at
                    ];
                }
            }

            // Sort by activity count
            usort($teamStats, function ($a, $b) {
                return $b['activity_count'] <=> $a['activity_count'];
            });

            // Calculate performance metrics
            $activeMembers = collect($teamStats)->where('status', 'active')->count();
            $avgActivitiesPerMember = $activeMembers > 0 ? $totalActivities / $activeMembers : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'team_members' => $teamStats,
                    'summary' => [
                        'total_members' => count($teamStats),
                        'active_members' => $activeMembers,
                        'total_activities' => $totalActivities,
                        'avg_activities_per_member' => round($avgActivitiesPerMember, 1)
                    ],
                    'date_range' => $dateRange,
                    'period' => [
                        'start' => $startDate->format('Y-m-d'),
                        'end' => $endDate->format('Y-m-d')
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get team analytics',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get team member validation status
     */
    public function getTeamValidation(Request $request, $businessId)
    {
        $user = Auth::user();
        $business = Business::findOrFail($businessId);

        // Check if user owns this business
        if ($user->id !== $business->user_id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $subscription = \App\Models\Subscription::where('user_id', $user->id)
                ->where('status', 'ACTIVE')
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => true,
                    'validation' => [
                        'can_invite' => false,
                        'current_count' => 0,
                        'limit' => 0,
                        'remaining' => 0,
                        'percentage' => 0,
                        'message' => 'No active subscription found.',
                        'upgrade_required' => true
                    ]
                ]);
            }

            // Get current team member count for this business
            $currentCount = TeamMember::where('business_id', $businessId)
                ->where('status', 'active')
                ->count() + 1; // +1 for owner

            $limit = $subscription->plan->getTeamMembersLimit();
            $remaining = max(0, $limit - $currentCount);
            $percentage = $limit > 0 ? ($currentCount / $limit) * 100 : 0;
            $canInvite = $currentCount < $limit;

            $message = '';
            $upgradeRequired = false;

            if (!$canInvite) {
                $message = "Team member limit of {$limit} reached. Upgrade to invite more members.";
                $upgradeRequired = true;
            } elseif ($percentage >= 80) {
                $message = "Approaching team member limit. {$remaining} slot(s) remaining.";
            } else {
                $message = "You can invite {$remaining} more team member(s).";
            }

            return response()->json([
                'success' => true,
                'validation' => [
                    'can_invite' => $canInvite,
                    'current_count' => $currentCount,
                    'limit' => $limit,
                    'remaining' => $remaining,
                    'percentage' => round($percentage, 1),
                    'message' => $message,
                    'upgrade_required' => $upgradeRequired,
                    'plan_name' => $subscription->plan->name
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get team validation',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get start date from range string
     */
    private function getStartDateFromRange(string $range): \Carbon\Carbon
    {
        return match ($range) {
            '7_days' => \Carbon\Carbon::now()->subDays(7),
            '30_days' => \Carbon\Carbon::now()->subDays(30),
            '90_days' => \Carbon\Carbon::now()->subDays(90),
            '1_year' => \Carbon\Carbon::now()->subYear(),
            'all_time' => \Carbon\Carbon::now()->subYears(10),
            default => \Carbon\Carbon::now()->subDays(30)
        };
    }
}
