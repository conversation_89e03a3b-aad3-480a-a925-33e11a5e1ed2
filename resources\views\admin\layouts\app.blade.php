<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel') - ReviewBiz Admin</title>
    <link rel="shortcut icon" type="image/x-icon" href="{{ url('rm-icon.ico') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @vite(['resources/assets/css/styles.css'])
    @stack('styles')
</head>
<body class="font-poppins bg-gray-100 text-gray-900">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
            <div class="flex items-center justify-center h-16 bg-gray-800">
                <img src="{{ url('logo.png') }}" alt="ReviewBiz Admin" class="h-8">
            </div>
            
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <!-- Dashboard -->
                    <a href="{{ route('admin.dashboard') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.dashboard') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    @if(auth('admin')->user()->hasPermission('users.view'))
                    <!-- Users -->
                    <a href="{{ route('admin.users.index') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.users.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-users mr-3"></i>
                        Users
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('subscriptions.view'))
                    <!-- Subscriptions -->
                    <a href="{{ route('admin.subscriptions.index') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.subscriptions.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-credit-card mr-3"></i>
                        Subscriptions
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('plans.view'))
                    <!-- Plans -->
                    <a href="{{ route('admin.plans.index') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.plans.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-layer-group mr-3"></i>
                        Plans
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('coupons.view'))
                    <!-- Coupons -->
                    <a href="{{ route('admin.coupons.index') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.coupons.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-tags mr-3"></i>
                        Coupons
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('payments.view'))
                    <!-- Payments -->
                    <a href="{{ route('admin.payments.index') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.payments.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-money-bill-wave mr-3"></i>
                        Payments
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('analytics.view'))
                    <!-- Analytics -->
                    <a href="{{ route('admin.analytics') }}" 
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.analytics') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Analytics
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('admins.view'))
                    <!-- Admin Management -->
                    <a href="{{ route('admin.admins.index') }}"
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.admins.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-user-shield mr-3"></i>
                        Admins
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('logs.view'))
                    <!-- Logs Viewer -->
                    <a href="{{ route('admin.logs.index') }}"
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.logs.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-list-alt mr-3"></i>
                        API Logs
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('email_templates.view'))
                    <!-- Email Templates -->
                    <a href="{{ route('admin.email-templates.index') }}"
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.email-templates.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-envelope-open-text mr-3"></i>
                        Email Templates
                    </a>
                    @endif

                    @if(auth('admin')->user()->hasPermission('site_settings.view'))
                    <!-- Site Settings -->
                    <a href="{{ route('admin.site-settings.index') }}"
                       class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.site-settings.*') ? 'bg-gray-700 text-white' : '' }}">
                        <i class="fas fa-cogs mr-3"></i>
                        Site Settings
                    </a>
                    @endif
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button id="sidebar-toggle" class="text-gray-500 hover:text-gray-600 lg:hidden">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="ml-4 text-xl font-semibold text-gray-800 lg:ml-0">
                            @yield('page-title', 'Dashboard')
                        </h1>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Admin Profile Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-medium">
                                            {{ substr(auth('admin')->user()->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <span class="ml-2 text-gray-700 font-medium">{{ auth('admin')->user()->name }}</span>
                                    <i class="fas fa-chevron-down ml-2 text-gray-400"></i>
                                </div>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="{{ route('admin.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <form method="POST" action="{{ route('admin.logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <div class="container mx-auto px-6 py-8">
                    @if(session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden"></div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });

        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });
    </script>
    @stack('scripts')
</body>
</html>
