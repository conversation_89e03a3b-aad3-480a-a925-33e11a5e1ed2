{"__meta": {"id": "01K0PEDEYPTHAJNV0S7R4TPGHB", "datetime": "2025-07-21 12:12:55", "utime": **********.639089, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.207708, "end": **********.639127, "duration": 2.4314191341400146, "duration_str": "2.43s", "measures": [{"label": "Booting", "start": **********.207708, "relative_start": 0, "end": **********.738508, "relative_end": **********.738508, "duration": 0.****************, "duration_str": "531ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.738522, "relative_start": 0.****************, "end": **********.639141, "relative_end": 1.4066696166992188e-05, "duration": 1.****************, "duration_str": "1.9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.772779, "relative_start": 0.****************, "end": **********.777473, "relative_end": **********.777473, "duration": 0.0046939849853515625, "duration_str": "4.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.309087, "relative_start": 1.***************, "end": **********.636009, "relative_end": **********.636009, "duration": 1.****************, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.dashboard.index", "start": **********.312334, "relative_start": 1.***************, "end": **********.312334, "relative_end": **********.312334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin.layouts.app", "start": **********.993062, "relative_start": 1.****************, "end": **********.993062, "relative_end": **********.993062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 40725888, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "admin.dashboard.index", "param_count": null, "params": [], "start": **********.312256, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/admin/dashboard/index.blade.phpadmin.dashboard.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fadmin%2Fdashboard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin.layouts.app", "param_count": null, "params": [], "start": **********.992964, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 46, "nb_statements": 46, "nb_visible_statements": 46, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06368000000000001, "accumulated_duration_str": "63.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt' limit 1", "type": "query", "params": [], "bindings": ["R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.805353, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "review_master", "explain": null, "start_percent": 0, "width_percent": 2.057}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 14}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.827549, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 2.057, "width_percent": 1.617}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` = 1 and `business_accounts`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.8386738, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "CheckGoogleTokenExpiration.php:18", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FMiddleware%2FCheckGoogleTokenExpiration.php&line=18", "ajax": false, "filename": "CheckGoogleTokenExpiration.php", "line": "18"}, "connection": "review_master", "explain": null, "start_percent": 3.675, "width_percent": 1.445}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************', '[]', '{\\\"azp\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"aud\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"sub\\\":\\\"110486499747300774507\\\",\\\"scope\\\":\\\"https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/business.manage https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.email https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.profile openid\\\",\\\"exp\\\":\\\"1753101336\\\",\\\"expires_in\\\":\\\"1357\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_verified\\\":\\\"true\\\",\\\"access_type\\\":\\\"offline\\\"}', 200, 'success', 144, null, 1, '2025-07-21 12:12:53', '2025-07-21 12:12:53')", "type": "query", "params": [], "bindings": ["google_api", "https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************", "[]", "{\"azp\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"aud\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"sub\":\"110486499747300774507\",\"scope\":\"https:\\/\\/www.googleapis.com\\/auth\\/business.manage https:\\/\\/www.googleapis.com\\/auth\\/userinfo.email https:\\/\\/www.googleapis.com\\/auth\\/userinfo.profile openid\",\"exp\":\"1753101336\",\"expires_in\":\"1357\",\"email\":\"<EMAIL>\",\"email_verified\":\"true\",\"access_type\":\"offline\"}", 200, "success", 144, null, 1, "2025-07-21 12:12:53", "2025-07-21 12:12:53"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 99}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 236}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 24}], "start": **********.992896, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 5.119, "width_percent": 3.753}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin.auth", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\AdminAuth.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.018546, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 8.872, "width_percent": 3.533}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `subscriptions` where `users`.`id` = `subscriptions`.`user_id` and `status` = 'ACTIVE' and `expiry_date` >= '2025-07-21 12:12:54')", "type": "query", "params": [], "bindings": ["ACTIVE", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 82}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.031602, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=82", "ajax": false, "filename": "DashboardController.php", "line": "82"}, "connection": "review_master", "explain": null, "start_percent": 12.406, "width_percent": 2.481}, {"sql": "select count(*) as aggregate from `subscriptions` where `status` = 'ACTIVE' and `expiry_date` >= '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["ACTIVE", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 90}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.040529, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:90", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=90", "ajax": false, "filename": "DashboardController.php", "line": "90"}, "connection": "review_master", "explain": null, "start_percent": 14.887, "width_percent": 1.382}, {"sql": "select count(*) as aggregate from `subscriptions` where `status` = 'ACTIVE' and `expiry_date` between '2025-07-21 12:12:54' and '2025-08-20 12:12:54'", "type": "query", "params": [], "bindings": ["ACTIVE", "2025-07-21 12:12:54", "2025-08-20 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 98}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.046931, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:98", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=98", "ajax": false, "filename": "DashboardController.php", "line": "98"}, "connection": "review_master", "explain": null, "start_percent": 16.269, "width_percent": 1.351}, {"sql": "select count(*) as aggregate from `users` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 102}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.052853, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:102", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 102}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=102", "ajax": false, "filename": "DashboardController.php", "line": "102"}, "connection": "review_master", "explain": null, "start_percent": 17.619, "width_percent": 1.523}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payment_status` = 'completed' and `payment_date` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["completed", "2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 113}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0605018, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:113", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=113", "ajax": false, "filename": "DashboardController.php", "line": "113"}, "connection": "review_master", "explain": null, "start_percent": 19.143, "width_percent": 8.087}, {"sql": "select count(*) as aggregate from `coupon_usage_logs` where `status` = 'used' and `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["used", "2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.07338, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "review_master", "explain": null, "start_percent": 27.23, "width_percent": 4.68}, {"sql": "select count(*) as aggregate from `businesses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.082078, "duration": 0.01122, "duration_str": "11.22ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:121", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=121", "ajax": false, "filename": "DashboardController.php", "line": "121"}, "connection": "review_master", "explain": null, "start_percent": 31.91, "width_percent": 17.619}, {"sql": "select count(*) as aggregate from `team_members` where `status` = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.098475, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:124", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=124", "ajax": false, "filename": "DashboardController.php", "line": "124"}, "connection": "review_master", "explain": null, "start_percent": 49.529, "width_percent": 2.67}, {"sql": "select count(*) as aggregate from `users` where `created_at` between '2025-05-22 12:12:54' and '2025-06-20 12:12:54'", "type": "query", "params": [], "bindings": ["2025-05-22 12:12:54", "2025-06-20 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 131}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.106984, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:131", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=131", "ajax": false, "filename": "DashboardController.php", "line": "131"}, "connection": "review_master", "explain": null, "start_percent": 52.198, "width_percent": 1.225}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payment_status` = 'completed' and `payment_date` between '2025-05-22 12:12:54' and '2025-06-20 12:12:54'", "type": "query", "params": [], "bindings": ["completed", "2025-05-22 12:12:54", "2025-06-20 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 141}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.113136, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:141", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 141}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=141", "ajax": false, "filename": "DashboardController.php", "line": "141"}, "connection": "review_master", "explain": null, "start_percent": 53.423, "width_percent": 1.335}, {"sql": "select DATE(payment_date) as date, SUM(amount) as revenue, currency from `payments` where `payment_status` = 'SUCCESS' and `payment_date` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54' group by `date`, `currency` order by `date` asc", "type": "query", "params": [], "bindings": ["SUCCESS", "2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 187}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.119755, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:187", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 187}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=187", "ajax": false, "filename": "DashboardController.php", "line": "187"}, "connection": "review_master", "explain": null, "start_percent": 54.758, "width_percent": 2.293}, {"sql": "select DATE(created_at) as date, COUNT(*) as signups from `users` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 194}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.126365, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:194", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 194}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=194", "ajax": false, "filename": "DashboardController.php", "line": "194"}, "connection": "review_master", "explain": null, "start_percent": 57.051, "width_percent": 1.084}, {"sql": "select plans.name, COUNT(*) as count, plans.currency from `subscriptions` inner join `plans` on `subscriptions`.`plan_id` = `plans`.`id` where `status` = 'ACTIVE' and `expiry_date` >= '2025-07-21 12:12:54' group by `plans`.`name`, `plans`.`currency`", "type": "query", "params": [], "bindings": ["ACTIVE", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 206}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.131026, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:206", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=206", "ajax": false, "filename": "DashboardController.php", "line": "206"}, "connection": "review_master", "explain": null, "start_percent": 58.134, "width_percent": 3.251}, {"sql": "select status, COUNT(*) as count from `subscriptions` group by `status`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 211}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.1377609, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:211", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=211", "ajax": false, "filename": "DashboardController.php", "line": "211"}, "connection": "review_master", "explain": null, "start_percent": 61.385, "width_percent": 0.958}, {"sql": "select YEAR(payment_date) as year, MONTH(payment_date) as month, SUM(amount) as revenue from `payments` where `payment_status` = 'completed' and `payment_date` >= '2024-07-21 12:12:54' group by `year`, `month` order by `year` asc, `month` asc", "type": "query", "params": [], "bindings": ["completed", "2024-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 224}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.1422708, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:224", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 224}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=224", "ajax": false, "filename": "DashboardController.php", "line": "224"}, "connection": "review_master", "explain": null, "start_percent": 62.343, "width_percent": 0.958}, {"sql": "select YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as users from `users` where `created_at` >= '2024-07-21 12:12:54' group by `year`, `month` order by `year` asc, `month` asc", "type": "query", "params": [], "bindings": ["2024-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.1464689, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:232", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=232", "ajax": false, "filename": "DashboardController.php", "line": "232"}, "connection": "review_master", "explain": null, "start_percent": 63.301, "width_percent": 0.911}, {"sql": "select * from `users` order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.15103, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:252", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=252", "ajax": false, "filename": "DashboardController.php", "line": "252"}, "connection": "review_master", "explain": null, "start_percent": 64.212, "width_percent": 0.989}, {"sql": "select * from `subscriptions` where `subscriptions`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 252}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.156685, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:252", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=252", "ajax": false, "filename": "DashboardController.php", "line": "252"}, "connection": "review_master", "explain": null, "start_percent": 65.201, "width_percent": 1.084}, {"sql": "select * from `plans` where `plans`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 252}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 41}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.190866, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:252", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=252", "ajax": false, "filename": "DashboardController.php", "line": "252"}, "connection": "review_master", "explain": null, "start_percent": 66.285, "width_percent": 1.256}, {"sql": "select * from `payments` where `payment_status` = 'SUCCESS' order by `payment_date` desc limit 5", "type": "query", "params": [], "bindings": ["SUCCESS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 264}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.1950989, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:264", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 264}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=264", "ajax": false, "filename": "DashboardController.php", "line": "264"}, "connection": "review_master", "explain": null, "start_percent": 67.541, "width_percent": 2.261}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 264}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.2005482, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:264", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 264}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=264", "ajax": false, "filename": "DashboardController.php", "line": "264"}, "connection": "review_master", "explain": null, "start_percent": 69.802, "width_percent": 1.005}, {"sql": "select * from `plans` where `plans`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 264}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.204963, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:264", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 264}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=264", "ajax": false, "filename": "DashboardController.php", "line": "264"}, "connection": "review_master", "explain": null, "start_percent": 70.807, "width_percent": 1.005}, {"sql": "select * from `subscriptions` where `status` = 'ACTIVE' and `expiry_date` between '2025-07-21 12:12:54' and '2025-08-20 12:12:54' order by `expiry_date` asc limit 5", "type": "query", "params": [], "bindings": ["ACTIVE", "2025-07-21 12:12:54", "2025-08-20 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 277}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.209002, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:277", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=277", "ajax": false, "filename": "DashboardController.php", "line": "277"}, "connection": "review_master", "explain": null, "start_percent": 71.812, "width_percent": 1.162}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 277}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.21332, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:277", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=277", "ajax": false, "filename": "DashboardController.php", "line": "277"}, "connection": "review_master", "explain": null, "start_percent": 72.974, "width_percent": 1.052}, {"sql": "select * from `plans` where `plans`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 277}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.217631, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:277", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=277", "ajax": false, "filename": "DashboardController.php", "line": "277"}, "connection": "review_master", "explain": null, "start_percent": 74.026, "width_percent": 0.989}, {"sql": "select count(*) as aggregate from `businesses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 285}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.222324, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:285", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 285}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=285", "ajax": false, "filename": "DashboardController.php", "line": "285"}, "connection": "review_master", "explain": null, "start_percent": 75.016, "width_percent": 1.994}, {"sql": "select count(*) as aggregate from `businesses` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 286}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.226661, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:286", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 286}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=286", "ajax": false, "filename": "DashboardController.php", "line": "286"}, "connection": "review_master", "explain": null, "start_percent": 77.01, "width_percent": 1.162}, {"sql": "select count(*) as aggregate from `businesses` where exists (select * from `users` where `businesses`.`user_id` = `users`.`id` and exists (select * from `subscriptions` where `users`.`id` = `subscriptions`.`user_id` and `status` = 'ACTIVE' and `expiry_date` >= '2025-07-21 12:12:54'))", "type": "query", "params": [], "bindings": ["ACTIVE", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 289}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.231184, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:289", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 289}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=289", "ajax": false, "filename": "DashboardController.php", "line": "289"}, "connection": "review_master", "explain": null, "start_percent": 78.172, "width_percent": 1.539}, {"sql": "select count(*) as aggregate from `businesses` where exists (select * from `team_members` where `businesses`.`id` = `team_members`.`business_id`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 291}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.237542, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:291", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 291}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=291", "ajax": false, "filename": "DashboardController.php", "line": "291"}, "connection": "review_master", "explain": null, "start_percent": 79.711, "width_percent": 1.068}, {"sql": "select `businesses`.*, (select count(*) from `team_members` where `businesses`.`id` = `team_members`.`business_id`) as `team_members_count` from `businesses`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 292}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.241861, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:292", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 292}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=292", "ajax": false, "filename": "DashboardController.php", "line": "292"}, "connection": "review_master", "explain": null, "start_percent": 80.779, "width_percent": 1.084}, {"sql": "select count(*) as aggregate from `team_members`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 308}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.2463892, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:308", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 308}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=308", "ajax": false, "filename": "DashboardController.php", "line": "308"}, "connection": "review_master", "explain": null, "start_percent": 81.862, "width_percent": 0.769}, {"sql": "select count(*) as aggregate from `team_members` where `status` = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 309}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.251652, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:309", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 309}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=309", "ajax": false, "filename": "DashboardController.php", "line": "309"}, "connection": "review_master", "explain": null, "start_percent": 82.632, "width_percent": 1.225}, {"sql": "select count(*) as aggregate from `team_members` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 310}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.256455, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:310", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 310}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=310", "ajax": false, "filename": "DashboardController.php", "line": "310"}, "connection": "review_master", "explain": null, "start_percent": 83.857, "width_percent": 0.832}, {"sql": "select count(*) as aggregate from `team_members` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 311}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.26034, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:311", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 311}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=311", "ajax": false, "filename": "DashboardController.php", "line": "311"}, "connection": "review_master", "explain": null, "start_percent": 84.689, "width_percent": 0.864}, {"sql": "select role, COUNT(*) as count from `team_members` where `status` = 'active' group by `role`", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 316}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.264379, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:316", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 316}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=316", "ajax": false, "filename": "DashboardController.php", "line": "316"}, "connection": "review_master", "explain": null, "start_percent": 85.553, "width_percent": 0.848}, {"sql": "select count(*) as aggregate from `business_activity_logs` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54'", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 332}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.270302, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:332", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 332}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=332", "ajax": false, "filename": "DashboardController.php", "line": "332"}, "connection": "review_master", "explain": null, "start_percent": 86.401, "width_percent": 7.506}, {"sql": "select activity_category, COUNT(*) as count from `business_activity_logs` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54' group by `activity_category`", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 337}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.278398, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:337", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=337", "ajax": false, "filename": "DashboardController.php", "line": "337"}, "connection": "review_master", "explain": null, "start_percent": 93.907, "width_percent": 1.727}, {"sql": "select performed_by_type, COUNT(*) as count from `business_activity_logs` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54' group by `performed_by_type`", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 342}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.283071, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:342", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 342}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=342", "ajax": false, "filename": "DashboardController.php", "line": "342"}, "connection": "review_master", "explain": null, "start_percent": 95.634, "width_percent": 1.162}, {"sql": "select DATE(created_at) as date, COUNT(*) as activities from `business_activity_logs` where `created_at` between '2025-06-21 12:12:54' and '2025-07-21 12:12:54' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["2025-06-21 12:12:54", "2025-07-21 12:12:54"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 348}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.288333, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:348", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 348}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=348", "ajax": false, "filename": "DashboardController.php", "line": "348"}, "connection": "review_master", "explain": null, "start_percent": 96.796, "width_percent": 1.382}, {"sql": "select distinct `currency` from `payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 363}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.292721, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:363", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 363}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=363", "ajax": false, "filename": "DashboardController.php", "line": "363"}, "connection": "review_master", "explain": null, "start_percent": 98.178, "width_percent": 0.989}, {"sql": "select `id`, `name`, `currency` from `plans`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 364}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.296736, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:364", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 364}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=364", "ajax": false, "filename": "DashboardController.php", "line": "364"}, "connection": "review_master", "explain": null, "start_percent": 99.168, "width_percent": 0.832}]}, "models": {"data": {"App\\Models\\Plan": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\User": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "App\\Models\\Payment": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\BusinessAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusinessAccount.php&line=1", "ajax": false, "filename": "BusinessAccount.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Business": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusiness.php&line=1", "ajax": false, "filename": "Business.php", "line": "?"}}}, "count": 26, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "admin.dashboard", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@index", "uri": "GET admin/dashboard", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=24\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=24\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/DashboardController.php:24-67</a>", "middleware": "web, admin.auth", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f718a21-69ac-4303-95eb-81b585778e0b\" target=\"_blank\">View in Telescope</a>", "duration": "2.45s", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1961218396 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1961218396\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1322235166 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1322235166\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-709953926 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1694 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im1ySEFHV2hQVDlGcjRqUSs4bHlUclE9PSIsInZhbHVlIjoibDlUcW14ZXYzYU04Sk5hNkhNc3FsTjNvbmVvdzI3ZndKb1RmQzl5dHhjUnB2N1N2ek5PZDhVNUtsVmFSR3NsS3VPcTAwYlhIRlc1YkhIVlllUUE3K1VlSEZEWGd1RG9oU2c2Q0xpcS9kZE5ScHpaZlpaM3FMVjI0dlFKQWRWTTMzdEliSHhVaXB2L0p2OTNLWVNodEpRN1hiNmowQ0RQNkloTExmdlAvM1poS051Qm1oTVAvSDJpcFI2LzJ6eWMxWEpVQU1uTlgyVFJ4eWw4OWpROURaMkx3MmN2TE5id0RRZi9oYnJOZ2hOZz0iLCJtYWMiOiIzNGFiOWI5ZjdiYzFjZWJmZjZlMDI2NzFjY2FhYjhlZDIzOTQyNjNjZjAzNWU0YjBlY2VkYjVhZDkxMmFjOTMxIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InhsS1ErNFB2VC9UOVE1R1ovemFtVmc9PSIsInZhbHVlIjoieStNUXZDUDNZU3RqRHdvKzFUT25jSHg4dUVCSjFwUmllU25qNXFiV0F1OW84OTBCNU9CdTE2MTVMNUFvVHB2TVlTS2NSQjJMTkduZE0wU3I0cTJ4Tmp0UisxU1VqNlRqR0F2cnJEM3ZNZGQzSTFUMEtaUWFqUDNMQkNqTkE5MWpoRUN1YkhGOEpDWmthUTJ4RWlVeUl3PT0iLCJtYWMiOiIzZTI3OTliODc5NGUyZTBhMzcwYzdmZjA1MjM1MDVjY2RhNjcyNzMyZTk2NjRjYTczYjE4YmZhMGMwNTQ3YzNlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRlK0huSHZaSWYzUkhsMkl1SlhxVHc9PSIsInZhbHVlIjoiWVQrWmFLS0xPNnRtdGtidW5ibmQ4QmNMRlJVRzRyZ3BWODZVeWZSNlNOQ1ovWGc5U3dBaittMDNnbFhOMUxzQ3hPV1V1T3U5UVVPK3ZMLyttZlgvak1pY0hSR1NGR0taVWhzVlpScWYyNnI2TkkyZmh2Uk5kbXBRSFFJMDMrTFQiLCJtYWMiOiIzZTNkODQ1M2I1N2Q2YTg4Njg1ZjUxMmFkNDNhODgzMGNhOTEzMmVjMWIwNDU4ZGVmZDE3NjRkNTBhOTgxMTFhIiwidGFnIjoiIn0%3D; reviewmasterai_session=eyJpdiI6ImFWN3VLR3ZxRmk1R1h6T0JJeTJSWFE9PSIsInZhbHVlIjoiRTBkL3hscXA3WFJTd3R0OEZJWWZUZVUwUEdwTHRWQTh4ckZRWno1a2ppbDNYRm8xbUpsbWhPS2ljTy9GN3d5ZVNhSnZraWpXUDZlVktEMUdORXNWbGJmWEcyL2hjZmpsQ3ZoNzEycVE4ME5XdHBJSkZ3U04zQS9QbFdSN204ZFEiLCJtYWMiOiJiZDFkMDJjZWVhM2FlOTYwNTdjY2I5ZGMzYzEzNmRmYzZmZjMwYTQwYWE2ODcyZjA4YTk3NmE1YzdiMmQ3MmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709953926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|pmk0ZPcwNvdcyZh8txRDWRjQhRBItD6bQKyiIIwPOGZVYyePFjEhKDNxjvoa|$2y$12$quO4cLolCFDH2wXsSke/u.BarK4ZwLV6ACtlaLyMBFl5.eec7Yrdi</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|d4gsFumgVybRUrjg0Rh5kBgW54R5vfpkeMJcrWrT7FkGNX8F4D9Tf62Ue7fx|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  \"<span class=sf-dump-key>reviewmasterai_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R2pv35MfsJpmO2Bq0LMqLWgyb0oh6ieDXvOrFHEt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1936846070 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 12:12:54 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936846070\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1023662338 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HsXbHw4OqhI2FwcJatkUbGKgjhqDaqnaGmkGQEX7</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/business-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>business_google_token</span>\" => \"<span class=sf-dump-str title=\"224 characters\">********************************************************************************************************************************************************************************************************************************</span>\"\n  \"<span class=sf-dump-key>business_google_refresh_token</span>\" => \"<span class=sf-dump-str title=\"103 characters\">1//0gTNtsu4DcUdMCgYIARAAGBASNwF-L9IrdXV9dXsoYipZErrwYsSEb4XPiKiODRngXtCKIqm0BDgTYJem9CDOhq2TaCAHNMGEzQM</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K0PEDCGQA73QTGNH4TTDWCSA</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Welcome back, Super Admin!</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023662338\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "admin.dashboard", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@index"}, "badge": null}}