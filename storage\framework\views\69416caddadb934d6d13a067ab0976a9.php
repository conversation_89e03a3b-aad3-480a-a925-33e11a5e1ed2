<?php
$user = $user ?? Auth::user();
$lastSegment = collect(request()->segments())->last();
?>

<?php echo $__env->make('components.upgrade-subscription-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<header class="bg-[#0F1D36] text-white p-4 py-2 md:py-4 flex md:justify-between items-center sticky top-0 z-20">
    <div class="max-w-[1200px] mx-auto w-full">
        <div class="flex md:justify-between items-center">
            <div class="flex items-center gap-2 justify-center w-full md:w-auto relative">
                <!-- <button id="menubarToggle" class="text-white focus:outline-none block md:hidden">
                    <i class="fas fa-bars text-xl"></i>
                </button> -->
                <div class="hamburger block md:hidden absolute left-1" id="menubarToggle">
                    <span class="line"></span>
                    <span class="line"></span>
                    <span class="line"></span>
                </div>
                <a href="<?php echo e(route('business.dashboard')); ?>">
                    <img src="<?php echo e(url('logo.png')); ?>" alt="ReviewMaster AI Logo" width="285" height="43" class="desktop-logo w-[200px] h-auto md:w-[285px] md:h-[43px]">
                </a>
                <!-- <button id="mobileSidebarToggle" class="text-white focus:outline-none block md:hidden">
                    <i class="fas fa-filter text-xl"></i>
                </button> -->
                <!-- <img src="<?php echo e(url('logo.png')); ?>" alt="ReviewMaster AI Logo" width="285" height="43" class="mr-2 desktop-logo hidden md:block">
                <img src="<?php echo e(url('logo1.png')); ?>" alt="ReviewMaster AI Logo" width="60" height="32" class="mr-2 mobile-logo block md:hidden"> -->
                <a href="https://reviewmaster.biz/getting-started.php" target="_blank" id="gettingStarted" title="Getting Started" class="w-[24px] h-[24px] flex md:hidden text-xs absolute right-1 items-center justify-center gap-2 shadow-lg p-2 z-50 bg-indigo-600 text-white ring-indigo-600 rounded-md">
                    <i class="fa-solid fa-life-ring"></i>
                </a>
            </div>
            <div class="items-center gap-2 md:gap-4 hidden md:flex">
                <?php if($lastSegment == 'business-dashboard' && $connectedBusinesses->count() > 0): ?>
                <div class="flex gap-2 md:gap-4 items-center">
                    <input type="hidden" id="business_contact" value="<?php echo e($business['primary_phone']); ?>">
                    <input type="hidden" id="business_name" value="<?php echo e($business['title']); ?>">
                    <select class="form-control border text-xs sm:text-sm w-42 border-gray-500 rounded-md px-2 md:px-3 py-1 md:py-2 focus:outline-0 truncate ... getSelectBusiness" id="selectBusiness" name="business_id" required data-dashboard-url="<?php echo e(route('business.dashboard')); ?>">
                        <?php $__currentLoopData = $connectedBusinesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $connectedBusiness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($connectedBusiness->id); ?>" <?php echo e($connectedBusiness->id == $business['id'] ? 'selected' : ''); ?>><?php echo e($connectedBusiness->title); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
        
                    <?php if(!$teamMembers): ?>
                    <button type="button"
                        class="inline-flex justify-center items-center gap-1 rounded-md border border-transparent bg-indigo-600 py-1.5 lg:py-1 px-2 lg:px-4 leading-loose text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" id="connectBusiness" title="Connect Business">
                        <i class="fa-solid fa-link block"></i>
                        <span class="hidden md:block">Connect Business</span>
                    </button>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <div class="flex-shrink-0">
                    <?php if($lastSegment == 'business-dashboard'): ?>
                    <span class="text-sm hidden md:inline"> <?php echo e($business['name']); ?></span>
                    <?php endif; ?>
                    <div class="relative">
                        <div id="userProfileContainer" class="user-profile flex items-center cursor-pointer relative ">
                            <img src="<?php echo e($user['image']); ?>" alt="User" class="w-6 sm:w-8 h-6 sm:h-8 rounded-full">
                            <span class="ml-2 text-sm hidden lg:inline"><?php echo e($user['name']); ?></span>
                            <i class="fas fa-chevron-down ml-1 text-xs hidden lg:inline"></i>
            
                            <!-- Hidden Inputs -->
                            <input type="hidden" id="user_name" value="<?php echo e($user['name']); ?>">
                            <input type="hidden" id="user_email" value="<?php echo e($user['email']); ?>">
                            <input type="hidden" id="user_avatar" value="<?php echo e($user['image']); ?>">
                            <input type="hidden" id="user_id" value="<?php echo e($user['id']); ?>">
                            <input type="hidden" id="user_google_id" value="<?php echo e($user['google_id']); ?>">
                        </div>
                        <!-- User Menu Dropdown -->
                        <div id="userMenu" class="absolute right-0 container mx-auto bg-white top-10 w-48 bg-white rounded-md shadow-lg py-1 z-30 hidden">
                            <?php if(in_array($lastSegment, ['business-dashboard', 'teamRequest', 'activity-dashboard', 'details']) && $connectedBusinesses->count() > 0 || Request::is('team/*')): ?>
                
                            <?php if(isset($teamMembers) && $teamMembers->count() > 0): ?>
                
                            <?php else: ?>
                            <a href="<?php echo e(route('team.index', [$locationId])); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-2"></i> Account
                            </a>
                
                            <a href="<?php echo e(route('activity.dashboard')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-chart-line mr-3"></i> Activities
                            </a>
                                        
                
                            <a href="<?php echo e(route('billing.details')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-credit-card mr-2"></i> Subscription
                            </a>
                            

                            
                            
                            <a href="#" id="editProfileBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-edit mr-2"></i> Edit Profile
                            </a>
                            
                            <?php endif; ?>
                            <?php endif; ?>
                            <div class="border-t border-gray-100"></div>
                            <a href="<?php echo e(route('logout')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- <div class="hamburger block lg:hidden" id="menubarToggle">
        <span class="line"></span>
        <span class="line"></span>
        <span class="line"></span>
    </div> -->
</header><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partials/header-nav.blade.php ENDPATH**/ ?>