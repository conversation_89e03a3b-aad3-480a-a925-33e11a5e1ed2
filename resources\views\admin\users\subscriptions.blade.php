@extends('admin.layouts.app')

@section('title', 'User Subscriptions - ' . ($user->name ?? $user->email))
@section('page-title', 'User Subscriptions')

@section('content')
<div class="space-y-6">
    <!-- User Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($user->image)
                            <img class="h-12 w-12 rounded-full" src="{{ $user->image }}" alt="{{ $user->name }}">
                        @else
                            <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-lg font-medium text-gray-700">
                                    {{ substr($user->name ?? $user->email, 0, 1) }}
                                </span>
                            </div>
                        @endif
                    </div>
                    <div class="ml-4">
                        <h1 class="text-xl font-bold text-gray-900">{{ $user->name ?? 'No Name' }}</h1>
                        <p class="text-sm text-gray-500">{{ $user->email }}</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.users.show', $user) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscriptions List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Subscription History</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">All subscriptions for this user</p>
        </div>
        
        @if($subscriptions->count() > 0)
            <ul class="divide-y divide-gray-200">
                @foreach($subscriptions as $subscription)
                    <li class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center min-w-0 flex-1">
                                <div class="min-w-0 flex-1">
                                    <div class="flex items-center">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $subscription->plan->name ?? 'Unknown Plan' }}
                                        </p>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            {{ $subscription->status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 
                                               ($subscription->status === 'EXPIRED' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800') }}">
                                            {{ $subscription->status }}
                                        </span>
                                        @if($subscription->status === 'ACTIVE' && $subscription->expiry_date < now()->addDays(7))
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                                Expiring Soon
                                            </span>
                                        @endif
                                    </div>
                                    <div class="flex items-center mt-1 space-x-4">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            {{ $subscription->start_date->format('M d, Y') }} - {{ $subscription->expiry_date->format('M d, Y') }}
                                        </div>
                                        @if($subscription->payment)
                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-credit-card mr-1"></i>
                                                {{ $subscription->payment->currency }} {{ number_format($subscription->payment->amount, 2) }}
                                            </div>
                                        @endif
                                        @if($subscription->business)
                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-building mr-1"></i>
                                                {{ $subscription->business->title }}
                                            </div>
                                        @endif
                                    </div>
                                    @if($subscription->features)
                                        <div class="mt-2">
                                            <div class="flex flex-wrap gap-2">
                                                @foreach($subscription->features as $feature)
                                                    @if(is_array($feature) && isset($feature['title'], $feature['value']))
                                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                            {{ $feature['title'] }}: {{ $feature['value'] }}
                                                        </span>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="text-right">
                                    <p class="text-sm text-gray-500">Duration</p>
                                    <p class="text-sm font-medium text-gray-900">
                                        {{ $subscription->start_date->diffInDays($subscription->expiry_date) }} days
                                    </p>
                                </div>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
            
            <!-- Pagination -->
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                {{ $subscriptions->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-credit-card text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No subscriptions found</h3>
                <p class="text-gray-500">This user has no subscription history.</p>
            </div>
        @endif
    </div>

    <!-- Subscription Summary -->
    @if($subscriptions->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Subscription Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $subscriptions->total() }}</div>
                    <div class="text-sm text-gray-500">Total Subscriptions</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ $subscriptions->where('status', 'ACTIVE')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Active</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">
                        {{ $subscriptions->where('status', 'EXPIRED')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Expired</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-600">
                        {{ $subscriptions->where('status', 'CANCELLED')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Cancelled</div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
