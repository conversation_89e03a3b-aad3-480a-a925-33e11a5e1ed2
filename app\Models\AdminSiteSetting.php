<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class AdminSiteSetting extends Model
{
    protected $table = 'admin_site_settings';

    protected $fillable = [
        'key',
        'name',
        'description',
        'value',
        'type',
        'category',
        'group',
        'sort_order',
        'validation_rules',
        'options',
        'help_text',
        'is_public',
        'is_required',
        'is_encrypted',
        'default_value',
        'access_level',
        'updated_by',
    ];

    protected $casts = [
        'validation_rules' => 'array',
        'options' => 'array',
        'is_public' => 'boolean',
        'is_required' => 'boolean',
        'is_encrypted' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the admin who last updated this setting
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'updated_by');
    }

    /**
     * Scope to get settings by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get settings by group
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Scope to get public settings
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get settings by access level
     */
    public function scopeByAccessLevel($query, $level)
    {
        return $query->where('access_level', $level);
    }

    /**
     * Get the decrypted value if encrypted
     */
    public function getDecryptedValueAttribute()
    {
        if ($this->is_encrypted && !empty($this->value)) {
            try {
                return Crypt::decryptString($this->value);
            } catch (\Exception $e) {
                return $this->value; // Return original if decryption fails
            }
        }

        return $this->value;
    }

    /**
     * Set encrypted value if needed
     */
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && !empty($value)) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    /**
     * Get typed value based on setting type
     */
    public function getTypedValue()
    {
        $value = $this->is_encrypted ? $this->decrypted_value : $this->value;

        switch ($this->type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'multiselect':
                return is_string($value) ? explode(',', $value) : $value;
            default:
                return $value;
        }
    }

    /**
     * Get setting value by key with caching
     */
    public static function getValue($key, $default = null)
    {
        $cacheKey = "site_setting_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            
            if (!$setting) {
                return $default;
            }

            return $setting->getTypedValue();
        });
    }

    /**
     * Set setting value by key
     */
    public static function setValue($key, $value, $updatedBy = null)
    {
        $setting = static::where('key', $key)->first();
        
        if ($setting) {
            $setting->update([
                'value' => $value,
                'updated_by' => $updatedBy,
            ]);
        } else {
            // Create new setting if it doesn't exist
            static::create([
                'key' => $key,
                'name' => ucwords(str_replace(['_', '.'], ' ', $key)),
                'value' => $value,
                'type' => 'string',
                'category' => 'general',
                'updated_by' => $updatedBy,
            ]);
        }

        // Clear cache
        Cache::forget("site_setting_{$key}");
        
        return true;
    }

    /**
     * Get all settings grouped by category
     */
    public static function getGroupedSettings()
    {
        return static::orderBy('category')
            ->orderBy('group')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('category')
            ->map(function ($categorySettings) {
                return $categorySettings->groupBy('group');
            });
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache()
    {
        $keys = static::pluck('key');
        
        foreach ($keys as $key) {
            Cache::forget("site_setting_{$key}");
        }
    }

    /**
     * Boot method to clear cache on model events
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($setting) {
            Cache::forget("site_setting_{$setting->key}");
        });

        static::deleted(function ($setting) {
            Cache::forget("site_setting_{$setting->key}");
        });
    }
}
