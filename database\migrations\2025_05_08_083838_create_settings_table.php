<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id(); // bigint unsigned auto-increment
            $table->unsignedBigInteger('business_id');

            $table->string('ai_provider', 50)->default('ChatGPT');
            $table->string('model_version', 50)->default('GPT-4');
            $table->string('response_style', 100)->default('BALANCED');
            $table->string('response_length', 100)->default('MODERATE');

            $table->string('custom_signoff_text', 255)->nullable()->default('Thank you for your business!');
            $table->text('custom_instruction')->nullable();

            $table->string('response_language', 50)->default('Same as review');

            $table->boolean('auto_check_reviews')->default(true);
            $table->integer('check_interval_minutes')->default(30);

            $table->boolean('auto_reply')->default(false);
            $table->unsignedTinyInteger('auto_reply_rating_min')->default(1);
            $table->unsignedTinyInteger('auto_reply_rating_max')->default(5);

            $table->string('sentiment_filter', 100)->default('ANY');
            $table->string('review_length_filter', 100)->default('ANY');
            $table->string('template_selection', 255)->default('AUTO_SELECT');
            $table->string('reply_tone', 50)->default('PROFESSIONAL');
            $table->string('reply_timing', 100)->default('IMMEDIATELY');

            $table->string('timezone', 50)->default('GMT-05:00');

            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci'; // created_at and updated_at (nullable by default)
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
