<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-slow': 'bounce 2s infinite',
                        'pulse-slow': 'pulse 3s ease-in-out infinite',

                    },
                    keyframes: {
                        fadeIn: {
                            '0%': {
                                opacity: '0'
                            },
                            '100%': {
                                opacity: '1'
                            }
                        },
                        slideUp: {
                            '0%': {
                                transform: 'translateY(20px)',
                                opacity: '0'
                            },
                            '100%': {
                                transform: 'translateY(0)',
                                opacity: '1'
                            }
                        },
                        float: {
                            '0%, 100%': {
                                transform: 'translateY(0px)'
                            },
                            '50%': {
                                transform: 'translateY(-10px)'
                            }
                        }
                    }
                }
            },
            darkMode: 'class'
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">



    <!-- Main Content -->
    <div class="text-center animate-fade-in">
        <!-- Error Code with Animation -->
        <div class="relative mb-8">
            <div class="text-9xl font-bold text-primary-600 dark:text-primary-400 mb-4 animate-pulse-slow">
                404
            </div>
            <div class="absolute inset-0 text-9xl font-bold text-primary-200 dark:text-primary-800 opacity-30 animate-bounce-slow">
                404
            </div>
        </div>

        <!-- Title -->
        <h2 class="text-3xl md:text-4xl font-semibold text-gray-800 dark:text-white mb-4 animate-slide-up">
            Oops! Page not found
        </h2>

        <!-- Description -->
        <p class="text-gray-600 dark:text-gray-300 mb-8 max-w-md mx-auto leading-relaxed animate-slide-up" style="animation-delay: 0.1s;">
            The page you're looking for might have been removed or is temporarily unavailable.
        </p>

        <!-- Action Buttons -->
        <div class="space-x-4 animate-slide-up" style="animation-delay: 0.3s;">
            <!-- Primary Button - Go Home -->
            <a href="{{ route('home') }}" class="inline-block px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg shadow-primary-500/25">
                <i class="fas fa-home mr-2"></i>
                Go Home
            </a>

            <!-- Secondary Button - Need Help -->
            <a href="https://reviewmaster.biz/getting-started.php" class="inline-block px-8 py-3 border border-primary-600 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-gray-800 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                <i class="fa-solid fa-life-ring"></i>
                Need Help
            </a>
        </div>



    </div>
</body>

</html>