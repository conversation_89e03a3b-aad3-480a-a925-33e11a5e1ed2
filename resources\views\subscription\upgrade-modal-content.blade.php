@php
    $currentPlan = $data['current_plan'] ?? null;
    $upgradePlans = $data['upgrade_plans'] ?? [];
    $allPlans = [];

    // Add current plan first if exists
    if($currentPlan) {
        $allPlans[] = array_merge($currentPlan, ['is_current' => true]);
    }

    // Add upgrade plans
    foreach($upgradePlans as $plan) {
        $allPlans[] = array_merge($plan, ['is_current' => false]);
    }
@endphp

@if(empty($upgradePlans))
    <div class="text-center py-8">
        <p class="text-gray-500">No upgrade options available for your current plan</p>
    </div>
@else
    <!-- Desktop View: Horizontal Cards -->
    <div class="hidden md:block p-6">
        <div class="flex flex-wrap justify-center gap-6">
            <!-- Current Plan Card -->
            @if($currentPlan)
                <div class="max-w-[250px] relative border border-indigo-500 bg-indigo-50 rounded-lg p-6">
                    <div class="text-center mb-4">
                        <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm">Current Plan</span>
                    </div>

                    <div class="text-center mb-4">
                        <h3 class="text-xl font-bold text-gray-900">{{ $currentPlan['name'] }}</h3>
                        @if(!empty($currentPlan['short_description']))
                            <p class="text-gray-600 text-sm mt-1">{{ $currentPlan['short_description'] }}</p>
                        @endif
                    </div>

                    <div class="text-center mb-6">
                        <div class="text-3xl font-bold text-gray-900">
                            {{ $currentPlan['currency_symbol'] }}{{ number_format($currentPlan['price'], 0) }}
                        </div>
                        <div class="text-sm text-gray-500">per {{ $currentPlan['duration_days'] }} days</div>
                    </div>

                    <div class="space-y-3">
                        @include('subscription.plan-features', ['features' => $currentPlan['features'] ?? []])
                    </div>
                </div>
            @endif

            <!-- Upgrade Plan Cards -->
            @foreach($upgradePlans as $plan)
                <div class="max-w-[250px] relative border border-gray-200 pt-6 px-6 pb-[80px] hover:border-indigo-300 rounded-lg transition-colors">
                    <div class="text-center mb-4">
                        <h3 class="text-xl font-bold text-gray-900">{{ $plan['name'] }}</h3>
                        @if(!empty($plan['short_description']))
                            <p class="text-gray-600 text-sm mt-1">{{ $plan['short_description'] }}</p>
                        @endif
                    </div>

                    <div class="text-center mb-6">
                        <div class="text-3xl font-bold text-gray-900">
                            {{ $plan['currency_symbol'] }}{{ number_format($plan['price'], 0) }}
                        </div>
                        <div class="text-sm text-gray-500">per {{ $plan['duration_days'] }} days</div>
                        @if($currentPlan)
                            <div class="text-xs text-green-600 mt-1">
                                +{{ $plan['currency_symbol'] }}{{ number_format($plan['price'] - $currentPlan['price'], 0) }} from current plan
                            </div>
                        @endif
                    </div>

                    <div class="space-y-3 mb-6">
                        @include('subscription.plan-features', ['features' => $plan['features'] ?? []])
                    </div>

                    <button
                        class="absolute w-[calc(100%-theme(spacing.12))] bottom-6 left-6 upgrade-plan-btn py-3 px-4 rounded-md font-normal text-sm transition-colors bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-tl-0 rounded-tr-0 before:absolute before:top-0 before:left-[-75%] before:h-full before:w-[50%] before:bg-gradient-to-r before:from-white before:to-transparent before:opacity-30 hover:before:left-[125%] before:transform before:skew-x-[-20deg] before:transition-all before:duration-500"
                        data-plan-id="{{ $plan['id'] }}"
                    >
                        ⚡ Upgrade Now
                    </button>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Mobile View: Interactive Slider -->
    <div class="block md:hidden">
        <!-- Slider Container -->
        <div class="relative overflow-hidden">
            

            <!-- Slider Wrapper -->
            <div class="relative">
                <!-- Navigation Arrows -->
                <button
                    id="prevSlide"
                    class="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white hover:bg-indigo-500 hover:text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 opacity-50"
                >
                    <i class="fa-solid fa-angle-left"></i>
                </button>

                <button
                    id="nextSlide"
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white hover:bg-indigo-500 hover:text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200"
                >
                    <i class="fa-solid fa-angle-right"></i>
                </button>

                <!-- Slides Container -->
                <div
                    id="planSlider"
                    class="flex transition-transform duration-300 ease-in-out"
                    style="transform: translateX(0%)"
                >
                    @foreach($allPlans as $index => $plan)
                        <div class="w-full flex-shrink-0 p-4">
                            <div class="max-w-md mx-auto border {{ $plan['is_current'] ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200' }} rounded-lg px-12 p-6 shadow-sm">
                                @if($plan['is_current'])
                                    <div class="text-center mb-4">
                                        <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm">Current Plan</span>
                                    </div>
                                @endif

                                <div class="text-center mb-4">
                                    <h3 class="text-xl font-bold text-gray-900">{{ $plan['name'] }}</h3>
                                    @if(!empty($plan['short_description']))
                                        <p class="text-gray-600 text-sm mt-1">{{ $plan['short_description'] }}</p>
                                    @endif
                                </div>

                                <div class="text-center mb-6">
                                    <div class="text-3xl font-bold text-gray-900">
                                        {{ $plan['currency_symbol'] }}{{ number_format($plan['price'], 0) }}
                                    </div>
                                    <div class="text-sm text-gray-500">per {{ $plan['duration_days'] }} days</div>
                                    @if(!$plan['is_current'] && $currentPlan)
                                        <div class="text-xs text-green-600 mt-1">
                                            +{{ $plan['currency_symbol'] }}{{ number_format($plan['price'] - $currentPlan['price'], 0) }} from current plan
                                        </div>
                                    @endif
                                </div>

                                <div class="space-y-3 mb-6">
                                    @include('subscription.plan-features', ['features' => $plan['features'] ?? []])
                                </div>

                                @if(!$plan['is_current'])
                                    <button
                                        class="w-full upgrade-plan-btn py-3 px-4 rounded-md font-normal text-sm transition-colors bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                                        data-plan-id="{{ $plan['id'] }}"
                                    >
                                        ⚡ Upgrade Now
                                    </button>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <!-- Swipe Hint -->
            <div class="text-center py-2 text-xs text-gray-500">
                <span class="inline-flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                    </svg>
                    Swipe or use arrows to navigate
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </span>
            </div>
            <!-- Plan Indicator Dots -->
            <div class="flex justify-center items-center py-4">
                <div class="flex space-x-2" id="planIndicators">
                    @foreach($allPlans as $index => $plan)
                        <button
                            class="w-3 h-3 rounded-full transition-all duration-300 plan-indicator {{ $index === 0 ? 'bg-indigo-600 scale-110' : 'bg-gray-300' }}"
                            data-slide="{{ $index }}"
                        ></button>
                    @endforeach
                </div>
                <div class="ml-4 text-sm text-gray-600">
                    <span id="currentSlideNumber">1</span> / {{ count($allPlans) }}
                </div>
            </div>
            
        </div>
    </div>
@endif



    
