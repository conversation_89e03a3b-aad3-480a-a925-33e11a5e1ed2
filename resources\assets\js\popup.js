//import { initializeAnalyticsDashboard } from './analytics-dashboard.js'
/**
 * ReviewMaster AI - Main Popup <PERSON>
 * Handles the initialization and tab switching functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    
  // Set up tab navigation
  setupTabNavigation();
  setupUserProfileMenu();
  localStorage.setItem('reviewMasterActiveTab', 'reviewsTab')
  // console.log(' first data ',localStorage.getItem('reviewMasterActiveTab'))
});

/**
 * Set up tab navigation functionality
 */
function setupTabNavigation() {
  
  // Get all tab buttons and tab contents
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  const sideMenuBar = document.getElementById('sideMenuBar');
  
  // Function to activate a specific tab
  function activateTab(tabId) {
    // Hide all tabs and remove active class from all buttons
    tabContents.forEach(tab => {
      tab.classList.add('hidden');
      tab.classList.remove('active');
    });
    
    tabButtons.forEach(btn => {
      btn.classList.remove('active', 'border-indigo-600', 'text-indigo-600');
      btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show the selected tab and activate its button
    const selectedTab = document.getElementById(tabId);
    const desktopBtn = document.querySelector(`#${tabId}Btn`);
    const mobileBtn = document.querySelector(`#${tabId}BtnMobile`);
    
    // Activate both desktop and mobile buttons for the same tab
    if (selectedTab) {
      selectedTab.classList.remove('hidden');
      selectedTab.classList.add('active');
      
      // Activate all buttons for this tab (both desktop and mobile versions)
      document.querySelectorAll(`[data-tab="${tabId}"]`).forEach(btn => {
        btn.classList.add('active', 'border-indigo-600', 'text-indigo-600');
        btn.classList.remove('border-transparent', 'text-gray-500');
      });
      
      // Store the active tab in localStorage for persistence
      localStorage.setItem('reviewMasterActiveTab', tabId);
    } else {
      console.error(`Could not find tab for ${tabId}`);
    }
  }
  
  // Add click event listeners to all tab buttons
  tabButtons.forEach(button => {
    button.addEventListener('click', function() {
      const tabId = this.getAttribute('data-tab');
      if (tabId) {
        activateTab(tabId);
        
        // Check if this is a mobile tab button (in the sidemenubar)
        if (this.id.includes('Mobile') && sideMenuBar && window.innerWidth < 768) {
          // Close the sidebar on mobile after selecting a tab
          document.body.classList.remove('menu-active');
        }
      }
    });
  });
  
  // Set the initial active tab (either from localStorage or default to reviews)
  const savedTab = localStorage.getItem('reviewMasterActiveTab');
  if (savedTab && document.getElementById(savedTab)) {
    activateTab(savedTab);
  } else {
    // Default to reviews tab
    activateTab('reviewsTab');
  }

}

/**
 * Set up user profile menu
 */
function setupUserProfileMenu() {
  const userProfileContainer = document.getElementById('userProfileContainer');
  const userMenu = document.getElementById('userMenu');
  
  if (userProfileContainer && userMenu) {
    // Toggle menu when user profile is clicked
    userProfileContainer.addEventListener('click', (e) => {
      e.stopPropagation();
      userMenu.classList.toggle('hidden');
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', () => {
      if (!userMenu.classList.contains('hidden')) {
        userMenu.classList.add('hidden');
      }
    });
    
    // Prevent menu from closing when clicking inside it
    userMenu.addEventListener('click', (e) => {
      e.stopPropagation();
    });
    
    // Handle menu item clicks
    document.getElementById('editProfileBtn')?.addEventListener('click', (e) => {
      e.preventDefault();
      showEditProfileModal();
      userMenu.classList.add('hidden');
    });
    
    document.getElementById('editProfileBtnMobile')?.addEventListener('click', (e) => {
      e.preventDefault();
      showEditProfileModal();
      userMenu.classList.add('hidden');

      const mainWrapper = document.querySelector('.menu-active');
      if (mainWrapper) {
        mainWrapper.classList.remove('menu-active');
      }

    });
    
    document.getElementById('planBtn')?.addEventListener('click', (e) => {
      e.preventDefault();
      alert('Plan management feature coming soon!');
      userMenu.classList.add('hidden');
    });
    
    document.getElementById('logoutBtn')?.addEventListener('click', (e) => {
      e.preventDefault();
      if (confirm('Are you sure you want to log out?')) {
        alert('You have been logged out successfully.');
        // In a real extension, this would handle actual logout logic
      }
      userMenu.classList.add('hidden');
    });
  }
}

/**
 * Show the edit profile modal
 */
function showEditProfileModal() {
  // Create modal if it doesn't exist
  let modal = document.getElementById('editProfileModal');

  const userName = document.getElementById('user_name').value;
  const userEmail = document.getElementById('user_email').value;
  const userAvatar = document.getElementById('user_avatar').value;
  const isGoogleUser = document.getElementById('user_google_id') ? document.getElementById('user_google_id').value : '';
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'editProfileModal';
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm';
    modal.innerHTML = `
      <div class="bg-white md:rounded-lg shadow-xl w-full md:max-w-[500px] h-auto overflow-y-auto">
        <form id="profileForm"> 
          <div class="modal-header p-4 flex justify-between items-center border-b border-gray-200">
            <h3 class="text-lg font-semibold">Edit Profile</h3>
            <button type="button" id="closeProfileModalBtn" class="text-gray-400 hover:text-gray-500">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body overflow-y-auto space-y-4 p-4 h-[calc(100vh-126px)] md:h-[calc(804px-220px)]">
            <!-- Profile Picture -->
            <div class="flex justify-center mb-6">
              <div class="relative">
                <img src="${userAvatar}" alt="Profile" class="w-24 h-24 rounded-full object-cover border-4 border-gray-200">
                <button type="button" class="absolute bottom-0 right-0 bg-indigo-600 text-white rounded-full p-2 shadow-md hover:bg-indigo-700">
                  <i class="fas fa-camera text-xs"></i>
                </button>
              </div>
            </div>
            
            <!-- Name -->
            <div>
              <label for="profileName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
              <input type="text" id="profileName" value="${userName}" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
            </div>
            
            <!-- Email -->
            <div>
              <label for="profileEmail" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <input type="email" id="profileEmail" value="${userEmail}" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
            </div>
            
            <!-- Phone -->
            <div class="w-full">
              <label for="profilePhone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
              <input type="tel" id="profilePhone" placeholder="Enter your phone number" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
            </div>
            
            ${isGoogleUser ? `
              <!-- Google Account -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Google Account</label>
                <div class="flex items-center justify-between border border-gray-300 rounded-md px-3 py-2 bg-gray-50">
                  <div class="flex items-center text-sm">
                    <i class="fab fa-google text-red-500 mr-2"></i>
                    <span>Connected as ${userEmail}</span>
                  </div>
                </div>
              </div>
            
              <!-- Google users can still set a password -->
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Set Password</label>
                <div id="passwordFields" class="space-y-3 mt-3">
                  <input type="password" id="newPassword" placeholder="New password" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
                  <input type="password" id="confirmPassword" placeholder="Confirm new password" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
                </div>
              </div>
            ` : `
              <!-- Password -->
              <div>
                <label for="profilePassword" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <button type="button" id="changePasswordBtn" class="text-sm text-indigo-600 hover:text-indigo-800">Change Password</button>
                <div id="passwordFields" class="hidden space-y-3 mt-3">
                  <input type="password" id="currentPassword" placeholder="Current password" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
                  <input type="password" id="newPassword" placeholder="New password" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
                  <input type="password" id="confirmPassword" placeholder="Confirm new password" class="w-full text-sm border border-gray-300 rounded-md px-3 py-2">
                </div>
              </div>
            `}            
            </div>
            <div class="modal-footer flex justify-end border-t border-gray-200 p-4 gap-2">
              <button type="button" id="cancelProfileBtn" class="px-2 py-1 md:px-4 md:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 mr-2">
                Cancel
              </button>
              <button type="submit" class="px-2 py-1 md:px-4 md:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Save Changes
              </button>
            </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Initialize international telephone input
    const phoneInput = document.getElementById('profilePhone');
    const iti = window.intlTelInput(phoneInput, {
      utilsScript: 'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js',
      separateDialCode: true,
      initialCountry: 'auto',
      geoIpLookup: function(callback) {
        fetch('https://ipapi.co/json')
          .then(res => res.json())
          .then(data => callback(data.country_code))
          .catch(() => callback('in'));
      }
    });
    
    // Validate phone number before form submission
    const profileForm = document.getElementById('profileForm')
    if(profileForm)
    {
      profileForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (phoneInput.value && !iti.isValidNumber()) {
          alert('Please enter a valid phone number.');
          return;
        }
        
        const formData = {
          name: document.getElementById('profileName').value,
          email: document.getElementById('profileEmail').value,
          country_code: '+' + iti.getSelectedCountryData().dialCode,
          phone_number: phoneInput.value
        };
  
        // Add password fields if they're filled
        const newPassword = document.getElementById('newPassword').value;
        if (newPassword) {
          if (!isGoogleUser) {
            formData.current_password = document.getElementById('currentPassword').value;
          }
          formData.new_password = newPassword;
          formData.new_password_confirmation = document.getElementById('confirmPassword').value;
        }
  
        try {
          const response = await fetch('/profile/update', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(formData)
          });
  
          const data = await response.json();
  
          if (!response.ok) {
            throw new Error(data.message || 'An error occurred while updating profile');
          }
  
          alert(data.message);
          hideEditProfileModal();
          
          // Update displayed user information if needed
          if (document.getElementById('user_name')) {
            document.getElementById('user_name').value = data.user.name;
          }
          if (document.getElementById('user_email')) {
            document.getElementById('user_email').value = data.user.email;
          }
  
        } catch (error) {
          alert(error.message);
        }
      });
    }
    
    // Set up event listeners for the modal
    document.getElementById('closeProfileModalBtn').addEventListener('click', hideEditProfileModal);
    document.getElementById('cancelProfileBtn').addEventListener('click', hideEditProfileModal);
    
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener('click', changePassword);
    }
  } else {
    modal.classList.remove('hidden');
  }
}

/**
 * Hide the edit profile modal
 */
function hideEditProfileModal() {
  const modal = document.getElementById('editProfileModal');
  if (modal) {
    modal.classList.add('hidden');
  }
}


//safe area for iOS devices
  // function isIOS() {
  //   return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  // }

  // document.addEventListener("DOMContentLoaded", () => {
  //   if (isIOS()) {
  //     document.querySelector(".body-content")?.classList.add("pt-safe", "pb-safe", "h-safe");
  //   }
  // });