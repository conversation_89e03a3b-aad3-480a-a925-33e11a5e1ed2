<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Business extends Model
{
    protected $table = 'businesses';
    protected $fillable = [
        'location_name',
        'title',
        'primary_phone',
        'additional_phones',
        'region_code',
        'language_code',
        'postal_code',
        'administrative_area',
        'locality',
        'address_lines',
        'website',
        'latitude',
        'longitude',
        'profile_description',
        'user_id',
        'next_token',
        'average_rating',
        'total_reviews'
    ];

    protected $casts = [
        'additional_phones' => 'array',
        'address_lines' => 'array',
        'latitude' => 'float',
        'longitude' => 'float',
    ];

    public function setting()
    {
        return $this->hasOne(Setting::class, 'business_id', 'id');
    }

    public function templates()
    {
        return $this->hasMany(Template::class, 'business_id', 'id');
    }

    public function teamMembers()
    {
        return $this->hasMany(TeamMember::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function userPreferences()
    {
        return $this->hasMany(UserBusinessPreference::class);
    }
}
