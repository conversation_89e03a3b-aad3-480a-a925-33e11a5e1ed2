<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plan Pricing Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .strikethrough {
            text-decoration: line-through;
            opacity: 0.6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Choose Your Plan</h1>
        
        <!-- Billing Toggle -->
        <div class="flex justify-center mb-8">
            <div class="bg-white rounded-lg p-1 shadow-sm border">
                <button id="monthlyBtn" class="px-6 py-2 rounded-md text-sm font-medium transition-colors bg-blue-600 text-white">
                    Monthly
                </button>
                <button id="annualBtn" class="px-6 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900">
                    Annual <span class="text-green-600 text-xs">(Save up to 20%)</span>
                </button>
            </div>
        </div>

        <!-- Plans Grid -->
        <div id="plansContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Plans will be loaded here -->
        </div>
    </div>

    <script>
        let currentBilling = 'monthly';
        let plansData = [];

        // Load plans data
        async function loadPlans() {
            try {
                const response = await fetch('/reviewbiz/plans/pricing?currency=INR');
                plansData = await response.json();
                renderPlans();
            } catch (error) {
                console.error('Error loading plans:', error);
            }
        }

        // Render plans
        function renderPlans() {
            const container = document.getElementById('plansContainer');
            container.innerHTML = '';

            plansData.forEach(plan => {
                const planCard = createPlanCard(plan);
                container.appendChild(planCard);
            });
        }

        // Create plan card
        function createPlanCard(plan) {
            const div = document.createElement('div');
            div.className = `bg-white rounded-lg shadow-lg p-6 border ${plan.name === 'Business' ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'}`;
            
            const pricing = plan.pricing;
            const isAnnual = currentBilling === 'annual';
            const price = isAnnual ? pricing.annual.discounted_price : pricing.monthly.price;
            const originalPrice = isAnnual ? pricing.annual.regular_price : null;
            
            div.innerHTML = `
                ${plan.name === 'Business' ? '<div class="text-center mb-4"><span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">Most Popular</span></div>' : ''}
                
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-2">${plan.name}</h3>
                    <p class="text-gray-600 text-sm mb-4">${plan.short_description}</p>
                    
                    <div class="mb-4">
                        ${originalPrice && isAnnual && pricing.annual.has_discount ? 
                            `<div class="text-lg text-gray-500 strikethrough">${pricing.annual.formatted_regular}</div>` : 
                            ''
                        }
                        <div class="text-3xl font-bold text-gray-900">
                            ${plan.pricing.monthly.formatted.replace('.00', '')}
                            ${isAnnual ? '<span class="text-lg font-normal text-gray-600">/year</span>' : '<span class="text-lg font-normal text-gray-600">/month</span>'}
                        </div>
                        ${isAnnual && pricing.annual.has_discount ? 
                            `<div class="text-sm text-green-600 font-medium">Save ${pricing.annual.formatted_savings} annually</div>` : 
                            ''
                        }
                    </div>
                </div>

                <div class="space-y-3 mb-6">
                    ${plan.features.map(feature => `
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-3 ${feature.status === 'enabled' ? 'text-green-500' : 'text-gray-400'}" fill="currentColor" viewBox="0 0 20 20">
                                ${feature.status === 'enabled' ? 
                                    '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>' :
                                    '<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>'
                                }
                            </svg>
                            <span class="text-sm ${feature.status === 'enabled' ? 'text-gray-700' : 'text-gray-400'}">${feature.title}</span>
                        </div>
                    `).join('')}
                </div>

                <button class="w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                    plan.is_free ? 
                        'bg-gray-100 text-gray-700 hover:bg-gray-200' : 
                        plan.name === 'Business' ? 
                            'bg-blue-600 text-white hover:bg-blue-700' : 
                            'bg-gray-900 text-white hover:bg-gray-800'
                }">
                    ${plan.is_free ? 'Get Started Free' : 'Choose Plan'}
                </button>
            `;

            return div;
        }

        // Event listeners
        document.getElementById('monthlyBtn').addEventListener('click', () => {
            currentBilling = 'monthly';
            updateBillingButtons();
            renderPlans();
        });

        document.getElementById('annualBtn').addEventListener('click', () => {
            currentBilling = 'annual';
            updateBillingButtons();
            renderPlans();
        });

        function updateBillingButtons() {
            const monthlyBtn = document.getElementById('monthlyBtn');
            const annualBtn = document.getElementById('annualBtn');

            if (currentBilling === 'monthly') {
                monthlyBtn.className = 'px-6 py-2 rounded-md text-sm font-medium transition-colors bg-blue-600 text-white';
                annualBtn.className = 'px-6 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900';
            } else {
                monthlyBtn.className = 'px-6 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900';
                annualBtn.className = 'px-6 py-2 rounded-md text-sm font-medium transition-colors bg-blue-600 text-white';
            }
        }

        // Initialize
        loadPlans();
    </script>
</body>
</html>
