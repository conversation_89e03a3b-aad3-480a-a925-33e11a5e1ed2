<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdminEmailTemplate extends Model
{
    protected $table = 'admin_email_templates';

    protected $fillable = [
        'name',
        'subject',
        'description',
        'html_content',
        'text_content',
        'category',
        'type',
        'available_variables',
        'required_variables',
        'settings',
        'is_active',
        'is_default',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'available_variables' => 'array',
        'required_variables' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the admin who created this template
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * Get the admin who last updated this template
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'updated_by');
    }

    /**
     * Scope to get active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get templates by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get templates by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get default templates
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get the default template for a specific type
     */
    public static function getDefaultTemplate($type)
    {
        return static::active()->byType($type)->default()->first();
    }

    /**
     * Replace template variables with actual values
     */
    public function renderContent($variables = [])
    {
        $htmlContent = $this->html_content;
        $textContent = $this->text_content;

        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $htmlContent = str_replace($placeholder, $value, $htmlContent);
            if ($textContent) {
                $textContent = str_replace($placeholder, $value, $textContent);
            }
        }

        return [
            'html' => $htmlContent,
            'text' => $textContent,
            'subject' => $this->renderSubject($variables),
        ];
    }

    /**
     * Replace variables in subject line
     */
    public function renderSubject($variables = [])
    {
        $subject = $this->subject;
        
        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $subject = str_replace($placeholder, $value, $subject);
        }

        return $subject;
    }

    /**
     * Get all available template variables
     */
    public static function getGlobalVariables()
    {
        return [
            'app_name' => 'Application Name',
            'app_url' => 'Application URL',
            'user_name' => 'User Name',
            'user_email' => 'User Email',
            'admin_name' => 'Admin Name',
            'current_date' => 'Current Date',
            'current_year' => 'Current Year',
            'support_email' => 'Support Email',
            'company_name' => 'Company Name',
            'company_address' => 'Company Address',
        ];
    }

    /**
     * Validate required variables are present
     */
    public function validateVariables($variables = [])
    {
        $required = $this->required_variables ?? [];
        $missing = [];

        foreach ($required as $variable) {
            if (!isset($variables[$variable]) || empty($variables[$variable])) {
                $missing[] = $variable;
            }
        }

        return [
            'valid' => empty($missing),
            'missing' => $missing,
        ];
    }
}
