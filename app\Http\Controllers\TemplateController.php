<?php

namespace App\Http\Controllers;

use App\Models\Template;
use Illuminate\Http\Request;

class TemplateController extends Controller
{
    public function addTemplate(Request $request)
    {
        $template = new Template();
        $template->title = $request->input('title');
        $template->description = $request->input('description');
        $template->template_text = $request->input('template_text');
        $template->ratings = $request->input('ratings');
        $template->sentiment = $request->input('sentiment');
        $template->length = $request->input('length');
        $template->save();
        return response()->json(['success' => true]);
    }

    public function editTemplate(Request $request, $id)
    {
        $template = Template::find($id);
        return response()->json(['success' => true, 'template' => $template]);
    }

    public function updateTemplate(Request $request, $id)
    {
        $template = Template::find($id);
        if ($template) {
            $template->title = $request->input('title');
            $template->description = $request->input('description');
            $template->template_text = $request->input('template_text');
            $template->ratings = $request->input('ratings');
            $template->sentiment = $request->input('sentiment');
            $template->length = $request->input('length');
            $template->save();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false]);
        }
    }

    public function deleteTemplate($id)
    {
        $template = Template::find($id);
        if ($template) {
            $template->delete();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false]);
        }
    }


    public function getTemplate($id)
    {
        $template = Template::find($id);

        if (!$template) {
            return response()->json(['success' => false, 'message' => 'Template not found']);
        }

        return response()->json(['success' => true, 'template' => $template]);
    }
}
