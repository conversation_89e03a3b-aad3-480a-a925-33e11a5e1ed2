{"__meta": {"id": "01K0RF9BDPRJZSXZ2GSR0RQ0WC", "datetime": "2025-07-22 07:06:38", "utime": **********.391564, "method": "GET", "uri": "/business-dashboard", "ip": "127.0.0.1"}, "messages": {"count": 11, "messages": [{"message": "[07:06:36] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php on line 61", "message_html": null, "is_string": false, "label": "warning", "time": **********.972463, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:37] LOG.info: Token still valid, skipping refresh {\n    \"business_account_id\": 1,\n    \"expires_at\": \"2025-07-22T08:05:00.000000Z\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.010899, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:37] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php on line 61", "message_html": null, "is_string": false, "label": "warning", "time": **********.574998, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:37] LOG.warning: Optional parameter $reviewText declared before required parameter $starRating is implicitly treated as a required parameter in C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php on line 290", "message_html": null, "is_string": false, "label": "warning", "time": **********.58466, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:37] LOG.info: Token still valid, skipping refresh {\n    \"business_account_id\": 1,\n    \"expires_at\": \"2025-07-22T08:05:00.000000Z\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.625453, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:38] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php on line 61", "message_html": null, "is_string": false, "label": "warning", "time": **********.203368, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:38] LOG.debug: Total reviews counted {\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\",\n    \"count\": 15\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.275653, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:38] LOG.debug: Calculating rating statistics {\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.276307, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:38] LOG.debug: Rating statistics calculated {\n    \"avgRating\": \"4.7\",\n    \"totalRatings\": 15,\n    \"ratingCounts\": {\n        \"FIVE\": 12,\n        \"FOUR\": 2,\n        \"THREE\": 0,\n        \"TWO\": 1,\n        \"ONE\": 0\n    },\n    \"sumRatings\": 70\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.287355, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:38] LOG.debug: Calculating response metrics {\n    \"location_id\": \"13522179532217756997\",\n    \"account_id\": \"110486499747300774507\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.288176, "xdebug_link": null, "collector": "log"}, {"message": "[07:06:38] LOG.debug: Response metrics calculated {\n    \"responseRate\": 40,\n    \"avgResponseTime\": 24445,\n    \"reviewsWithReplies\": 6,\n    \"totalReviews\": 15,\n    \"validResponseCount\": 6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.301333, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.086721, "end": **********.391602, "duration": 3.****************, "duration_str": "3.3s", "measures": [{"label": "Booting", "start": **********.086721, "relative_start": 0, "end": **********.841929, "relative_end": **********.841929, "duration": 0.****************, "duration_str": "755ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.84195, "relative_start": 0.****************, "end": **********.391604, "relative_end": 1.9073486328125e-06, "duration": 2.***************, "duration_str": "2.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.904379, "relative_start": 0.****************, "end": **********.913214, "relative_end": **********.913214, "duration": 0.008835077285766602, "duration_str": "8.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.341884, "relative_start": 3.****************, "end": **********.388601, "relative_end": **********.388601, "duration": 0.046717166900634766, "duration_str": "46.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: business-dashboard", "start": **********.346641, "relative_start": 3.***************, "end": **********.346641, "relative_end": **********.346641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.header-nav", "start": **********.367735, "relative_start": 3.****************, "end": **********.367735, "relative_end": **********.367735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.upgrade-subscription-modal", "start": **********.36929, "relative_start": 3.282569169998169, "end": **********.36929, "relative_end": **********.36929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.reviews-filter", "start": **********.372038, "relative_start": 3.2853169441223145, "end": **********.372038, "relative_end": **********.372038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partial-reviews", "start": **********.373617, "relative_start": 3.286895990371704, "end": **********.373617, "relative_end": **********.373617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.analytics", "start": **********.375159, "relative_start": 3.288438081741333, "end": **********.375159, "relative_end": **********.375159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.templates", "start": **********.376634, "relative_start": 3.2899129390716553, "end": **********.376634, "relative_end": **********.376634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.settings", "start": **********.379564, "relative_start": 3.2928431034088135, "end": **********.379564, "relative_end": **********.379564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.sidebar", "start": **********.385743, "relative_start": 3.2990219593048096, "end": **********.385743, "relative_end": **********.385743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 41678664, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "business-dashboard", "param_count": null, "params": [], "start": **********.346551, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/business-dashboard.blade.phpbusiness-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fbusiness-dashboard.blade.php&line=1", "ajax": false, "filename": "business-dashboard.blade.php", "line": "?"}}, {"name": "partials.header-nav", "param_count": null, "params": [], "start": **********.367654, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/header-nav.blade.phppartials.header-nav", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fheader-nav.blade.php&line=1", "ajax": false, "filename": "header-nav.blade.php", "line": "?"}}, {"name": "components.upgrade-subscription-modal", "param_count": null, "params": [], "start": **********.369214, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/components/upgrade-subscription-modal.blade.phpcomponents.upgrade-subscription-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fcomponents%2Fupgrade-subscription-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-subscription-modal.blade.php", "line": "?"}}, {"name": "partials.reviews-filter", "param_count": null, "params": [], "start": **********.37196, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/reviews-filter.blade.phppartials.reviews-filter", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Freviews-filter.blade.php&line=1", "ajax": false, "filename": "reviews-filter.blade.php", "line": "?"}}, {"name": "partial-reviews", "param_count": null, "params": [], "start": **********.373535, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partial-reviews.blade.phppartial-reviews", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartial-reviews.blade.php&line=1", "ajax": false, "filename": "partial-reviews.blade.php", "line": "?"}}, {"name": "partials.analytics", "param_count": null, "params": [], "start": **********.375082, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/analytics.blade.phppartials.analytics", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "partials.templates", "param_count": null, "params": [], "start": **********.376556, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/templates.blade.phppartials.templates", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Ftemplates.blade.php&line=1", "ajax": false, "filename": "templates.blade.php", "line": "?"}}, {"name": "partials.settings", "param_count": null, "params": [], "start": **********.379413, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/settings.blade.phppartials.settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fsettings.blade.php&line=1", "ajax": false, "filename": "settings.blade.php", "line": "?"}}, {"name": "partials.sidebar", "param_count": null, "params": [], "start": **********.385665, "type": "blade", "hash": "bladeC:\\wamp64\\www\\reviewbiz\\resources\\views/partials/sidebar.blade.phppartials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fresources%2Fviews%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}]}, "queries": {"count": 43, "nb_statements": 43, "nb_visible_statements": 43, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05079999999999999, "accumulated_duration_str": "50.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'Vori1DaJ0hdCuHyNJxvvhkgKiqcBYo3aYugM2Yh4' limit 1", "type": "query", "params": [], "bindings": ["Vori1DaJ0hdCuHyNJxvvhkgKiqcBYo3aYugM2Yh4"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.9692159, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "review_master", "explain": null, "start_percent": 0, "width_percent": 1.85}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.006953, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "review_master", "explain": null, "start_percent": 1.85, "width_percent": 2.126}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` = 1 and `business_accounts`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.025761, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "CheckGoogleTokenExpiration.php:18", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FMiddleware%2FCheckGoogleTokenExpiration.php&line=18", "ajax": false, "filename": "CheckGoogleTokenExpiration.php", "line": "18"}, "connection": "review_master", "explain": null, "start_percent": 3.976, "width_percent": 2.933}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************', '[]', '{\\\"azp\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"aud\\\":\\\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\\\",\\\"sub\\\":\\\"110486499747300774507\\\",\\\"scope\\\":\\\"https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/business.manage https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.email https:\\\\/\\\\/www.googleapis.com\\\\/auth\\\\/userinfo.profile openid\\\",\\\"exp\\\":\\\"1753171505\\\",\\\"expires_in\\\":\\\"3504\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_verified\\\":\\\"true\\\",\\\"access_type\\\":\\\"offline\\\"}', 200, 'success', 223, null, 1, '2025-07-22 07:06:36', '2025-07-22 07:06:36')", "type": "query", "params": [], "bindings": ["google_api", "https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=********************************************************************************************************************************************************************************************************************************", "[]", "{\"azp\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"aud\":\"789440363905-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com\",\"sub\":\"110486499747300774507\",\"scope\":\"https:\\/\\/www.googleapis.com\\/auth\\/business.manage https:\\/\\/www.googleapis.com\\/auth\\/userinfo.email https:\\/\\/www.googleapis.com\\/auth\\/userinfo.profile openid\",\"exp\":\"1753171505\",\"expires_in\":\"3504\",\"email\":\"<EMAIL>\",\"email_verified\":\"true\",\"access_type\":\"offline\"}", 200, "success", 223, null, 1, "2025-07-22 07:06:36", "2025-07-22 07:06:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 99}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 236}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/CheckGoogleTokenExpiration.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Middleware\\CheckGoogleTokenExpiration.php", "line": 24}], "start": **********.270099, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 6.909, "width_percent": 2.185}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.285114, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:441", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=441", "ajax": false, "filename": "BusinessController.php", "line": "441"}, "connection": "review_master", "explain": null, "start_percent": 9.094, "width_percent": 2.008}, {"sql": "select * from `team_members` where `team_members`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.293588, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:441", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=441", "ajax": false, "filename": "BusinessController.php", "line": "441"}, "connection": "review_master", "explain": null, "start_percent": 11.102, "width_percent": 1.457}, {"sql": "select * from `business_accounts` where `business_accounts`.`user_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3008862, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:441", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 441}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=441", "ajax": false, "filename": "BusinessController.php", "line": "441"}, "connection": "review_master", "explain": null, "start_percent": 12.559, "width_percent": 3.74}, {"sql": "select exists(select * from `team_members` where `user_id` = 1) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 450}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.308094, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:450", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 450}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=450", "ajax": false, "filename": "BusinessController.php", "line": "450"}, "connection": "review_master", "explain": null, "start_percent": 16.299, "width_percent": 1.516}, {"sql": "select * from `businesses` where (`user_id` = 1 and `status` = 'active') limit 1", "type": "query", "params": [], "bindings": [1, "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3185198, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:465", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=465", "ajax": false, "filename": "BusinessController.php", "line": "465"}, "connection": "review_master", "explain": null, "start_percent": 17.815, "width_percent": 2.52}, {"sql": "select * from `settings` where `settings`.`business_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.330075, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:465", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 465}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=465", "ajax": false, "filename": "BusinessController.php", "line": "465"}, "connection": "review_master", "explain": null, "start_percent": 20.335, "width_percent": 3.701}, {"sql": "select * from `subscriptions` where `subscriptions`.`user_id` = 1 and `subscriptions`.`user_id` is not null and `status` = 'ACTIVE'", "type": "query", "params": [], "bindings": [1, "ACTIVE"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 480}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.339468, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:480", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 480}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=480", "ajax": false, "filename": "BusinessController.php", "line": "480"}, "connection": "review_master", "explain": null, "start_percent": 24.035, "width_percent": 3.031}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 493}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.347629, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:493", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 493}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=493", "ajax": false, "filename": "BusinessController.php", "line": "493"}, "connection": "review_master", "explain": null, "start_percent": 27.067, "width_percent": 4.154}, {"sql": "select * from `cache` where `key` in ('reviewmasterai_cache_fetched_reviews_110486499747300774507_locations/13522179532217756997')", "type": "query", "params": [], "bindings": ["reviewmasterai_cache_fetched_reviews_110486499747300774507_locations/13522179532217756997"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.3555968, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "review_master", "explain": null, "start_percent": 31.22, "width_percent": 1.555}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng', null, '{\\\"error\\\":{\\\"code\\\":401,\\\"message\\\":\\\"Request is missing required authentication credential. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https:\\\\/\\\\/developers.google.com\\\\/identity\\\\/sign-in\\\\/web\\\\/devconsole-project.\\\",\\\"status\\\":\\\"UNAUTHENTICATED\\\",\\\"details\\\":[{\\\"@type\\\":\\\"type.googleapis.com\\\\/google.rpc.ErrorInfo\\\",\\\"reason\\\":\\\"CREDENTIALS_MISSING\\\",\\\"domain\\\":\\\"googleapis.com\\\",\\\"metadata\\\":{\\\"service\\\":\\\"mybusinessbusinessinformation.googleapis.com\\\",\\\"method\\\":\\\"google.mybusiness.businessinformation.v1.Locations.ListLocations\\\"}}]}}', 401, 'success', 605, null, 1, '2025-07-22 07:06:36', '2025-07-22 07:06:36')", "type": "query", "params": [], "bindings": ["google_api", "https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng", null, "{\"error\":{\"code\":401,\"message\":\"Request is missing required authentication credential. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https:\\/\\/developers.google.com\\/identity\\/sign-in\\/web\\/devconsole-project.\",\"status\":\"UNAUTHENTICATED\",\"details\":[{\"@type\":\"type.googleapis.com\\/google.rpc.ErrorInfo\",\"reason\":\"CREDENTIALS_MISSING\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"mybusinessbusinessinformation.googleapis.com\",\"method\":\"google.mybusiness.businessinformation.v1.Locations.ListLocations\"}}]}}", 401, "success", 605, null, 1, "2025-07-22 07:06:36", "2025-07-22 07:06:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 680}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 511}], "start": **********.974535, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 32.776, "width_percent": 2.677}, {"sql": "select * from `business_accounts` where `business_google_id` is null or `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 205}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 270}, {"index": 18, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 704}, {"index": 19, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 511}], "start": **********.9846559, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:205", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=205", "ajax": false, "filename": "GoogleBusinessService.php", "line": "205"}, "connection": "review_master", "explain": null, "start_percent": 35.453, "width_percent": 1.555}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 174}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 273}, {"index": 18, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 704}, {"index": 19, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 511}], "start": **********.99123, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:174", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=174", "ajax": false, "filename": "GoogleBusinessService.php", "line": "174"}, "connection": "review_master", "explain": null, "start_percent": 37.008, "width_percent": 1.988}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 191}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 285}, {"index": 18, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 704}, {"index": 19, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 511}], "start": **********.001222, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:191", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=191", "ajax": false, "filename": "GoogleBusinessService.php", "line": "191"}, "connection": "review_master", "explain": null, "start_percent": 38.996, "width_percent": 2.165}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng', null, '{\\\"locations\\\":[{\\\"name\\\":\\\"locations\\\\/13522179532217756997\\\",\\\"title\\\":\\\"IndiaNIC Infotech Limited (Udaipur)\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"079 6191 6000\\\",\\\"additionalPhones\\\":[\\\"0294 252 7111\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"313001\\\",\\\"administrativeArea\\\":\\\"Rajasthan\\\",\\\"locality\\\":\\\"Udaipur\\\",\\\"addressLines\\\":[\\\"24, <PERSON><PERSON><PERSON>\\\",\\\"Shakti Nagar\\\",\\\"above Andhra Bank\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.indianic.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":24.5848982,\\\"longitude\\\":73.6984058},\\\"profile\\\":{\\\"description\\\":\\\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\\\n\\\\nThis place represents the Udaipur Development Center.\\\"}},{\\\"name\\\":\\\"locations\\\\/2713736367053130525\\\",\\\"title\\\":\\\"TopSpin Club\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"099306 63000\\\"},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"380015\\\",\\\"administrativeArea\\\":\\\"Gujarat\\\",\\\"locality\\\":\\\"Ahmedabad\\\",\\\"addressLines\\\":[\\\"101\\\\/3, DevArc Mall\\\",\\\"SG Highway\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.portray.work\\\\/topspinclub\\\",\\\"latlng\\\":{\\\"latitude\\\":23.0257952,\\\"longitude\\\":72.5075995},\\\"profile\\\":{\\\"description\\\":\\\"Announcing TopSpin Club. Ahmedabad\\'s first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\\\n\\\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad\\'s first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\\\n\\\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\\\n\\\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\\\"}},{\\\"name\\\":\\\"locations\\\\/3612416279359725233\\\",\\\"title\\\":\\\"NIC Gulf Software House LLC\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"04 834 2243\\\",\\\"additionalPhones\\\":[\\\"************\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"AE\\\",\\\"languageCode\\\":\\\"ar-Latn\\\",\\\"administrativeArea\\\":\\\"Dubai\\\",\\\"addressLines\\\":[\\\"Westburry office towers, Marasi Dr, Business Bay\\\",\\\"Suite 604\\\"]},\\\"websiteUri\\\":\\\"http:\\\\/\\\\/www.nicgulf.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":25.1858249,\\\"longitude\\\":55.2748255},\\\"profile\\\":{\\\"description\\\":\\\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\\\"}}]}', 200, 'success', 562, null, 1, '2025-07-22 07:06:37', '2025-07-22 07:06:37')", "type": "query", "params": [], "bindings": ["google_api", "https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng", null, "{\"locations\":[{\"name\":\"locations\\/13522179532217756997\",\"title\":\"IndiaNIC Infotech Limited (Udaipur)\",\"phoneNumbers\":{\"primaryPhone\":\"079 6191 6000\",\"additionalPhones\":[\"0294 252 7111\"]},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"313001\",\"administrativeArea\":\"Rajasthan\",\"locality\":\"Udaipur\",\"addressLines\":[\"24, <PERSON><PERSON><PERSON> Bhawan\",\"Shakti Nagar\",\"above Andhra Bank\"]},\"websiteUri\":\"https:\\/\\/www.indianic.com\\/\",\"latlng\":{\"latitude\":24.5848982,\"longitude\":73.6984058},\"profile\":{\"description\":\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\n\\nThis place represents the Udaipur Development Center.\"}},{\"name\":\"locations\\/2713736367053130525\",\"title\":\"TopSpin Club\",\"phoneNumbers\":{\"primaryPhone\":\"099306 63000\"},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"380015\",\"administrativeArea\":\"Gujarat\",\"locality\":\"Ahmedabad\",\"addressLines\":[\"101\\/3, DevArc Mall\",\"SG Highway\"]},\"websiteUri\":\"https:\\/\\/www.portray.work\\/topspinclub\",\"latlng\":{\"latitude\":23.0257952,\"longitude\":72.5075995},\"profile\":{\"description\":\"Announcing TopSpin Club. Ahmedabad's first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\n\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad's first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\n\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\n\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\"}},{\"name\":\"locations\\/3612416279359725233\",\"title\":\"NIC Gulf Software House LLC\",\"phoneNumbers\":{\"primaryPhone\":\"04 834 2243\",\"additionalPhones\":[\"************\"]},\"storefrontAddress\":{\"regionCode\":\"AE\",\"languageCode\":\"ar-Latn\",\"administrativeArea\":\"Dubai\",\"addressLines\":[\"Westburry office towers, Marasi Dr, Business Bay\",\"Suite 604\"]},\"websiteUri\":\"http:\\/\\/www.nicgulf.com\\/\",\"latlng\":{\"latitude\":25.1858249,\"longitude\":55.2748255},\"profile\":{\"description\":\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\"}}]}", 200, "success", 562, null, 1, "2025-07-22 07:06:37", "2025-07-22 07:06:37"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 680}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 706}, {"index": 25, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}], "start": **********.5769749, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 41.161, "width_percent": 2.48}, {"sql": "select * from `businesses` where (`location_name` = 'locations/13522179532217756997' and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["locations/13522179532217756997", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 512}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.586102, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ReviewHelper.php:411", "source": {"index": 20, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHelpers%2FReviewHelper.php&line=411", "ajax": false, "filename": "ReviewHelper.php", "line": "411"}, "connection": "review_master", "explain": null, "start_percent": 43.642, "width_percent": 2.185}, {"sql": "select count(*) as aggregate from `templates` where `business_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 434}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 512}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.591776, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ReviewHelper.php:434", "source": {"index": 16, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHelpers%2FReviewHelper.php&line=434", "ajax": false, "filename": "ReviewHelper.php", "line": "434"}, "connection": "review_master", "explain": null, "start_percent": 45.827, "width_percent": 1.87}, {"sql": "select * from `settings` where `business_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\SettingService.php", "line": 19}, {"index": 17, "namespace": null, "name": "app/Helpers/ReviewHelper.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Helpers\\ReviewHelper.php", "line": 438}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 512}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.597495, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "SettingService.php:19", "source": {"index": 16, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\SettingService.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FSettingService.php&line=19", "ajax": false, "filename": "SettingService.php", "line": "19"}, "connection": "review_master", "explain": null, "start_percent": 47.697, "width_percent": 1.772}, {"sql": "select * from `businesses` where `user_id` != 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 2124}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 514}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.603331, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:2124", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 2124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=2124", "ajax": false, "filename": "BusinessController.php", "line": "2124"}, "connection": "review_master", "explain": null, "start_percent": 49.469, "width_percent": 2.047}, {"sql": "select * from `business_accounts` where `business_google_id` is null or `id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 205}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 270}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 752}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.608154, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:205", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=205", "ajax": false, "filename": "GoogleBusinessService.php", "line": "205"}, "connection": "review_master", "explain": null, "start_percent": 51.516, "width_percent": 1.22}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 174}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 273}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 752}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.6128569, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:174", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=174", "ajax": false, "filename": "GoogleBusinessService.php", "line": "174"}, "connection": "review_master", "explain": null, "start_percent": 52.736, "width_percent": 1.535}, {"sql": "select * from `business_accounts` where `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 191}, {"index": 17, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 285}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 752}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.617949, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "GoogleBusinessService.php:191", "source": {"index": 16, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FGoogleBusinessService.php&line=191", "ajax": false, "filename": "GoogleBusinessService.php", "line": "191"}, "connection": "review_master", "explain": null, "start_percent": 54.272, "width_percent": 2.146}, {"sql": "insert into `external_api_logs` (`log_type`, `endpoint`, `request_payload`, `response_payload`, `status_code`, `status_message`, `duration_ms`, `business_id`, `user_id`, `updated_at`, `created_at`) values ('google_api', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng', null, '{\\\"locations\\\":[{\\\"name\\\":\\\"locations\\\\/13522179532217756997\\\",\\\"title\\\":\\\"IndiaNIC Infotech Limited (Udaipur)\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"079 6191 6000\\\",\\\"additionalPhones\\\":[\\\"0294 252 7111\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"313001\\\",\\\"administrativeArea\\\":\\\"Rajasthan\\\",\\\"locality\\\":\\\"Udaipur\\\",\\\"addressLines\\\":[\\\"24, <PERSON><PERSON><PERSON>\\\",\\\"Shakti Nagar\\\",\\\"above Andhra Bank\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.indianic.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":24.5848982,\\\"longitude\\\":73.6984058},\\\"profile\\\":{\\\"description\\\":\\\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\\\n\\\\nThis place represents the Udaipur Development Center.\\\"}},{\\\"name\\\":\\\"locations\\\\/2713736367053130525\\\",\\\"title\\\":\\\"TopSpin Club\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"099306 63000\\\"},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"IN\\\",\\\"languageCode\\\":\\\"en\\\",\\\"postalCode\\\":\\\"380015\\\",\\\"administrativeArea\\\":\\\"Gujarat\\\",\\\"locality\\\":\\\"Ahmedabad\\\",\\\"addressLines\\\":[\\\"101\\\\/3, DevArc Mall\\\",\\\"SG Highway\\\"]},\\\"websiteUri\\\":\\\"https:\\\\/\\\\/www.portray.work\\\\/topspinclub\\\",\\\"latlng\\\":{\\\"latitude\\\":23.0257952,\\\"longitude\\\":72.5075995},\\\"profile\\\":{\\\"description\\\":\\\"Announcing TopSpin Club. Ahmedabad\\'s first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\\\n\\\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad\\'s first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\\\n\\\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\\\n\\\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\\\"}},{\\\"name\\\":\\\"locations\\\\/3612416279359725233\\\",\\\"title\\\":\\\"NIC Gulf Software House LLC\\\",\\\"phoneNumbers\\\":{\\\"primaryPhone\\\":\\\"04 834 2243\\\",\\\"additionalPhones\\\":[\\\"************\\\"]},\\\"storefrontAddress\\\":{\\\"regionCode\\\":\\\"AE\\\",\\\"languageCode\\\":\\\"ar-Latn\\\",\\\"administrativeArea\\\":\\\"Dubai\\\",\\\"addressLines\\\":[\\\"Westburry office towers, Marasi Dr, Business Bay\\\",\\\"Suite 604\\\"]},\\\"websiteUri\\\":\\\"http:\\\\/\\\\/www.nicgulf.com\\\\/\\\",\\\"latlng\\\":{\\\"latitude\\\":25.1858249,\\\"longitude\\\":55.2748255},\\\"profile\\\":{\\\"description\\\":\\\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\\\"}}]}', 200, 'success', 576, null, 1, '2025-07-22 07:06:38', '2025-07-22 07:06:38')", "type": "query", "params": [], "bindings": ["google_api", "https://mybusinessaccountmanagement.googleapis.com/v1/accounts/110486499747300774507/locations?read_mask=name,title,websiteUri,phoneNumbers,profile,storefrontAddress,latlng", null, "{\"locations\":[{\"name\":\"locations\\/13522179532217756997\",\"title\":\"IndiaNIC Infotech Limited (Udaipur)\",\"phoneNumbers\":{\"primaryPhone\":\"079 6191 6000\",\"additionalPhones\":[\"0294 252 7111\"]},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"313001\",\"administrativeArea\":\"Rajasthan\",\"locality\":\"Udaipur\",\"addressLines\":[\"24, <PERSON><PERSON><PERSON> Bhawan\",\"Shakti Nagar\",\"above Andhra Bank\"]},\"websiteUri\":\"https:\\/\\/www.indianic.com\\/\",\"latlng\":{\"latitude\":24.5848982,\"longitude\":73.6984058},\"profile\":{\"description\":\"A successful offshore software application development company since 1998, providing a full range of IT services and solutions globally. IndiaNIC is not only a globally recognised IT company but also a family filled with talented experts that help global brands, enterprises, mid-size businesses or even startups with innovative solutions.\\n\\nThis place represents the Udaipur Development Center.\"}},{\"name\":\"locations\\/2713736367053130525\",\"title\":\"TopSpin Club\",\"phoneNumbers\":{\"primaryPhone\":\"099306 63000\"},\"storefrontAddress\":{\"regionCode\":\"IN\",\"languageCode\":\"en\",\"postalCode\":\"380015\",\"administrativeArea\":\"Gujarat\",\"locality\":\"Ahmedabad\",\"addressLines\":[\"101\\/3, DevArc Mall\",\"SG Highway\"]},\"websiteUri\":\"https:\\/\\/www.portray.work\\/topspinclub\",\"latlng\":{\"latitude\":23.0257952,\"longitude\":72.5075995},\"profile\":{\"description\":\"Announcing TopSpin Club. Ahmedabad's first of its kind activity focused club, with Indoor Sports, Fitness and Cafe.\\n\\nTopSpin is the community club that offers an array of mediums through which you can not only become a part of Ahmedabad's first of its kind community focused on three major aspects of sports and fitness enthusiasts.\\n\\nAt TopSpin, we aim to culminate the passion of developing a fit life, powered by sports and activities, topped up with nutritious food options.\\n\\nWe want to empower people who are keen on developing a healthy lifestyle by providing them easy to access infrastructure without long term commitments.\"}},{\"name\":\"locations\\/3612416279359725233\",\"title\":\"NIC Gulf Software House LLC\",\"phoneNumbers\":{\"primaryPhone\":\"04 834 2243\",\"additionalPhones\":[\"************\"]},\"storefrontAddress\":{\"regionCode\":\"AE\",\"languageCode\":\"ar-Latn\",\"administrativeArea\":\"Dubai\",\"addressLines\":[\"Westburry office towers, Marasi Dr, Business Bay\",\"Suite 604\"]},\"websiteUri\":\"http:\\/\\/www.nicgulf.com\\/\",\"latlng\":{\"latitude\":25.1858249,\"longitude\":55.2748255},\"profile\":{\"description\":\"Enterprise Software Development company with expertise on building digital solutions for leading brands across the globe, start ups and SMB.\"}}]}", 200, "success", 576, null, 1, "2025-07-22 07:06:38", "2025-07-22 07:06:38"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 680}, {"index": 24, "namespace": null, "name": "app/Services/GoogleBusinessService.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\GoogleBusinessService.php", "line": 747}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 755}], "start": **********.20493, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ExternalApiLogger.php:57", "source": {"index": 21, "namespace": null, "name": "app/Services/ExternalApiLogger.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Services\\ExternalApiLogger.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FServices%2FExternalApiLogger.php&line=57", "ajax": false, "filename": "ExternalApiLogger.php", "line": "57"}, "connection": "review_master", "explain": null, "start_percent": 56.417, "width_percent": 1.929}, {"sql": "select `location_name` from `associate_businesses` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1697}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 757}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 553}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.211635, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1697", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1697}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1697", "ajax": false, "filename": "BusinessController.php", "line": "1697"}, "connection": "review_master", "explain": null, "start_percent": 58.346, "width_percent": 6.142}, {"sql": "select * from `associate_businesses` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 555}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.220203, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:555", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=555", "ajax": false, "filename": "BusinessController.php", "line": "555"}, "connection": "review_master", "explain": null, "start_percent": 64.488, "width_percent": 1.693}, {"sql": "select * from `businesses` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 556}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2256572, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:556", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 556}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=556", "ajax": false, "filename": "BusinessController.php", "line": "556"}, "connection": "review_master", "explain": null, "start_percent": 66.181, "width_percent": 1.909}, {"sql": "select * from `businesses` where (`user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.232764, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:566", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=566", "ajax": false, "filename": "BusinessController.php", "line": "566"}, "connection": "review_master", "explain": null, "start_percent": 68.091, "width_percent": 1.654}, {"sql": "select * from `settings` where `settings`.`business_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.238543, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:566", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=566", "ajax": false, "filename": "BusinessController.php", "line": "566"}, "connection": "review_master", "explain": null, "start_percent": 69.744, "width_percent": 1.437}, {"sql": "select * from `businesses` where (`id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.244733, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:569", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=569", "ajax": false, "filename": "BusinessController.php", "line": "569"}, "connection": "review_master", "explain": null, "start_percent": 71.181, "width_percent": 1.949}, {"sql": "select * from `settings` where `settings`.`business_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.25287, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:569", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=569", "ajax": false, "filename": "BusinessController.php", "line": "569"}, "connection": "review_master", "explain": null, "start_percent": 73.13, "width_percent": 2.008}, {"sql": "select * from `templates` where `business_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1383}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 573}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.259499, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1383", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1383}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1383", "ajax": false, "filename": "BusinessController.php", "line": "1383"}, "connection": "review_master", "explain": null, "start_percent": 75.138, "width_percent": 2.303}, {"sql": "select count(*) as aggregate from `google_reviews` where (`location_id` = '13522179532217756997' and `account_id` = '110486499747300774507') and `parent_id` is null", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 582}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.269262, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:110", "source": {"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=110", "ajax": false, "filename": "GoogleReview.php", "line": "110"}, "connection": "review_master", "explain": null, "start_percent": 77.441, "width_percent": 2.008}, {"sql": "select\nstar_rating,\nCOUNT(*) as count,\nSUM(CASE\nWHEN UPPER(star_rating) = \"FIVE\" THEN 5\nWHEN UPPER(star_rating) = \"FOUR\" THEN 4\nWHEN UPPER(star_rating) = \"THREE\" THEN 3\nWHEN UPPER(star_rating) = \"TWO\" THEN 2\nWHEN UPPER(star_rating) = \"ONE\" THEN 1\nELSE 0\nEND) as total_numeric_rating\nfrom `google_reviews` where (`location_id` = '13522179532217756997' and `account_id` = '110486499747300774507') and `parent_id` is null and `star_rating` is not null group by `star_rating`", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 173}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 589}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.27841, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:173", "source": {"index": 15, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=173", "ajax": false, "filename": "GoogleReview.php", "line": "173"}, "connection": "review_master", "explain": null, "start_percent": 79.449, "width_percent": 4.626}, {"sql": "select count(*) as aggregate from `google_reviews` where (`location_id` = '13522179532217756997' and `account_id` = '110486499747300774507') and `parent_id` is null", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 227}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 592}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.289645, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:227", "source": {"index": 16, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 227}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=227", "ajax": false, "filename": "GoogleReview.php", "line": "227"}, "connection": "review_master", "explain": null, "start_percent": 84.075, "width_percent": 1.969}, {"sql": "select `parent`.`id`, `parent`.`created_at_google`, `reply`.`updated_at_google` from `google_reviews` as `parent` inner join `google_reviews` as `reply` on `parent`.`id` = `reply`.`parent_id` where `parent`.`location_id` = '13522179532217756997' and `parent`.`account_id` = '110486499747300774507' and `parent`.`parent_id` is null and `reply`.`parent_id` is not null and `parent`.`created_at_google` is not null and `reply`.`updated_at_google` is not null", "type": "query", "params": [], "bindings": ["13522179532217756997", "110486499747300774507"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 252}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 592}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.29543, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "GoogleReview.php:252", "source": {"index": 13, "namespace": null, "name": "app/Models/GoogleReview.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\GoogleReview.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=252", "ajax": false, "filename": "GoogleReview.php", "line": "252"}, "connection": "review_master", "explain": null, "start_percent": 86.043, "width_percent": 4.252}, {"sql": "select * from `google_reviews` where `location_id` = '13522179532217756997' and `parent_id` is null and `updated_at_google` between '2015-01-01 00:00:00' and '2025-07-31 23:59:59' order by `updated_at_google` asc", "type": "query", "params": [], "bindings": ["13522179532217756997", "2015-01-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1176}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 603}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.302568, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1176", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1176}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1176", "ajax": false, "filename": "BusinessController.php", "line": "1176"}, "connection": "review_master", "explain": null, "start_percent": 90.295, "width_percent": 2.362}, {"sql": "select * from `google_reviews` where `location_id` = '13522179532217756997' and `parent_id` is null and `updated_at_google` between '2015-01-01 00:00:00' and '2025-07-31 23:59:59' order by `updated_at_google` asc", "type": "query", "params": [], "bindings": ["13522179532217756997", "2015-01-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1266}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 604}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.310508, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1266", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1266}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1266", "ajax": false, "filename": "BusinessController.php", "line": "1266"}, "connection": "review_master", "explain": null, "start_percent": 92.657, "width_percent": 2.087}, {"sql": "select * from `google_reviews` where `location_id` = '13522179532217756997' and `parent_id` is not null and `updated_at_google` between '2015-01-01 00:00:00' and '2025-07-31 23:59:59' order by `updated_at_google` asc", "type": "query", "params": [], "bindings": ["13522179532217756997", "2015-01-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1273}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 604}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.318024, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:1273", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 1273}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=1273", "ajax": false, "filename": "BusinessController.php", "line": "1273"}, "connection": "review_master", "explain": null, "start_percent": 94.744, "width_percent": 1.811}, {"sql": "select * from `templates` where `business_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 608}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.32509, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BusinessController.php:608", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/BusinessController.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Http\\Controllers\\BusinessController.php", "line": 608}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=608", "ajax": false, "filename": "BusinessController.php", "line": "608"}, "connection": "review_master", "explain": null, "start_percent": 96.555, "width_percent": 1.791}, {"sql": "select exists(select * from `subscriptions` where `subscriptions`.`user_id` = 1 and `subscriptions`.`user_id` is not null and `status` = 'ACTIVE' and `expiry_date` >= '2025-07-22 07:06:38') as `exists`", "type": "query", "params": [], "bindings": [1, "ACTIVE", "2025-07-22 07:06:38"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\User.php", "line": 74}, {"index": 15, "namespace": "view", "name": "business-dashboard", "file": "C:\\wamp64\\www\\reviewbiz\\resources\\views/business-dashboard.blade.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\reviewbiz\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.361113, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "User.php:74", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\wamp64\\www\\reviewbiz\\app\\Models\\User.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=74", "ajax": false, "filename": "User.php", "line": "74"}, "connection": "review_master", "explain": null, "start_percent": 98.346, "width_percent": 1.654}]}, "models": {"data": {"App\\Models\\GoogleReview": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FGoogleReview.php&line=1", "ajax": false, "filename": "GoogleReview.php", "line": "?"}}, "App\\Models\\Template": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}, "App\\Models\\BusinessAccount": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusinessAccount.php&line=1", "ajax": false, "filename": "BusinessAccount.php", "line": "?"}}, "App\\Models\\Business": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FBusiness.php&line=1", "ajax": false, "filename": "Business.php", "line": "?"}}, "App\\Models\\Setting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\AssociateBusiness": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FAssociateBusiness.php&line=1", "ajax": false, "filename": "AssociateBusiness.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}}, "count": 73, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/business-dashboard", "action_name": "business.dashboard", "controller_action": "App\\Http\\Controllers\\BusinessController@businessDashboard", "uri": "GET business-dashboard", "controller": "App\\Http\\Controllers\\BusinessController@businessDashboard<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=438\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Freviewbiz%2Fapp%2FHttp%2FControllers%2FBusinessController.php&line=438\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/BusinessController.php:438-643</a>", "middleware": "web, auth", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f731f93-e227-435c-9369-113ff1db4e67\" target=\"_blank\">View in Telescope</a>", "duration": "3.32s", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1693650470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1693650470\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1871551027 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1871551027\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1683154067 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/business-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1694 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im1ySEFHV2hQVDlGcjRqUSs4bHlUclE9PSIsInZhbHVlIjoibDlUcW14ZXYzYU04Sk5hNkhNc3FsTjNvbmVvdzI3ZndKb1RmQzl5dHhjUnB2N1N2ek5PZDhVNUtsVmFSR3NsS3VPcTAwYlhIRlc1YkhIVlllUUE3K1VlSEZEWGd1RG9oU2c2Q0xpcS9kZE5ScHpaZlpaM3FMVjI0dlFKQWRWTTMzdEliSHhVaXB2L0p2OTNLWVNodEpRN1hiNmowQ0RQNkloTExmdlAvM1poS051Qm1oTVAvSDJpcFI2LzJ6eWMxWEpVQU1uTlgyVFJ4eWw4OWpROURaMkx3MmN2TE5id0RRZi9oYnJOZ2hOZz0iLCJtYWMiOiIzNGFiOWI5ZjdiYzFjZWJmZjZlMDI2NzFjY2FhYjhlZDIzOTQyNjNjZjAzNWU0YjBlY2VkYjVhZDkxMmFjOTMxIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InhsS1ErNFB2VC9UOVE1R1ovemFtVmc9PSIsInZhbHVlIjoieStNUXZDUDNZU3RqRHdvKzFUT25jSHg4dUVCSjFwUmllU25qNXFiV0F1OW84OTBCNU9CdTE2MTVMNUFvVHB2TVlTS2NSQjJMTkduZE0wU3I0cTJ4Tmp0UisxU1VqNlRqR0F2cnJEM3ZNZGQzSTFUMEtaUWFqUDNMQkNqTkE5MWpoRUN1YkhGOEpDWmthUTJ4RWlVeUl3PT0iLCJtYWMiOiIzZTI3OTliODc5NGUyZTBhMzcwYzdmZjA1MjM1MDVjY2RhNjcyNzMyZTk2NjRjYTczYjE4YmZhMGMwNTQ3YzNlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlJYYWNReVhHMkJFYTZVRnc2bjNWNXc9PSIsInZhbHVlIjoiZ1JybkRwVkMxY2NjaXJWWUtpQmIxMncrdnc3UEgvUnFMVUlNajdpbFN4L2I4NTV6SGU4L25oTlY1MEUyZzN6WC9Eb2gvUUt4ZTJkZXJKSkEwemRrakRTcGxRRHo3cklaeHhqZkdpMnFTaldEOFhhbUxwNHozSmx2NXRpRTV1d1giLCJtYWMiOiI0YjgwOGE4OGUwZTAyZTMzNmRlYzkyNGI4YWUzYTNjYTE2NzI4YjI0MzRkMDA3MTEwZjE3NTBiMWY5YTVlZGJkIiwidGFnIjoiIn0%3D; reviewmasterai_session=eyJpdiI6InRCU1kxaklJNmNyaXhiRm5nTDZXdFE9PSIsInZhbHVlIjoiV096NDdqMXpNSjgxMFFVWFYyUUJCVGxDSkxJYjNSWVMzQkNsOGhLU0MrdnhIUkdBbG0vUU1lQmFycUdSOVNJYXhmZVNteVBmKzRRa0cwYURVNmdSRlIzcU9jc0ZHdlphb3VZdFk3TWYrUTcwUmpTa3UrT3RhaFpaTkFkMEs1ZFkiLCJtYWMiOiI3ZjVmMjY5NGFlMmI0NDA5YmZmYWFkMWQzMDkzNDgzNmU1N2YwOTk2YzgyM2FhMjRmNmI4MDhkNzgyMDE1ZDQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683154067\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-85249971 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|pmk0ZPcwNvdcyZh8txRDWRjQhRBItD6bQKyiIIwPOGZVYyePFjEhKDNxjvoa|$2y$12$quO4cLolCFDH2wXsSke/u.BarK4ZwLV6ACtlaLyMBFl5.eec7Yrdi</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|d4gsFumgVybRUrjg0Rh5kBgW54R5vfpkeMJcrWrT7FkGNX8F4D9Tf62Ue7fx|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Mro0TUh9EIuWDDngJ39D9c6rZTK0o3bUbLsYoMk8</span>\"\n  \"<span class=sf-dump-key>reviewmasterai_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Vori1DaJ0hdCuHyNJxvvhkgKiqcBYo3aYugM2Yh4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85249971\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-605366824 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 07:06:38 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605366824\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1524729037 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Mro0TUh9EIuWDDngJ39D9c6rZTK0o3bUbLsYoMk8</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524729037\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/business-dashboard", "action_name": "business.dashboard", "controller_action": "App\\Http\\Controllers\\BusinessController@businessDashboard"}, "badge": null}}