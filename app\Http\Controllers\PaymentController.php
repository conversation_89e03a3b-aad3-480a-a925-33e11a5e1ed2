<?php

namespace App\Http\Controllers;

use App\Mail\SubscriptionPurchaseNotification;
use App\Models\Business;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\CouponUsageLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Checkout\Session as StripeSession;
use Razorpay\Api\Api as RazorpayApi;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class PaymentController extends Controller
{
    /**
     * Redirect to Stripe Customer Portal
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function billingPortal()
    {
        try {
            $user = Auth::user();
            
            if (!$user->stripe_customer_id) {
                return redirect()->back()->with('error', 'No billing information available.');
            }
        
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
        
        
            $session = \Stripe\BillingPortal\Session::create([
                'customer' => $user->stripe_customer_id,
                'return_url' => route('business.dashboard'),
            ]);
            
            return redirect()->to($session->url);
        } catch (\Exception $e) {
            \Log::error('Billing portal creation failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Unable to access billing portal. Please try again later.');
        }
    }
    
    public function processPayment(Request $request)
    {

        $request->validate([
            //'business_id' => 'nullable|exists:businesses,id',
            'plan_id' => 'required|exists:plans,id',
            'payment_type' => 'required|in:ONE_TIME_PAYMENT,COUPON_CODE',
            'gateway' => 'required|in:STRIPE,RAZORPAY',
            'billing_cycle' => 'nullable|in:monthly,annual',
        ]);

        $user = Auth::user();
        $business = null;
        if ($request->buisness_id) {
            $business = Business::findOrFail($request->business_id);
        }
        $plan = Plan::findOrFail($request->plan_id);
        $isAnnual = $request->billing_cycle === 'annual';
        $amount = $plan->getEffectivePrice($isAnnual);

        // Calculate tax
        $taxAmount = 0;
        if ($plan->tax_percentage && $plan->tax_percentage > 0) {
            $taxAmount = ($amount * $plan->tax_percentage) / 100;
        }
        $finalAmount = $amount + $taxAmount;

        $payment = Payment::create([
            'user_id' => $user->id,
            'payment_type' => $request->payment_type,
            'gateway_name' => $request->gateway,
            'amount' => $finalAmount,
            'original_amount' => $amount,
            'tax_amount' => $taxAmount,
            'currency' => $plan->currency,
            'payment_status' => 'PENDING',
            'billing_cycle' => $isAnnual ? 'annual' : 'monthly',
            'payment_date' => now(),
            'metadata' => [
                'billing_cycle' => $isAnnual ? 'annual' : 'monthly',
                'tax_percentage' => $plan->tax_percentage ?? 0,
                'plan_name' => $plan->name,
            ]
        ]);

        if ($request->gateway === 'STRIPE') {
            return $this->processStripePayment($user, $business, $plan, $payment);
        } else {
            return $this->processRazorpayPayment($user, $business, $plan, $payment);
        }
    }

    private function processStripePayment($user, $business, $plan, $payment)
    {
        \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
        
        // Create or retrieve Stripe customer
        $customer = null;
        if ($user->stripe_customer_id) {
            try {
                $customer = \Stripe\Customer::retrieve($user->stripe_customer_id);
            } catch (\Exception $e) {
                // Customer not found, will create a new one
                $customer = null;
            }
        }
        
        if (!$customer) {
            $customer = \Stripe\Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);
            
            // Save the customer ID to the user
            $user->update(['stripe_customer_id' => $customer->id]);
        }
        
        $success_url = route('payment.success') . '?session_id={CHECKOUT_SESSION_ID}&payment_id=' . $payment->id . '&business_id=1' . '&plan_id=1';
        $session = StripeSession::create([
            'customer' => $customer->id,
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => 'inr',
                    'product_data' => [
                        'name' => $plan->name,
                    ],
                    'unit_amount' => round($payment->amount) * 100,
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => $success_url,
            'cancel_url' => route('payment.cancel'),
            'metadata' => [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'business_id' => 1,
                'plan_id' => 1,
            ],
        ]);
        
        return redirect()->to($session->url);
    }

    protected function processRazorpayPayment($user, $business, $plan, $payment)
    {
        $api = new RazorpayApi(env('RAZORPAY_KEY'), env('RAZORPAY_SECRET'));

        // Check if user already has a Razorpay customer ID
        if ($user->razorpay_customer_id) {
            $customerId = $user->razorpay_customer_id;
        } else {
            $customerData = [
                'name' => $user->name,
                'email' => $user->email,
            ];

            try {
                $razorpayCustomer = $api->customer->create($customerData);
                $customerId = $razorpayCustomer->id;

                $user->update([
                    'razorpay_customer_id' => $customerId,
                ]);
            } catch (\Razorpay\Api\Errors\BadRequestError $e) {
                if (strpos($e->getMessage(), 'Customer already exists') !== false) {
                    $customers = $api->customer->all(['email' => $user->email]);
                    if ($customers->count > 0) {
                        $customerId = $customers->items[0]->id;
                        $user->update([
                            'razorpay_customer_id' => $customerId,
                        ]);
                    } else {
                        \Log::error('Razorpay customer fetch failed: ' . $e->getMessage());
                        return redirect()->route('dashboard')->with('error', 'Failed to process payment. Please try again.');
                    }
                } else {
                    \Log::error('Razorpay customer creation failed: ' . $e->getMessage());
                    return redirect()->route('dashboard')->with('error', 'Failed to process payment. Please try again.');
                }
            }
        }

        $orderData = [
            'amount' => $payment->amount * 100,
            'currency' => 'INR',
            'customer_id' => $customerId,
            'payment_capture' => 1,
        ];

        $order = $api->order->create($orderData);

        $payment->update([
            'order_id' => $order->id,
        ]);

        return view('payment.razorpay', [
            'user' => $user,
            'order_id' => $order->id,
            'amount' => $payment->amount * 100,
            'razorpay_key' => env('RAZORPAY_KEY'),
            'payment_id' => $payment->id,
            'business_id' => $business->id,
            'plan_id' => $plan->id,
            'name' => $user->name,
            'email' => $user->email,
        ]);
    }

    public function paymentSuccess(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            \Log::error('User not authenticated in payment success');
            return redirect()->route('login')->with('error', 'Please log in to complete the payment.');
        }

        if (!$request->payment_id || !$request->business_id || !$request->plan_id) {
            \Log::error('Missing required parameters in payment success', $request->all());
            return redirect()->route('dashboard')->with('error', 'Invalid payment data. Please try again.');
        }


        $payment = Payment::findOrFail($request->payment_id);
        // $business = Business::findOrFail($request->business_id);
        $plan = Plan::findOrFail($request->plan_id);



        if (!$payment || !$plan) {
            \Log::error('Invalid payment, business, or plan ID', [
                'payment_id' => $request->payment_id,
                'business_id' => 1,
                'plan_id' => $request->plan_id,
            ]);
            return redirect()->route('dashboard')->with('error', 'Payment data not found. Please try again.');
        }

        if ($payment->gateway_name === 'RAZORPAY') {
            $api = new RazorpayApi(env('RAZORPAY_KEY'), env('RAZORPAY_SECRET'));
            $razorpayPayment = $api->payment->fetch($request->razorpay_payment_id);

            $payment->update([
                'transaction_id' => $razorpayPayment->id,
                'payment_status' => 'SUCCESS',
                'payment_method' => $razorpayPayment->method,
            ]);

            // Update Razorpay payment method details
            $user->update([
                'razorpay_payment_method' => $razorpayPayment->method,
                'razorpay_card_last_four' => $razorpayPayment->method === 'card' ? $razorpayPayment->card->last4 : null,
            ]);            
        } else if ($payment->gateway_name === 'STRIPE') {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
            $session = StripeSession::retrieve($request->session_id);

            $payment->update([
                'transaction_id' => $session->payment_intent,
                'payment_status' => 'SUCCESS',
                'payment_method' => $session->payment_method_types[0],
            ]);

            $paymentIntent = \Stripe\PaymentIntent::retrieve($session->payment_intent);
            if ($paymentIntent->payment_method) {
                $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentIntent->payment_method);
                if ($paymentMethod->type === 'card') {
                    $user->update([
                        'card_brand' => $paymentMethod->card->brand,
                        'card_last_four' => $paymentMethod->card->last4,
                    ]);
                }
            }
        }

        // Calculate expiry date based on billing cycle
        $isAnnual = $payment->billing_cycle === 'annual';
        $durationDays = $isAnnual ? 365 : $plan->duration_days;

        // Check if user already has an active subscription to prevent duplicates within an hour
        $subscription = Subscription::where('user_id', $user->id)
            ->where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->where('created_at', '>=', Carbon::now()->subHour())
            ->first();

        if (!$subscription) {
            // Cancel any existing active subscription            
            Subscription::where('user_id', $user->id)
                ->where('status', 'ACTIVE')
                ->where('expiry_date', '>=', now())
                ->update([
                    'status' => 'CANCELLED',
                    'end_date' => now(),
            ]);

            // Create subscription
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'business_id' => 1,
                'payment_id' => $payment->id,
                'status' => 'ACTIVE',
                'start_date' => now(),
                'expiry_date' => Carbon::now()->addDays($durationDays),
                'features' => $plan->features, // Copy plan features to subscription
            ]);            
        }

        // Update coupon usage log if coupon was used
        if ($payment->coupon_code) {
            $couponUsageLog = CouponUsageLog::where('payment_id', $payment->id)
                ->where('user_id', $user->id)
                ->first();

            if ($couponUsageLog) {
                $couponUsageLog->update([
                    'subscription_id' => $subscription->id,
                    'status' => 'used'
                ]);
            }
        }

        Mail::to($user->email)->send(new SubscriptionPurchaseNotification(
            $user,
            $plan
        ));

        return redirect()->route('business.setup')->with('success', 'Payment successful! Your subscription is now active.');
        // return redirect()->route('dashboard')->with('success', 'Payment successful! Your subscription is now active.');
    }

    public function paymentCancel()
    {
        return redirect()->route('dashboard')->with('error', 'Payment cancelled. Please try again.');
    }
}
