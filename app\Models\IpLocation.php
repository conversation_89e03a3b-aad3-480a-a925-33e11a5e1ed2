<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class IpLocation extends Model
{
    protected $table = 'ip_locations';

    protected $fillable = [
        'ip_address',
        'ip_version',
        'latitude',
        'longitude',
        'country_name',
        'country_code',
        'capital',
        'phone_codes',
        'time_zones',
        'city_name',
        'region_name',
        'continent',
        'continent_code',
        'currencies',
        'primary_currency',
        'languages',
        'asn',
        'asn_organization'
    ];

    protected $casts = [
        'phone_codes' => 'array',
        'time_zones' => 'array',
        'currencies' => 'array',
        'languages' => 'array',
        'latitude' => 'float',
        'longitude' => 'float',
        'ip_version' => 'integer'
    ];

    /**
     * Find IP location by IP address
     */
    public static function findByIp($ipAddress)
    {
        return static::where('ip_address', $ipAddress)->first();
    }

    /**
     * Get the primary currency for the location
     */
    public function getPrimaryCurrencyAttribute($value)
    {
        if ($value) {
            return $value;
        }

        // If primary_currency is not set, get the first currency from currencies array
        if ($this->currencies && is_array($this->currencies) && count($this->currencies) > 0) {
            return $this->currencies[0];
        }

        return null;
    }
}
