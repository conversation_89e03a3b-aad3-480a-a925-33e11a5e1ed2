<?php

namespace App\Services;

use App\Models\ExternalApiLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class ExternalApiLogger
{
    /**
     * Log a Google API call
     */
    public static function logGoogleApi($endpoint, $request, $response, $statusCode = null, $statusMessage = null, $durationMs = null)
    {
        try {
            return self::log('google_api', $endpoint, $request, $response, $statusCode, $statusMessage, $durationMs);
        } catch (\Exception $e) {
            Log::error('Failed to log Google API call:', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);            
        }
        return true;
    }

    /**
     * Log an AI call
     */
    public static function logAi($endpoint, $request, $response, $statusCode = null, $statusMessage = null, $durationMs = null)
    {
        try {
            return self::log('ai', $endpoint, $request, $response, $statusCode, $statusMessage, $durationMs);
        } catch (\Exception $e) {
            Log::error('Failed to log AI call:', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);            
        }
        return true;
    }

    /**
     * Core log method
     */
    protected static function log($type, $endpoint, $request, $response, $statusCode, $statusMessage, $durationMs)
    {
        try {
            // Mask sensitive data if needed
            $maskedRequest = self::maskSensitiveData($request);
            $maskedResponse = self::maskSensitiveData($response);

            $userId = Auth::id() ?? null;
            $businessId = Session::has('business_id') ? Session::get('business_id') : null;

            return ExternalApiLog::create([
                'log_type'        => $type,
                'endpoint'        => $endpoint,
                'method'          => request()->method() ?? 'GET',
                'request_payload' => is_array($maskedRequest) ? $maskedRequest : json_decode($maskedRequest, true),
                'response_payload'=> is_array($maskedResponse) ? $maskedResponse : json_decode($maskedResponse, true),
                'request_headers' => self::getSafeHeaders(request()->headers->all() ?? []),
                'response_headers'=> [], // Will be populated if available
                'status_code'     => $statusCode,
                'status_message'  => $statusMessage,
                'duration_ms'     => $durationMs,
                'business_id'     => $businessId,
                'user_id'         => $userId,
                'ip_address'      => request()->ip(),
                'user_agent'      => request()->userAgent(),
                'session_id'      => Session::getId(),
                'error_message'   => $statusCode >= 400 ? $statusMessage : null,
                'is_retry'        => false, // Will be set by retry logic
                'retry_count'     => 0,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log External API store:', [
                'type' => $type,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
        }
        return true;
    }

    /**
     * Mask sensitive data in request/response
     */
    protected static function maskSensitiveData($data)
    {
        if (is_string($data)) {
            $data = json_decode($data, true);
        }

        if (!is_array($data)) {
            return $data;
        }

        $sensitiveKeys = [
            'password', 'token', 'secret', 'key', 'authorization', 'auth',
            'client_secret', 'api_key', 'access_token', 'refresh_token',
            'private_key', 'credential', 'credentials'
        ];

        return self::maskArrayKeys($data, $sensitiveKeys);
    }

    /**
     * Recursively mask sensitive keys in array
     */
    protected static function maskArrayKeys($data, $sensitiveKeys)
    {
        if (!is_array($data)) {
            return $data;
        }

        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);

            // Check if key contains sensitive information
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (strpos($lowerKey, $sensitiveKey) !== false) {
                    $data[$key] = '***MASKED***';
                    break;
                }
            }

            // Recursively process nested arrays
            if (is_array($value)) {
                $data[$key] = self::maskArrayKeys($value, $sensitiveKeys);
            }
        }

        return $data;
    }

    /**
     * Get safe headers (excluding sensitive ones)
     */
    protected static function getSafeHeaders($headers)
    {
        $sensitiveHeaders = [
            'authorization', 'cookie', 'x-api-key', 'x-auth-token',
            'x-access-token', 'x-csrf-token', 'x-xsrf-token'
        ];

        $safeHeaders = [];

        foreach ($headers as $key => $value) {
            $lowerKey = strtolower($key);

            $isSensitive = false;
            foreach ($sensitiveHeaders as $sensitiveHeader) {
                if (strpos($lowerKey, $sensitiveHeader) !== false) {
                    $safeHeaders[$key] = ['***MASKED***'];
                    $isSensitive = true;
                    break;
                }
            }

            if (!$isSensitive) {
                $safeHeaders[$key] = is_array($value) ? $value : [$value];
            }
        }

        return $safeHeaders;
    }
}
