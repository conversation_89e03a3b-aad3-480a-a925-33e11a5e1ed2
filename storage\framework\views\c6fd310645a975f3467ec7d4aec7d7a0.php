<?php

$lastSegment = collect(request()->segments())->last();
?>

<?php if($lastSegment == 'business-dashboard' || $lastSegment == 'billing-details'): ?>
<div id="sideMenuBar" class="sidemenubar fixed w-60 bg-white shadow-lg transition-transform duration-300 ease-in-out">
    <div class="sidebarnav p-4 h-[calc(100%-108px)] pt-[env(safe-area-inset-top)] pb-[env(safe-area-inset-bottom)] overflow-auto">
        <span class="text-gray-500 mb-2 block uppercase text-sm" id="changeBusinessName">NIC Gulf Software House LLC</span>
        <?php if($lastSegment == 'business-dashboard'): ?>
        <nav id="tabScrollContainer2"
            class="flex sm:px-0 overflow-x-auto overflow-y-hidden scrollbar-hide transition-all duration-300">
            <ul class="mb-4">
                <li class="py-2 w-full">
                    <button id="reviewsTabBtnMobile" class="tab-btn text-gray-900 hover:text-indigo-500 border-0 text-sm" data-tab="reviewsTab">
                        <i class="fas fa-star mr-1"></i> Reviews
                        <span class="text-xs rounded-full bg-red-600 text-white px-2 py-1"><?php echo e($totalReviews); ?></span>
                    </button>
                </li>

                <li class="py-2 w-full">
                    <button id="analyticsTabBtnMobile" class="tab-btn text-gray-900 hover:text-indigo-500 text-sm" data-tab="analyticsTab">
                        <i class="fas fa-chart-line mr-1"></i> Analytics
                    </button>
                </li>

                <li class="py-2 w-full">
                    <button id="templateTabBtnMobile" class="tab-btn text-gray-900 hover:text-indigo-500 text-sm" data-tab="templatesTab">
                        <i class="fas fa-file-alt mr-1"></i> Templates
                    </button>
                </li>

                <li class="py-2 w-full">
                    <button id="settingsTabBtnMobile" class="tab-btn text-gray-900 hover:text-indigo-500 text-sm" data-tab="settingsTab" <?php if(isset($business)): ?> data-business-id="<?php echo e($business['id']); ?>" <?php endif; ?>>
                        <i class="fas fa-cog mr-1"></i> Settings
                    </button>
                </li>
            </ul>
        </nav>
        <?php endif; ?>
        <span class="text-gray-500 mb-2 block uppercase text-sm">ACCOUNT</span>
        <ul class="mb-4">
            <li class="py-2 w-full">
                <?php if(!$teamMembers || count($teamMembers) == 0): ?>
                <a href="<?php echo e(route('team.index', [$locationId])); ?>" class="text-gray-900 hover:text-indigo-500 text-sm">
                    <i class="fas fa-user-cog mr-2"></i> Account
                </a>
                <?php endif; ?>
            </li>
            <li class="py-2 w-full"><a href="<?php echo e(route('activity.dashboard')); ?>" class="text-gray-900 hover:text-indigo-500 text-sm"><i class="fas fa-chart-line mr-2"></i> Activities </a></li>
            <li class="py-2 w-full"><a href="" id="editProfileBtnMobile" class="text-gray-900 hover:text-indigo-500 text-sm"><i class="fas fa-user-edit mr-2"></i> Edit Profile </a></li>

            <li class="py-2 w-full">
                <a href="<?php echo e(route('billing.details')); ?>" class="text-gray-900 hover:text-indigo-500 text-sm">
                    <i class="fas fa-credit-card mr-2"></i> Subscription
                </a>
            </li>

            <li class="py-2 w-full"><a href="<?php echo e(route('logout')); ?>" class="text-red-600 hover:text-red-800 text-sm"><i class="fas fa-sign-out-alt mr-2"></i> Logout</a></li>
        </ul>
    </div>
    <div class="sidebarfooter flex flex-col gap-2 justify-center items-center p-4 border-t border-gray-200">
        <div class="flex flex-col gap-2 md:gap-4 items-center w-full">
            <input type="hidden" id="business_contact" value="<?php echo e($business['primary_phone']); ?>">
            <input type="hidden" id="business_name" value="<?php echo e($business['title']); ?>">

            <select class="form-control border text-xs sm:text-sm w-full border-gray-500 rounded-md px-2 md:px-3 py-1 md:py-2 focus:outline-0 truncate  getSelectBusiness" id="business_id_mobile" name="business_id" required data-dashboard-url="<?php echo e(route('business.dashboard')); ?>">
                <?php $__currentLoopData = $connectedBusinesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $connectedBusiness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($connectedBusiness->id); ?>" <?php echo e($connectedBusiness->id == $business['id'] ? 'selected' : ''); ?>><?php echo e($connectedBusiness->title); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>

            <?php if(!$teamMembers || count($teamMembers) == 0): ?>
            <button type="button" class="w-full inline-flex justify-center items-center gap-1 rounded-md border border-transparent bg-indigo-600 py-1 px-2 md:px-4 leading-loose text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" id="connectBusinessMobile" title="Connect Business">
                <i class="fa-solid fa-link block"></i>
                <span>Connect Business</span>
            </button>
            <?php endif; ?>
        </div>

    </div>
</div>
<?php endif; ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partials/sidebar.blade.php ENDPATH**/ ?>