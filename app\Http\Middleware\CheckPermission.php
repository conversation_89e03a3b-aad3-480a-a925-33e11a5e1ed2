<?php

namespace App\Http\Middleware;

use App\Models\TeamMember;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
        // Get the business account ID from the route parameters
        $businessAccountId = $request->route('businessAccountId') ?? $request->route('business_account_id');
        
        if (!$businessAccountId) {
            // Try to get it from other route parameters that might contain a model with business_account_id
            foreach ($request->route()->parameters() as $param) {
                if (is_object($param) && property_exists($param, 'business_account_id')) {
                    $businessAccountId = $param->business_account_id;
                    break;
                }
            }
        }
        
        if (!$businessAccountId) {
            return redirect()->route('business.dashboard')
                ->with('error', 'Unable to determine business account for this action.');
        }
        
        // Check if user is the owner of the business account
        $user = Auth::user();
        $businessAccount = \App\Models\BusinessAccount::find($businessAccountId);
        
        if (!$businessAccount) {
            return redirect()->route('business.dashboard')
                ->with('error', 'Business account not found.');
        }
        
        // If user is the owner, they have all permissions
        if ($user->id === $businessAccount->user_id) {
            return $next($request);
        }
        
        // Check if user is a team member with the required permission
        $teamMember = TeamMember::where('user_id', $user->id)
            ->where('business_account_id', $businessAccountId)
            ->where('status', 'active')
            ->first();
        
        if (!$teamMember) {
            return redirect()->route('business.dashboard')
                ->with('error', 'You do not have access to this business account.');
        }
        
        // Admin role has all permissions
        if ($teamMember->isAdmin()) {
            return $next($request);
        }
        
        // Check specific permission
        if (!$teamMember->hasPermission($permission)) {
            return redirect()->back()
                ->with('error', 'You do not have permission to perform this action.');
        }
        
        return $next($request);
    }
}
