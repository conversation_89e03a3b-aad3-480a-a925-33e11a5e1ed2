@extends('layouts.app')

@section('content')
@php $locationId = explode("/", $business['location_name'])[1]; @endphp
<div class="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8 bg-white">
    <div class="mb-4 flex items-center">
        <a href="{{ route('business.dashboard') }}" class="flex items-center px-2 py-1 md:px-4 md:py-2 bg-white-600 text-indigo-600 border ring-2 ring-indigo-600 rounded-md text-sm font-medium hover:text-white hover:bg-indigo-600">
            <i class="fas fa-arrow-left mr-2"></i> Back <span class="hidden md:block ml-1">to Business</span>
        </a>
    </div>

    <div class="mb-4 p-3 bg-gray-50 rounded-md">
        <label for="businessSelectorData" class="block text-sm font-medium text-gray-700 mb-1">Select Business:</label>
        <select id="businessSelectorData" class="w-full border text-sm border-gray-300 rounded-md px-3 py-2 bg-white">
            @foreach($connectedBusinesses as $connectedBusiness)
            <option value="{{ $connectedBusiness->id }}" {{ $connectedBusiness->id == $business['id'] ? 'selected' : '' }} data-locationName="{{ $connectedBusiness->location_name }}">{{ $connectedBusiness->title }}</option>
            @endforeach
        </select>
        <p class="text-xs text-gray-500 mt-1">Team members are assigned roles for each business separately</p>
    </div>

    <div class="pt-2 pb-4 mb-4">
        <div class="flex justify-between flex-wrap items-center mb-4">
            <h3 class="text-md md:text-lg font-semibold">Team Management</h3>
            <div class="flex gap-2 justify-end">
                <!-- <button id="addTeamMemberToBusinessBtn" title="Add to Business" aria-label="Add to Business" class="flex items-center gap-1 px-2 md:px-3 py-2 bg-indigo-100 text-indigo-700 rounded-md text-sm font-medium hover:bg-indigo-200">
                    <i class="fas fa-user-plus"></i> <span class="hidden md:block">Add to Business</span>
                </button> -->
                <button id="inviteTeamMemberBtn" title="Invite New" aria-label="Invite New" class="flex items-center gap-1 px-2 md:px-3 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700">
                    <i class="fas fa-envelope"></i> <span class="hidden md:block">Invite New</span>
                </button>
            </div>
        </div>

        <div class="mb-4">
            <p class="text-sm text-gray-600">
                Manage your team members and their permissions. Team members can be assigned different roles for each business separately.
            </p>
        </div>



        <!-- Team Members List -->
        <div class="mt-6">
            <h4 class="text-md font-medium mb-3">Team Members</h4>

            <div class="bg-gray-50 rounded-md p-3 mb-4">
                <div class="flex items-center justify-between flex-wrap gap-2">
                    <div class="flex items-center w-full">
                        <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-800 font-semibold mr-3">
                            {{ substr($business->user->name, 0, 2) }}
                        </div>
                        <div class="flex-col w-[calc(100%-40px)]">
                            <div class="flex items-center gap-2 flex-wrap justify-between">
                                <div class="font-medium">{{ $business->user->name }}</div>
                                <div>
                                    <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs font-medium">Owner</span>
                                </div>
                            </div>
                            <div class="text-sm text-gray-500">{{ $business->user->email }}</div>
                        </div>
                    </div>

                </div>
            </div>

            @foreach ($teamUser as $teamMember)
            <div class="bg-gray-50 rounded-md p-3 mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-800 font-semibold mr-3">
                            {{ substr($teamMember->user->name, 0, 2) }}
                        </div>
                        <div>
                            <div class="font-medium">{{ $teamMember->user->name }}</div>
                            <div class="text-sm text-gray-500">{{ $teamMember->user->email }}</div>
                        </div>
                    </div>
                    @php
                    $permission = $teamMember->permissions->where('is_granted', true)->pluck('permission_name')->first();
                    @endphp

                    <div>
                        <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs font-medium">{{ $permission }} </span>
                        <div class="relative inline-block text-left">
                            <button class="text-gray-500 hover:text-gray-700 focus:outline-none tooltip-trigger" data-member-id="{{ $teamMember->id }}">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="tooltip-menu origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-10 hidden" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                                <div class="py-1" role="none">

                                    <button class="editPermissions text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900" role="menuitem" tabindex="-1" data-member-id="{{ $teamMember->id }}" data-user-id="{{ $teamMember->id }}">
                                        <i class="fas fa-edit mr-3 text-gray-500 group-hover:text-gray-600"></i>
                                        Edit Permissions
                                    </button>

                                    @if($permission !== 'manager')
                                    <button class="removeMember text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900" role="menuitem" tabindex="-1" data-member-id="{{ $teamMember->id }}" data-user-id="{{ $teamMember->id }}">
                                        <i class="fas fa-user-times mr-3 text-gray-500 group-hover:text-gray-600"></i>
                                        Remove Member
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach

            <div id="teamMembersList">
                @if ($pendingInvitations)
                <div class="text-sm text-gray-500 italic mb-4">No team members have access to this business yet</div>
                @endif
                <!-- <button id="addTeamMemberToBusiness" class="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-md text-sm font-medium hover:bg-indigo-200">
                    <i class="fas fa-plus mr-1"></i> Add Team Member to Business
                </button> -->
            </div>
            <!-- Pending Invitations -->
            <div class="mt-8">
                <h4 class="text-md font-medium mb-3">Pending Invitations</h4>
                <div id="pendingInvitationsList">
                    @if ($pendingInvitations)
                    @foreach ($pendingInvitations as $pendingInvitation)
                    <div class="bg-gray-50 rounded-md p-3 mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-800 font-semibold mr-3">
                                    {{ substr($pendingInvitation->name, 0, 2) }}
                                </div>
                                <div>
                                    <div class="font-medium">{{ $pendingInvitation->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $pendingInvitation->email }}</div>
                                </div>
                            </div>
                            <div>
                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs font-medium">Pending</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    @else
                    <div class="text-sm text-gray-500 italic">No pending invitations for this business</div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div id="inviteTeamMemberModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm" style="display: none;">
        <div class="bg-white md:rounded-lg shadow-xl w-full md:max-w-[500px] h-full md:h-auto overflow-y-auto">
            <div class="modal-header flex justify-between items-center p-4 border-b border-gray-200">
                <h3 class="text-md md:text-lg font-semibold">Invite New Team Member</h3>
                <button id="closeInviteModal" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST" action="{{ route('team.invite', $locationId) }}">
                @csrf
                <div class="modal-body h-[calc(100vh-118px)] md:h-auto overflow-y-auto gap-4 p-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input id="email" type="email" name="email" class="w-full border border-gray-300 rounded-md px-3 py-2" required="" placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="inviteBusinessSelect" class="block text-sm font-medium text-gray-700 mb-1">Add to Business</label>
                        <select id="inviteBusinessSelect" name="role" class="w-full border border-gray-300 rounded-md px-3 py-2" required="">
                            @foreach($connectedBusinesses as $connectedBusiness)
                            <option value="{{ $connectedBusiness->id }}" {{ $connectedBusiness->id == $business['id'] ? 'selected' : '' }}>{{ $connectedBusiness->title }}</option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Select the business to add this team member to</p>
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select id="permission" name="permission" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                            <option value="manager">Manager</option>
                            <option value="viewer" selected="">Viewer</option>
                        </select>
                        <input type="hidden" name="role" value="member">
                        <p class="text-xs text-gray-500 mt-1">
                            <strong>Owner:</strong> Created the business connection (automatic)<br>
                            <strong>Manager:</strong> Can manage reviews, templates, and settings<br>
                            <strong>Viewer:</strong> Can only view reviews and analytics
                        </p>
                    </div>

                    <div>
                        <label for="inviteMessage" class="block text-sm font-medium text-gray-700 mb-1">Personal Message (Optional)</label>
                        <textarea id="inviteMessage" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 resize-none" placeholder="Add a personal message to your invitation..."></textarea>
                    </div>
                </div>

                <div class="modal-footer flex justify-end space-x-2 p-4 border-t border-gray-200">
                    <button type="button" id="cancelInviteBtn" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-2 md:px-4 py-1 md:py-2 text-sm md:text-md bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Send Invitation
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div id="edit-permission-modal-container"></div>

    <div class="transition-opacity duration-300 ease-out opacity-100 hidden" id="connectBusinessModal">
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-2xl shadow-lg max-w-3xl w-full min-h-[400px] max-h-[90vh] flex flex-col">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-2xl font-bold text-gray-800">Connect a Business</h3>
                        <button onclick="document.getElementById('connectBusinessModal').classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="flex-1 overflow-y-auto p-4">
                    <div class="space-y-4">
                        @forelse($associateBusinesses as $associateBusiness)
                        <div class="bg-gray-50 hover:bg-gray-100 transition rounded-lg shadow-sm p-4">
                            <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-800">{{ $associateBusiness->title ?? 'Business Name' }}</h4>
                                    <div class="mt-2 text-sm text-gray-600 space-y-1">
                                        @if(isset($associateBusiness->address_lines) && is_array($associateBusiness->address_lines))
                                        <p class="flex items-center gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            {{ implode(', ', $associateBusiness->address_lines) }},
                                            {{ $associateBusiness->locality ?? '' }},
                                            {{ $associateBusiness->administrative_area ?? '' }}
                                            {{ $associateBusiness->postal_code ?? '' }}
                                        </p>
                                        @endif
                                        @if(isset($associateBusiness->primary_phone))
                                        <p class="flex items-center gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                            {{ $associateBusiness->primary_phone }}
                                        </p>
                                        @endif
                                        @if(isset($associateBusiness->website))
                                        <p class="flex items-center gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                            </svg>
                                            <a href="{{ $associateBusiness->website }}" class="text-indigo-600 hover:underline word-break-all" target="_blank">{{ $associateBusiness->website }}</a>
                                        </p>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    @if(isset($associateBusiness->location_name) && in_array($associateBusiness->location_name, $connectedBusinessIds))
                                    <button
                                        class="remove-business-btn text-red-600 hover:text-red-800 px-4 py-2 rounded-md border border-red-600 hover:bg-red-50 transition"
                                        data-business-id="{{ $associateBusiness->id ?? '' }}"
                                        data-business-name="{{ $associateBusiness->title ?? 'Business' }}"
                                        data-location-name="{{ $associateBusiness->location_name ?? '' }}">
                                        Remove
                                    </button>
                                    @else
                                    <button
                                        class="connect-business-btn text-indigo-600 hover:text-indigo-800 px-4 py-2 rounded-md border border-indigo-600 hover:bg-indigo-50 transition"
                                        data-business-id="{{ $associateBusiness->id ?? '' }}"
                                        data-business-name="{{ $associateBusiness->title ?? 'Business' }}"
                                        data-location-name="{{ $associateBusiness->location_name ?? '' }}"
                                        data-primary-phone="{{ $associateBusiness->primary_phone ?? '' }}"
                                        data-website="{{ $associateBusiness->website ?? '' }}">
                                        Connect
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <p class="mt-4 text-gray-600">No businesses found. Please add a business to your Google account.</p>
                        </div>
                        @endforelse
                    </div>
                </div>
                <div class="p-4 border-t border-gray-200">
                    <button onclick="document.getElementById('connectBusinessModal1').classList.add('hidden')"
                        class="w-full bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>



</div>
@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const businessSelector = document.getElementById('businessSelectorData');
        const inviteModal = document.getElementById('inviteTeamMemberModal');
        const closeInviteModal = document.getElementById('closeInviteModal');
        const cancelInviteBtn = document.getElementById('cancelInviteBtn');
        const inviteEmail = document.getElementById('inviteEmail');
        const inviteBusinessSelect = document.getElementById('inviteBusinessSelect');
        const inviteRole = document.getElementById('inviteRole');
        const inviteMessage = document.getElementById('inviteMessage');
        const inviteTeamMemberForm = document.getElementById('inviteTeamMemberForm');

        // Open invite modal - manual binding example:
        /*
        // Example of manual binding for team member invite validation:
        if (typeof window.bindUpgradeValidation === 'function') {
            const businessSelector = document.getElementById('businessSelectorData');
            const currentBusinessId = businessSelector ? businessSelector.value : null;

            window.bindUpgradeValidation('inviteTeamMemberBtn', 'team_member_invite',
                { business_id: currentBusinessId },
                function() {
                    // Original functionality - called only if validation passes
                    inviteModal.style.display = 'flex';
                }
            );
        } else {
            // Fallback to original functionality
            document.getElementById('inviteTeamMemberBtn').addEventListener('click', () => {
                inviteModal.style.display = 'flex';
            });
        }
        */

        // Default behavior (you can replace this with manual binding)
        document.getElementById('inviteTeamMemberBtn').addEventListener('click', () => {
            validateSubscriptionAndUpgrade('team_member_invite', {
                business_id: logSelectedLocationName()
            }).then(validation => {
                if (validation.allowed) {
                    // Original functionality - called only if validation passes
                    inviteModal.style.display = 'flex';
                    document.body.style.overflow = 'hidden';
                }
            });
        });

        // Close invite modal
        closeInviteModal.addEventListener('click', () => {
            inviteModal.style.display = 'none';
        });

        // Cancel invite
        cancelInviteBtn.addEventListener('click', () => {
            inviteModal.style.display = 'none';
        });


        function logSelectedLocationName() {
            const selectedOption = businessSelector.options[businessSelector.selectedIndex];
            const locationName = selectedOption.getAttribute('data-locationName');
            return locationName;
        }

        logSelectedLocationName();

        const selectedBusinessLocation = logSelectedLocationName();
        const splitLocation = selectedBusinessLocation.split('/');
        const businessLocation = splitLocation[splitLocation.length - 1];

        if (businessSelector) {

            businessSelector.addEventListener('change', (e) => {
                const businessId = e.target.value;
                const selectedBusinessLocation = logSelectedLocationName()
                const splitLocation = selectedBusinessLocation.split('/');
                const businessLocation = splitLocation[splitLocation.length - 1];

                window.location.href = `/team/${businessLocation}/${businessId}`;
            });
        }



        const connectBusinessBtn = document.getElementById("connectBusiness");
        const connectBusinessModal = document.getElementById("connectBusinessModal");
        const getSelectBusiness = document.querySelector('.getSelectBusiness')

        // Show modal when connect button is clicked
        if (connectBusinessBtn) {
            connectBusinessBtn.addEventListener("click", () => {
                connectBusinessModal.classList.remove("hidden");
            });
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target.id === 'connectBusinessModal' && !connectBusinessModal.classList.contains('hidden')) {
                connectBusinessModal.classList.add('hidden');
            }
        });

        // Handle business connection - manual binding example:
        /*
        // Example of manual binding for business connection validation:
        const connectBusinessBtns = document.querySelectorAll('.connect-business-btn');
        connectBusinessBtns.forEach(btn => {
            if (typeof window.validateSubscriptionAndUpgrade === 'function') {
                btn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const validation = await window.validateSubscriptionAndUpgrade('business_connection');

                    if (validation.allowed) {
                        // If validation passes, proceed with business connection
                        const businessId = this.getAttribute('data-business-id');
                        const businessName = this.getAttribute('data-business-name');
                        const locationName = this.getAttribute('data-location-name');
                        const primaryPhone = this.getAttribute('data-primary-phone');
                        const website = this.getAttribute('data-website');

                        connectBusiness({
                            id: businessId,
                            title: businessName,
                            location_name: locationName,
                            primary_phone: primaryPhone,
                            website: website
                        });
                    }
                });
            }
        });
        */

        // Default behavior (you can replace this with manual binding)
        const connectBusinessBtns = document.querySelectorAll('.connect-business-btn');
        connectBusinessBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const businessId = this.getAttribute('data-business-id');
                const businessName = this.getAttribute('data-business-name');
                const locationName = this.getAttribute('data-location-name');
                const primaryPhone = this.getAttribute('data-primary-phone');
                const website = this.getAttribute('data-website');

                // Send AJAX request to connect the business
                connectBusiness({
                    id: businessId,
                    title: businessName,
                    location_name: locationName,
                    primary_phone: primaryPhone,
                    website: website
                });
            });
        });


        // Handle business removal
        const removeBusinessBtns = document.querySelectorAll('.remove-business-btn');
        removeBusinessBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const businessId = this.getAttribute('data-business-id');
                const businessName = this.getAttribute('data-business-name');
                const locationName = this.getAttribute('data-location-name');

                // Confirm before removing
                if (confirm(`Are you sure you want to remove ${businessName}?`)) {
                    // Send AJAX request to remove the business
                    removeBusiness(locationName, businessName);
                }
            });
        });

        if (getSelectBusiness) {
            getSelectBusiness.addEventListener('change', function() {
                const businessId = this.value;
                const dashboardUrl = this.dataset.dashboardUrl;
                // Send AJAX request to change the business status
                changeStatus(businessId, dashboardUrl);
            });
        }
        // Tooltip menu functionality
        const tooltipTriggers = document.querySelectorAll('.tooltip-trigger');
        const tooltipMenus = document.querySelectorAll('.tooltip-menu');

        // Close all tooltips when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.tooltip-trigger') && !event.target.closest('.tooltip-menu')) {
                tooltipMenus.forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });

        // Toggle tooltip on click
        tooltipTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(event) {
                event.stopPropagation();
                const menu = this.nextElementSibling;

                // Close all other menus
                tooltipMenus.forEach(otherMenu => {
                    if (otherMenu !== menu) {
                        otherMenu.classList.add('hidden');
                    }
                });

                // Toggle current menu
                menu.classList.toggle('hidden');
            });
        });

        // Edit permissions functionality
        const editPermissionsButtons = document.querySelectorAll('.editPermissions');
        editPermissionsButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                event.stopPropagation();
                const memberId = this.dataset.memberId;
                // Send AJAX request to edit permissions
                editPermissions(memberId);
            });
        });

        // Remove member functionality
        const removeMemberButtons = document.querySelectorAll('.removeMember');
        removeMemberButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                event.stopPropagation();
                const memberId = this.dataset.memberId;

                const confirmDelete = confirm("Are you sure you want to remove this team member?");
                if (!confirmDelete) {}

                removeMember(memberId);
            });
        });

        function editPermissions(memberId) {
            fetch(`/team/edit-permissions/${memberId}`)
                .then(response => response.json())
                .then(data => {
                    const modalHtml = `
        <div id="changeRoleModal-${memberId}" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm" style="display: flex;">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Change Role</h3>
                        <button id="closeChangeRoleModal-${memberId}" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="mb-4">
            <p class="text-sm font-medium">Team Member: <span id="changeRoleMemberName-${memberId}" class="font-normal"> ${data.user.name} </span></p>
            <p class="text-sm font-medium">Email: <span id="changeRoleMemberEmail-${memberId}" class="font-normal"> ${data.user.email} </span></p>
            <p class="text-sm font-medium">Current Role: <span id="changeRoleCurrentRole-${memberId}" class="font-normal"> ${data.permission.permission_name} </span></p>
          </div>
            
          <div>
            <label for="newRole-${memberId}" class="block text-sm font-medium text-gray-700 mb-1">New Role</label>
            <select id="newRole-${memberId}" class="w-full border border-gray-300 rounded-md px-3 py-2" required="">
              <option value="manager" ${data.permission.permission_name === 'manager' ? 'selected' : ''}>Manager</option>
              <option value="viewer" ${data.permission.permission_name === 'viewer' ? 'selected' : ''}>Viewer</option>
            </select>
              <p class="text-xs text-gray-500 mt-1">
                <strong>Manager:</strong> Can manage reviews, templates, and settings<br>
                <strong>Viewer:</strong> Can only view reviews and analytics
              </p>
            </div>
            
            <div class="flex justify-end space-x-2 pt-4">
              <button type="button" id="cancelChangeRoleBtn-${memberId}" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                Cancel
              </button>
              <button type="button" class="save-change-role px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Save Changes
              </button>
            </div>
            </div>
        </div>
        </div>`;

                    document.getElementById('edit-permission-modal-container').innerHTML = modalHtml;

                    // Show modal
                    document.getElementById(`changeRoleModal-${memberId}`).style.display = 'flex';

                    // Close modal handlers
                    document.getElementById(`closeChangeRoleModal-${memberId}`).onclick = () => {
                        document.getElementById(`changeRoleModal-${memberId}`).style.display = 'none';
                    };
                    document.getElementById(`cancelChangeRoleBtn-${memberId}`).onclick = () => {
                        document.getElementById(`changeRoleModal-${memberId}`).style.display = 'none';
                    };

                    document.querySelector(`#changeRoleModal-${memberId} .save-change-role`).addEventListener('click', function() {
                        console.log('save changes clicked');
                        saveChanges(memberId);
                    });
                })

                .catch(error => {
                    console.error('Error:', error);
                });


        }

        function saveChanges(memberId) {
            const newRole = document.getElementById(`newRole-${memberId}`).value;

            fetch(`/team/update-permissions/${memberId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        newRole,
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Close modal
                    document.getElementById(`changeRoleModal-${memberId}`).style.display = 'none';
                    if (data.success) {
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function removeMember(memberId) {
            fetch(`/team/remove-member/${memberId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

    });
</script>
@endpush
@endsection