<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\CouponUsageLog;
use App\Models\Plan;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CouponController extends Controller
{
    /**
     * Display a listing of coupons with filters and search
     */
    public function index(Request $request)
    {
        $query = Coupon::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('coupon_code', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'expired') {
                $query->where('expires_at', '<', now());
            } elseif ($request->status === 'upcoming') {
                $query->where('starts_at', '>', now());
            }
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $coupons = $query->withCount('usageLogs')->paginate(15);

        // Get statistics for dashboard
        $stats = $this->getCouponStats();

        return view('admin.coupons.index', compact('coupons', 'stats'));
    }

    /**
     * Get coupon statistics
     */
    private function getCouponStats()
    {
        return [
            'total_coupons' => Coupon::count(),
            'active_coupons' => Coupon::active()->count(),
            'expired_coupons' => Coupon::where('expires_at', '<', now())->count(),
            'total_usage' => CouponUsageLog::count(),
            'total_discount_given' => CouponUsageLog::sum('discount_amount'),
            'most_used_coupon' => Coupon::withCount('usageLogs')
                ->orderBy('usage_logs_count', 'desc')
                ->first(),
        ];
    }

    /**
     * Show the form for creating a new coupon
     */
    public function create()
    {
        $plans = Plan::where('is_active', true)->get();
        return view('admin.coupons.create', compact('plans'));
    }

    /**
     * Store a newly created coupon
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'coupon_code' => 'required|string|max:50|unique:coupons,coupon_code',
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'starts_at' => 'nullable|date|after_or_equal:today',
            'expires_at' => 'nullable|date|after:starts_at',
            'applicable_plans' => 'nullable|array',
            'applicable_plans.*' => 'exists:plans,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Additional validation for percentage type
        if ($request->type === 'percentage' && $request->value > 100) {
            return redirect()->back()
                ->withErrors(['value' => 'Percentage discount cannot exceed 100%'])
                ->withInput();
        }

        $couponData = $request->only([
            'coupon_code', 'name', 'description', 'type', 'value',
            'usage_limit', 'usage_limit_per_user', 'minimum_amount',
            'maximum_discount', 'starts_at', 'expires_at'
        ]);

        $couponData['applicable_plans'] = $request->applicable_plans;
        $couponData['is_active'] = $request->has('is_active');

        Coupon::create($couponData);

        return redirect()->route('admin.coupons.index')
            ->with('success', 'Coupon created successfully!');
    }

    /**
     * Display the specified coupon with usage analytics
     */
    public function show(Coupon $coupon)
    {
        $coupon->load('usageLogs.user');

        // Get usage analytics
        $analytics = [
            'total_usage' => $coupon->usageLogs()->count(),
            'total_discount_given' => $coupon->usageLogs()->sum('discount_amount'),
            'total_revenue_impact' => $coupon->usageLogs()->sum('original_amount'),
            'average_discount' => $coupon->usageLogs()->avg('discount_amount'),
            'unique_users' => $coupon->usageLogs()->distinct('user_id')->count(),
            'usage_by_status' => $coupon->usageLogs()
                ->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status'),
            'recent_usage' => $coupon->usageLogs()
                ->with('user')
                ->latest()
                ->limit(10)
                ->get(),
            'monthly_usage' => $coupon->usageLogs()
                ->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('COUNT(*) as usage_count'),
                    DB::raw('SUM(discount_amount) as total_discount')
                )
                ->groupBy('year', 'month')
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->limit(12)
                ->get()
        ];

        return view('admin.coupons.show', compact('coupon', 'analytics'));
    }

    /**
     * Show the form for editing the specified coupon
     */
    public function edit(Coupon $coupon)
    {
        $plans = Plan::where('is_active', true)->get();
        return view('admin.coupons.edit', compact('coupon', 'plans'));
    }

    /**
     * Update the specified coupon
     */
    public function update(Request $request, Coupon $coupon)
    {
        $validator = Validator::make($request->all(), [
            'coupon_code' => 'required|string|max:50|unique:coupons,coupon_code,' . $coupon->id,
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
            'applicable_plans' => 'nullable|array',
            'applicable_plans.*' => 'exists:plans,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Additional validation for percentage type
        if ($request->type === 'percentage' && $request->value > 100) {
            return redirect()->back()
                ->withErrors(['value' => 'Percentage discount cannot exceed 100%'])
                ->withInput();
        }

        $couponData = $request->only([
            'coupon_code', 'name', 'description', 'type', 'value',
            'usage_limit', 'usage_limit_per_user', 'minimum_amount',
            'maximum_discount', 'starts_at', 'expires_at'
        ]);

        $couponData['applicable_plans'] = $request->applicable_plans;
        $couponData['is_active'] = $request->has('is_active');

        $coupon->update($couponData);

        return redirect()->route('admin.coupons.index')
            ->with('success', 'Coupon updated successfully!');
    }

    /**
     * Remove the specified coupon
     */
    public function destroy(Coupon $coupon)
    {
        // Check if coupon has been used
        if ($coupon->usageLogs()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Cannot delete coupon that has been used. You can deactivate it instead.');
        }

        $coupon->delete();

        return redirect()->route('admin.coupons.index')
            ->with('success', 'Coupon deleted successfully!');
    }

    /**
     * Toggle coupon status
     */
    public function toggleStatus(Coupon $coupon)
    {
        $coupon->update(['is_active' => !$coupon->is_active]);

        $status = $coupon->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Coupon {$status} successfully!",
            'is_active' => $coupon->is_active
        ]);
    }

    /**
     * Bulk operations on coupons
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'coupon_ids' => 'required|array|min:1',
            'coupon_ids.*' => 'exists:coupons,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data'
            ], 422);
        }

        $coupons = Coupon::whereIn('id', $request->coupon_ids);
        $count = $coupons->count();

        switch ($request->action) {
            case 'activate':
                $coupons->update(['is_active' => true]);
                $message = "{$count} coupon(s) activated successfully!";
                break;

            case 'deactivate':
                $coupons->update(['is_active' => false]);
                $message = "{$count} coupon(s) deactivated successfully!";
                break;

            case 'delete':
                // Check if any coupon has been used
                $usedCoupons = $coupons->whereHas('usageLogs')->count();
                if ($usedCoupons > 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot delete coupons that have been used'
                    ], 422);
                }

                $coupons->delete();
                $message = "{$count} coupon(s) deleted successfully!";
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Get coupon analytics data
     */
    public function analytics(Request $request)
    {
        $dateRange = $request->get('date_range', '30');
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $analytics = [
            'overview' => [
                'total_coupons' => Coupon::count(),
                'active_coupons' => Coupon::active()->count(),
                'total_usage' => CouponUsageLog::whereBetween('created_at', [$startDate, $endDate])->count(),
                'total_discount' => CouponUsageLog::whereBetween('created_at', [$startDate, $endDate])->sum('discount_amount'),
                'average_discount' => CouponUsageLog::whereBetween('created_at', [$startDate, $endDate])->avg('discount_amount'),
                'conversion_rate' => $this->calculateConversionRate($startDate, $endDate)
            ],
            'top_coupons' => Coupon::withCount(['usageLogs' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                }])
                ->orderBy('usage_logs_count', 'desc')
                ->limit(10)
                ->get(),
            'usage_trend' => $this->getUsageTrend($startDate, $endDate),
            'discount_distribution' => $this->getDiscountDistribution($startDate, $endDate)
        ];

        return view('admin.coupons.analytics', compact('analytics'));
    }

    /**
     * Calculate conversion rate (usage vs total coupons created)
     */
    private function calculateConversionRate($startDate, $endDate)
    {
        $totalCoupons = Coupon::whereBetween('created_at', [$startDate, $endDate])->count();
        $usedCoupons = CouponUsageLog::whereBetween('created_at', [$startDate, $endDate])
            ->distinct('coupon_id')
            ->count();

        return $totalCoupons > 0 ? round(($usedCoupons / $totalCoupons) * 100, 2) : 0;
    }

    /**
     * Get usage trend data for charts
     */
    private function getUsageTrend($startDate, $endDate)
    {
        return CouponUsageLog::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as usage_count'),
                DB::raw('SUM(discount_amount) as total_discount')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get discount distribution by coupon type
     */
    private function getDiscountDistribution($startDate, $endDate)
    {
        return CouponUsageLog::join('coupons', 'coupon_usage_logs.coupon_id', '=', 'coupons.id')
            ->select(
                'coupons.type',
                DB::raw('COUNT(*) as usage_count'),
                DB::raw('SUM(coupon_usage_logs.discount_amount) as total_discount'),
                DB::raw('AVG(coupon_usage_logs.discount_amount) as average_discount')
            )
            ->whereBetween('coupon_usage_logs.created_at', [$startDate, $endDate])
            ->groupBy('coupons.type')
            ->get();
    }

    /**
     * Export coupon data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        $type = $request->get('type', 'coupons'); // coupons or usage_logs

        if ($type === 'usage_logs') {
            return $this->exportUsageLogs($format);
        }

        return $this->exportCoupons($format);
    }

    /**
     * Export coupons data
     */
    private function exportCoupons($format)
    {
        $coupons = Coupon::withCount('usageLogs')->get();

        $data = $coupons->map(function ($coupon) {
            return [
                'Code' => $coupon->coupon_code,
                'Name' => $coupon->name,
                'Type' => ucfirst($coupon->type),
                'Value' => $coupon->value,
                'Usage Count' => $coupon->usage_logs_count,
                'Usage Limit' => $coupon->usage_limit ?? 'Unlimited',
                'Status' => $coupon->is_active ? 'Active' : 'Inactive',
                'Starts At' => $coupon->starts_at?->format('Y-m-d H:i:s'),
                'Expires At' => $coupon->expires_at?->format('Y-m-d H:i:s'),
                'Created At' => $coupon->created_at->format('Y-m-d H:i:s')
            ];
        });

        return $this->downloadData($data, 'coupons', $format);
    }

    /**
     * Export usage logs data
     */
    private function exportUsageLogs($format)
    {
        $usageLogs = CouponUsageLog::with(['coupon', 'user'])->get();

        $data = $usageLogs->map(function ($log) {
            return [
                'Coupon Code' => $log->coupon_code,
                'User Email' => $log->user->email ?? 'N/A',
                'Original Amount' => $log->original_amount,
                'Discount Amount' => $log->discount_amount,
                'Final Amount' => $log->final_amount,
                'Status' => ucfirst($log->status),
                'Used At' => $log->created_at->format('Y-m-d H:i:s')
            ];
        });

        return $this->downloadData($data, 'coupon_usage_logs', $format);
    }

    /**
     * Download data in specified format
     */
    private function downloadData($data, $filename, $format)
    {
        if ($format === 'json') {
            return response()->json($data);
        }

        // CSV format
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}_" . date('Y-m-d') . ".csv\"",
        ];

        $callback = function () use ($data) {
            $file = fopen('php://output', 'w');

            if ($data->isNotEmpty()) {
                fputcsv($file, array_keys($data->first()));

                foreach ($data as $row) {
                    fputcsv($file, array_values($row));
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
