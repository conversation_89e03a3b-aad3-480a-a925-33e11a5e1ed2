<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_email_templates', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            
            // Template identification
            $table->string('name', 100); // Template name/identifier
            $table->string('subject', 255); // Email subject line
            $table->text('description')->nullable(); // Template description
            
            // Template content
            $table->longText('html_content'); // HTML version of email
            $table->longText('text_content')->nullable(); // Plain text version
            
            // Template categorization
            $table->enum('category', ['system', 'custom'])->default('custom');
            $table->enum('type', [
                'welcome', 'password_reset', 'email_verification', 
                'subscription_created', 'subscription_expired', 'subscription_cancelled',
                'payment_success', 'payment_failed', 'invoice',
                'notification', 'marketing', 'user_registration','team_invitation','welcome_user', 'other'
            ])->default('other');
            
            // Template variables and settings
            $table->json('available_variables')->nullable(); // Available template variables
            $table->json('required_variables')->nullable(); // Required variables for this template
            $table->json('settings')->nullable(); // Additional template settings
            
            // Status and metadata
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Is this the default template for this type
            $table->unsignedBigInteger('created_by')->nullable(); // Admin who created it
            $table->unsignedBigInteger('updated_by')->nullable(); // Admin who last updated it
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['category', 'type']);
            $table->index(['is_active', 'is_default']);
            $table->index('created_by');
            
            // Foreign keys
            //$table->foreign('created_by')->references('id')->on('admin')->onDelete('set null');
            //$table->foreign('updated_by')->references('id')->on('admin')->onDelete('set null');
            
            // Charset
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_email_templates');
    }
};
