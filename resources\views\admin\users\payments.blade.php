@extends('admin.layouts.app')

@section('title', 'User Payments - ' . ($user->name ?? $user->email))
@section('page-title', 'User Payments')

@section('content')
<div class="space-y-6">
    <!-- User Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($user->image)
                            <img class="h-12 w-12 rounded-full" src="{{ $user->image }}" alt="{{ $user->name }}">
                        @else
                            <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-lg font-medium text-gray-700">
                                    {{ substr($user->name ?? $user->email, 0, 1) }}
                                </span>
                            </div>
                        @endif
                    </div>
                    <div class="ml-4">
                        <h1 class="text-xl font-bold text-gray-900">{{ $user->name ?? 'No Name' }}</h1>
                        <p class="text-sm text-gray-500">{{ $user->email }}</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.users.show', $user) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Summary -->
    @if($payments->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Payment Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $payments->total() }}</div>
                    <div class="text-sm text-gray-500">Total Payments</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ $payments->where('payment_status', 'SUCCESS')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Completed</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">
                        {{ $payments->where('payment_status', 'PENDING')->count() }}
                    </div>
                    <div class="text-sm text-gray-500">Pending</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        @php
                            $totalAmount = $payments->where('payment_status', 'SUCCESS')->sum('amount');
                            $currency = $payments->first()->currency ?? 'USD';
                        @endphp
                        {{ $currency }} {{ number_format($totalAmount, 2) }}
                    </div>
                    <div class="text-sm text-gray-500">Total Revenue</div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Payments List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Payment History</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">All payments made by this user</p>
        </div>
        
        @if($payments->count() > 0)
            <ul class="divide-y divide-gray-200">
                @foreach($payments as $payment)
                    <li class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center min-w-0 flex-1">
                                <div class="min-w-0 flex-1">
                                    <div class="flex items-center">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $payment->plan->name ?? 'Unknown Plan' }}
                                        </p>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $payment->payment_status === 'SUCCESS' ? 'bg-green-100 text-green-800' :
                                               ($payment->payment_status === 'FAILED' ? 'bg-red-100 text-red-800' :
                                               ($payment->payment_status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                                            {{ ucfirst(strtolower($payment->payment_status)) }}
                                        </span>
                                        @if($payment->billing_cycle === 'annual')
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                Annual
                                            </span>
                                        @endif
                                    </div>
                                    <div class="flex items-center mt-1 space-x-4">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-calendar mr-1"></i>
                                            {{ $payment->payment_date ? $payment->payment_date->format('M d, Y g:i A') : 'N/A' }}
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-credit-card mr-1"></i>
                                            {{ ucfirst($payment->payment_method ?? 'N/A') }}
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-building mr-1"></i>
                                            {{ ucfirst($payment->gateway_name ?? 'N/A') }}
                                        </div>
                                        @if($payment->transaction_id)
                                            <div class="flex items-center text-sm text-gray-500">
                                                <i class="fas fa-hashtag mr-1"></i>
                                                {{ $payment->transaction_id }}
                                            </div>
                                        @endif
                                    </div>
                                    @if($payment->coupon_code || $payment->discount_amount > 0)
                                        <div class="flex items-center mt-1 space-x-4">
                                            @if($payment->coupon_code)
                                                <div class="flex items-center text-sm text-green-600">
                                                    <i class="fas fa-tag mr-1"></i>
                                                    Coupon: {{ $payment->coupon_code }}
                                                </div>
                                            @endif
                                            @if($payment->discount_amount > 0)
                                                <div class="flex items-center text-sm text-green-600">
                                                    <i class="fas fa-percentage mr-1"></i>
                                                    Discount: {{ $payment->currency }} {{ number_format($payment->discount_amount, 2) }}
                                                </div>
                                            @endif
                                        </div>
                                    @endif
                                    @if($payment->refund_id)
                                        <div class="flex items-center mt-1">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="fas fa-undo mr-1"></i>
                                                Refunded on {{ $payment->refund_date ? $payment->refund_date->format('M d, Y') : 'N/A' }}
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-1">
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-gray-900">
                                        {{ $payment->currency }} {{ number_format($payment->amount, 2) }}
                                    </p>
                                    @if($payment->original_amount != $payment->amount)
                                        <p class="text-sm text-gray-500 line-through">
                                            {{ $payment->currency }} {{ number_format($payment->original_amount, 2) }}
                                        </p>
                                    @endif
                                </div>
                                @if($payment->tax_amount > 0)
                                    <div class="text-right">
                                        <p class="text-xs text-gray-500">
                                            Tax: {{ $payment->currency }} {{ number_format($payment->tax_amount, 2) }}
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Payment Details Expandable -->
                        @if($payment->metadata || $payment->order_id)
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <button onclick="togglePaymentDetails('{{ $payment->id }}')" 
                                        class="text-sm text-indigo-600 hover:text-indigo-500">
                                    <i class="fas fa-chevron-down mr-1" id="chevron-{{ $payment->id }}"></i>
                                    View Details
                                </button>
                                <div id="details-{{ $payment->id }}" class="hidden mt-2">
                                    <dl class="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                                        @if($payment->order_id)
                                            <div>
                                                <dt class="text-xs font-medium text-gray-500">Order ID</dt>
                                                <dd class="text-xs text-gray-900">{{ $payment->order_id }}</dd>
                                            </div>
                                        @endif
                                        @if($payment->payment_type)
                                            <div>
                                                <dt class="text-xs font-medium text-gray-500">Payment Type</dt>
                                                <dd class="text-xs text-gray-900">{{ ucfirst($payment->payment_type) }}</dd>
                                            </div>
                                        @endif
                                        @if($payment->metadata)
                                            @foreach($payment->metadata as $key => $value)
                                                <div>
                                                    <dt class="text-xs font-medium text-gray-500">{{ ucfirst(str_replace('_', ' ', $key)) }}</dt>
                                                    <dd class="text-xs text-gray-900">{{ is_array($value) ? json_encode($value) : $value }}</dd>
                                                </div>
                                            @endforeach
                                        @endif
                                    </dl>
                                </div>
                            </div>
                        @endif
                    </li>
                @endforeach
            </ul>
            
            <!-- Pagination -->
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                {{ $payments->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No payments found</h3>
                <p class="text-gray-500">This user has no payment history.</p>
            </div>
        @endif
    </div>
</div>

<script>
function togglePaymentDetails(paymentId) {
    const details = document.getElementById('details-' + paymentId);
    const chevron = document.getElementById('chevron-' + paymentId);
    
    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        chevron.classList.remove('fa-chevron-down');
        chevron.classList.add('fa-chevron-up');
    } else {
        details.classList.add('hidden');
        chevron.classList.remove('fa-chevron-up');
        chevron.classList.add('fa-chevron-down');
    }
}
</script>
@endsection
