<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Dashboard - Business Reviews</title>

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">
    <link rel="stylesheet" href="https://unpkg.com/intro.js/minified/introjs.min.css">
    <script src="https://unpkg.com/intro.js/minified/intro.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
    <!-- <link href="<?php echo e(asset('css/styles.css')); ?>" rel="stylesheet"> -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/css/styles.css']); ?>

</head>

<body class="font-poppins bg-gray-100 text-gray-900">

    <?php $locationId = explode("/", $business['location_name'])[1]; ?>
    <?php if(!$user->hasActiveSubscription() && $user->invitedMembers()->count() == 0): ?>
    <h2>You don't have an active subscription. Please choose a plan and payment method to continue.</h2>
    <?php else: ?>
    <!-- Header -->
    <input type="hidden" id="checkTeamMember" value="<?php echo e($checkTeamMember); ?>" />
    <input type="hidden" id="business_id" value="<?php echo e($business['id']); ?>">
    <?php echo $__env->make('partials.header-nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="flex overflow-hidden body-content h-[calc(100vh-47px)] md:h-[calc(100vh-75px)] w-full max-w-[1200px] mx-auto">

        <!-- Main Content Area -->
        <main class="flex-1 flex flex-col overflow-hidden bg-white">
            <!-- Tabs -->
            <div class="relative group bg-white border-b border-gray-200 hidden md:flex justify-between flex-wrap items-center">
                <nav id="tabScrollContainer"
                    class="flex sm:px-0 overflow-x-auto overflow-y-hidden scrollbar-hide transition-all duration-300">
                    <?php
                    $isManager = count(array_intersect($permissions, ['manager', 'all'])) > 0;
                    $isViewer = in_array('viewer', $permissions);
                    ?>

                    <?php if($isManager): ?>
                    
                    <button id="reviewsTabBtn" class="tab-btn active px-4 py-3 text-sm font-medium border-b-2 border-indigo-600 text-indigo-600 flex-shrink-0" data-tab="reviewsTab" data-hint='Hello step one!'>
                        <i class="fas fa-star mr-1"></i> Reviews
                        <span class="text-xs rounded-full bg-red-600 text-white px-2 py-1"><?php echo e($totalReviews); ?></span>
                    </button>

                    <button id="analyticsTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 flex-shrink-0" data-tab="analyticsTab">
                        <i class="fas fa-chart-line mr-1"></i> Analytics
                    </button>

                    <button id="templatesTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 flex-shrink-0" data-tab="templatesTab">
                        <i class="fas fa-file-alt mr-1"></i> Templates
                    </button>

                    <button id="settingsTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 flex-shrink-0" data-tab="settingsTab" <?php if(isset($business)): ?> data-business-id="<?php echo e($business['id']); ?>" <?php endif; ?>>
                        <i class="fas fa-cog mr-1"></i> Settings
                    </button>

                    <!-- <a href="<?php echo e(route('team.index', $locationId)); ?>" class="px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 flex items-center">
                            <i class="fas fa-users mr-1"></i> Team Management
                        </a> -->
                    <?php elseif($isViewer): ?>
                    
                    <button id="reviewsTabBtn" class="tab-btn active px-4 py-3 text-sm font-medium border-b-2 border-indigo-600 text-indigo-600" data-tab="reviewsTab">
                        <i class="fas fa-star mr-1"></i> Reviews
                        <span class="text-xs rounded-full bg-red-600 text-white px-2 py-1"><?php echo e($totalReviews); ?></span>
                    </button>

                    <button id="analyticsTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="analyticsTab">
                        <i class="fas fa-chart-line mr-1"></i> Analytics
                    </button>

                    <button id="templatesTabBtn" class="tab-btn px-4 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="templatesTab">
                        <i class="fas fa-file-alt mr-1"></i> Templates
                    </button>
                    <?php else: ?>
                    <?php endif; ?>
                </nav>
            </div>
            <!-- Tab Content -->
            <div class="flex-1 overflow-hidden">
                <!-- Reviews Tab (Default) -->
                <div id="reviewsTab" class="tab-content active h-full flex flex-col">
                    <!-- Filter Bar -->
                    <div class="flex h-screen overflow-hidden">
                        <?php echo $__env->make('partials.reviews-filter', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <div class="w-full flex-1 flex flex-col overflow-hidden relative">
                            <!-- <div class="loading"></div> -->
                            <!-- <div class="w-0 h-0 p-[15px] border-2 border-solid border-gray-300 border-r-gray-500 rounded-[22px] animate-spin absolute left-1/2 top-1/2"></div> -->


                            <div class="flex-1 flex flex-col overflow-hidden relative">
                                <div class="bg-white border-b border-gray-200 px-4 py-2 flex justify-between items-center flex-wrap gap-1">
                                    <div class="flex gap-1 md:gap-2">
                                        <button id="fetchReviewsBtnDesktop"
                                            class="w-auto flex-shrink-0 flex gap-0.5 items-center justify-center px-2 p-1 text-xs border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors"
                                            data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>" title="Fetch New Reviews">
                                            <i class="fas fa-arrow-down" id="fetchReviewsIconDesktop"></i>
                                            <span class="flex items-center">Fetch New <span class="hidden md:block">Reviews</span></span>
                                        </button>

                                        <!-- <button onclick="sendAutoReplies('<?php echo e($businessId); ?>', '<?php echo e($selectedLocationName); ?>')"
                                                class="w-auto flex-shrink-0 flex gap-1 items-center justify-center px-2 p-1 text-xs border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors">
                                                <i class="fas fa-reply-all"></i> Send Auto Replies
                                            </button> -->

                                        <button class="w-auto flex-shrink-0 flex gap-1 items-center justify-center px-2 p-1 text-xs border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors sendAutoReplies"
                                            data-account-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>" data-business-id="<?php echo e($business['id']); ?>" title="Send Auto Replies" data-settings="0">
                                            <i class="fas fa-reply-all"></i>
                                            <span class="flex items-center"><span class="hidden md:block">Send Auto </span> Replies</span>
                                        </button>

                                        <!-- Toast/Progress Container for AJAX feedback (desktop) -->
                                        <div id="reviewsActionToastDesktop" class="fixed top-4 right-4 z-50"></div>

                                        <button id="syncReviewsBtnDesktop"
                                            class="w-auto flex-shrink-0 flex gap-1 items-center justify-center px-2 p-1 text-xs border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors"
                                            data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>" title="Sync Reviews">
                                            <i class="fas fa-sync" id="syncReviewsIconDesktop"></i>
                                            <span class="hidden md:block">Sync Reviews</span>
                                        </button>
                                    </div>
                                    <div class="hidden md:flex gap-4 items-center">
                                        <span class="text-indigo-600 flex-shrink-0 text-xs md:text-sm">Reviews <span id="reviewDynamicCount">-</span> out of <span id="totalDynamicReviews"><?php echo e($totalReviews); ?></span></span>
                                    </div>
                                    <div class="flex items-center md:hidden">
                                        <button id="filterMenuBtn" class="px-2 py-1 bg-gray-100 hover:bg-gray-300 rounded-md text-xs font-medium relative block">
                                            <i class="fas fa-filter"></i>
                                            <span class="ml-1 text-sm">Filters</span>
                                        </button>
                                        <h1 class="text-lg font-semibold text-gray-900"><?php echo e($business['name']); ?></h1>
                                    </div>
                                </div>

                                <!-- Reviews List -->
                                <div class="flex-1 overflow-y-auto relative pb-6">
                                    <?php echo $__env->make('partial-reviews', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analyticsTab" class="tab-content hidden h-full overflow-y-auto p-4">
                    <?php echo $__env->make('partials.analytics', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Templates Tab -->
                <div id="templatesTab" class="tab-content hidden h-full overflow-y-auto p-4">
                    <?php echo $__env->make('partials.templates', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Settings Tab -->
                <div id="settingsTab" class="tab-content hidden h-full overflow-y-auto p-4 items-center">
                    <?php echo $__env->make('partials.settings', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </main>
    </div>
    <?php endif; ?>

    <?php if($showCouponPopup): ?>
    <div class="transition-opacity duration-300 ease-out opacity-100" id="firstLoginModal">
        <div id="firstLoginModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="bg-white rounded-2xl shadow-lg max-w-md w-full p-6 text-center">
                <h2 class="text-2xl font-bold mb-4 text-gray-800">🎉 Welcome!</h2>
                <p class="text-gray-700 mb-6">Thanks for joining us! Hope you loved it</p>
                <!-- <div class="text-2xl font-semibold text-green-600 bg-green-100 px-4 py-2 rounded mb-4">
                        WELCOME50
                    </div> -->
                <button onclick="document.getElementById('firstLoginModal').remove()"
                    class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition">
                    Got it!
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <input type="hidden" id="businessValues" value="321456" data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>" />
    <div class="transition-opacity duration-300 ease-out opacity-100 hidden" id="settingCompletion">
        <div id="settingCompletion" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="bg-white md:rounded-2xl shadow-lg max-w-full md:max-w-md h-screen md:h-auto  px-6 py-8 text-center flex gap-6 flex-col items-center justify-center">
                <!-- <h2 class="text-2xl font-bold mb-4 text-gray-800">Setting Saved Successfully </h2> -->
                <i class="fa-solid fa-circle-check text-green-600 text-5xl"></i>
                <h3 class="text-2xl font-semibold text-green-600 px-4 rounded">
                    Setting Saved Successfully
                </h3>
                <p class="text-gray-500 px-6">
                    Your settings have been saved successfully. You can now proceed with your business operations.
                </p>
                <button onclick="document.getElementById('settingCompletion').classList.add('hidden')"
                    class="min-w-[120px] bg-green-600 text-white px-6 py-2 rounded hover:bg-green-100 hover:text-green-800 transition uppercase font-semibold">
                    Ok
                </button>
            </div>
        </div>
    </div>

    <div class="transition-opacity duration-300 ease-out opacity-100 hidden" id="replyModal">
        <div id="replyModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="bg-white rounded-2xl shadow-lg max-w-md w-full p-6 text-center flex flex-col gap-4">
                <span class="text-6xl font-bold">🎉 </span>
                <h2 class="text-2xl font-bold text-gray-800">Post reply successfully</h2>
                <div class="flex justify-center">
                    <button onclick="document.getElementById('replyModal').classList.add('hidden')"
                        class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition">
                        Got it!
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="transition-opacity duration-300 ease-out opacity-100 hidden" id="connectBusinessModal">
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="absolute md:relative top-0 bg-white md:rounded-2xl shadow-lg max-w-3xl w-full h-full md:h-auto md:min-h-[400px] md:max-h-[90vh] flex flex-col">
                <div class="p-4 md:p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-md md:text-2xl font-bold text-gray-800">Connect a Business</h3>
                        <button onclick="document.getElementById('connectBusinessModal').classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <?php
                $checkLocationMap = collect($checkLocation)->keyBy('location_name');

                $notAssociatedBusinesses = collect($associateBusinesses)->filter(function($business) use ($checkLocationMap) {
                $matched = $checkLocationMap[$business->location_name] ?? null;
                return !$matched || !$matched->user;
                });

                $associatedBusinesses = collect($associateBusinesses)->filter(function($business) use ($checkLocationMap) {
                $matched = $checkLocationMap[$business->location_name] ?? null;
                return $matched && $matched->user;
                });
                ?>

                <div class="flex-1 overflow-y-auto p-4">
                    <div class="space-y-4">
                        <?php $__currentLoopData = $notAssociatedBusinesses->merge($associatedBusinesses); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $associateBusiness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                        $matchedBusiness = $checkLocationMap[$associateBusiness->location_name] ?? null;
                        $associatedUser = $matchedBusiness?->user?->name ?? null;
                        ?>

                        <div class="bg-gray-50 hover:bg-gray-100 transition rounded-lg shadow-sm p-4">
                            <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                                <div class="flex-1">
                                    <h4 class="text-sm md:text-lg font-semibold text-gray-800">
                                        <?php echo e($associateBusiness->title ?? 'Business Name'); ?>

                                    </h4>

                                    <?php if($associatedUser): ?>
                                    <p class="mt-1 text-sm text-green-500 font-medium">
                                        This business is already associated with <strong><?php echo e($associatedUser); ?></strong>.
                                    </p>
                                    <?php endif; ?>

                                    <div class="mt-2 text-sm text-gray-600 flex flex-col gap-2">
                                        <?php if(isset($associateBusiness->address_lines) && is_array($associateBusiness->address_lines)): ?>
                                        <p class="flex items-start gap-2">
                                            <i class="fa-solid fa-location-dot relative top-1"></i>
                                            <?php echo e(implode(', ', $associateBusiness->address_lines)); ?>,
                                            <?php echo e($associateBusiness->locality ?? ''); ?>,
                                            <?php echo e($associateBusiness->administrative_area ?? ''); ?>

                                            <?php echo e($associateBusiness->postal_code ?? ''); ?>

                                        </p>
                                        <?php endif; ?>

                                        <?php if(isset($associateBusiness->primary_phone)): ?>
                                        <p class="flex items-center gap-2">
                                            <i class="fa-solid fa-phone"></i>
                                            <?php echo e($associateBusiness->primary_phone); ?>

                                        </p>
                                        <?php endif; ?>

                                        <?php if(isset($associateBusiness->website)): ?>
                                        <a href="<?php echo e($associateBusiness->website); ?>" class="text-indigo-600 hover:underline word-break-all" target="_blank">
                                            <p class="flex items-start gap-2">
                                                <i class="fa-solid fa-globe relative top-1"></i>
                                                <?php echo e($associateBusiness->website); ?>

                                            </p>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                
                                <div class="flex items-center">
                                    <?php if(isset($associateBusiness->location_name) && in_array($associateBusiness->location_name, $connectedBusinessIds)): ?>
                                    <button
                                        class="remove-business-btn text-red-600 hover:text-red-800 px-2 py-1 md:px-4 md:py-2 text-sm rounded-md border border-red-600 hover:bg-red-50 transition"
                                        data-business-id="<?php echo e($associateBusiness->id); ?>"
                                        data-business-name="<?php echo e($associateBusiness->title); ?>"
                                        data-location-name="<?php echo e($associateBusiness->location_name); ?>">
                                        Remove
                                    </button>
                                    <?php elseif(!$associatedUser): ?>
                                    <button
                                        class="connect-business-btn text-indigo-600 hover:text-indigo-800 px-2 py-1 md:px-4 md:py-2 text-sm rounded-md border border-indigo-600 hover:bg-indigo-50 transition"
                                        data-business-id="<?php echo e($associateBusiness->id); ?>"
                                        data-business-name="<?php echo e($associateBusiness->title); ?>"
                                        data-location-name="<?php echo e($associateBusiness->location_name); ?>"
                                        data-primary-phone="<?php echo e($associateBusiness->primary_phone); ?>"
                                        data-website="<?php echo e($associateBusiness->website); ?>">
                                        Connect
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php if($associateBusinesses->isEmpty()): ?>
                        <div class="text-center py-8">
                            <svg class="h-12 w-12 mx-auto text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m2 0a2 2 0 01-2 2H9a2 2 0 01-2-2m10 0V7a2 2 0 00-2-2H9a2 2 0 00-2 2v6m12 0H3" />
                            </svg>
                            <p class="mt-4 text-gray-600">No businesses found. Please add a business to your Google account.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="p-4 border-t border-gray-200 flex justify-center">
                    <button onclick="document.getElementById('connectBusinessModal').classList.add('hidden')"
                        class="w-full md:w-auto bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="transition-opacity duration-300 ease-out opacity-100 hidden" id="sendAutoReplyModel">
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="bg-white md:rounded-2xl shadow-lg max-w-3xl w-full h-full md:min-h-[400px] md:max-h-[90vh] flex flex-col">
                <div class="p-4 md:p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-md md:text-2xl font-bold text-gray-800">Send Auto Reviews</h3>
                        <button onclick="document.getElementById('sendAutoReplyModel').classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="flex-1 overflow-y-auto p-4">
                    <div id="defaultSelectedReviewContainer">
                        <!-- This will be populated via AJAX -->
                    </div>
                    <div id="defaultSelectedReplyContainer" class="hidden flex-col items-center justify-center gap-4 pt-4">
                        <div id="autoReplyLoader" class="text-center py-4 hidden">
                            <div class="loader border-4 border-blue-500 border-t-transparent rounded-full w-6 h-6 mx-auto animate-spin"></div>
                            <p class="text-xs text-gray-500 mt-2">Generating replies...</p>
                        </div>
                        <div id="defaultSelectedReplyList"></div>
                    </div>
                </div>
                <?php
                $autoReply = $business->setting->auto_reply;
                $autoReplySettings = json_decode($business->setting->auto_reply_settings ?? [], true);
                $anyStatusOn = collect($autoReplySettings)->contains(function($item) {
                return isset($item['status']) && $item['status'] === 'on';
                });
                ?>
                <?php if($autoReply == 1 && $anyStatusOn): ?>
                <div class="p-4 border-t border-gray-200 flex justify-center">
                    <button id="generateReplyAutoId" data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>"
                        class="w-full md:w-auto bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition">
                        Generate Auto Reply
                    </button>
                    <button id="sendReplyAutoId" data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>"
                        class="w-full md:w-auto bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-400 transition sendReplyAutoId hidden">
                        Send Auto Reply
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="backdrop fixed top-0 left-0 bg-black bg-opacity-30 w-full h-full md:hidden backdrop-blur-md"></div>

    <a href="https://reviewmaster.biz/getting-started.php" target="_blank" id="gettingStarted" class="hidden fixed md:flex text-sm items-center gap-2 shadow-lg bottom-4 py-2 px-3 right-4 z-50 bg-white text-indigo-600 ring-indigo-600 hover:bg-indigo-600 hover:text-white ring-2 rounded-full" title="Getting Started">
        <i class="fa-solid fa-life-ring"></i>
        Need Help?
    </a>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/js/app.js']); ?>

    <script>
        function initSentimentChart(sentimentData) {
            const chartContainer = document.getElementById('sentimentChartCanvas');

            // Set explicit dimensions for the chart canvas
            chartContainer.style.height = '350px';
            chartContainer.style.width = '100%';

            const ctx = chartContainer.getContext('2d');

            // Get the selected time range for the title
            const timeRangeSelect = document.getElementById('analyticsTimeRange');
            const selectedTimeRange = timeRangeSelect.options[timeRangeSelect.selectedIndex].text;

            window.sentimentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: sentimentData.labels,
                    datasets: [{
                            label: 'Positive',
                            data: sentimentData.positive,
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Neutral',
                            data: sentimentData.neutral,
                            borderColor: '#6B7280',
                            backgroundColor: 'rgba(107, 114, 128, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Negative',
                            data: sentimentData.negative,
                            borderColor: '#EF4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sentiment Trends - ' + selectedTimeRange
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const datasetLabel = context.dataset.label || '';
                                    const value = context.parsed.y;
                                    return `${datasetLabel}: ${value}% of reviews`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Review Activity Chart
        function initActivityChart(activityData) {
            const chartContainer = document.getElementById('activityChartCanvas');

            // Set explicit dimensions for the chart canvas
            chartContainer.style.height = '350px';
            chartContainer.style.width = '100%';

            const ctx = chartContainer.getContext('2d');

            // Get the selected time range for the title
            const timeRangeSelect = document.getElementById('analyticsTimeRange');
            const selectedTimeRange = timeRangeSelect.options[timeRangeSelect.selectedIndex].text;

            window.activityChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: activityData.labels,
                    datasets: [{
                            label: 'New Reviews',
                            data: activityData.reviews,
                            backgroundColor: 'rgba(79, 70, 229, 0.8)',
                        },
                        {
                            label: 'Responses',
                            data: activityData.responses,
                            backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Review Activity - ' + selectedTimeRange
                        }
                    }
                }
            });
        }


        document.addEventListener('DOMContentLoaded', function() {
            // First check if we have data
            const sentimentData = <?php echo json_encode($sentimentTrend, 15, 512) ?>;
            const activityData = <?php echo json_encode($reviewActivity, 15, 512) ?>;


            // Initialize sentiment chart
            const sentimentChart = initSentimentChart(sentimentData);

            // Initialize activity chart
            const activityChart = initActivityChart(activityData);

            // Cache for chart data to prevent redundant API calls
            let chartDataCache = {
                'sentiment': {},
                'activity': {}
            };

            // Add hidden field to track forced refresh state
            document.body.insertAdjacentHTML('beforeend', '<input type="hidden" id="forceDataRefresh" value="false">');

            document.getElementById('analyticsTimeRange').addEventListener('change', function() {
                // Update both charts with the same time range
                const timeRange = this.value;
                updateChartData('sentiment', timeRange);
                updateChartData('activity', timeRange);
            });


            function updateChartData(chartType, timeRange) {
                // Get the business ID based on which chart is being updated
                let businessId;
                if (chartType === 'sentiment') {
                    businessId = document.getElementById('sentiment_business_id').value;
                } else {
                    businessId = document.getElementById('activity_business_id').value;
                }

                console.log(`Updating ${chartType} chart with timeRange: ${timeRange}, businessId: ${businessId}`);

                if (!businessId) {
                    console.error('Business ID is empty or not found');
                    return;
                }

                // Show loading indicator
                const loadingElement = document.getElementById(`${chartType}ChartLoading`);
                const canvasElement = document.getElementById(`${chartType}ChartCanvas`);
                if (loadingElement && canvasElement) {
                    loadingElement.classList.remove('hidden');
                    canvasElement.classList.add('opacity-25');
                }

                // Determine the cache key for this chart type and time range
                const cacheKey = `${businessId}-${timeRange}`;

                // For time filter changes, always fetch fresh data instead of using cache
                const forceRefresh = document.getElementById('forceDataRefresh')?.value === 'true';

                // Only use cached data if not forcing refresh
                if (!forceRefresh && chartDataCache[chartType][cacheKey]) {
                    console.log(`Using cached data for ${chartType} chart with timeRange: ${timeRange}`);

                    // Use cached data
                    setTimeout(() => {
                        if (chartType === 'sentiment') {
                            updateSentimentChart(chartDataCache[chartType][cacheKey]);
                        } else {
                            updateActivityChart(chartDataCache[chartType][cacheKey]);
                        }

                        // Update summary metrics when using cached data too
                        updateSummaryMetrics(chartDataCache[chartType][cacheKey]);

                        // Hide loading indicator
                        if (loadingElement && canvasElement) {
                            loadingElement.classList.add('hidden');
                            canvasElement.classList.remove('opacity-25');
                        }
                    }, 100); // Small timeout to ensure loading indicator is shown

                    return;
                }

                // Correct URL format with business ID in the path
                const url = `/business/${businessId}/chart-data?chartType=${chartType}&timeRange=${timeRange}`;
                console.log('Fetching URL:', url);

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Received data:', data);

                        // Cache the data
                        chartDataCache[chartType][cacheKey] = data;

                        if (chartType === 'sentiment') {
                            // Update sentiment chart
                            updateSentimentChart(data);
                        } else {
                            // Update activity chart
                            updateActivityChart(data);
                        }

                        // Update summary metrics when data is fetched
                        updateSummaryMetrics(data);

                        // Hide loading indicator
                        if (loadingElement && canvasElement) {
                            loadingElement.classList.add('hidden');
                            canvasElement.classList.remove('opacity-25');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching chart data:', error);

                        // Hide loading indicator on error
                        if (loadingElement && canvasElement) {
                            loadingElement.classList.add('hidden');
                            canvasElement.classList.remove('opacity-25');
                        }
                    });
            }

            // Function to update sentiment chart with new data
            function updateSentimentChart(data) {
                if (window.sentimentChart) {
                    window.sentimentChart.destroy();
                }
                initSentimentChart(data);
            }

            // Function to update activity chart with new data
            function updateActivityChart(data) {
                if (window.activityChart) {
                    window.activityChart.destroy();
                }
                initActivityChart(data);
            }

            // Function to update summary metrics with new data
            function updateSummaryMetrics(data) {
                // Check if data contains summary metrics
                if (!data.summaryMetrics) return;

                // Update Overall Rating
                const avgRatingValueEl = document.getElementById('avgRatingValue');
                if (avgRatingValueEl && data.summaryMetrics.overallRating !== undefined) {
                    avgRatingValueEl.textContent = parseFloat(data.summaryMetrics.overallRating).toFixed(1);

                    // Update rating stars
                    updateRatingStars(data.summaryMetrics.overallRating);
                }

                // Update Total Reviews
                const totalReviewsValueEl = document.getElementById('totalReviewsValue');
                if (totalReviewsValueEl && data.summaryMetrics.totalReviews !== undefined) {
                    totalReviewsValueEl.textContent = data.summaryMetrics.totalReviews.toLocaleString();
                }

                // Update Response Rate
                const responseRateValueEl = document.getElementById('responseRateValue');
                if (responseRateValueEl && data.summaryMetrics.responseRate !== undefined) {
                    responseRateValueEl.textContent = data.summaryMetrics.responseRate + '%';
                }

                // Update Average Response Time
                const avgResponseTimeValueEl = document.getElementById('avgResponseTimeValue');
                if (avgResponseTimeValueEl && data.summaryMetrics.averageResponseTime !== undefined) {
                    let responseTime = data.summaryMetrics.averageResponseTime;
                    if (responseTime > 0) {
                        avgResponseTimeValueEl.textContent = responseTime < 24 ?
                            responseTime + 'h' :
                            Math.round(responseTime / 24) + 'd';
                    } else {
                        avgResponseTimeValueEl.textContent = 'N/A';
                    }
                }
            }

            // Function to update rating stars based on overall rating
            function updateRatingStars(rating) {
                const ratingStarsEl = document.getElementById('ratingStars');
                if (!ratingStarsEl) return;

                const fullStars = Math.floor(rating);
                const hasHalfStar = (rating - fullStars) >= 0.5;
                const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

                let starsHTML = '';

                // Add full stars
                for (let i = 0; i < fullStars; i++) {
                    starsHTML += '<i class="icon-star"></i>';
                }

                // Add half star if applicable
                if (hasHalfStar) {
                    starsHTML += '<i class="fas fa-star-half-alt"></i>';
                }

                // Add empty stars
                for (let i = 0; i < emptyStars; i++) {
                    starsHTML += '<i class="far fa-star"></i>';
                }

                ratingStarsEl.innerHTML = starsHTML;
            }
        });

        let reviewReplyData = []

        function sendAutoReplies(businessId, location, $reviewIds) {
            document.getElementById("defaultSelectedReviewContainer").classList.add('hidden')
            document.getElementById("defaultSelectedReplyContainer").classList.remove('hidden')
            document.getElementById("autoReplyLoader").classList.remove('hidden');
            fetch('/business/reviews/auto-reply?businessId=' + businessId + '&location=' + location + '&reviewIds=' + $reviewIds)
                .then(res => res.json())
                .then(data => {
                    reviewReplyData = data.data
                    document.getElementById("autoReplyLoader").classList.add('hidden');
                    if (data.success == true) {
                        data.data.forEach((reply) => {
                            document.getElementById("defaultSelectedReplyList").innerHTML +=
                                `<label class="block font-bold text-gray-700 mb-2">Reply: ${reply.review.reviewer_name}</label>
                                <div class="bg-white p-4 mb-4 rounded-lg shadow-md text-xs pd-5 border border-gray-200">
                                        <input type="checkbox" value="${reply.review.id}" class="reply-checkbox mt-1 accent-indigo-600 w-5 h-5" name="selected_reply[]"/>
                                        <p class="text-gray-700">${reply.reply}</p>
                                    </div>`
                            document.getElementById("sendReplyAutoId").classList.remove('hidden');
                        })
                    }
                })
        }

        document.getElementById("sendReplyAutoId").addEventListener('click', function(e) {
            const checkedCheckboxes = document.querySelectorAll('.reply-checkbox:checked');
            const selectedReplyIds = Array.from(checkedCheckboxes).map(cb => cb.value);
            const sendButton = this; // Store button reference

            // Check if any replies are selected
            if (selectedReplyIds.length === 0) {
                alert('Please select at least one reply to send.');
                return;
            }

            let getFilteredReply = reviewReplyData.filter(reply =>
                selectedReplyIds.includes(String(reply.review_id))
            );

            // Show loading state
            sendButton.disabled = true;
            sendButton.innerHTML = `
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Sending Replies...</span>
                </div>
            `;

            fetch('/business/reviews/send-auto-reply', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    replies: getFilteredReply
                })
            }).then(res => {
                if (!res.ok) {
                    throw new Error(`HTTP error! status: ${res.status}`);
                }
                return res.json();
            }).then(data => {
                if (data.success) {
                    // Show success message
                    const successMessage = data.message || 'Auto replies sent successfully';

                    // Show success notification
                    const replyModal = document.getElementById('replyModal');
                    const modalContent = replyModal.querySelector('.bg-white');
                    document.getElementById("sendAutoReplyModel").classList.add('hidden')

                    modalContent.innerHTML = `
                        <div class="text-center py-8">
                            <span class="text-6xl font-bold">🎉</span>
                            <h2 class="text-2xl font-bold text-gray-800 mt-4">${successMessage}</h2>
                            ${data.sent_count ? `<p class="text-sm text-gray-600 mt-2">Sent ${data.sent_count} replies</p>` : ''}
                            ${data.failures && data.failures.length > 0 ? `
                                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                    <p class="text-sm text-yellow-800 font-medium">Some replies failed:</p>
                                    <ul class="text-xs text-yellow-700 mt-1 text-left">
                                        ${data.failures.slice(0, 3).map(failure => `<li>• ${failure}</li>`).join('')}
                                        ${data.failures.length > 3 ? `<li>• ... and ${data.failures.length - 3} more</li>` : ''}
                                    </ul>
                                </div>
                            ` : ''}
                            <div class="flex justify-center mt-6">
                                <button onclick="window.location.reload()" 
                                    class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition">
                                    Continue
                                </button>
                            </div>
                        </div>
                    `;

                    // Show the modal
                    replyModal.classList.remove('hidden');
                } else {
                    // Show error message
                    const errorMessage = data.message || 'Failed to send replies';
                    alert(`Error: ${errorMessage}`);

                    // Log additional error details for debugging
                    if (data.failures) {
                        console.error('Reply failures:', data.failures);
                    }

                    // Reset button state
                    sendButton.disabled = false;
                    sendButton.innerHTML = 'Send Auto Reply';
                }
            }).catch(error => {
                console.error('Error sending replies:', error);
                alert('An error occurred while sending replies. Please try again.');

                // Reset button state
                sendButton.disabled = false;
                sendButton.innerHTML = 'Send Auto Reply';
            });
        })
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Analytics Time Range Filter Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const hasSeenIntro = localStorage.getItem('introShown');
            const screenWidth = window.innerWidth;

            // Show intro tour only on desktop and if not seen before
            if (!hasSeenIntro && screenWidth >= 768) {
                introJs().setOptions({
                    steps: [{
                            element: '#reviewsTabBtn',
                            intro: "This is review tab where we can manage all business reviews"
                        },
                        {
                            element: '#filtersTour',
                            intro: "Filters Options for reviews"
                        },
                        {
                            element: '#dateRangeStep',
                            intro: "We can select any date range as filter the reviews"
                        },
                        {
                            element: '#ratingFiltersStep',
                            intro: "Get reviews according to this ratings and it can be multiple"
                        },
                        {
                            element: '#reviewStatusStep',
                            intro: "Get review replied/non replied from status filter"
                        },
                        {
                            element: '#sortFilterStep',
                            intro: "Sort filter helps you to get older/latest reviews"
                        },
                        {
                            element: '#refreshFilterStep',
                            intro: "We can remove all filters and reset it."
                        },
                        {
                            element: '#analyticsTabBtn',
                            intro: "Check all analytics data in chart and graph"
                        },
                        {
                            element: '#templatesTabBtn',
                            intro: "Below are all the business-related templates available for you to generate and customize as needed."
                        },
                        {
                            element: '#settingsTabBtn',
                            intro: "This is all business related settings options"
                        }
                    ]
                }).start();
                // Mark as shown
                localStorage.setItem('introShown', 'true');
            } else {
                document.getElementById('menubarToggle').addEventListener('click', function() {
                    document.body.classList.toggle('menu-active');
                    this.classList.toggle('active');
                    document.querySelector('.backdrop').classList.remove('hidden');
                });
                document.getElementById('filterMenuBtn').addEventListener('click', function() {
                    document.body.classList.toggle('filter-sidebar-active');
                    document.querySelector('.backdrop').classList.remove('hidden');

                });
                document.getElementsByClassName('filter-close')[0].addEventListener('click', function() {
                    document.body.classList.remove('filter-sidebar-active');
                });
                document.getElementsByClassName('backdrop')[0].addEventListener('click', function() {
                    document.body.classList.remove('sidebar-active', 'menu-active', 'filter-sidebar-active');
                    this.classList.remove('hidden');
                });
            }
            // Get the time range dropdown element
            const timeRangeSelect = document.getElementById('analyticsTimeRange');
            const businessId = document.getElementById('activity_business_id').value;

            // Add event listener for time range change
            timeRangeSelect.addEventListener('change', function() {
                // Update both charts when time range changes
                const selectedTimeRange = this.value;

                // Set force refresh flag to true to bypass cache
                document.getElementById('forceDataRefresh').value = 'true';

                // Update sentiment chart with new time range
                updateChartData('sentiment', selectedTimeRange);

                // Update activity chart with new time range
                setTimeout(() => {
                    updateChartData('activity', selectedTimeRange);
                    // Reset force refresh flag after updates are complete
                    document.getElementById('forceDataRefresh').value = 'false';
                }, 300); // Small delay between requests to prevent server load

                // Clear all cached data to ensure fresh data on next load
                chartDataCache = {
                    'sentiment': {},
                    'activity': {}
                };
            });

            // Function to update summary metrics in the UI
            function updateSummaryMetrics(data) {
                // Update Overall Rating
                const overallRatingEl = document.getElementById('avgRatingValue');
                if (overallRatingEl) {
                    overallRatingEl.textContent = parseFloat(data.overallRating).toFixed(1);
                }

                // Update Rating Stars
                const ratingStarsEl = document.getElementById('ratingStars');
                if (ratingStarsEl) {
                    updateRatingStars(data.overallRating, ratingStarsEl);
                }

                // Update Total Reviews
                const totalReviewsEl = document.getElementById('totalReviewsValue');
                if (totalReviewsEl) {
                    totalReviewsEl.textContent = data.totalReviews.toLocaleString();
                }

                // Update Response Rate
                const responseRateEl = document.getElementById('responseRateValue');
                if (responseRateEl) {
                    responseRateEl.textContent = `${data.responseRate}%`;
                }

                // Update Average Response Time
                const avgResponseTimeEl = document.getElementById('avgResponseTimeValue');
                if (avgResponseTimeEl) {
                    if (data.averageResponseTime > 0) {
                        if (data.averageResponseTime < 24) {
                            avgResponseTimeEl.textContent = `${data.averageResponseTime}h`;
                        } else {
                            avgResponseTimeEl.textContent = `${Math.round(data.averageResponseTime / 24)}d`;
                        }
                    } else {
                        avgResponseTimeEl.textContent = 'N/A';
                    }
                }
            }

            // Helper function to update rating stars display
            function updateRatingStars(rating, element) {
                const fullStars = Math.floor(rating);
                const hasHalfStar = (rating - fullStars) >= 0.5;
                const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

                let starsHTML = '';

                // Add full stars
                for (let i = 0; i < fullStars; i++) {
                    starsHTML += '<i class="icon-star"></i>';
                }

                // Add half star if needed
                if (hasHalfStar) {
                    starsHTML += '<i class="fas fa-star-half-alt"></i>';
                }

                // Add empty stars
                for (let i = 0; i < emptyStars; i++) {
                    starsHTML += '<i class="far fa-star"></i>';
                }

                element.innerHTML = starsHTML;
            }

            //send auto reply code
            document.querySelectorAll(".sendAutoReplies").forEach(button => {
                button.addEventListener('click', function() {
                    // Fetch the attributes from the clicked button
                    const accountId = this.getAttribute('data-account-id');
                    const location = this.getAttribute('data-location');
                    const businessId = this.getAttribute('data-business-id');
                    const locationId = location.split('/')[1];
                    const settings = this.getAttribute('data-settings');

                    // Show the modal first
                    document.body.classList.toggle('filter-sidebar-active');
                    document.getElementById("sendAutoReplyModel").classList.remove('hidden');

                    // Show loading state
                    const container = document.getElementById("defaultSelectedReviewContainer");
                    container.innerHTML = `
                        <div class="flex items-center justify-center py-8">
                            <div class="loader border-4 border-blue-500 border-t-transparent rounded-full w-8 h-8 animate-spin"></div>
                            <p class="text-sm text-gray-500 ml-3">Loading reviews...</p>
                        </div>
                    `;

                    // Fetch auto-reply settings and reviews
                    // Note: accountId is Google Business account ID, businessId is internal DB business ID
                    Promise.all([
                            fetch(`/get-pending-reviews/${accountId}/${businessId}/${locationId}?setting_value=${settings}`),
                            fetch(`/business/${businessId}/auto-reply-settings`)
                        ])
                        .then(responses => Promise.all(responses.map(r => r.json())))
                        .then(([reviewsData, settingsData]) => {
                            // Check if auto-reply is enabled and has valid settings
                            if (settings == 1) {
                                if (settingsData.autoReplyEnabled && settingsData.hasValidSettings) {
                                    populateReviewsContainer(reviewsData, accountId, location, businessId, settings);
                                } else {
                                    showAutoReplyDisabledMessage();
                                }
                            } else {
                                populateReviewsContainer(reviewsData, accountId, location, businessId, settings);
                            }

                        })
                        .catch(error => {
                            console.error('Error fetching data:', error);
                            container.innerHTML = `
                            <div class="flex items-center justify-center py-8">
                                <p class="text-sm text-red-500">Error loading reviews. Please try again.</p>
                            </div>
                        `;
                        });
                });
            });

            // Function to populate the reviews container with AJAX data
            function populateReviewsContainer(reviews, accountId, location, businessId, settings) {
                const container = document.getElementById("defaultSelectedReviewContainer");

                const reviewsHtml = `
                    <div class="flex items-center justify-between border-b pb-2 border-gray-200 mb-4">
                        <label class="inline-flex items-center space-x-2 text-sm text-gray-700">
                            <input type="checkbox" id="selectAllCheckbox" class="accent-indigo-600 w-5 h-5" data-business-id="${accountId}" data-location="${location}">
                            <span>Select All</span>
                        </label>

                        <div class="relative">
                            <input type="text" placeholder="search reviewer" id="searchUserByName" class="text-sm border border-gray-200 rounded-md px-2 py-1 pr-8" />
                            <div id="searchLoader" class="absolute right-2 top-1/2 transform -translate-y-1/2 hidden">
                                <div class="w-4 h-4 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                            </div>
                        </div>
                    </div>

                    <ul class="space-y-4" id="reviewListData">
                        ${reviews.map(review => renderReviewItem(review, businessId, location)).join('')}
                    </ul>
                `;

                container.innerHTML = reviewsHtml;

                // Rebind checkbox events after populating
                bindCheckboxEvents();

                // Rebind search functionality with settings parameter
                bindSearchFunctionality(accountId, businessId, location, settings);
            }

            // Function to show auto-reply disabled message
            function showAutoReplyDisabledMessage() {
                const container = document.getElementById("defaultSelectedReviewContainer");
                container.innerHTML = `
                    <div class="flex flex-col items-center justify-center gap-4 pt-4">
                        <p class="text-sm text-gray-500">Please enable auto-reply from settings option and at least make any template status ON</p>
                    </div>
                `;
            }

            // Function to bind search functionality with debouncing
            function bindSearchFunctionality(accountId, businessId, location, settings) {
                const searchInput = document.getElementById("searchUserByName");
                const searchLoader = document.getElementById("searchLoader");
                const reviewList = document.getElementById("reviewListData");

                if (!searchInput || !reviewList) {
                    console.warn('Search elements not found');
                    return;
                }

                let searchTimeout;

                searchInput.addEventListener("input", function() {
                    const reviewerName = this.value.trim();
                    const locationId = location.split('/')[1];

                    // Clear previous timeout
                    clearTimeout(searchTimeout);

                    // Show loading spinner
                    if (searchLoader) {
                        searchLoader.classList.remove('hidden');
                    }

                    // Debounce the search - wait 300ms after user stops typing
                    searchTimeout = setTimeout(() => {
                        // Build search URL with all necessary parameters
                        const searchParams = new URLSearchParams({
                            reviewer_name: reviewerName,
                            setting_value: settings || '0'
                        });

                        fetch(`/get-pending-reviews/${accountId}/${businessId}/${locationId}?${searchParams}`)
                            .then(res => {
                                if (!res.ok) {
                                    throw new Error(`HTTP error! status: ${res.status}`);
                                }
                                return res.json();
                            })
                            .then(data => {
                                // Check if we have results
                                if (data && data.length > 0) {
                                    // Update review list with search results
                                    reviewList.innerHTML = data.map(review => renderReviewItem(review, businessId, location)).join('');
                                } else if (reviewerName.length > 0) {
                                    // Show "no results found" message for non-empty search
                                    reviewList.innerHTML = `
                                        <li class="flex items-center justify-center py-8">
                                            <p class="text-sm text-gray-500">No reviews found for "${reviewerName}"</p>
                                        </li>
                                    `;
                                } else {
                                    // Empty search - reload all filtered reviews
                                    fetch(`/get-pending-reviews/${accountId}/${businessId}/${locationId}?setting_value=${settings || '0'}`)
                                        .then(res => res.json())
                                        .then(originalData => {
                                            reviewList.innerHTML = originalData.map(review => renderReviewItem(review, businessId, location)).join('');
                                            bindCheckboxEvents();
                                        })
                                        .catch(() => {
                                            reviewList.innerHTML = `
                                                <li class="flex items-center justify-center py-8">
                                                    <p class="text-sm text-red-500">Error loading reviews. Please refresh the page.</p>
                                                </li>
                                            `;
                                        });
                                    return; // Exit early to avoid running bindCheckboxEvents twice
                                }

                                // Rebind checkbox events for new content
                                bindCheckboxEvents();

                                // Hide loading spinner
                                if (searchLoader) {
                                    searchLoader.classList.add('hidden');
                                }
                            })
                            .catch(error => {
                                console.error('Search error:', error);

                                // Show error message to user
                                reviewList.innerHTML = `
                                    <li class="flex items-center justify-center py-8">
                                        <p class="text-sm text-red-500">Error searching reviews. Please try again.</p>
                                    </li>
                                `;

                                // Hide loading spinner
                                if (searchLoader) {
                                    searchLoader.classList.add('hidden');
                                }
                            });
                    }, 300); // 300ms debounce delay
                });

                // Clear search on escape key
                searchInput.addEventListener("keydown", function(e) {
                    if (e.key === 'Escape') {
                        this.value = '';
                        this.dispatchEvent(new Event('input'));
                    }
                });
            }


            let selectedReviewIds = [];

            function updateSelectedArray() {
                selectedReviewIds = Array.from(document.querySelectorAll('.review-checkbox:checked'))
                    .map(cb => cb.value);
            }

            function bindCheckboxEvents() {
                const individualCheckboxes = document.querySelectorAll('.review-checkbox');
                const currentSelectAllCheckbox = document.getElementById('selectAllCheckbox');

                // Exit if no checkboxes found
                if (!currentSelectAllCheckbox || individualCheckboxes.length === 0) {
                    return;
                }

                individualCheckboxes.forEach(cb => {
                    cb.addEventListener('change', () => {
                        if (!cb.checked) {
                            currentSelectAllCheckbox.checked = false;
                        } else {
                            const allChecked = Array.from(individualCheckboxes).every(box => box.checked);
                            if (allChecked) {
                                currentSelectAllCheckbox.checked = true;
                            }
                        }
                        updateSelectedArray();
                    });
                });

                currentSelectAllCheckbox.addEventListener('change', function() {
                    individualCheckboxes.forEach(cb => {
                        cb.checked = this.checked;
                    });
                    updateSelectedArray();
                });

                updateSelectedArray();
            }

            bindCheckboxEvents()

            document.getElementById("generateReplyAutoId").addEventListener('click', function() {
                this.classList.add('hidden');
                let businessId = this.getAttribute('data-business-id')
                let location = this.getAttribute('data-location')
                let locationId = location.split('/')[1]

                // Get the checkbox element at runtime (after AJAX content is loaded)
                const currentSelectAllCheckbox = document.getElementById('selectAllCheckbox');
                let reviewsIds;

                if (currentSelectAllCheckbox && currentSelectAllCheckbox.checked) {
                    // Select all checked - get all review checkbox values
                    reviewsIds = Array.from(document.querySelectorAll('.review-checkbox'))
                        .map(cb => cb.value);
                } else {
                    // Use individually selected checkboxes
                    reviewsIds = Array.from(document.querySelectorAll('.review-checkbox:checked'))
                        .map(cb => cb.value);
                }

                // Check if any reviews are selected
                if (!reviewsIds || reviewsIds.length === 0) {
                    alert('Please select at least one review to generate auto replies.');
                    this.classList.remove('hidden'); // Show the button again
                    return;
                }

                sendAutoReplies(businessId, locationId, reviewsIds)
            })

            function renderReviewItem(review, businessId, location) {
                const ratingMap = {
                    'ONE': 1,
                    'TWO': 2,
                    'THREE': 3,
                    'FOUR': 4,
                    'FIVE': 5,
                };

                const ratingValue = ratingMap[review.star_rating] || 0;

                // Generate stars HTML
                let starsHtml = '';
                for (let i = 0; i < ratingValue; i++) {
                    starsHtml += `<i class="icon-star text-yellow-400"></i>`;
                }
                for (let i = 0; i < (5 - ratingValue); i++) {
                    starsHtml += `<i class="icon-star text-gray-300"></i>`;
                }

                return `
                    <li class="flex items-start gap-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition">
                        <input type="checkbox" class="review-checkbox mt-1 accent-indigo-600 w-5 h-5" name="selected_reviews[]" value="${review.id}" data-business-id="${businessId}" data-location="${location}" />

                        <div class="flex-1">
                            <p class="text-sm text-gray-600 flex items-center gap-2">
                                <span class="font-medium text-gray-800">Name:</span>
                                ${review.reviewer_name}

                                <span class="flex items-center gap-0.5 ml-3">
                                    ${starsHtml}
                                </span>
                            </p>
            ${review.comment == null ? '':`
                            <div class="mt-2 bg-gray-50 border border-gray-100 rounded-md p-3">
                                <span class="text-sm text-gray-700">
                                    <span class="font-medium text-gray-800">Comment:</span>
                                    ${review.comment}
                                </span>
                            </div>
            `}
                        </div>
                    </li>
                `;
            }


        });
    </script>
</body>

</html><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/business-dashboard.blade.php ENDPATH**/ ?>