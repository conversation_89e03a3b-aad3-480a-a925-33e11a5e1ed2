<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\AdminSiteSetting;

class SiteSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $admin = auth('admin')->user();
        
        if (!$admin) {
            return false;
        }

        return $admin->hasPermission('site_settings.edit');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'settings' => 'required|array|max:100', // Limit number of settings that can be updated at once
        ];

        // Get all settings being updated and add their specific validation rules
        $settings = $this->input('settings', []);
        $admin = auth('admin')->user();

        foreach ($settings as $key => $value) {
            $setting = AdminSiteSetting::where('key', $key)->first();
            
            if (!$setting) {
                continue;
            }

            // Check access level
            if ($admin->role !== 'super_admin' && $setting->access_level === 'super_admin') {
                continue;
            }
            
            if ($admin->role === 'moderator' && $setting->access_level !== 'moderator') {
                continue;
            }

            // Add setting-specific validation rules
            $settingRules = $this->getSettingValidationRules($setting);
            if (!empty($settingRules)) {
                $rules["settings.{$key}"] = $settingRules;
            }
        }

        return $rules;
    }

    /**
     * Get validation rules for a specific setting
     */
    private function getSettingValidationRules(AdminSiteSetting $setting): array
    {
        $rules = [];

        // Add required validation if setting is required
        if ($setting->is_required) {
            $rules[] = 'required';
        } else {
            $rules[] = 'nullable';
        }

        // Add type-specific validation
        switch ($setting->type) {
            case 'string':
                $rules[] = 'string';
                $rules[] = 'max:255';
                break;
            case 'text':
                $rules[] = 'string';
                $rules[] = 'max:65535';
                break;
            case 'integer':
                $rules[] = 'integer';
                break;
            case 'float':
                $rules[] = 'numeric';
                break;
            case 'boolean':
                $rules[] = 'boolean';
                break;
            case 'email':
                $rules[] = 'email';
                $rules[] = 'max:255';
                break;
            case 'url':
                $rules[] = 'url';
                $rules[] = 'max:2048';
                break;
            case 'json':
                $rules[] = 'json';
                break;
            case 'select':
                if ($setting->options && is_array($setting->options)) {
                    $rules[] = 'in:' . implode(',', array_keys($setting->options));
                }
                break;
            case 'multiselect':
                $rules[] = 'array';
                if ($setting->options && is_array($setting->options)) {
                    $rules[] = 'in:' . implode(',', array_keys($setting->options));
                }
                break;
        }

        // Add custom validation rules if defined
        if ($setting->validation_rules && is_array($setting->validation_rules)) {
            $rules = array_merge($rules, $setting->validation_rules);
        }

        // Add security-specific rules for sensitive settings
        if ($setting->is_encrypted || strpos($setting->key, 'password') !== false || strpos($setting->key, 'secret') !== false) {
            $rules[] = 'string';
            $rules[] = 'max:255';
            // Don't allow certain characters that could be used for injection
            $rules[] = 'regex:/^[a-zA-Z0-9\-_\.@\s]*$/';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'settings.required' => 'Settings data is required.',
            'settings.array' => 'Settings must be provided as an array.',
            'settings.max' => 'Too many settings provided at once.',
            'settings.*.json' => 'The :attribute field must be valid JSON.',
            'settings.*.regex' => 'The :attribute field contains invalid characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $settings = $this->input('settings', []);
        $sanitizedSettings = [];

        foreach ($settings as $key => $value) {
            // Sanitize the key
            $key = preg_replace('/[^a-zA-Z0-9\._]/', '', $key);
            
            if (empty($key)) {
                continue;
            }

            $setting = AdminSiteSetting::where('key', $key)->first();
            
            if (!$setting) {
                continue;
            }

            // Sanitize the value based on type
            $sanitizedValue = $this->sanitizeSettingValue($setting, $value);
            $sanitizedSettings[$key] = $sanitizedValue;
        }

        $this->merge(['settings' => $sanitizedSettings]);
    }

    /**
     * Sanitize setting value based on its type
     */
    private function sanitizeSettingValue(AdminSiteSetting $setting, $value)
    {
        if (is_null($value)) {
            return null;
        }

        switch ($setting->type) {
            case 'string':
            case 'text':
                // Strip tags and trim whitespace
                return trim(strip_tags($value));
                
            case 'email':
                // Basic email sanitization
                return filter_var(trim($value), FILTER_SANITIZE_EMAIL);
                
            case 'url':
                // Basic URL sanitization
                return filter_var(trim($value), FILTER_SANITIZE_URL);
                
            case 'integer':
                return (int) $value;
                
            case 'float':
                return (float) $value;
                
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                
            case 'json':
                if (is_string($value)) {
                    // Validate JSON and return as string
                    $decoded = json_decode($value, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        return $value;
                    }
                }
                return null;
                
            case 'multiselect':
                if (is_array($value)) {
                    // Sanitize each value in the array
                    return array_map(function($item) {
                        return trim(strip_tags($item));
                    }, $value);
                }
                return [];
                
            default:
                return trim(strip_tags($value));
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $settings = $this->input('settings', []);
            $admin = auth('admin')->user();

            foreach ($settings as $key => $value) {
                $setting = AdminSiteSetting::where('key', $key)->first();
                
                if (!$setting) {
                    $validator->errors()->add("settings.{$key}", "Setting '{$key}' does not exist.");
                    continue;
                }

                // Additional access level check
                if ($admin->role !== 'super_admin' && $setting->access_level === 'super_admin') {
                    $validator->errors()->add("settings.{$key}", "You do not have permission to modify this setting.");
                    continue;
                }

                // Validate JSON format for JSON type settings
                if ($setting->type === 'json' && !empty($value)) {
                    if (is_string($value)) {
                        $decoded = json_decode($value, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            $validator->errors()->add("settings.{$key}", "Invalid JSON format.");
                        }
                    }
                }

                // Validate multiselect options
                if ($setting->type === 'multiselect' && is_array($value) && $setting->options) {
                    $validOptions = array_keys($setting->options);
                    foreach ($value as $selectedValue) {
                        if (!in_array($selectedValue, $validOptions)) {
                            $validator->errors()->add("settings.{$key}", "Invalid option selected: {$selectedValue}");
                        }
                    }
                }
            }
        });
    }
}
