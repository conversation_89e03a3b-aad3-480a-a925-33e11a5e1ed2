/**
 * Permissions Handler - JavaScript utility for managing UI based on user permissions
 * 
 * This utility provides functions to check and enforce permissions in the frontend UI.
 * It works with the Laravel backend permission system to consistently apply permissions.
 */

class PermissionsHandler {
    constructor() {
        this.userRole = null;
        this.permissions = [];
        this.isOwner = false;
        this.initialized = false;
    }

    /**
     * Initialize the permissions handler with user data
     * @param {Object} userData - User data containing role and permissions
     */
    init(userData) {
        if (!userData) {
            console.error('PermissionsHandler: No user data provided');
            return;
        }

        this.userRole = userData.role || null;
        this.permissions = userData.permissions || [];
        this.isOwner = userData.isOwner || false;
        this.initialized = true;

        // Apply permissions to UI elements immediately after initialization
        this.applyPermissionsToUI();
    }

    /**
     * Check if the user has a specific permission
     * @param {string} permission - The permission to check
     * @returns {boolean} - Whether the user has the permission
     */
    hasPermission(permission) {
        if (!this.initialized) {
            console.warn('PermissionsHandler: Not initialized yet');
            return false;
        }

        // Owner and admin have all permissions
        if (this.isOwner || this.userRole === 'admin') {
            return true;
        }

        // Check specific permission
        return this.permissions.includes(permission);
    }

    /**
     * Apply permissions to UI elements based on data-requires-permission attributes
     */
    applyPermissionsToUI() {
        if (!this.initialized) {
            console.warn('PermissionsHandler: Not initialized yet');
            return;
        }

        // Find all elements with data-requires-permission attribute
        const elements = document.querySelectorAll('[data-requires-permission]');
        
        elements.forEach(element => {
            const requiredPermission = element.getAttribute('data-requires-permission');
            
            if (!this.hasPermission(requiredPermission)) {
                // If user doesn't have permission, either hide or disable the element
                if (element.hasAttribute('data-permission-hide')) {
                    element.style.display = 'none';
                } else {
                    element.disabled = true;
                    element.classList.add('permission-disabled');
                    
                    // Add tooltip explaining why it's disabled
                    element.setAttribute('title', 'You do not have permission to perform this action');
                    
                    // For links, prevent navigation
                    if (element.tagName === 'A') {
                        element.addEventListener('click', (e) => {
                            e.preventDefault();
                            this.showPermissionDeniedMessage();
                        });
                    }
                }
            }
        });
    }

    /**
     * Show a permission denied message
     * @param {string} customMessage - Optional custom message to display
     */
    showPermissionDeniedMessage(customMessage = null) {
        const message = customMessage || 'You do not have permission to perform this action';
        
        // Check if we have a toast notification system
        if (typeof showToast === 'function') {
            showToast('error', message);
        } else {
            alert(message);
        }
    }

    /**
     * Load user permissions from the server
     * @param {number} businessAccountId - The business account ID
     * @returns {Promise} - Promise resolving to the user permissions
     */
    loadUserPermissions(businessAccountId) {
        return fetch(`/api/user/permissions/${businessAccountId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load permissions');
                }
                return response.json();
            })
            .then(data => {
                this.init(data);
                return data;
            })
            .catch(error => {
                console.error('Error loading permissions:', error);
                return null;
            });
    }
}

// Create a global instance
const permissionsHandler = new PermissionsHandler();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = permissionsHandler;
}
