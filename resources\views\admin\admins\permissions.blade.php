@extends('admin.layouts.app')

@section('title', 'Admin Permissions')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Admin Permissions</h1>
                    <p class="mt-2 text-sm text-gray-600">Overview of permissions for each admin role</p>
                </div>
                <a href="{{ route('admin.admins.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Admins
                </a>
            </div>
        </div>

        <!-- Role Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            @foreach($roles as $roleKey => $roleName)
                @php
                    $adminModel = new \App\Models\Admin(['role' => $roleKey]);
                    $rolePermissions = $adminModel->getPermissions();
                @endphp
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">{{ $roleName }}</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($roleKey === 'super_admin') bg-purple-100 text-purple-800
                                @elseif($roleKey === 'admin') bg-blue-100 text-blue-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ count($rolePermissions) }} permissions
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-2">
                            @foreach($rolePermissions as $permission)
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">{{ ucfirst(str_replace(['.', '_'], [' ', ' '], $permission)) }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Detailed Permissions Matrix -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Permissions Matrix</h3>
                <p class="mt-1 text-sm text-gray-600">Detailed breakdown of permissions by module and role</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Module</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permission</th>
                            @foreach($roles as $roleKey => $roleName)
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $roleName }}</th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($permissions as $module => $modulePermissions)
                            @foreach($modulePermissions as $permissionKey => $permissionName)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $module }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                        {{ $permissionName }}
                                    </td>
                                    @foreach($roles as $roleKey => $roleName)
                                        @php
                                            $adminModel = new \App\Models\Admin(['role' => $roleKey]);
                                            $hasPermission = $adminModel->hasPermission($permissionKey);
                                        @endphp
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            @if($hasPermission)
                                                <svg class="w-5 h-5 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            @else
                                                <svg class="w-5 h-5 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            @endif
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Permission Descriptions -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Role Descriptions</h3>
                <p class="mt-1 text-sm text-gray-600">Understanding the different admin roles and their capabilities</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="border-l-4 border-purple-400 pl-4">
                        <h4 class="text-lg font-medium text-purple-900">Super Admin</h4>
                        <p class="mt-1 text-sm text-gray-600">
                            Has complete access to all system features and settings. Can manage all users, admins, and system configurations. 
                            This role should be reserved for system administrators and key personnel only.
                        </p>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Full System Access
                            </span>
                        </div>
                    </div>

                    <div class="border-l-4 border-blue-400 pl-4">
                        <h4 class="text-lg font-medium text-blue-900">Admin</h4>
                        <p class="mt-1 text-sm text-gray-600">
                            Has access to most administrative features including user management, subscription management, and analytics. 
                            Cannot manage other admins or system settings. Ideal for day-to-day administrative tasks.
                        </p>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Administrative Access
                            </span>
                        </div>
                    </div>

                    <div class="border-l-4 border-gray-400 pl-4">
                        <h4 class="text-lg font-medium text-gray-900">Moderator</h4>
                        <p class="mt-1 text-sm text-gray-600">
                            Has read-only access to most system features. Can view users, subscriptions, analytics, and other data 
                            but cannot make changes. Perfect for support staff or reporting roles.
                        </p>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Read-Only Access
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Notes -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="px-6 py-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Security Considerations</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Super Admin accounts should be limited and closely monitored</li>
                                <li>Regular audits of admin permissions and access logs are recommended</li>
                                <li>Inactive admin accounts should be disabled promptly</li>
                                <li>Use strong passwords and consider implementing two-factor authentication</li>
                                <li>Admin accounts should not be shared between multiple users</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
