<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('external_api_logs', function (Blueprint $table) {
            $table->id();
            $table->enum('log_type', ['google_api', 'ai']);
            $table->text('endpoint');
            $table->json('request_payload')->nullable();
            $table->json('response_payload')->nullable();
            $table->integer('status_code')->nullable();
            $table->text('status_message');
            $table->integer('duration_ms')->nullable();
            $table->string('business_id', 255)->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->timestamps();

            // MySQL index length fix: limit endpoint index to 100 chars
            $table->index(['log_type', 'created_at']);
            // We'll add the endpoint index with length 100 using a raw DB statement after table creation (see below)            
            $table->index('user_id');
        });
        // Add index for endpoint with length 100 (MySQL limitation workaround)
        if (env('DB_CONNECTION') === 'mysql') {
            \DB::statement('CREATE INDEX external_api_logs_endpoint_index ON external_api_logs (endpoint(100))');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('external_api_logs');
    }
};
