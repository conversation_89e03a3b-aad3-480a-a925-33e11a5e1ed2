<?php

namespace App\Services;

use App\Models\IpLocation;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class IpLocationService
{
    private const FREEIPAPI_BASE_URL = 'https://freeipapi.com/api/json/';
    private const CACHE_TTL = 86400; // 24 hours in seconds

    /**
     * Get IP location data
     * First checks database, then calls external API if not found
     */
    public function getIpLocation($ipAddress)
    {
        try {
            // Validate IP address
            if (!$this->isValidIpAddress($ipAddress)) {
                throw new \InvalidArgumentException('Invalid IP address format');
            }

            // Check if data exists in database
            $existingLocation = IpLocation::findByIp($ipAddress);
            if ($existingLocation) {
                return $this->formatResponse($existingLocation);
            }

            // If not in database, call external API
            $apiData = $this->fetchFromExternalApi($ipAddress);
            
            // Store in database
            $location = $this->storeIpLocation($ipAddress, $apiData);
            
            return $this->formatResponse($location);

        } catch (\Exception $e) {
            Log::error('IP Location Service Error: ' . $e->getMessage(), [
                'ip_address' => $ipAddress,
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Validate IP address format
     */
    private function isValidIpAddress($ipAddress)
    {
        return filter_var($ipAddress, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Fetch data from external API
     */
    private function fetchFromExternalApi($ipAddress)
    {
        $url = self::FREEIPAPI_BASE_URL . $ipAddress;
        
        try {
            $response = Http::timeout(10)->get($url);
            
            if (!$response->successful()) {
                throw new \Exception('External API request failed with status: ' . $response->status());
            }

            $data = $response->json();
            
            if (!$data || !isset($data['countryName'])) {
                throw new \Exception('Invalid response from external API');
            }

            return $data;

        } catch (\Exception $e) {
            Log::error('External API Error: ' . $e->getMessage(), [
                'url' => $url,
                'ip_address' => $ipAddress
            ]);
            
            throw new \Exception('Failed to fetch location data from external service: ' . $e->getMessage());
        }
    }

    /**
     * Store IP location data in database
     */
    private function storeIpLocation($ipAddress, $apiData)
    {
        try {
            $locationData = [
                'ip_address' => $ipAddress,
                'ip_version' => $apiData['ipVersion'] ?? null,
                'latitude' => $apiData['latitude'] ?? null,
                'longitude' => $apiData['longitude'] ?? null,
                'country_name' => $apiData['countryName'] ?? null,
                'country_code' => $apiData['countryCode'] ?? null,
                'capital' => $apiData['capital'] ?? null,
                'phone_codes' => $apiData['phoneCodes'] ?? null,
                'time_zones' => $apiData['timeZones'] ?? null,
                'city_name' => $apiData['cityName'] ?? null,
                'region_name' => $apiData['regionName'] ?? null,
                'continent' => $apiData['continent'] ?? null,
                'continent_code' => $apiData['continentCode'] ?? null,
                'currencies' => $apiData['currencies'] ?? null,
                'primary_currency' => isset($apiData['currencies']) && is_array($apiData['currencies']) && count($apiData['currencies']) > 0 
                    ? $apiData['currencies'][0] 
                    : null,
                'languages' => $apiData['languages'] ?? null,
                'asn' => $apiData['asn'] ?? null,
                'asn_organization' => $apiData['asnOrganization'] ?? null,
            ];

            return IpLocation::create($locationData);

        } catch (\Exception $e) {
            Log::error('Database Storage Error: ' . $e->getMessage(), [
                'ip_address' => $ipAddress,
                'api_data' => $apiData
            ]);
            
            throw new \Exception('Failed to store location data: ' . $e->getMessage());
        }
    }

    /**
     * Format response for API output
     */
    private function formatResponse($location)
    {
        return [
            'countryName' => $location->country_name,
            'countryCode' => $location->country_code,
            'primaryCurrency' => $location->primary_currency,
            'ipAddress' => $location->ip_address,
            'cityName' => $location->city_name,
            'regionName' => $location->region_name,
            'continent' => $location->continent,
            'latitude' => $location->latitude,
            'longitude' => $location->longitude,
            'timeZones' => $location->time_zones,
            'currencies' => $location->currencies,
            'languages' => $location->languages
        ];
    }

    /**
     * Get client IP address from request
     */
    public function getClientIpAddress($request)
    {
        // Check for various headers that might contain the real IP
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if ($this->isValidIpAddress($ip) && !$this->isPrivateIp($ip)) {
                    return $ip;
                }
            }
        }

        // Fallback to request IP
        return $request->ip();
    }

    /**
     * Check if IP is private/local
     */
    private function isPrivateIp($ip)
    {
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }
}
