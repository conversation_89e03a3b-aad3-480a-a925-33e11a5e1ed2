<?php

use App\Models\BusinessAccount;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

if (!function_exists('google_api_request')) {
    function google_api_request($url, $accessToken, $method = 'GET', $data = null)
    {
        // Use GoogleBusinessService for all Google API requests
        $googleBusinessService = new \App\Services\GoogleBusinessService($accessToken);
        return $googleBusinessService->makeRequest($url, $accessToken, $method, $data);
    }
}


if (!function_exists('isGoogleAccessTokenValid')) {
    function isGoogleAccessTokenValid($token)
    {
        if (!$token) return false;

        $response = Http::withToken($token)->get('https://www.googleapis.com/oauth2/v3/userinfo');
        return $response->ok();        
    }
}

if (!function_exists('refreshGoogleAccessToken')) {
    function refreshGoogleAccessToken($refreshToken)
    {
        $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
            'client_id' => config('services.google.client_id'),
            'client_secret' => config('services.google.client_secret'),
            'refresh_token' => $refreshToken,
            'grant_type' => 'refresh_token',
        ]);

        if ($response->successful()) {
            return $response->json()['access_token'];
        }

        return null;
    }
}


if (!function_exists('getGoogleTokens')) {
    function getGoogleTokens($userId)
    {
        $businessAccountGoogleTokens = BusinessAccount::where('user_id', $userId)->first();
        return $businessAccountGoogleTokens;
    }
}

if (!function_exists('availableTones')) {
    function availableTones()
    {
        return [
            ['id' => 'friendly', 'name' => 'Friendly'],
            ['id' => 'professional', 'name' => 'Professional'],
            ['id' => 'apologetic', 'name' => 'Apologetic'],
            ['id' => 'thankful', 'name' => 'Thankful'],
            ['id' => 'formal', 'name' => 'Formal'],
            ['id' => 'casual', 'name' => 'Casual'],
        ];
    }
}

if (!function_exists('availableLanguages')) {
    function availableLanguages()
    {
        return [
            ['id' => 'same-as-review', 'name' => 'Same as review'],
            ['id' => 'english', 'name' => 'English'],
            ['id' => 'spanish', 'name' => 'Spanish'],
            ['id' => 'french', 'name' => 'French'],
            ['id' => 'german', 'name' => 'German'],
            ['id' => 'italian', 'name' => 'Italian'],
            ['id' => 'portuguese', 'name' => 'Portuguese'],
            ['id' => 'dutch', 'name' => 'Dutch'],
            ['id' => 'russian', 'name' => 'Russian'],
            ['id' => 'chinese', 'name' => 'Chinese (Simplified)'],
            ['id' => 'japanese', 'name' => 'Japanese'],
            ['id' => 'korean', 'name' => 'Korean'],
            ['id' => 'arabic', 'name' => 'Arabic'],
            ['id' => 'hindi', 'name' => 'Hindi'],
        ];
    }
}

if (!function_exists('availableAiProviders')) {
    function availableAiProviders()
    {
        return [
            [
                'id' => 'chatgpt',
                'name' => 'ChatGPT (OpenAI)',
                'models' => [
                    ['id' => 'gpt-3.5-turbo', 'name' => 'GPT-3.5 Turbo'],
                    ['id' => 'gpt-4', 'name' => 'GPT-4'],
                    ['id' => 'gpt-4-turbo', 'name' => 'GPT-4 Turbo'],
                ],
            ],
            [
                'id' => 'claude',
                'name' => 'Claude (Anthropic)',
                'models' => [
                    ['id' => 'claude-2', 'name' => 'Claude 2'],
                    ['id' => 'claude-3-opus', 'name' => 'Claude 3 Opus'],
                    ['id' => 'claude-3-sonnet', 'name' => 'Claude 3 Sonnet'],
                    ['id' => 'claude-3-haiku', 'name' => 'Claude 3 Haiku'],
                ],
            ],
            [
                'id' => 'gemini',
                'name' => 'Gemini (Google)',
                'models' => [
                    ['id' => 'gemini-pro', 'name' => 'Gemini Pro'],
                    ['id' => 'gemini-ultra', 'name' => 'Gemini Ultra'],
                ],
            ],
        ];
    }
}

if (!function_exists('availableSentiments')) {
    function availableSentiments()
    {
        return [
            ['id' => 'any', 'name' => 'Any'],
            ['id' => 'positive', 'name' => 'Positive Only'],
            ['id' => 'negative', 'name' => 'Negative Only'],
            ['id' => 'neutral', 'name' => 'Neutral Only'],
        ];
    }
}

// Helper for associative array (id => name) for languages
if (!function_exists('languageOptionsAssoc')) {
    function languageOptionsAssoc()
    {
        return collect(availableLanguages())->pluck('name', 'id')->toArray();
    }
}

// Helper for associative array (id => name) for tones
if (!function_exists('toneOptionsAssoc')) {
    function toneOptionsAssoc()
    {
        return collect(availableTones())->pluck('name', 'id')->toArray();
    }
}

// Helper for associative array (id => name) for sentiments
if (!function_exists('sentimentOptionsAssoc')) {
    function sentimentOptionsAssoc()
    {
        return collect(availableSentiments())->pluck('name', 'id')->toArray();
    }
}

// Helper for flat AI model list (id => name)
if (!function_exists('aiModelOptionsAssoc')) {
    function aiModelOptionsAssoc()
    {
        $models = [];
        foreach (availableAiProviders() as $provider) {
            foreach ($provider['models'] as $model) {
                $models[$model['id']] = $model['name'];
            }
        }
        return $models;
    }
}
