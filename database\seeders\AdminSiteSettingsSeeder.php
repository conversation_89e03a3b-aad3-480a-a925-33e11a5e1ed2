<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdminSiteSetting;

class AdminSiteSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // AI Settings
            [
                'key' => 'ai.provider',
                'name' => 'AI Provider',
                'description' => 'Default AI provider for the application',
                'value' => 'openai',
                'type' => 'select',
                'category' => 'ai',
                'group' => 'general',
                'sort_order' => 1,
                'options' => ['openai' => 'OpenAI', 'anthropic' => 'Anthropic', 'google' => 'Google AI'],
                'help_text' => 'Select the default AI provider for generating responses',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'super_admin',
            ],
            [
                'key' => 'ai.max_tokens',
                'name' => 'Maximum Tokens',
                'description' => 'Maximum number of tokens for AI responses',
                'value' => '1000',
                'type' => 'integer',
                'category' => 'ai',
                'group' => 'limits',
                'sort_order' => 2,
                'validation_rules' => ['required', 'integer', 'min:100', 'max:4000'],
                'help_text' => 'Maximum tokens allowed for AI-generated responses (100-4000)',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'super_admin',
            ],
            [
                'key' => 'ai.temperature',
                'name' => 'AI Temperature',
                'description' => 'Creativity level for AI responses (0.0 to 1.0)',
                'value' => '0.7',
                'type' => 'float',
                'category' => 'ai',
                'group' => 'parameters',
                'sort_order' => 3,
                'validation_rules' => ['required', 'numeric', 'min:0', 'max:1'],
                'help_text' => 'Controls randomness in AI responses. Lower values = more focused, higher values = more creative',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'admin',
            ],

            // Google API Settings
            [
                'key' => 'google.api_key',
                'name' => 'Google API Key',
                'description' => 'Google API key for business profile access',
                'value' => '',
                'type' => 'string',
                'category' => 'google_api',
                'group' => 'authentication',
                'sort_order' => 1,
                'validation_rules' => ['required', 'string'],
                'help_text' => 'API key for Google My Business API access',
                'is_public' => false,
                'is_required' => true,
                'is_encrypted' => true,
                'access_level' => 'super_admin',
            ],
            [
                'key' => 'google.client_id',
                'name' => 'Google Client ID',
                'description' => 'Google OAuth client ID',
                'value' => '',
                'type' => 'string',
                'category' => 'google_api',
                'group' => 'oauth',
                'sort_order' => 2,
                'validation_rules' => ['required', 'string'],
                'help_text' => 'OAuth 2.0 client ID for Google authentication',
                'is_public' => true,
                'is_required' => true,
                'access_level' => 'super_admin',
            ],
            [
                'key' => 'google.client_secret',
                'name' => 'Google Client Secret',
                'description' => 'Google OAuth client secret',
                'value' => '',
                'type' => 'string',
                'category' => 'google_api',
                'group' => 'oauth',
                'sort_order' => 3,
                'validation_rules' => ['required', 'string'],
                'help_text' => 'OAuth 2.0 client secret for Google authentication',
                'is_public' => false,
                'is_required' => true,
                'is_encrypted' => true,
                'access_level' => 'super_admin',
            ],
            [
                'key' => 'google.rate_limit_per_minute',
                'name' => 'Google API Rate Limit',
                'description' => 'Maximum API calls per minute to Google',
                'value' => '100',
                'type' => 'integer',
                'category' => 'google_api',
                'group' => 'limits',
                'sort_order' => 4,
                'validation_rules' => ['required', 'integer', 'min:1', 'max:1000'],
                'help_text' => 'Rate limit for Google API calls to prevent quota exhaustion',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'admin',
            ],

            // Email Settings
            [
                'key' => 'email.from_address',
                'name' => 'From Email Address',
                'description' => 'Default from email address for system emails',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'email',
                'group' => 'general',
                'sort_order' => 1,
                'validation_rules' => ['required', 'email'],
                'help_text' => 'Email address used as sender for system emails',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'admin',
            ],
            [
                'key' => 'email.from_name',
                'name' => 'From Name',
                'description' => 'Default from name for system emails',
                'value' => 'ReviewBiz',
                'type' => 'string',
                'category' => 'email',
                'group' => 'general',
                'sort_order' => 2,
                'validation_rules' => ['required', 'string', 'max:100'],
                'help_text' => 'Name displayed as sender for system emails',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'admin',
            ],
            [
                'key' => 'email.support_address',
                'name' => 'Support Email',
                'description' => 'Support email address for customer inquiries',
                'value' => '<EMAIL>',
                'type' => 'email',
                'category' => 'email',
                'group' => 'support',
                'sort_order' => 3,
                'validation_rules' => ['required', 'email'],
                'help_text' => 'Email address for customer support',
                'is_public' => true,
                'is_required' => true,
                'access_level' => 'admin',
            ],

            // Application Settings
            [
                'key' => 'app.maintenance_mode',
                'name' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode for the application',
                'value' => 'false',
                'type' => 'boolean',
                'category' => 'application',
                'group' => 'general',
                'sort_order' => 1,
                'help_text' => 'When enabled, only admins can access the application',
                'is_public' => true,
                'is_required' => false,
                'access_level' => 'super_admin',
            ],
            [
                'key' => 'app.max_businesses_per_user',
                'name' => 'Max Businesses Per User',
                'description' => 'Maximum number of businesses a user can connect',
                'value' => '25',
                'type' => 'integer',
                'category' => 'application',
                'group' => 'limits',
                'sort_order' => 2,
                'validation_rules' => ['required', 'integer', 'min:1', 'max:100'],
                'help_text' => 'Global limit for business connections per user',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'admin',
            ],
            [
                'key' => 'app.session_timeout',
                'name' => 'Session Timeout (minutes)',
                'description' => 'User session timeout in minutes',
                'value' => '120',
                'type' => 'integer',
                'category' => 'application',
                'group' => 'security',
                'sort_order' => 3,
                'validation_rules' => ['required', 'integer', 'min:15', 'max:1440'],
                'help_text' => 'Automatic logout time for inactive users (15-1440 minutes)',
                'is_public' => false,
                'is_required' => true,
                'access_level' => 'admin',
            ],
        ];

        foreach ($settings as $setting) {
            AdminSiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
