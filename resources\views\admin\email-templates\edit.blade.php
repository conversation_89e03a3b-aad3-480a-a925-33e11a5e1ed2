 @extends('admin.layouts.app')

@section('title', 'Edit Email Template')
@section('page-title', 'Edit Email Template')

@push('styles')
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .variable-tag {
        @apply px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded cursor-pointer hover:bg-blue-200;
    }
    .ql-editor {
        min-height: 300px;
    }
    .preview-frame {
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        min-height: 400px;
    }
</style>
@endpush

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Email Template</h1>
            <p class="text-gray-600">Update the email template details and content</p>
        </div>
        <a href="{{ route('admin.email-templates.index') }}" 
           class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
            <i class="fas fa-arrow-left mr-2"></i>Back to Templates
        </a>
    </div>

    <form method="POST" action="{{ route('admin.email-templates.update', $emailTemplate) }}" id="templateForm">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Template Name *</label>
                                <input type="text" name="name" value="{{ old('name', $emailTemplate->name) }}" required
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Subject Line *</label>
                                <input type="text" name="subject" value="{{ old('subject', $emailTemplate->subject) }}" required
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('subject') border-red-500 @enderror">
                                @error('subject')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea name="description" rows="2"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('description') border-red-500 @enderror"</textarea>
                            @error('description')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Email Content -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Email Content</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">HTML Content *</label>
                            <div id="html-editor" style="height: 300px;">{!! old('html_content', $emailTemplate->html_content) !!}</div>
                            <input type="hidden" name="html_content" id="html_content">
                            @error('html_content')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Plain Text Content</label>
                            <textarea id="editor" name="text_content" rows="4"
                                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 @error('text_content') border-red-500 @enderror"
                                      placeholder="Plain text version of the email">{{ old('text_content', $emailTemplate->text_content) }}</textarea>
                            @error('text_content')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Variables -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Variables</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Available Variables</label>
                            <div id="available-variables" class="border border-gray-300 rounded-md p-3 min-h-[100px]">
                                @if($emailTemplate->available_variables && count($emailTemplate->available_variables) > 0)
                                    @foreach($emailTemplate->available_variables as $variable)
                                        <span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded mr-2 mb-2 cursor-pointer" 
                                              onclick="toggleRequired('{!! $variable !!}')">{!! $variable !!}</span>
                                    @endforeach
                                @else
                                    <p class="text-sm text-gray-500">No variables selected</p>
                                @endif
                            </div>
                            <input type="hidden" name="available_variables" id="available_variables_input" value="{{ json_encode($emailTemplate->available_variables) }}">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Variables</label>
                            <div id="required-variables" class="border border-gray-300 rounded-md p-3 min-h-[100px]">
                                @if($emailTemplate->required_variables && count($emailTemplate->required_variables) > 0)
                                    @foreach($emailTemplate->required_variables as $variable)
                                        <span class="inline-block px-2 py-1 text-xs bg-red-100 text-red-800 rounded mr-2 mb-2 cursor-pointer" 
                                              onclick="toggleRequired('{{ $variable }}')">{{ $variable }}</span>
                                    @endforeach
                                @else
                                    <p class="text-sm text-gray-500">No required variables</p>
                                @endif
                            </div>
                            <input type="hidden" name="required_variables" id="required_variables_input" value="{{ json_encode($emailTemplate->required_variables) }}">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Live Preview</h3>
                    </div>
                    <div class="p-6">
                        <iframe id="preview-frame" class="preview-frame w-full" src="about:blank"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script src="https://cdn.quilljs.com/1.3.6/quill.js"></script>
<script>
    // Initialize Quill editor
    var quill = new Quill('#html-editor', {
        theme: 'snow'
    });

    // Set initial content
    quill.root.innerHTML = {!! json_encode(old('html_content', $emailTemplate->html_content)) !!};

    // Variable management
    let availableVariables = {!! json_encode($emailTemplate->available_variables) !!};
    let requiredVariables = {!! json_encode($emailTemplate->required_variables) !!};

    // Update variable display
    function updateVariableDisplay() {
        const availableDiv = document.getElementById('available-variables');
        const requiredDiv = document.getElementById('required-variables');

        // Update available variables
        if (availableVariables.length > 0) {
            availableDiv.innerHTML = availableVariables.map(variable => 
                `<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded mr-2 mb-2 cursor-pointer" 
                       onclick="toggleRequired('${variable}')">{!! $variable !!}</span>`
            ).join('');
        } else {
            availableDiv.innerHTML = '<p class="text-sm text-gray-500">No variables selected</p>';
        }

        // Update required variables
        if (requiredVariables.length > 0) {
            requiredDiv.innerHTML = requiredVariables.map(variable => 
                `<span class="inline-block px-2 py-1 text-xs bg-red-100 text-red-800 rounded mr-2 mb-2 cursor-pointer" 
                       onclick="toggleRequired('${variable}')">{!! $variable !!}</span>`
            ).join('');
        } else {
            requiredDiv.innerHTML = '<p class="text-sm text-gray-500">No required variables</p>';
        }

        // Update hidden inputs
        document.getElementById('available_variables_input').value = JSON.stringify(availableVariables);
        document.getElementById('required_variables_input').value = JSON.stringify(requiredVariables);
    }

    // Toggle required variable
    window.toggleRequired = function(variable) {
        const index = requiredVariables.indexOf(variable);
        if (index > -1) {
            requiredVariables.splice(index, 1);
        } else {
            requiredVariables.push(variable);
        }
        updateVariableDisplay();
    };

    // Form submission
    document.getElementById('templateForm').addEventListener('submit', function() {
        document.getElementById('html_content').value = quill.root.innerHTML;
    });

    // Preview functionality
    document.getElementById('preview-btn').addEventListener('click', function() {
        document.getElementById('html_content').value = quill.root.innerHTML;
        updatePreview();
    });

    function updatePreview() {
        const htmlContent = quill.root.innerHTML;
        const iframe = document.getElementById('preview-frame');
        const doc = iframe.contentDocument || iframe.contentWindow.document;
        
        doc.open();
        doc.write(htmlContent);
        doc.close();
    }
</script>
@endpush


