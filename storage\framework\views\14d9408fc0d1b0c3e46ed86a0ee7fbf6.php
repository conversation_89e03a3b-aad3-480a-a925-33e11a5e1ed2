<?php $__env->startSection('title', 'Edit Plan - ' . $plan->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Plan: <?php echo e($plan->name); ?></h1>
            <p class="text-gray-600 mt-1">Update plan details, features and pricing</p>
        </div>
        <div class="flex gap-3 mt-4 sm:mt-0">
            <a href="<?php echo e(route('admin.plans.show', $plan)); ?>" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-eye mr-2"></i>View Plan
            </a>
            <a href="<?php echo e(route('admin.plans.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Plans
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="<?php echo e(route('admin.plans.update', $plan)); ?>" method="POST" class="space-y-6">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div class="p-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="lg:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    </div>

                    <!-- Plan Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Plan Name *</label>
                        <input type="text" name="name" id="name" value="<?php echo e(old('name', $plan->name)); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Currency -->
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Currency *</label>
                        <select name="currency" id="currency" required onchange="updateCurrencySymbol()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <option value="">Select Currency</option>
                            <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($code); ?>" <?php echo e(old('currency', $plan->currency) === $code ? 'selected' : ''); ?>>
                                    <?php echo e($code); ?> (<?php echo e($info['symbol']); ?>) - <?php echo e($info['name']); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Short Description -->
                    <div class="lg:col-span-2">
                        <label for="short_description" class="block text-sm font-medium text-gray-700 mb-2">Short Description *</label>
                        <input type="text" name="short_description" id="short_description" value="<?php echo e(old('short_description', $plan->short_description)); ?>" required
                               placeholder="Brief description for plan cards"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['short_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['short_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Long Description -->
                    <div class="lg:col-span-2">
                        <label for="long_description" class="block text-sm font-medium text-gray-700 mb-2">Long Description *</label>
                        <textarea name="long_description" id="long_description" rows="4" required
                                  placeholder="Detailed description for plan details page"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['long_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('long_description', $plan->long_description)); ?></textarea>
                        <?php $__errorArgs = ['long_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="lg:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Information</h3>
                    </div>

                    <!-- Monthly Price -->
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Monthly Price *</label>
                        <div class="relative">
                            <span id="currency-symbol" class="absolute left-3 top-2 text-gray-500"><?php echo e($plan->currency_symbol); ?></span>
                            <input type="number" name="price" id="price" value="<?php echo e(old('price', $plan->price)); ?>" step="0.01" min="0" required
                                   class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        </div>
                        <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Duration -->
                    <div>
                        <label for="duration_days" class="block text-sm font-medium text-gray-700 mb-2">Duration (Days) *</label>
                        <input type="number" name="duration_days" id="duration_days" value="<?php echo e(old('duration_days', $plan->duration_days)); ?>" min="1" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['duration_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['duration_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Annual Discount -->
                    <div>
                        <label for="annual_discount_percentage" class="block text-sm font-medium text-gray-700 mb-2">Annual Discount (%)</label>
                        <input type="number" name="annual_discount_percentage" id="annual_discount_percentage" 
                               value="<?php echo e(old('annual_discount_percentage', $plan->annual_discount_percentage)); ?>" min="0" max="100" onchange="calculateAnnualPrice()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['annual_discount_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for no annual discount</p>
                        <?php $__errorArgs = ['annual_discount_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Tax Percentage -->
                    <div>
                        <label for="tax_percentage" class="block text-sm font-medium text-gray-700 mb-2">Tax Percentage (%)</label>
                        <input type="number" name="tax_percentage" id="tax_percentage" value="<?php echo e(old('tax_percentage', $plan->tax_percentage)); ?>" 
                               step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 <?php $__errorArgs = ['tax_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['tax_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Annual Price Preview -->
                    <div class="lg:col-span-2">
                        <div id="annual-price-preview" class="hidden p-4 bg-blue-50 rounded-lg">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Annual Pricing Preview</h4>
                            <div class="text-sm text-blue-800">
                                <div>Regular Annual Price: <span id="regular-annual-price"><?php echo e($plan->currency_symbol); ?>0.00</span></div>
                                <div>Discounted Annual Price: <span id="discounted-annual-price"><?php echo e($plan->currency_symbol); ?>0.00</span></div>
                                <div>Annual Savings: <span id="annual-savings"><?php echo e($plan->currency_symbol); ?>0.00</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Plan Features -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Plan Features</h3>
                        <button type="button" onclick="addFeature()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-plus mr-1"></i>Add Feature
                        </button>
                    </div>

                    <div id="features-container">
                        <?php
                            $features = old('features') ?: $plan->features;
                        ?>
                        <?php if($features): ?>
                            <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="feature-row grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-200 rounded-lg">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Feature Title *</label>
                                        <input type="text" name="features[<?php echo e($index); ?>][title]" value="<?php echo e($feature['title'] ?? ''); ?>" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Feature Key *</label>
                                        <input type="text" name="features[<?php echo e($index); ?>][key]" value="<?php echo e($feature['key'] ?? ''); ?>" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Feature Value *</label>
                                        <input type="text" name="features[<?php echo e($index); ?>][value]" value="<?php echo e($feature['value'] ?? ''); ?>" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div class="flex items-end">
                                        <div class="flex-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="features[<?php echo e($index); ?>][marketing_status]" value="1" 
                                                       <?php echo e(isset($feature['marketing_status']) && $feature['marketing_status'] ? 'checked' : ''); ?>

                                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-700">Show in marketing</span>
                                            </label>
                                        </div>
                                        <button type="button" onclick="removeFeature(this)" class="ml-2 text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Plan Status -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Status</h3>
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', $plan->is_active) ? 'checked' : ''); ?>

                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <label for="is_active" class="ml-2 text-sm text-gray-700">Plan is active and available for subscription</label>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="<?php echo e(route('admin.plans.show', $plan)); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg text-sm font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>Update Plan
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let featureIndex = <?php echo e($plan->features ? count($plan->features) : 0); ?>;

function updateCurrencySymbol() {
    const currency = document.getElementById('currency').value;
    const symbolElement = document.getElementById('currency-symbol');
    
    if (currency === 'USD') {
        symbolElement.textContent = '$';
    } else if (currency === 'INR') {
        symbolElement.textContent = '₹';
    } else {
        symbolElement.textContent = '₹';
    }
    
    calculateAnnualPrice();
}

function calculateAnnualPrice() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discount = parseFloat(document.getElementById('annual_discount_percentage').value) || 0;
    const currency = document.getElementById('currency').value;
    const symbol = currency === 'USD' ? '$' : '₹';
    
    if (price > 0 && discount > 0) {
        const regularAnnualPrice = price * 12;
        const discountAmount = (regularAnnualPrice * discount) / 100;
        const discountedAnnualPrice = regularAnnualPrice - discountAmount;
        
        document.getElementById('regular-annual-price').textContent = symbol + regularAnnualPrice.toFixed(2);
        document.getElementById('discounted-annual-price').textContent = symbol + discountedAnnualPrice.toFixed(2);
        document.getElementById('annual-savings').textContent = symbol + discountAmount.toFixed(2);
        document.getElementById('annual-price-preview').classList.remove('hidden');
    } else {
        document.getElementById('annual-price-preview').classList.add('hidden');
    }
}

function addFeature() {
    const container = document.getElementById('features-container');
    const featureRow = document.createElement('div');
    featureRow.className = 'feature-row grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-200 rounded-lg';
    featureRow.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Feature Title *</label>
            <input type="text" name="features[${featureIndex}][title]" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Feature Key *</label>
            <input type="text" name="features[${featureIndex}][key]" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Feature Value *</label>
            <input type="text" name="features[${featureIndex}][value]" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="flex items-end">
            <div class="flex-1">
                <label class="flex items-center">
                    <input type="checkbox" name="features[${featureIndex}][marketing_status]" value="1" checked
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-700">Show in marketing</span>
                </label>
            </div>
            <button type="button" onclick="removeFeature(this)" class="ml-2 text-red-600 hover:text-red-800">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(featureRow);
    featureIndex++;
}

function removeFeature(button) {
    button.closest('.feature-row').remove();
}

// Initialize currency symbol and calculations
document.addEventListener('DOMContentLoaded', function() {
    updateCurrencySymbol();
    calculateAnnualPrice();
    
    // Add event listeners for price calculation
    document.getElementById('price').addEventListener('input', calculateAnnualPrice);
    document.getElementById('annual_discount_percentage').addEventListener('input', calculateAnnualPrice);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/plans/edit.blade.php ENDPATH**/ ?>