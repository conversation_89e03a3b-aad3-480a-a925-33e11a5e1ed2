<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class TeamMember extends Model
{
    protected $fillable = [
        'user_id',
        'business_id',
        'invited_by',
        'role',
        'status',
        'business_status',
        'invitation_token',
        'invitation_sent_at',
        'invitation_accepted_at'
    ];

    protected $casts = [
        'invitation_sent_at' => 'datetime',
        'invitation_accepted_at' => 'datetime',
    ];

    /**
     * Get the user that owns this team membership.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the business account this team member belongs to.
     */
    public function businessAccount(): BelongsTo
    {
        return $this->belongsTo(BusinessAccount::class);
    }

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the user who invited this team member.
     */
    public function invitedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    public function invitedByBusinessAccount()
    {
        return $this->hasOneThrough(
            \App\Models\BusinessAccount::class, // Final related model
            \App\Models\User::class,            // Intermediate model
            'id',                               // Foreign key on User table...
            'user_id',                          // Foreign key on BusinessAccount table...
            'invited_by',                       // Local key on TeamMember table...
            'id'                                // Local key on User table...
        );
    }

    /**
     * Get the permissions for this team member.
     */
    public function permissions(): HasMany
    {
        return $this->hasMany(Permission::class);
    }

    public function permission(): HasOne
    {
        return $this->hasOne(Permission::class);
    }

    /**
     * Check if the team member has a specific permission.
     *
     * @param string $permissionName
     * @return bool
     */
    public function hasPermission(string $permissionName): bool
    {
        return $this->permissions()
            ->where('permission_name', $permissionName)
            ->where('is_granted', true)
            ->exists();
    }

    /**
     * Check if the team member is an admin.
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if the team member is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the team member is pending.
     *
     * @return bool
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
}
