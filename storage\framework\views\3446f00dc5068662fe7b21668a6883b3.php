<?php $__env->startSection('title', 'Edit Site Settings'); ?>
<?php $__env->startSection('page-title', 'Edit Site Settings'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .setting-field {
        @apply mb-6 p-4 border border-gray-200 rounded-lg;
    }
    .setting-field:hover {
        @apply border-gray-300;
    }
    .setting-field.required {
        @apply border-l-4 border-l-red-500;
    }
    .setting-field.encrypted {
        @apply border-l-4 border-l-yellow-500;
    }
    .category-icon {
        @apply w-8 h-8 rounded-lg flex items-center justify-center text-white;
    }
    .category-ai { @apply bg-purple-500; }
    .category-google_api { @apply bg-blue-500; }
    .category-email { @apply bg-green-500; }
    .category-application { @apply bg-orange-500; }
    .category-general { @apply bg-gray-500; }
    .json-editor {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <div class="category-icon category-<?php echo e($category); ?>">
                <?php switch($category):
                    case ('ai'): ?>
                        <i class="fas fa-brain"></i>
                        <?php break; ?>
                    <?php case ('google_api'): ?>
                        <i class="fab fa-google"></i>
                        <?php break; ?>
                    <?php case ('email'): ?>
                        <i class="fas fa-envelope"></i>
                        <?php break; ?>
                    <?php case ('application'): ?>
                        <i class="fas fa-cog"></i>
                        <?php break; ?>
                    <?php default: ?>
                        <i class="fas fa-sliders-h"></i>
                <?php endswitch; ?>
            </div>
            <div class="ml-3">
                <h1 class="text-2xl font-bold text-gray-900">
                    Edit <?php echo e(ucfirst(str_replace('_', ' ', $category))); ?> Settings
                </h1>
                <p class="text-gray-600">Configure settings for this category</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.site-settings.index')); ?>" 
               class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                <i class="fas fa-arrow-left mr-2"></i>Back to Settings
            </a>
        </div>
    </div>

    <!-- Category Tabs -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Category</h3>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-2">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('admin.site-settings.edit', ['category' => $cat])); ?>" 
                       class="px-4 py-2 rounded-md <?php echo e($category == $cat ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'); ?>">
                        <?php echo e(ucfirst(str_replace('_', ' ', $cat))); ?>

                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <form method="POST" action="<?php echo e(route('admin.site-settings.update')); ?>" id="settings-form">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <?php if($settings->isEmpty()): ?>
            <div class="bg-white rounded-lg shadow p-12 text-center">
                <i class="fas fa-cog text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No settings found</h3>
                <p class="text-gray-600">No settings are available in this category for your access level.</p>
            </div>
        <?php else: ?>
            <?php $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupName => $groupSettings): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow mb-6">
                    <?php if($groupName): ?>
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">
                                <?php echo e(ucfirst(str_replace('_', ' ', $groupName))); ?>

                            </h3>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-4 sm:p-6 space-y-4 sm:space-y-6">
                        <?php $__currentLoopData = $groupSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="setting-field <?php echo e($setting->is_required ? 'required' : ''); ?> <?php echo e($setting->is_encrypted ? 'encrypted' : ''); ?>">
                                <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2 space-y-2 sm:space-y-0">
                                    <div class="flex-1">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            <?php echo e($setting->name); ?>

                                            <?php if($setting->is_required): ?>
                                                <span class="text-red-500">*</span>
                                            <?php endif; ?>
                                            <?php if($setting->is_encrypted): ?>
                                                <i class="fas fa-lock text-yellow-500 ml-1" title="Encrypted"></i>
                                            <?php endif; ?>
                                        </label>

                                        <?php if($setting->description): ?>
                                            <p class="text-sm text-gray-600 mb-3"><?php echo e($setting->description); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    <?php if($setting->default_value): ?>
                                        <button type="button"
                                                class="reset-btn text-sm text-indigo-600 hover:text-indigo-900 sm:ml-4 self-start"
                                                data-key="<?php echo e($setting->key); ?>"
                                                data-default="<?php echo e($setting->default_value); ?>">
                                            <i class="fas fa-undo mr-1"></i>Reset
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <!-- Setting Input -->
                                <?php switch($setting->type):
                                    case ('boolean'): ?>
                                        <div class="flex items-center">
                                            <input type="checkbox" 
                                                   name="settings[<?php echo e($setting->key); ?>]" 
                                                   value="1"
                                                   <?php echo e($setting->getTypedValue() ? 'checked' : ''); ?>

                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm text-gray-700">Enable this setting</span>
                                        </div>
                                        <?php break; ?>

                                    <?php case ('select'): ?>
                                        <select name="settings[<?php echo e($setting->key); ?>]" 
                                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                            <?php if($setting->options): ?>
                                                <?php $__currentLoopData = $setting->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($value); ?>" <?php echo e($setting->getTypedValue() == $value ? 'selected' : ''); ?>>
                                                        <?php echo e($label); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                        <?php break; ?>

                                    <?php case ('multiselect'): ?>
                                        <select name="settings[<?php echo e($setting->key); ?>][]" 
                                                multiple
                                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                            <?php if($setting->options): ?>
                                                <?php
                                                    $selectedValues = is_array($setting->getTypedValue()) ? $setting->getTypedValue() : [];
                                                ?>
                                                <?php $__currentLoopData = $setting->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($value); ?>" <?php echo e(in_array($value, $selectedValues) ? 'selected' : ''); ?>>
                                                        <?php echo e($label); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                        <?php break; ?>

                                    <?php case ('text'): ?>
                                        <textarea name="settings[<?php echo e($setting->key); ?>]" 
                                                  rows="4"
                                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                  placeholder="<?php echo e($setting->help_text); ?>"><?php echo e($setting->getTypedValue()); ?></textarea>
                                        <?php break; ?>

                                    <?php case ('json'): ?>
                                        <textarea name="settings[<?php echo e($setting->key); ?>]" 
                                                  rows="6"
                                                  class="json-editor w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                  placeholder='{"key": "value"}'><?php echo e(is_array($setting->getTypedValue()) ? json_encode($setting->getTypedValue(), JSON_PRETTY_PRINT) : $setting->getTypedValue()); ?></textarea>
                                        <?php break; ?>

                                    <?php default: ?>
                                        <input type="<?php echo e($setting->type === 'integer' || $setting->type === 'float' ? 'number' : ($setting->type === 'email' ? 'email' : ($setting->type === 'url' ? 'url' : 'text'))); ?>" 
                                               name="settings[<?php echo e($setting->key); ?>]" 
                                               value="<?php echo e($setting->getTypedValue()); ?>"
                                               <?php echo e($setting->type === 'integer' ? 'step=1' : ($setting->type === 'float' ? 'step=0.01' : '')); ?>

                                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 <?php $__errorArgs = ['settings.'.$setting->key];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               placeholder="<?php echo e($setting->help_text); ?>">
                                <?php endswitch; ?>

                                <?php $__errorArgs = ['settings.'.$setting->key];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                <!-- Validation Rules Display -->
                                <?php if($setting->validation_rules): ?>
                                    <div class="mt-2 text-xs text-gray-500">
                                        <strong>Validation:</strong> <?php echo e(implode(', ', $setting->validation_rules)); ?>

                                    </div>
                                <?php endif; ?>

                                <!-- Help Text -->
                                <?php if($setting->help_text): ?>
                                    <div class="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
                                        <i class="fas fa-info-circle mr-1"></i><?php echo e($setting->help_text); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-1"></i>
                        Changes will be applied immediately and cached for performance.
                    </div>
                    <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                        <a href="<?php echo e(route('admin.site-settings.index')); ?>"
                           class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 text-center">
                            Cancel
                        </a>
                        <button type="submit"
                                class="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-save mr-2"></i>Save Settings
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reset button functionality
    document.querySelectorAll('.reset-btn').forEach(button => {
        button.addEventListener('click', function() {
            const key = this.dataset.key;
            const defaultValue = this.dataset.default;
            
            if (confirm('Reset this setting to its default value?')) {
                fetch('<?php echo e(route('admin.site-settings.reset')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ key: key })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Find the input field and update its value
                        const input = document.querySelector(`[name="settings[${key}]"]`);
                        if (input) {
                            if (input.type === 'checkbox') {
                                input.checked = defaultValue === '1' || defaultValue === 'true';
                            } else {
                                input.value = data.value || defaultValue;
                            }
                        }
                        
                        // Show success message
                        showNotification('Setting reset successfully', 'success');
                    } else {
                        showNotification(data.message || 'Failed to reset setting', 'error');
                    }
                })
                .catch(error => {
                    showNotification('An error occurred: ' + error.message, 'error');
                });
            }
        });
    });

    // JSON validation for JSON fields
    document.querySelectorAll('.json-editor').forEach(textarea => {
        textarea.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                try {
                    JSON.parse(value);
                    this.classList.remove('border-red-500');
                    this.classList.add('border-green-500');
                } catch (error) {
                    this.classList.remove('border-green-500');
                    this.classList.add('border-red-500');
                }
            } else {
                this.classList.remove('border-red-500', 'border-green-500');
            }
        });
    });

    // Form validation
    document.getElementById('settings-form').addEventListener('submit', function(e) {
        let hasErrors = false;
        
        // Validate JSON fields
        document.querySelectorAll('.json-editor').forEach(textarea => {
            const value = textarea.value.trim();
            if (value) {
                try {
                    JSON.parse(value);
                } catch (error) {
                    hasErrors = true;
                    textarea.classList.add('border-red-500');
                    showNotification('Invalid JSON format in ' + textarea.closest('.setting-field').querySelector('label').textContent, 'error');
                }
            }
        });

        if (hasErrors) {
            e.preventDefault();
        }
    });

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                ${message}
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/site-settings/edit.blade.php ENDPATH**/ ?>