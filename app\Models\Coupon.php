<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Coupon extends Model
{
    protected $table = 'coupons';

    protected $fillable = [
        'coupon_code',
        'name',
        'description',
        'type',
        'value',
        'usage_limit',
        'usage_limit_per_user',
        'usage_count',
        'is_active',
        'starts_at',
        'expires_at',
        'minimum_amount',
        'maximum_discount',
        'applicable_plans',
        'metadata'
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'is_active' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'applicable_plans' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the usage logs for this coupon
     */
    public function usageLogs()
    {
        return $this->hasMany(CouponUsageLog::class);
    }

    /**
     * Check if the coupon is valid for a specific user and plan
     */
    public function isValid($userId, $planId = null, $amount = 0)
    {
        // Check if coupon is active
        if (!$this->is_active) {
            return [
                'valid' => false,
                'message' => 'This coupon is no longer active.'
            ];
        }

        // Check if coupon has started
        if ($this->starts_at && Carbon::now()->lt($this->starts_at)) {
            return [
                'valid' => false,
                'message' => 'This coupon is not yet available.'
            ];
        }

        // Check if coupon has expired
        if ($this->expires_at && Carbon::now()->gt($this->expires_at)) {
            return [
                'valid' => false,
                'message' => 'This coupon has expired.'
            ];
        }

        // Check usage limit
        if ($this->usage_limit && $this->usage_count >= $this->usage_limit) {
            return [
                'valid' => false,
                'message' => 'This coupon has reached its usage limit.'
            ];
        }

        // Check per-user usage limit
        if ($this->usage_limit_per_user) {
            $userUsageCount = $this->usageLogs()
                ->where('user_id', $userId)
                ->whereIn('status', ['used', 'applied'])
                ->count();

            if ($userUsageCount >= $this->usage_limit_per_user) {
                return [
                    'valid' => false,
                    'message' => 'You have already used this coupon the maximum number of times.'
                ];
            }
        }

        // Check minimum amount
        if ($this->minimum_amount && $amount < $this->minimum_amount) {
            return [
                'valid' => false,
                'message' => "Minimum order amount of {$this->minimum_amount} required for this coupon."
            ];
        }

        // Check applicable plans
        if ($planId && $this->applicable_plans && !in_array($planId, $this->applicable_plans)) {
            return [
                'valid' => false,
                'message' => 'This coupon is not applicable to the selected plan.'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Coupon is valid.'
        ];
    }

    /**
     * Calculate discount amount for a given price
     */
    public function calculateDiscount($amount)
    {
        if ($this->type === 'percentage') {
            $discount = ($amount * $this->value) / 100;
        } else {
            $discount = $this->value;
        }

        // Apply maximum discount limit if set
        if ($this->maximum_discount && $discount > $this->maximum_discount) {
            $discount = $this->maximum_discount;
        }

        // Ensure discount doesn't exceed the amount
        return min($discount, $amount);
    }

    /**
     * Apply coupon and create usage log
     */
    public function apply($userId, $originalAmount, $planId = null)
    {
        $validation = $this->isValid($userId, $planId, $originalAmount);

        if (!$validation['valid']) {
            return $validation;
        }

        $discountAmount = $this->calculateDiscount($originalAmount);
        $finalAmount = max(0, $originalAmount - $discountAmount);

        // Create usage log
        $usageLog = CouponUsageLog::create([
            'coupon_id' => $this->id,
            'user_id' => $userId,
            'original_amount' => $originalAmount,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount,
            'coupon_code' => $this->coupon_code,
            'status' => 'applied',
            'metadata' => [
                'plan_id' => $planId,
                'applied_at' => now()->toISOString(),
            ]
        ]);

        // Increment usage count
        $this->increment('usage_count');

        return [
            'valid' => true,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount,
            'usage_log_id' => $usageLog->id,
            'message' => 'Coupon applied successfully!'
        ];
    }

    /**
     * Get formatted discount display
     */
    public function getDiscountDisplayAttribute()
    {
        if ($this->type === 'percentage') {
            return $this->value . '% OFF';
        } else {
            return '₹' . number_format($this->value, 2) . ' OFF';
        }
    }

    /**
     * Scope for active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('starts_at')
                          ->orWhere('starts_at', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>=', now());
                    });
    }
}
