<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\User;
use App\Models\Subscription;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\TeamMember;
use App\Services\BillingService;
use App\Services\SubscriptionDisplayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use App\Models\GoogleReview;

class BillingController extends Controller
{
    protected $billingService;
    protected $subscriptionDisplayService;

    public function __construct(BillingService $billingService, SubscriptionDisplayService $subscriptionDisplayService)
    {
        $this->billingService = $billingService;
        $this->subscriptionDisplayService = $subscriptionDisplayService;
    }

    /**
     * Display comprehensive billing details page
     */
    public function index()
    {
        try {
            $user = Auth::user();
            $billingData = $this->billingService->getBillingDetails($user->id);
            $connectedBusinesses = Business::where('user_id', $user->id)->get();
            $teamMembers = TeamMember::where('user_id', $user->id)
                ->with(['business', 'invitedBy', 'permissions'])
                ->get();

            $business = Business::where(['user_id' => $user->id, 'status' => 'active'])->with('setting')->first();

            return view('billing.details', compact('billingData', 'connectedBusinesses', 'teamMembers', 'business', 'user'));
            // return view('billing.details', compact('billingData'));
        } catch (\Exception $e) {
            Log::error('Billing details page error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Unable to load billing details. Please try again.');
        }
    }

    /**
     * Get billing data as JSON for AJAX requests
     */
    public function getBillingData()
    {
        try {
            $user = Auth::user();
            $billingData = $this->billingService->getBillingDetails($user->id);

            return response()->json([
                'success' => true,
                'data' => $billingData
            ]);
        } catch (\Exception $e) {
            Log::error('Billing data API error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Unable to fetch billing data'
            ], 500);
        }
    }

    /**
     * Get past subscriptions data
     */
    public function getPastSubscriptions()
    {
        try {
            $user = Auth::user();
            $pastSubscriptions = $this->billingService->getPastSubscriptions($user->id);

            return response()->json([
                'success' => true,
                'data' => $pastSubscriptions
            ]);
        } catch (\Exception $e) {
            Log::error('Past subscriptions API error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Unable to fetch past subscriptions'
            ], 500);
        }
    }

    /**
     * Get payment history data
     */
    public function getPaymentHistory()
    {
        try {
            $user = Auth::user();
            $paymentHistory = $this->billingService->getPaymentHistory($user->id);

            return response()->json([
                'success' => true,
                'data' => $paymentHistory
            ]);
        } catch (\Exception $e) {
            Log::error('Payment history API error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Unable to fetch payment history'
            ], 500);
        }
    }

    /**
     * Get current usage details
     */
    public function getCurrentUsage()
    {
        try {
            $user = Auth::user();
            $usageData = $this->billingService->getCurrentUsageDetails($user->id);

            return response()->json([
                'success' => true,
                'data' => $usageData
            ]);
        } catch (\Exception $e) {
            Log::error('Current usage API error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Unable to fetch usage data'
            ], 500);
        }
    }

    /**
     * Redirect to Stripe Customer Portal (existing functionality)
     */
    public function billingPortal()
    {
        try {
            $user = Auth::user();

            if (!$user->stripe_customer_id) {
                return redirect()->route('billing.details')->with('error', 'No billing information available.');
            }

            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

            $session = \Stripe\BillingPortal\Session::create([
                'customer' => $user->stripe_customer_id,
                'return_url' => route('billing.details'),
            ]);

            return redirect()->to($session->url);
        } catch (\Exception $e) {
            Log::error('Billing portal creation failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Unable to access billing portal. Please try again later.');
        }
    }
}
