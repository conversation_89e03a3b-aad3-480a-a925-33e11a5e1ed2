<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ip_locations', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->string('ip_address', 45)->unique(); // Support both IPv4 and IPv6
            $table->tinyInteger('ip_version')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('country_name', 100)->nullable();
            $table->string('country_code', 2)->nullable();
            $table->string('capital', 100)->nullable();
            $table->json('phone_codes')->nullable();
            $table->json('time_zones')->nullable();
            $table->string('city_name', 100)->nullable();
            $table->string('region_name', 100)->nullable();
            $table->string('continent', 50)->nullable();
            $table->string('continent_code', 2)->nullable();
            $table->json('currencies')->nullable();
            $table->string('primary_currency', 3)->nullable(); // Primary currency for easy access
            $table->json('languages')->nullable();
            $table->string('asn', 20)->nullable();
            $table->string('asn_organization', 255)->nullable();
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';

            // Indexes for better performance
            $table->index('country_code');
            $table->index('primary_currency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ip_locations');
    }
};
