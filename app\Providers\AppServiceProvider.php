<?php

namespace App\Providers;

use App\Services\GoogleAuthService;
use App\Services\GoogleBusinessService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(GoogleAuthService::class, function () {
            return new GoogleAuthService(
                config('google.client_id'),
                config('google.client_secret'),
                config('google.redirect_uri')
            );
        });

        $this->app->singleton(GoogleBusinessService::class, function () {
            return new GoogleBusinessService(session('access_token.access_token'));
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        \Schema::defaultStringLength(191);
    }
}
