<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ReplyLog extends Model
{
    protected $fillable = [
        'user_id',
        'subscription_id',
        'business_id',
        'review_id',
        'reply_type',
        'reply_content',
        'status',
        'month',
        'year',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'month' => 'integer',
        'year' => 'integer'
    ];

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Log a reply for tracking monthly usage
     */
    public static function logReply($userId,  $subscriptionId, $businessId, array $data = [])
    {
        $now = Carbon::now();

        return self::create([
            'user_id' => $userId,
            'subscription_id' => $subscriptionId,
            'business_id' => $businessId,
            'review_id' => $data['review_id'] ?? null,
            'reply_type' => $data['reply_type'] ?? 'manual',
            'reply_content' => $data['reply_content'] ?? null,
            'status' => $data['status'] ?? 'sent',
            'month' => $now->month,
            'year' => $now->year,
            'metadata' => $data['metadata'] ?? null
        ]);
    }

    /**
     * Get monthly reply count for a user/subscription
     */
    public static function getMonthlyReplyCount(int $userId, int $subscriptionId, ?int $month = null, ?int $year = null): int
    {
        $now = Carbon::now();
        $month = $month ?? $now->month;
        $year = $year ?? $now->year;

        return self::where('user_id', $userId)
            ->where('subscription_id', $subscriptionId)
            ->where('month', $month)
            ->where('year', $year)
            ->where('status', 'sent')
            ->count();
    }

    /**
     * Check if user has exceeded their monthly reply limit
     */
    public static function hasExceededLimit(int $userId, int $subscriptionId): bool
    {
        $subscription = Subscription::with('plan')->find($subscriptionId);

        if (!$subscription || !$subscription->plan) {
            return true; // Err on the side of caution
        }

        // Unlimited replies
        if ($subscription->plan->hasUnlimitedReplies()) {
            return false;
        }

        $currentCount = self::getMonthlyReplyCount($userId, $subscriptionId);
        return $currentCount >= $subscription->plan->monthly_reply_limit;
    }

    /**
     * Get remaining replies for current month
     */
    public static function getRemainingReplies(int $userId, int $subscriptionId): int
    {
        $subscription = Subscription::with('plan')->find($subscriptionId);

        if (!$subscription || !$subscription->plan) {
            return 0;
        }

        // Unlimited replies
        if ($subscription->plan->hasUnlimitedReplies()) {
            return -1; // Indicates unlimited
        }

        $currentCount = self::getMonthlyReplyCount($userId, $subscriptionId);
        return max(0, $subscription->plan->monthly_reply_limit - $currentCount);
    }

    /**
     * Scope for current month
     */
    public function scopeCurrentMonth($query)
    {
        $now = Carbon::now();
        return $query->where('month', $now->month)
            ->where('year', $now->year);
    }

    /**
     * Scope for specific month/year
     */
    public function scopeForMonth($query, int $month, int $year)
    {
        return $query->where('month', $month)
            ->where('year', $year);
    }

    /**
     * Scope for successful replies only
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'sent');
    }
}
