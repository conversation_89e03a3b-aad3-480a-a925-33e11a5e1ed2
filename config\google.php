<?php

return [
    'client_id' => env('GOOGLE_CLIENT_ID'),
    'client_secret' => env('GOOGLE_CLIENT_SECRET'),
    'redirect_uri' => env('GOOGLE_REDIRECT_URI'),
    'scopes' => env('GOOGLE_SCOPES', [
        'https://www.googleapis.com/auth/business.manage',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/plus.business.manage'
    ]),
    "accounts_api" => env('GOOGLE_ACCOUNTS_API', "https://mybusinessaccountmanagement.googleapis.com/v1/accounts"),
    "google_location_api" => env('GOOGLE_LOCATIONS_API', "https://mybusinessaccountmanagement.googleapis.com/v1/accounts/{accountId}/locations"),
    "reviews_api" => env('GOOGLE_REVIEWS_API', "https://mybusiness.googleapis.com/v4/accounts/{accountId}/locations/{locationId}/reviews"),
    "debug_mode" => env('DEBUG_MODE', true),
    'google_mybusiness_api_v4' => 'https://mybusiness.googleapis.com/v4'
];

// /**
//  * Configuration file for Google Review Manager POC
//  */

// // Google API Credentials
// // Note: In a production environment, these should be stored securely
// define('GOOGLE_CLIENT_ID', '************-a5ujs8lpi53idg6nesjn27jn9uq7h8vv.apps.googleusercontent.com');
// define('GOOGLE_CLIENT_SECRET', '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');
// //define('GOOGLE_REDIRECT_URI', 'http://localhost/jigar/review/index.php');
// define('GOOGLE_REDIRECT_URI', 'http://jigar.indianic.biz/review/index.php');

// // Google API Scopes
// define('GOOGLE_SCOPES', [
//     'https://www.googleapis.com/auth/business.manage',
//     'https://www.googleapis.com/auth/userinfo.email',
//     'https://www.googleapis.com/auth/userinfo.profile',
//     'https://www.googleapis.com/auth/plus.business.manage'
// ]);

// // API Endpoints
// define('GOOGLE_ACCOUNTS_API', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts');
// define('GOOGLE_LOCATIONS_API', 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts/{accountId}/locations');
// define('GOOGLE_REVIEWS_API', 'https://mybusiness.googleapis.com/v4/accounts/{accountId}/locations/{locationId}/reviews');

// // Alternative API Endpoints (for fallback)
// define('GOOGLE_MYBUSINESS_API_V4', 'https://mybusiness.googleapis.com/v4');
// define('GOOGLE_BUSINESSPROFILE_API_V1', 'https://businessprofileperformance.googleapis.com/v1');
// define('GOOGLE_MYBUSINESSINFO_API_V1', 'https://mybusinessbusinessinformation.googleapis.com/v1');

// // Debug mode (set to false in production)
// define('DEBUG_MODE', true);
