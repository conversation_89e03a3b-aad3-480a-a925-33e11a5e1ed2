<?php

namespace App\Services;

use App\Models\Template;
use Illuminate\Support\Carbon;

class TemplateService
{
    /**
     * Create default templates for a new business
     *
     * @param int $businessId
     * @return void
     */
    public static function createDefaultTemplatesForBusiness($businessId)
    {
        // Get all global templates (without business_id)
        $globalTemplates = Template::whereNull('business_id')->get();
        //dd($globalTemplates);
        // Create a copy of each template for the new business
        foreach ($globalTemplates as $template) {
            Template::create([
                'business_id' => $businessId,
                'title' => $template->title,
                'description' => $template->description,
                'template_text' => $template->template_text,
                'ratings' => $template->ratings,
                'sentiment' => $template->sentiment,
                'length' => $template->length,
            ]);
        }
    }
}
