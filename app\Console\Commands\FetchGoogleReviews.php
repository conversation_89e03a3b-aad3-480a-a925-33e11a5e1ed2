<?php

namespace App\Console\Commands;

use App\Jobs\FetchNewReviews;
use App\Models\User;
use App\Services\FetchNewReviewService;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class FetchGoogleReviews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch-google-reviews';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch latest Google reviews for all or specific businesses';

    /**
     * Execute the console command.
     */

    public function handle()
    {
        $users = User::with('preference.business')->get();
        $service = new FetchNewReviewService();

        foreach ($users as $user) {
            $data = $service->getScheduleTime($user->id);
            if ($data) {
                $interval = $data['schedule_time'];
                $lastFetched = $data['last_fetched_at'];

                if (!$lastFetched || Carbon::parse($lastFetched)->addMinutes($interval)->lte(now())) {
                    FetchNewReviews::dispatch($data['location'], $data['businessId'], $data['token']);

                    \App\Models\Setting::where('business_id', $data['businessId'])
                        ->update(['last_review_fetched_at' => now()]);

                    $this->info("Review fetch job dispatched for business ID: {$data['businessId']}");
                }
            }
        }
    }
}
