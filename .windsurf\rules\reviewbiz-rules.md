---
trigger: always_on
---

# Windsurf Configuration Rules
version: 1.0

# Code Quality Rules
code_quality:
  phpstan_level: 8
  php_cs_fixer: true
  php_mess_detector: true
  php_code_sniffer: true
  security_checks: true

# Testing Rules
testing:
  phpunit_coverage: 80%
  feature_tests: required
  unit_tests: required
  browser_tests: recommended
  api_tests: required

# Deployment Rules
deployment:
  staging_approval: required
  production_approval: required
  backup_required: true
  rollback_plan_required: true

# Security Rules
security:
  dependency_scanning: true
  secrets_detection: true
  sast_enabled: true
  dast_enabled: true

# Documentation Rules
documentation:
  api_docs_required: true
  inline_docs_required: true
  changelog_required: true
  architecture_diagram_required: true

# Monitoring Rules
monitoring:
  error_tracking: true
  performance_monitoring: true
  uptime_monitoring: true
  log_retention: 90d

# Performance Rules
performance:
  page_load_time: <2s
  api_response_time: <500ms
  database_query_time: <100ms
  cache_hit_ratio: >90%