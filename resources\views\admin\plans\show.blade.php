@extends('admin.layouts.app')

@section('title', 'Plan Details - ' . $plan->name)

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $plan->name }} Plan</h1>
            <p class="text-gray-600 mt-1">{{ $plan->short_description }}</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-0">
            @if(auth('admin')->user()->hasPermission('plans.edit'))
            <a href="{{ route('admin.plans.edit', $plan) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-edit mr-2"></i>Edit Plan
            </a>
            @endif
            <a href="{{ route('admin.plans.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Plans
            </a>
        </div>
    </div>

    <!-- Plan Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Plan Information Card -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Plan Information</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        {{ $plan->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $plan->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Basic Details</h4>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm text-gray-600">Plan Name:</span>
                                <span class="text-sm font-medium text-gray-900 ml-2">{{ $plan->name }}</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-600">Currency:</span>
                                <span class="text-sm font-medium text-gray-900 ml-2">{{ $plan->currency }} ({{ $plan->currency_symbol }})</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-600">Duration:</span>
                                <span class="text-sm font-medium text-gray-900 ml-2">{{ $plan->duration_days }} days</span>
                            </div>
                            @if($plan->tax_percentage)
                            <div>
                                <span class="text-sm text-gray-600">Tax Rate:</span>
                                <span class="text-sm font-medium text-gray-900 ml-2">{{ $plan->tax_percentage }}%</span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Pricing Details</h4>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm text-gray-600">Monthly Price:</span>
                                <span class="text-lg font-bold text-gray-900 ml-2">{{ $plan->currency_symbol }}{{ number_format($plan->price, 2) }}</span>
                            </div>
                            @if($plan->annual_price)
                            <div>
                                <span class="text-sm text-gray-600">Annual Price:</span>
                                <span class="text-lg font-bold text-green-600 ml-2">{{ $plan->currency_symbol }}{{ number_format($plan->annual_price, 2) }}</span>
                            </div>
                            @endif
                            @if($plan->annual_discount_percentage > 0)
                            <div>
                                <span class="text-sm text-gray-600">Annual Discount:</span>
                                <span class="text-sm font-medium text-green-600 ml-2">{{ $plan->annual_discount_percentage }}% off</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-600">Annual Savings:</span>
                                <span class="text-sm font-medium text-green-600 ml-2">{{ $plan->currency_symbol }}{{ number_format($plan->getAnnualSavings(), 2) }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-500 mb-2">Description</h4>
                    <p class="text-sm text-gray-700">{{ $plan->long_description }}</p>
                </div>
            </div>
        </div>

        <!-- Statistics Card -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Subscriptions</span>
                        <span class="text-lg font-bold text-gray-900">{{ number_format($subscriptionStats['total_subscriptions']) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Active Subscriptions</span>
                        <span class="text-lg font-bold text-green-600">{{ number_format($subscriptionStats['active_subscriptions']) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Expired Subscriptions</span>
                        <span class="text-lg font-bold text-orange-600">{{ number_format($subscriptionStats['expired_subscriptions']) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Cancelled Subscriptions</span>
                        <span class="text-lg font-bold text-red-600">{{ number_format($subscriptionStats['cancelled_subscriptions']) }}</span>
                    </div>
                </div>

                <div class="mt-6 pt-4 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-500 mb-2">Revenue Statistics</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Total Revenue</span>
                            <span class="text-sm font-bold text-gray-900">{{ $plan->currency_symbol }}{{ number_format($revenueStats['total_revenue'], 2) }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">This Month</span>
                            <span class="text-sm font-bold text-green-600">{{ $plan->currency_symbol }}{{ number_format($revenueStats['monthly_revenue'], 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Features -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Features</h3>
            @if($plan->features && is_array($plan->features))
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($plan->features as $feature)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $feature['title'] ?? 'Feature' }}</h4>
                                    <p class="text-xs text-gray-500 mt-1">{{ $feature['key'] ?? 'N/A' }}</p>
                                    <p class="text-sm text-gray-700 mt-2">
                                        @if(is_bool($feature['value'] ?? null))
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium 
                                                {{ $feature['value'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $feature['value'] ? 'Enabled' : 'Disabled' }}
                                            </span>
                                        @elseif(($feature['value'] ?? null) === -1)
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                Unlimited
                                            </span>
                                        @else
                                            <span class="font-medium">{{ $feature['value'] ?? 'N/A' }}</span>
                                        @endif
                                    </p>
                                </div>
                                @if(isset($feature['marketing_status']) && $feature['marketing_status'])
                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        <i class="fas fa-bullhorn text-xs"></i>
                                    </span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-list text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">No features defined for this plan</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Subscription Trends Chart -->
    @if($monthlyTrends->isNotEmpty())
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Trends (Last 12 Months)</h3>
            <div class="h-64">
                <canvas id="subscriptionTrendsChart"></canvas>
            </div>
        </div>
    </div>
    @endif

    <!-- Recent Subscriptions -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Subscriptions</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($recentSubscriptions as $subscription)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $subscription->user->name ?? 'N/A' }}</div>
                                    <div class="text-sm text-gray-500">{{ $subscription->user->email ?? 'N/A' }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    @switch($subscription->status)
                                        @case('active')
                                            bg-green-100 text-green-800
                                            @break
                                        @case('expired')
                                            bg-orange-100 text-orange-800
                                            @break
                                        @case('cancelled')
                                            bg-red-100 text-red-800
                                            @break
                                        @default
                                            bg-gray-100 text-gray-800
                                    @endswitch">
                                    {{ ucfirst($subscription->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                @if($subscription->payment)
                                    {{ $plan->currency_symbol }}{{ number_format($subscription->payment->amount, 2) }}
                                @else
                                    <span class="text-gray-400">N/A</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {{ $subscription->start_date ? $subscription->start_date->format('M d, Y') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {{ $subscription->expiry_date ? $subscription->expiry_date->format('M d, Y') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 text-sm font-medium">
                                @if(auth('admin')->user()->hasPermission('users.view'))
                                <a href="{{ route('admin.users.show', $subscription->user_id) }}" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                                <p class="text-lg font-medium">No subscriptions found</p>
                                <p class="text-sm">This plan doesn't have any subscriptions yet</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@if($monthlyTrends->isNotEmpty())
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Subscription Trends Chart
const trendsCtx = document.getElementById('subscriptionTrendsChart').getContext('2d');
const trendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($monthlyTrends->pluck('month')) !!},
        datasets: [{
            label: 'New Subscriptions',
            data: {!! json_encode($monthlyTrends->pluck('count')) !!},
            borderColor: 'rgb(99, 102, 241)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
@endpush
@endif
