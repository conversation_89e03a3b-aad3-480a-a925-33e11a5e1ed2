<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AssociateBusiness extends Model
{
    protected $table = 'associate_businesses';
    protected $fillable = [
        'location_name',
        'title',
        'primary_phone',
        'additional_phones',
        'region_code',
        'language_code',
        'postal_code',
        'administrative_area',
        'locality',
        'address_lines',
        'website',
        'latitude',
        'longitude',
        'profile_description',
        'user_id',
        'average_rating',
        'total_reviews'
    ];

    protected $casts = [
        'additional_phones' => 'array',
        'address_lines' => 'array',
        'latitude' => 'float',
        'longitude' => 'float',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
