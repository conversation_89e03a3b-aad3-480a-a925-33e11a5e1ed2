document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    let connectBusinessBtn = document.getElementById("connectBusiness");
    let connectBusinessModal = document.getElementById("connectBusinessModal");
    let connectBusinessMobile = document.getElementById("connectBusinessMobile");
    // const getSelectBusiness = document.querySelector('.getSelectBusiness')
    let getSelectBusiness = document.getElementById('selectBusiness');
    let getSelectBusinessMobile = document.getElementById('business_id_mobile');
    document.getElementById("changeBusinessName").innerHTML = document.getElementById("business_name").value
    // Show modal when connect button is clicked
    if (connectBusinessBtn) {
        connectBusinessBtn.addEventListener("click", () => {
            connectBusinessModal.classList.remove("hidden");
        });
    }

    if (connectBusinessMobile) {
        connectBusinessMobile.addEventListener("click", () => {
        connectBusinessModal.classList.remove("hidden");
        const mainWrapper = document.querySelector('.menu-active');
        if (mainWrapper) {
            mainWrapper.classList.remove('menu-active');
        }
    });
    }
    
    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        if (event.target.id === 'connectBusinessModal' && !connectBusinessModal.classList.contains('hidden')) {
            connectBusinessModal.classList.add('hidden');
        }
    });
    
    // Handle business connection with subscription validation
    const connectBusinessBtns = document.querySelectorAll('.connect-business-btn');
    connectBusinessBtns.forEach(btn => {
        btn.addEventListener('click', async function(e) {
            e.preventDefault();

            const businessId = this.getAttribute('data-business-id');
            const businessName = this.getAttribute('data-business-name');
            const locationName = this.getAttribute('data-location-name');
            const primaryPhone = this.getAttribute('data-primary-phone');
            const website = this.getAttribute('data-website');

            try {                            
                const validation = await validateSubscriptionAndUpgrade('business_connection');
                if (!validation.allowed) {
                    return;
                }

                // If validation passes, proceed with business connection
                connectBusiness({
                    id: businessId,
                    title: businessName,
                    location_name: locationName,
                    primary_phone: primaryPhone,
                    website: website
                });
                setTimeout(() => {
                    window.location.reload()
                }, 2000)

            } catch (error) {
                console.error('Error validating business connection:', error);
                // If validation fails, allow the action to prevent breaking functionality
                connectBusiness({
                    id: businessId,
                    title: businessName,
                    location_name: locationName,
                    primary_phone: primaryPhone,
                    website: website
                });
            }
        });
    });
    
    // Handle business removal
    const removeBusinessBtns = document.querySelectorAll('.remove-business-btn');
    removeBusinessBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const businessId = this.getAttribute('data-business-id');
            const businessName = this.getAttribute('data-business-name');
            const locationName = this.getAttribute('data-location-name');
            
            // Confirm before removing
            if (confirm(`Are you sure you want to remove ${businessName}?`)) {
                // Send AJAX request to remove the business
                removeBusiness(locationName, businessName);
                setTimeout(() => {
                    window.location.reload()
                }, 2000)
            }
        });
    });
    
    
    if(getSelectBusiness)
    {
        // This is for calling on load
    let businessId = getSelectBusiness.value;
    let dashboardUrl = getSelectBusiness.getAttribute('data-dashboard-url');    
        
        //And this is for calling on change
        getSelectBusiness.addEventListener('change', handleChange);

        async function handleChange() {
            let businessId = this.value;
            let dashboardUrl = this.dataset.dashboardUrl;
            try {
                await changeStatus(businessId, dashboardUrl, 'onchange');
                window.location.reload();
            } catch (e) {
                console.error('Error updating business:', e);
            }
        }
    }
    
    if(getSelectBusinessMobile)
    {
        // This is for calling on load
    let businessId = getSelectBusinessMobile.value;
    let dashboardUrl = getSelectBusinessMobile.getAttribute('data-dashboard-url');    
        // changeBusinessName
        //And this is for calling on change
        getSelectBusinessMobile.addEventListener('change', handleChange);
        // Extra event for mobile

        async function handleChange() {
            let businessId = this.value;
            let dashboardUrl = this.dataset.dashboardUrl;
            try {
                await changeStatus(businessId, dashboardUrl, 'onchange');
                window.location.reload();
            } catch (e) {
                console.error('Error updating business:', e);
            }
        }
    }
    
});

// Connect business via AJAX
function connectBusiness(businessData) {
    // Show loading state
    showNotification('Connecting business...', 'info');
    
    // Create form data
    const formData = new FormData();
    for (const key in businessData) {
        if (businessData[key]) {
            formData.append(key, businessData[key]);
        }
    }
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    formData.append('_token', csrfToken);
    
    // Send request
    fetch('/business/connect', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the select dropdown with the new business
            updateBusinessDropdown(data.business);
            
            // Close the modal
            document.getElementById('connectBusinessModal').classList.add('hidden');
            
            // Show success message
            showNotification(`Successfully connected to ${businessData.title}`, 'success');
            
            // Refresh the page if needed
            if (data.refresh) {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            showNotification(data.message || 'Failed to connect business', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while connecting the business', 'error');
    });
}

// Remove business via AJAX
function removeBusiness(locationName, businessName) {
    
    // Show loading state
    showNotification('Removing business...', 'info');
    
    // Create form data
    const formData = new FormData();
    formData.append('location_name', locationName);
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    formData.append('_token', csrfToken);
    
    // Send request
    fetch('/business/remove', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove from dropdown
            removeFromBusinessDropdown(data.businessId);
            
            // Close the modal
            document.getElementById('connectBusinessModal').classList.add('hidden');
            
            // Show success message
            showNotification(`Successfully removed ${businessName}`, 'success');
            
            // Refresh the page if needed
            if (data.refresh) {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            showNotification(data.message || 'Failed to remove business', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while removing the business', 'error');
    });
}

// Update business dropdown
function updateBusinessDropdown(business) {
    const businessSelect = document.getElementById('selectBusiness');
    if (!businessSelect) return;
    
    // Check if option already exists
    let optionExists = false;
    for (let i = 0; i < businessSelect.options.length; i++) {
        if (businessSelect.options[i].value === business.id.toString()) {
            businessSelect.options[i].selected = true;
            optionExists = true;
            break;
        }
    }
    
    // Add option if it doesn't exist
    if (!optionExists) {
        const option = document.createElement('option');
        option.value = business.id.toString();
        option.text = business.title;
        option.selected = true;
        businessSelect.appendChild(option);
    }
}

// Remove from business dropdown
function removeFromBusinessDropdown(businessId) {
    const businessSelect = document.getElementById('selectBusiness');
    if (!businessSelect) return;
    
    for (let i = 0; i < businessSelect.options.length; i++) {
        if (businessSelect.options[i].value === businessId.toString()) {
            businessSelect.remove(i);
            break;
        }
    }
}

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    let bgColor = 'bg-blue-500';
    
    if (type === 'success') bgColor = 'bg-green-500';
    else if (type === 'error') bgColor = 'bg-red-500';
    else if (type === 'warning') bgColor = 'bg-yellow-500';
    
    notification.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-500 translate-y-0 opacity-100 ${bgColor} text-white`;
    notification.innerHTML = message;
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('opacity-0', 'translate-y-10');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}

// Change business status via AJAX
function changeStatus(businessId, dashboardUrl, type) {
    // Show loading state
    if(type === 'onchange')
    {
        showNotification('Changing business status...', 'info');
    }
    // Create form data
    const formData = new FormData();
    formData.append('id', businessId);
    formData.append('status', 'active');
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    formData.append('_token', csrfToken);
    const redirectUrl = "{{ route('business.dashboard') }}";
    
    // Send request
    fetch('/business/changeStatus', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            if(type === 'onchange')
            {
                showNotification('Business switch successfully', 'success');
            }
            // setTimeout(() => {
            //     window.location.href = dashboardUrl;
            // }, 1000)
        } else {
            if(type === 'onchange')
            {
                showNotification(data.message || 'Failed to change business status', 'error');    
            }
            
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while changing the business status', 'error');
    });
}
