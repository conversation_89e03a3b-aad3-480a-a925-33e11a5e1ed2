<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmailTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $admin = auth('admin')->user();
        
        if (!$admin) {
            return false;
        }

        // Check if creating or updating
        if ($this->isMethod('POST')) {
            return $admin->hasPermission('email_templates.create');
        }

        return $admin->hasPermission('email_templates.edit');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9\s\-_]+$/', // Only alphanumeric, spaces, hyphens, underscores
            ],
            'subject' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:500',
            ],
            'html_content' => [
                'required',
                'string',
                'max:65535', // TEXT field limit
            ],
            'text_content' => [
                'nullable',
                'string',
                'max:65535',
            ],
            'category' => [
                'required',
                'in:system,custom',
            ],
            'type' => [
                'required',
                'string',
                'max:50',
                'regex:/^[a-z_]+$/', // Only lowercase letters and underscores
            ],
            'available_variables' => [
                'nullable',
                'array',
                'max:50', // Limit number of variables
            ],
            'available_variables.*' => [
                'string',
                'max:50',
                'regex:/^[a-z_]+$/', // Only lowercase letters and underscores
            ],
            'required_variables' => [
                'nullable',
                'array',
                'max:20', // Limit number of required variables
            ],
            'required_variables.*' => [
                'string',
                'max:50',
                'regex:/^[a-z_]+$/',
            ],
            'is_active' => [
                'boolean',
            ],
            'is_default' => [
                'boolean',
            ],
        ];

        // Add unique name validation
        if ($this->isMethod('POST')) {
            $rules['name'][] = 'unique:admin_email_templates,name';
        } else {
            $template = $this->route('emailTemplate');
            if ($template) {
                $rules['name'][] = Rule::unique('admin_email_templates', 'name')->ignore($template->id);
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The template name may only contain letters, numbers, spaces, hyphens, and underscores.',
            'type.regex' => 'The template type may only contain lowercase letters and underscores.',
            'available_variables.*.regex' => 'Variable names may only contain lowercase letters and underscores.',
            'required_variables.*.regex' => 'Variable names may only contain lowercase letters and underscores.',
            'html_content.max' => 'The HTML content is too large. Please reduce the content size.',
            'text_content.max' => 'The text content is too large. Please reduce the content size.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize HTML content
        if ($this->has('html_content')) {
            $this->merge([
                'html_content' => $this->sanitizeHtml($this->input('html_content')),
            ]);
        }

        // Ensure arrays are properly formatted
        if ($this->has('available_variables') && is_string($this->input('available_variables'))) {
            $this->merge([
                'available_variables' => json_decode($this->input('available_variables'), true) ?: [],
            ]);
        }

        if ($this->has('required_variables') && is_string($this->input('required_variables'))) {
            $this->merge([
                'required_variables' => json_decode($this->input('required_variables'), true) ?: [],
            ]);
        }
    }

    /**
     * Sanitize HTML content to prevent XSS
     */
    private function sanitizeHtml(string $html): string
    {
        // Allow basic HTML tags for email templates
        $allowedTags = [
            'html', 'head', 'title', 'meta', 'body', 'div', 'span', 'p', 'br', 'hr',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'b', 'em', 'i', 'u',
            'a', 'img', 'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
        ];

        $allowedAttributes = [
            'href', 'src', 'alt', 'title', 'width', 'height', 'style', 'class', 'id',
            'target', 'rel', 'border', 'cellpadding', 'cellspacing',
        ];

        // Basic HTML sanitization (in production, consider using HTMLPurifier)
        $html = strip_tags($html, '<' . implode('><', $allowedTags) . '>');
        
        // Remove potentially dangerous attributes
        $html = preg_replace('/on\w+="[^"]*"/i', '', $html); // Remove event handlers
        $html = preg_replace('/javascript:/i', '', $html); // Remove javascript: URLs
        
        return $html;
    }
}
