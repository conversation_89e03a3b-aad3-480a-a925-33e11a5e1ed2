<?php

namespace App\Services;

use App\Services\GoogleBusinessService;
use App\Services\SubscriptionValidationWrapper;
use Illuminate\Support\Facades\Auth;

class ValidatedGoogleBusinessService
{
    protected $googleBusinessService;
    protected $validationWrapper;

    public function __construct(GoogleBusinessService $googleBusinessService, SubscriptionValidationWrapper $validationWrapper)
    {
        $this->googleBusinessService = $googleBusinessService;
        $this->validationWrapper = $validationWrapper;
    }

    /**
     * Reply to review with subscription validation
     */
    public function replyToReview($locationId, $businessId, $reviewId, $reply, $token)
    {
        $userId = Auth::id();
        
        return $this->validationWrapper->validateReplySend($userId, function() use ($locationId, $businessId, $reviewId, $reply, $token) {
            return $this->googleBusinessService->replyToReview($locationId, $businessId, $reviewId, $reply, $token);
        });
    }

    /**
     * Fetch reviews with subscription validation
     */
    public function fetchReviews($locationName, $token)
    {
        $userId = Auth::id();
        
        return $this->validationWrapper->validateFeatureAccess($userId, 'api_access_level', function() use ($locationName, $token) {
            return $this->googleBusinessService->fetchReviews($locationName, $token);
        });
    }

    /**
     * Get business locations with subscription validation
     */
    public function getBusinessLocations($accountId, $token)
    {
        $userId = Auth::id();
        
        return $this->validationWrapper->validateFeatureAccess($userId, 'api_access_level', function() use ($accountId, $token) {
            return $this->googleBusinessService->getBusinessLocations($accountId, $token);
        });
    }

    /**
     * Delegate all other methods to the original service without validation
     */
    public function __call($method, $arguments)
    {
        return $this->googleBusinessService->$method(...$arguments);
    }
}
