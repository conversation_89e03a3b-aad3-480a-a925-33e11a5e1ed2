<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reviews - Google Review Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 min-h-screen">
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <div>
                <h1 class="text-xl font-bold text-gray-900">Google Review Manager POC</h1>
                <p class="text-sm text-gray-600">{{ htmlspecialchars($locationTitle) }}</p>
            </div>
            <div class="flex items-center space-x-4">
                @if (isset($userInfo['picture']))
                <img src="{{ htmlspecialchars($userInfo['picture']) }}" alt="Profile" class="h-8 w-8 rounded-full">
                @endif
                <span class="text-sm font-medium text-gray-700">{{ htmlspecialchars($userInfo['name'] ?? 'User') }}</span>
                <a href="{{ route('dashboard') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">Back to Dashboard</a>
                <a href="{{ route('logout') }}" resampling="true" class="text-sm font-medium text-red-600 hover:text-red-500">Logout</a>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @if (session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
        @elseif ($error)
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ $error }}
        </div>
        @endif

        @if (session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
        @endif

        <div class="bg-white shadow rounded-lg p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-900">Reviews</h2>
                <div class="flex items-center">
                    <div class="flex items-center mr-2">
                        @include('components.stars', ['rating' => $averageRating])
                    </div>
                    <span class="text-sm text-gray-600">{{ number_format($averageRating, 1) }} out of 5 ({{ $totalReviewCount }} reviews)</span>
                </div>
            </div>

            @if (empty($reviews))
            <p class="text-gray-500">No reviews found for this location.</p>
            @else
            <div class="space-y-6">
                @foreach ($reviews as $review)
                <div class="border rounded-lg p-4">
                    <div class="flex items-start">
                        @if (isset($review['reviewer']['profilePhotoUrl']))
                        <img src="{{ htmlspecialchars($review['reviewer']['profilePhotoUrl']) }}" alt="Reviewer" class="h-10 w-10 rounded-full mr-4">
                        @else
                        <div class="h-10 w-10 rounded-full bg-gray-200 mr-4 flex items-center justify-center">
                            <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        @endif

                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ htmlspecialchars($review['reviewer']['displayName'] ?? 'Anonymous') }}</h3>
                                    <div class="flex items-center">
                                        <div class="flex items-center">
                                            @include('components.stars', ['rating' => $review['starRating']])
                                        </div>
                                        <span class="ml-2 text-sm text-gray-500">{{ format_date($review['createTime'] ?? null) }}</span>
                                    </div>
                                </div>
                                <span class="text-xs text-gray-500">ID: {{ htmlspecialchars(substr($review['reviewId'], 0, 10) . '...') }}</span>
                            </div>

                            @if (isset($review['comment']))
                            <p class="mt-2 text-gray-700">{{ htmlspecialchars($review['comment']) }}</p>
                            @else
                            <p class="mt-2 text-gray-500 italic">No comment provided</p>
                            @endif

                            @if (isset($review['reviewReply']) && isset($review['reviewReply']['comment']))
                            <div class="mt-3 pl-4 border-l-2 border-gray-200">
                                <div class="flex justify-between items-start">
                                    <h4 class="font-medium text-gray-900">Your Reply</h4>
                                    <a href="{{ route('reviews.deleteReply', ['location' => urlencode($locationName), 'review_id' => urlencode($review['reviewId'])]) }}" class="text-xs text-red-600 hover:text-red-500">Delete Reply</a>
                                </div>
                                <p class="text-gray-700">{{ htmlspecialchars($review['reviewReply']['comment']) }}</p>
                                <p class="text-xs text-gray-500 mt-1">Replied on {{ format_date($review['reviewReply']['updateTime'] ?? null) }}</p>
                            </div>
                            @else
                            <form action="{{ route('reviews.reply') }}" method="post" class="mt-3">
                                @csrf
                                <input type="hidden" name="review_id" value="{{ htmlspecialchars($review['reviewId']) }}">
                                <input type="hidden" name="location" value="{{ htmlspecialchars($locationName) }}">
                                <div class="mt-1">
                                    <textarea name="reply" rows="2" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Write a reply to this review..."></textarea>
                                </div>
                                <div class="mt-2 flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Submit Reply
                                    </button>
                                </div>
                            </form>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @endif
        </div>

        @if (config('app.debug') && $rawResponse)
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">API Response (Debug Mode)</h2>
            <div class="bg-gray-100 p-4 rounded overflow-auto max-h-96">
                <pre class="text-xs">{{ htmlspecialchars($rawResponse) }}</pre>
            </div>
        </div>
        @endif

        @if (config('app.debug'))
        <div class="bg-white shadow rounded-lg p-6 mt-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Debug Information</h2>
            <div class="bg-gray-100 p-4 rounded overflow-auto max-h-96">
                <h3 class="font-medium mb-2">Request Parameters:</h3>
                <pre class="text-xs mb-4">Location Name: {{ htmlspecialchars($locationName) }}</pre>

                <h3 class="font-medium mb-2">Session Data:</h3>
                <pre class="text-xs">{{ htmlspecialchars(json_encode(session()->all(), JSON_PRETTY_PRINT)) }}</pre>
            </div>
        </div>
        @endif
    </main>
</body>

</html>