<?php

namespace App\Services;

use App\Models\BusinessActivityLog;
use App\Models\Subscription;
use App\Models\Business;
use App\Models\TeamMember;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SubscriptionUsageTrigger
{
    protected $subscriptionService;
    protected $usageTracker;

    public function __construct(SubscriptionService $subscriptionService, SubscriptionUsageTracker $usageTracker)
    {
        $this->subscriptionService = $subscriptionService;
        $this->usageTracker = $usageTracker;
    }

    /**
     * Trigger usage validation for business connection
     */
    public function triggerBusinessConnectionCheck(int $userId): array
    {
        try {
            // Get real-time count from database
            $currentCount = Business::where('user_id', $userId)->count();
            
            // Get subscription limits
            $subscription = $this->subscriptionService->getActiveSubscription($userId);
            
            if (!$subscription) {
                return $this->createTriggerResponse(false, 'No active subscription found', 0, 0);
            }

            $limit = $subscription->plan->getBusinessConnectionsLimit();
            $canConnect = $currentCount < $limit;
            $remaining = max(0, $limit - $currentCount);
            
            // Clear cache to ensure fresh data
            $this->usageTracker->clearUsageCache($userId);
            
            return $this->createTriggerResponse(
                $canConnect,
                $canConnect ? 
                    "You can connect {$remaining} more business(es)" : 
                    "Business connection limit of {$limit} reached. Upgrade to connect more businesses.",
                $currentCount,
                $limit,
                [
                    'remaining' => $remaining,
                    'percentage' => $limit > 0 ? ($currentCount / $limit) * 100 : 0,
                    'trigger_type' => 'business_connection'
                ]
            );
            
        } catch (\Exception $e) {
            Log::error('SubscriptionUsageTrigger: Business connection check failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return $this->createTriggerResponse(true, 'Unable to verify limits, allowing action', 0, 0);
        }
    }

    /**
     * Trigger usage validation for reply sending
     */
    public function triggerReplySendCheck(int $userId): array
    {
        try {
            // Get real-time count from activity logs
            $currentCount = BusinessActivityLog::forUser($userId)
                                              ->byActivityType('send_reply')
                                              ->successful()
                                              ->countsTowardLimit()
                                              ->thisMonth()
                                              ->sum('activity_count');
            
            // Get subscription limits
            $subscription = $this->subscriptionService->getActiveSubscription($userId);
            
            if (!$subscription) {
                return $this->createTriggerResponse(false, 'No active subscription found', 0, 0);
            }

            if ($subscription->plan->hasUnlimitedReplies()) {
                return $this->createTriggerResponse(
                    true,
                    'Unlimited replies available',
                    $currentCount,
                    -1,
                    ['unlimited' => true, 'trigger_type' => 'reply_send']
                );
            }

            $limit = $subscription->plan->getMonthlyReplyLimit();
            $canSend = $currentCount < $limit;
            $remaining = max(0, $limit - $currentCount);
            
            // Clear cache to ensure fresh data
            $this->usageTracker->clearUsageCache($userId);
            
            return $this->createTriggerResponse(
                $canSend,
                $canSend ? 
                    "You have {$remaining} replies remaining this month" : 
                    "Monthly reply limit of {$limit} reached. Upgrade for more replies or wait for next month.",
                $currentCount,
                $limit,
                [
                    'remaining' => $remaining,
                    'percentage' => $limit > 0 ? ($currentCount / $limit) * 100 : 0,
                    'trigger_type' => 'reply_send',
                    'days_remaining_in_month' => Carbon::now()->endOfMonth()->diffInDays(Carbon::now())
                ]
            );
            
        } catch (\Exception $e) {
            Log::error('SubscriptionUsageTrigger: Reply send check failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return $this->createTriggerResponse(true, 'Unable to verify limits, allowing action', 0, 0);
        }
    }

    /**
     * Trigger usage validation for team member invitation
     */
    public function triggerTeamMemberInviteCheck(int $userId, int $businessId): array
    {
        try {
            // Get real-time count from database
            $currentCount = TeamMember::where('business_id', $businessId)
                                     ->where('status', 'active')
                                     ->count() + 1; // +1 for owner
            
            // Get subscription limits
            $subscription = $this->subscriptionService->getActiveSubscription($userId);
            
            if (!$subscription) {
                return $this->createTriggerResponse(false, 'No active subscription found', 0, 0);
            }

            $limit = $subscription->plan->getTeamMembersLimit();
            $canInvite = $currentCount < $limit;
            $remaining = max(0, $limit - $currentCount);
            
            // Clear cache to ensure fresh data
            $this->usageTracker->clearUsageCache($userId);
            
            return $this->createTriggerResponse(
                $canInvite,
                $canInvite ? 
                    "You can invite {$remaining} more team member(s)" : 
                    "Team member limit of {$limit} reached. Upgrade to invite more members.",
                $currentCount,
                $limit,
                [
                    'remaining' => $remaining,
                    'percentage' => $limit > 0 ? ($currentCount / $limit) * 100 : 0,
                    'trigger_type' => 'team_member_invite',
                    'business_id' => $businessId
                ]
            );
            
        } catch (\Exception $e) {
            Log::error('SubscriptionUsageTrigger: Team member invite check failed', [
                'user_id' => $userId,
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            
            return $this->createTriggerResponse(true, 'Unable to verify limits, allowing action', 0, 0);
        }
    }

    /**
     * Trigger usage validation for feature access
     */
    public function triggerFeatureAccessCheck(int $userId, string $feature): array
    {
        try {
            $subscription = $this->subscriptionService->getActiveSubscription($userId);
            
            if (!$subscription) {
                return $this->createTriggerResponse(false, 'No active subscription found', 0, 0);
            }

            $hasAccess = $subscription->plan->hasFeature($feature);
            
            return $this->createTriggerResponse(
                $hasAccess,
                $hasAccess ? 
                    'Feature access granted' : 
                    "Your plan does not include {$feature}. Upgrade to access this feature.",
                $hasAccess ? 1 : 0,
                1,
                [
                    'feature' => $feature,
                    'trigger_type' => 'feature_access',
                    'plan_name' => $subscription->plan->name
                ]
            );
            
        } catch (\Exception $e) {
            Log::error('SubscriptionUsageTrigger: Feature access check failed', [
                'user_id' => $userId,
                'feature' => $feature,
                'error' => $e->getMessage()
            ]);
            
            return $this->createTriggerResponse(false, 'Unable to verify feature access', 0, 0);
        }
    }

    /**
     * Trigger comprehensive usage check
     */
    public function triggerComprehensiveCheck(int $userId): array
    {
        try {
            // Force refresh usage data
            $usageData = $this->usageTracker->refreshUsageCache($userId);
            
            $alerts = [];
            $hasWarnings = false;
            $hasCritical = false;
            
            // Check business connections
            if ($usageData['business_connections']['percentage'] >= 100) {
                $alerts[] = [
                    'type' => 'business_connections',
                    'level' => 'critical',
                    'message' => 'Business connection limit exceeded'
                ];
                $hasCritical = true;
            } elseif ($usageData['business_connections']['percentage'] >= 80) {
                $alerts[] = [
                    'type' => 'business_connections',
                    'level' => 'warning',
                    'message' => 'Approaching business connection limit'
                ];
                $hasWarnings = true;
            }
            
            // Check monthly replies
            if (!$usageData['monthly_replies']['unlimited'] && $usageData['monthly_replies']['percentage'] >= 100) {
                $alerts[] = [
                    'type' => 'monthly_replies',
                    'level' => 'critical',
                    'message' => 'Monthly reply limit exceeded'
                ];
                $hasCritical = true;
            } elseif (!$usageData['monthly_replies']['unlimited'] && $usageData['monthly_replies']['percentage'] >= 80) {
                $alerts[] = [
                    'type' => 'monthly_replies',
                    'level' => 'warning',
                    'message' => 'Approaching monthly reply limit'
                ];
                $hasWarnings = true;
            }
            
            // Check team members
            if ($usageData['team_members']['percentage'] >= 100) {
                $alerts[] = [
                    'type' => 'team_members',
                    'level' => 'critical',
                    'message' => 'Team member limit exceeded'
                ];
                $hasCritical = true;
            } elseif ($usageData['team_members']['percentage'] >= 80) {
                $alerts[] = [
                    'type' => 'team_members',
                    'level' => 'warning',
                    'message' => 'Approaching team member limit'
                ];
                $hasWarnings = true;
            }
            
            return [
                'success' => true,
                'has_alerts' => !empty($alerts),
                'has_warnings' => $hasWarnings,
                'has_critical' => $hasCritical,
                'alerts' => $alerts,
                'usage_data' => $usageData,
                'trigger_type' => 'comprehensive_check',
                'timestamp' => now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            Log::error('SubscriptionUsageTrigger: Comprehensive check failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => 'Unable to perform comprehensive usage check',
                'has_alerts' => false,
                'alerts' => []
            ];
        }
    }

    /**
     * Create standardized trigger response
     */
    private function createTriggerResponse(bool $allowed, string $message, int $current, int $limit, array $metadata = []): array
    {
        return [
            'allowed' => $allowed,
            'message' => $message,
            'current_count' => $current,
            'limit' => $limit,
            'upgrade_required' => !$allowed,
            'metadata' => array_merge($metadata, [
                'timestamp' => now()->toISOString(),
                'trigger_source' => 'real_time_validation'
            ])
        ];
    }

    /**
     * Get cached trigger result or execute fresh check
     */
    public function getCachedOrFreshTrigger(string $triggerType, int $userId, array $params = []): array
    {
        $cacheKey = "trigger_{$triggerType}_{$userId}_" . md5(serialize($params));
        
        // Cache for 30 seconds to prevent excessive database queries
        return Cache::remember($cacheKey, 30, function() use ($triggerType, $userId, $params) {
            switch ($triggerType) {
                case 'business_connection':
                    return $this->triggerBusinessConnectionCheck($userId);
                case 'reply_send':
                    return $this->triggerReplySendCheck($userId);
                case 'team_member_invite':
                    return $this->triggerTeamMemberInviteCheck($userId, $params['business_id'] ?? 0);
                case 'feature_access':
                    return $this->triggerFeatureAccessCheck($userId, $params['feature'] ?? '');
                case 'comprehensive':
                    return $this->triggerComprehensiveCheck($userId);
                default:
                    return $this->createTriggerResponse(false, 'Unknown trigger type', 0, 0);
            }
        });
    }
}
