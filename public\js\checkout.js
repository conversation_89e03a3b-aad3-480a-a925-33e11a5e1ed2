document.addEventListener('DOMContentLoaded', function() {
    // State management
    let selectedPlan = null;
    let appliedCoupon = null;
    let currentCurrency = document.querySelector('[data-plan-currency]')?.dataset.planCurrency || 'INR';
    let isMobile = window.innerWidth < 1024;

    // Desktop DOM elements
    const planCards = document.querySelectorAll('.plan-card');
    const checkoutSection = document.getElementById('checkoutSection');
    const emptyState = document.getElementById('emptyState');
    const checkoutForm = document.getElementById('checkoutForm');
    const selectedPlanIdInput = document.getElementById('selectedPlanId');
    const planDetails = document.getElementById('planDetails');
    const toggleCouponBtn = document.getElementById('toggleCoupon');
    const couponSection = document.getElementById('couponSection');
    const couponCodeInput = document.getElementById('couponCode');
    const applyCouponBtn = document.getElementById('applyCouponBtn');
    const couponMessage = document.getElementById('couponMessage');
    const checkoutBtn = document.getElementById('checkoutBtn');
    const checkoutBtnText = document.getElementById('checkoutBtnText');
    const checkoutLoader = document.getElementById('checkoutLoader');
    const currencyToggle = document.getElementById('currencyToggle');

    // Mobile DOM elements
    const mobilePlanCards = document.querySelectorAll('.mobile-plan-card');
    const mobilePlansSection = document.getElementById('mobilePlansSection');
    const mobileCheckoutSection = document.getElementById('mobileCheckoutSection');
    const mobileContinueBtn = document.getElementById('mobileContinueBtn');
    const mobileBackBtn = document.getElementById('mobileBackBtn');
    const mobileCheckoutForm = document.getElementById('mobileCheckoutForm');
    const mobileSelectedPlanIdInput = document.getElementById('mobileSelectedPlanId');
    const mobilePlanDetails = document.getElementById('mobilePlanDetails');
    const mobileToggleCoupon = document.getElementById('mobileToggleCoupon');
    const mobileCouponSection = document.getElementById('mobileCouponSection');
    const mobileCouponCode = document.getElementById('mobileCouponCode');
    const mobileApplyCouponBtn = document.getElementById('mobileApplyCouponBtn');
    const mobileCouponMessage = document.getElementById('mobileCouponMessage');
    const mobileCheckoutBtn = document.getElementById('mobileCheckoutBtn');
    const mobileCheckoutBtnText = document.getElementById('mobileCheckoutBtnText');
    const mobileCheckoutLoader = document.getElementById('mobileCheckoutLoader');
    const currencyToggleMobile = document.getElementById('currencyToggleMobile');

    // Initialize
    init();

    function init() {
        setupEventListeners();
        updatePricingDisplay();

        // Select initial plan if provided
        const urlParams = new URLSearchParams(window.location.search);
        const planId = urlParams.get('plan');
        if (planId) {
            selectPlan(planId);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            isMobile = window.innerWidth < 1024;
        });
    }

    function setupEventListeners() {
        // Desktop plan selection
        planCards.forEach(card => {
            card.addEventListener('click', () => {
                const planId = card.dataset.planId;
                selectPlan(planId);
            });
        });

        // Mobile plan selection
        mobilePlanCards.forEach(card => {
            card.addEventListener('click', () => {
                const planId = card.dataset.planId;
                selectPlan(planId);
            });
        });

        // Mobile navigation
        mobileContinueBtn?.addEventListener('click', () => {
            if (selectedPlan) {
                showMobileCheckout();
            }
        });

        mobileBackBtn?.addEventListener('click', () => {
            showMobilePlans();
        });

        // Desktop coupon toggle
        toggleCouponBtn?.addEventListener('click', () => {
            const isHidden = couponSection.classList.contains('hidden');
            couponSection.classList.toggle('hidden');
            document.getElementById('couponToggleText').textContent =
                isHidden ? 'Hide coupon' : 'Have a coupon?';
        });

        // Mobile coupon toggle
        mobileToggleCoupon?.addEventListener('click', () => {
            const isHidden = mobileCouponSection.classList.contains('hidden');
            mobileCouponSection.classList.toggle('hidden');
            const chevron = document.getElementById('mobileCouponChevron');
            if (chevron) {
                chevron.style.transform = isHidden ? 'rotate(180deg)' : 'rotate(0deg)';
            }
        });

        // Apply coupon - Desktop
        applyCouponBtn?.addEventListener('click', () => applyCoupon(false));
        couponCodeInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                applyCoupon(false);
            }
        });

        // Apply coupon - Mobile
        mobileApplyCouponBtn?.addEventListener('click', () => applyCoupon(true));
        mobileCouponCode?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                applyCoupon(true);
            }
        });

        // Form submission
        checkoutForm?.addEventListener('submit', handleCheckoutSubmit);
        mobileCheckoutForm?.addEventListener('submit', handleCheckoutSubmit);

        // Currency toggle
        currencyToggle?.addEventListener('click', toggleCurrency);
        currencyToggleMobile?.addEventListener('click', toggleCurrency);
    }

    function selectPlan(planId) {
        // Update desktop UI
        planCards.forEach(card => {
            card.classList.remove('border-primary-500', 'bg-primary-50');
            const radio = card.querySelector('.plan-radio');
            if (radio) {
                radio.classList.remove('border-primary-500', 'bg-primary-500');
                radio.innerHTML = '';
            }

            if (card.dataset.planId === planId) {
                card.classList.add('border-primary-500', 'bg-primary-50');
                if (radio) {
                    radio.classList.add('border-primary-500', 'bg-primary-500');
                    radio.innerHTML = '<div class="w-2 h-2 bg-white rounded-full"></div>';
                }
            }
        });

        // Update mobile UI
        mobilePlanCards.forEach(card => {
            card.classList.remove('border-primary-500', 'bg-primary-50');
            const radio = card.querySelector('.mobile-plan-radio');
            if (radio) {
                radio.classList.remove('border-primary-500', 'bg-primary-500');
                radio.innerHTML = '';
            }

            if (card.dataset.planId === planId) {
                card.classList.add('border-primary-500', 'bg-primary-50');
                if (radio) {
                    radio.classList.add('border-primary-500', 'bg-primary-500');
                    radio.innerHTML = '<div class="w-3 h-3 bg-white rounded-full"></div>';
                }
            }
        });

        // Get plan data
        const planCard = document.querySelector(`[data-plan-id="${planId}"]`);
        if (!planCard) return;

        selectedPlan = {
            id: planId,
            name: planCard.querySelector('h3').textContent,
            price: parseFloat(planCard.dataset.planPrice),
            currency: planCard.dataset.planCurrency,
            symbol: planCard.dataset.planSymbol
        };

        // Update form inputs
        if (selectedPlanIdInput) selectedPlanIdInput.value = planId;
        if (mobileSelectedPlanIdInput) mobileSelectedPlanIdInput.value = planId;

        // Update plan details
        updatePlanDetails();

        // Show/hide sections
        if (checkoutSection) {
            checkoutSection.classList.remove('hidden');
            if (emptyState) emptyState.classList.add('hidden');
        }

        // Enable mobile continue button
        if (mobileContinueBtn) {
            mobileContinueBtn.disabled = false;
            mobileContinueBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }

        // Reset coupon
        resetCoupon();

        // Update pricing
        updatePricingDisplay();
    }

    function showMobileCheckout() {
        if (mobilePlansSection) mobilePlansSection.classList.add('hidden');
        if (mobileCheckoutSection) mobileCheckoutSection.classList.remove('hidden');
        updatePlanDetails();
        updatePricingDisplay();
    }

    function showMobilePlans() {
        if (mobileCheckoutSection) mobileCheckoutSection.classList.add('hidden');
        if (mobilePlansSection) mobilePlansSection.classList.remove('hidden');
    }

    function updatePlanDetails() {
        if (!selectedPlan) return;

        const planDetailsHtml = `
            <div class="flex justify-between items-center">
                <div>
                    <div class="font-medium">${selectedPlan.name}</div>
                    <div class="text-sm opacity-75">${selectedPlan.currency} ${selectedPlan.price} / 30 days</div>
                </div>
                <div class="text-right">
                    <div class="text-lg font-bold">
                        ${selectedPlan.symbol}${formatPrice(selectedPlan.price)}
                    </div>
                </div>
            </div>
        `;

        if (planDetails) planDetails.innerHTML = planDetailsHtml;
        if (mobilePlanDetails) mobilePlanDetails.innerHTML = planDetailsHtml;
    }

    function updatePricingDisplay() {
        if (!selectedPlan) return;

        const subtotal = selectedPlan.price;
        const discount = appliedCoupon ? appliedCoupon.pricing.discount_amount : 0;
        const total = subtotal - discount;

        // Desktop pricing
        const subtotalEl = document.getElementById('subtotalAmount');
        const discountRowEl = document.getElementById('discountRow');
        const discountLabelEl = document.getElementById('discountLabel');
        const discountAmountEl = document.getElementById('discountAmount');
        const totalAmountEl = document.getElementById('totalAmount');

        if (subtotalEl) subtotalEl.textContent = `${selectedPlan.symbol}${formatPrice(subtotal)}`;
        if (totalAmountEl) totalAmountEl.textContent = `${selectedPlan.symbol}${formatPrice(total)}`;

        if (discount > 0 && discountRowEl) {
            discountRowEl.classList.remove('hidden');
            if (discountLabelEl) discountLabelEl.textContent = appliedCoupon.coupon.code;
            if (discountAmountEl) discountAmountEl.textContent = `-${selectedPlan.symbol}${formatPrice(discount)}`;
        } else if (discountRowEl) {
            discountRowEl.classList.add('hidden');
        }

        // Mobile pricing
        const mobileSubtotalEl = document.getElementById('mobileSubtotalAmount');
        const mobileDiscountRowEl = document.getElementById('mobileDiscountRow');
        const mobileDiscountLabelEl = document.getElementById('mobileDiscountLabel');
        const mobileDiscountAmountEl = document.getElementById('mobileDiscountAmount');
        const mobileTotalAmountEl = document.getElementById('mobileTotalAmount');

        if (mobileSubtotalEl) mobileSubtotalEl.textContent = `${selectedPlan.symbol}${formatPrice(subtotal)}`;
        if (mobileTotalAmountEl) mobileTotalAmountEl.textContent = `${selectedPlan.symbol}${formatPrice(total)}`;

        if (discount > 0 && mobileDiscountRowEl) {
            mobileDiscountRowEl.classList.remove('hidden');
            if (mobileDiscountLabelEl) mobileDiscountLabelEl.textContent = appliedCoupon.coupon.code;
            if (mobileDiscountAmountEl) mobileDiscountAmountEl.textContent = `-${selectedPlan.symbol}${formatPrice(discount)}`;
        } else if (mobileDiscountRowEl) {
            mobileDiscountRowEl.classList.add('hidden');
        }

        // Update checkout button text
        const buttonText = total === 0 ? 'Activate Free Subscription' : `Pay ${selectedPlan.symbol}${formatPrice(total)}`;
        if (checkoutBtnText) checkoutBtnText.textContent = buttonText;
        if (mobileCheckoutBtnText) mobileCheckoutBtnText.textContent = buttonText;
    }

    async function applyCoupon(isMobileContext = false) {
        if (!selectedPlan) return;

        const couponInput = isMobileContext ? mobileCouponCode : couponCodeInput;
        const applyBtn = isMobileContext ? mobileApplyCouponBtn : applyCouponBtn;
        const messageEl = isMobileContext ? mobileCouponMessage : couponMessage;

        if (!couponInput || !applyBtn) return;

        const couponCode = couponInput.value.trim().toUpperCase();
        if (!couponCode) {
            showCouponMessage('Please enter a coupon code.', 'error', isMobileContext);
            addShakeAnimation(couponInput);
            return;
        }

        // Show loading state
        applyBtn.disabled = true;
        applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Validating...';
        applyBtn.classList.add('opacity-75');

        try {
            const response = await fetch('/checkout/validate-coupon', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    coupon_code: couponCode,
                    plan_id: selectedPlan.id
                })
            });

            const data = await response.json();

            if (data.valid) {
                appliedCoupon = data;

                // Update both desktop and mobile inputs
                if (couponCodeInput) {
                    couponCodeInput.value = data.coupon.code;
                    couponCodeInput.disabled = true;
                    couponCodeInput.classList.add('bg-green-50', 'border-green-300');
                }
                if (mobileCouponCode) {
                    mobileCouponCode.value = data.coupon.code;
                    mobileCouponCode.disabled = true;
                    mobileCouponCode.classList.add('bg-green-900', 'border-green-500');
                }

                // Update both buttons
                [applyCouponBtn, mobileApplyCouponBtn].forEach(btn => {
                    if (btn) {
                        btn.innerHTML = '<i class="fas fa-check mr-2"></i>Applied';
                        btn.classList.remove('bg-gray-600', 'hover:bg-gray-700', 'bg-blue-600', 'hover:bg-blue-700', 'opacity-75');
                        btn.classList.add('bg-green-600', 'hover:bg-green-700');
                    }
                });

                showCouponMessage(data.message, 'success', isMobileContext);
                updatePricingDisplay();

                // Add success animation
                addSuccessAnimation(applyBtn);
            } else {
                showCouponMessage(data.message, 'error', isMobileContext);
                addShakeAnimation(couponInput);
            }
        } catch (error) {
            showCouponMessage('Failed to validate coupon. Please try again.', 'error', isMobileContext);
            addShakeAnimation(couponInput);
        } finally {
            if (!appliedCoupon) {
                applyBtn.disabled = false;
                applyBtn.innerHTML = 'Apply';
                applyBtn.classList.remove('opacity-75');
            }
        }
    }

    function resetCoupon() {
        appliedCoupon = null;

        // Reset desktop elements
        if (couponCodeInput) {
            couponCodeInput.value = '';
            couponCodeInput.disabled = false;
            couponCodeInput.classList.remove('bg-green-50', 'border-green-300');
        }
        if (applyCouponBtn) {
            applyCouponBtn.disabled = false;
            applyCouponBtn.innerHTML = 'Apply';
            applyCouponBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
            applyCouponBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
        }
        if (couponMessage) couponMessage.classList.add('hidden');

        // Reset mobile elements
        if (mobileCouponCode) {
            mobileCouponCode.value = '';
            mobileCouponCode.disabled = false;
            mobileCouponCode.classList.remove('bg-green-900', 'border-green-500');
        }
        if (mobileApplyCouponBtn) {
            mobileApplyCouponBtn.disabled = false;
            mobileApplyCouponBtn.innerHTML = 'Apply';
            mobileApplyCouponBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
            mobileApplyCouponBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
        }
        if (mobileCouponMessage) mobileCouponMessage.classList.add('hidden');

        updatePricingDisplay();
    }

    function showCouponMessage(message, type, isMobileContext = false) {
        const messageEl = isMobileContext ? mobileCouponMessage : couponMessage;
        if (!messageEl) return;

        messageEl.textContent = message;
        messageEl.classList.remove('hidden', 'text-red-600', 'text-green-600', 'text-red-400', 'text-green-400');

        if (isMobileContext) {
            messageEl.classList.add(type === 'error' ? 'text-red-400' : 'text-green-400');
        } else {
            messageEl.classList.add(type === 'error' ? 'text-red-600' : 'text-green-600');
        }
    }

    function handleCheckoutSubmit(e) {
        if (!selectedPlan) {
            e.preventDefault();
            showFormError('Please select a plan.');
            return;
        }

        // Determine which form is being submitted
        const isDesktop = e.target === checkoutForm;
        const btn = isDesktop ? checkoutBtn : mobileCheckoutBtn;
        const btnText = isDesktop ? checkoutBtnText : mobileCheckoutBtnText;
        const loader = isDesktop ? checkoutLoader : mobileCheckoutLoader;

        if (!btn || !btnText || !loader) return;

        // Show loading state
        btn.disabled = true;
        btnText.textContent = 'Processing Payment...';
        loader.classList.remove('hidden');
        btn.classList.add('opacity-75', 'cursor-not-allowed', 'animate-pulse');

        // Disable all form inputs
        const formInputs = e.target.querySelectorAll('input, button, select');
        formInputs.forEach(input => {
            if (input !== btn) {
                input.disabled = true;
            }
        });

        // Show loading overlay
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
        }
    }

    function toggleCurrency() {
        const newCurrency = currentCurrency === 'INR' ? 'USD' : 'INR';
        window.location.href = `${window.location.pathname}?currency=${newCurrency}`;
    }

    function formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(price);
    }

    // Animation utility functions
    function addShakeAnimation(element) {
        element.classList.add('animate-shake');
        setTimeout(() => {
            element.classList.remove('animate-shake');
        }, 500);
    }

    function addSuccessAnimation(element) {
        element.classList.add('animate-bounce');
        setTimeout(() => {
            element.classList.remove('animate-bounce');
        }, 1000);
    }

    function addPulseAnimation(element) {
        element.classList.add('animate-pulse');
        setTimeout(() => {
            element.classList.remove('animate-pulse');
        }, 2000);
    }

    // Form validation and error display
    function showFormError(message) {
        // Create or update error message element
        let errorElement = document.getElementById('form-error-message');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.id = 'form-error-message';
            errorElement.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
            checkoutForm.insertBefore(errorElement, checkoutForm.firstChild);
        }

        errorElement.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        addShakeAnimation(errorElement);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorElement) {
                errorElement.remove();
            }
        }, 5000);
    }

    // Enhanced plan selection with animation
    function enhancePlanSelection() {
        planCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                if (!card.classList.contains('border-primary-500')) {
                    card.classList.add('transform', 'scale-105', 'shadow-lg');
                }
            });

            card.addEventListener('mouseleave', () => {
                if (!card.classList.contains('border-primary-500')) {
                    card.classList.remove('transform', 'scale-105', 'shadow-lg');
                }
            });
        });
    }

    // Real-time form validation
    function setupFormValidation() {
        // Coupon code input validation
        [couponCodeInput, mobileCouponCode].forEach(input => {
            input?.addEventListener('input', (e) => {
                const value = e.target.value.trim();
                if (value.length > 0) {
                    e.target.value = value.toUpperCase();
                    e.target.classList.remove('border-red-300', 'border-red-500');
                    e.target.classList.add('border-gray-300', 'border-gray-600');
                }
            });
        });
    }

    // Progress indicator
    function updateProgressIndicator(step) {
        const steps = ['plan', 'payment', 'processing'];
        const currentStepIndex = steps.indexOf(step);

        // You can add a progress bar here if needed
        console.log(`Progress: Step ${currentStepIndex + 1} of ${steps.length}`);
    }

    // Initialize all enhancements
    function initializeEnhancements() {
        enhancePlanSelection();
        setupFormValidation();

        // Add custom CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            .animate-shake {
                animation: shake 0.5s ease-in-out;
            }
            .transition-all {
                transition: all 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize everything
    initializeEnhancements();
});
