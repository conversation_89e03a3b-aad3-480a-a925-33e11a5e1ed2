let applyCoupon = document.querySelector('.applyCoupon');

// Only add event listener if the element exists
if (applyCoupon) {
    applyCoupon.addEventListener('click', function(e) {
    e.preventDefault();

    let couponCodeValue = document.getElementById('couponCodeVal').value;
    let couponCodeValueUpdateEl = document.getElementById('couponCodeValUpdate');
    let successEl = document.querySelector('.successCoupon');
    let dangerEl = document.querySelector('.dangerCoupon');
    let validateCouponCodeEl = document.querySelector('.validateCouponCode');
    let proceedBtnCodeEl = document.querySelector('.proceedBtn');
    let payNowBtnBtnCodeEl = document.querySelector('.payNowBtn');
    
    

    if(couponCodeValue === "")
    {
        dangerEl.style.display = "none";
        dangerEl.style.display = "none";
        validateCouponCodeEl.style.display = "inline";
        proceedBtnCodeEl.style.display = "none";
        payNowBtnBtnCodeEl.style.display = "inline";
    }
    else
    {
        if (couponCodeValue === "FREEPASS27") {
            proceedBtnCodeEl.style.display = "inline";
            payNowBtnBtnCodeEl.style.display = "none";
            validateCouponCodeEl.style.display = "none";
            successEl.style.display = "inline";
            dangerEl.style.display = "none";
            couponCodeValueUpdateEl.value = couponCodeValue;
        } else {
            proceedBtnCodeEl.style.display = "none";
            payNowBtnBtnCodeEl.style.display = "inline";
            validateCouponCodeEl.style.display = "none";
            successEl.style.display = "none";
            dangerEl.style.display = "inline";
        }
    }
    });
}
