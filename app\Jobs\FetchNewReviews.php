<?php

namespace App\Jobs;

use App\Helpers\ReviewHelper;
use App\Models\GoogleReview;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FetchNewReviews implements ShouldQueue
{
    use Queueable;
    protected $location;
    protected $businessId;
    protected $token;

    /**
     * Create a new job instance.
     */
    public function __construct($location, $businessId, $token)
    {
        $this->location = $location;
        $this->businessId = $businessId;
        $this->token = $token;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->location) {
            throw new \Exception('No location specified.');
        }
        if(str_contains($this->location, '/')) {
            $locationId = explode("/", $this->location)[1];
        } else {
            $locationId = $this->location;
        }
        
        try {
            $reviewsResponse = ReviewHelper::getReviews($locationId, $this->businessId, $this->token);
            // Initialize counters for operations
            $response = [
                'success' => true,
                'new_reviews_added' => 0,
                'reviews_updated' => 0,
                'new_replies_added' => 0,
                'replies_updated' => 0,
                'message' => ''
            ];

            $businessId = $this->businessId;
            ReviewHelper::getReviewCondition($reviewsResponse, $locationId, $businessId, $response);

            // Return response data instead of JSON response in a job
            return;
        } catch (\Exception $e) {
            Log::error('Failed to fetch reviews', [
                'message' => $e->getMessage(),
                'location_id' => $locationId
            ]);

            return;
        }
    }
}
