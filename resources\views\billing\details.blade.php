@extends('layouts.app')

@section('content')
@php $locationId = explode("/", $business['location_name'])[1]; @endphp
<div class="min-h-screen bg-gray-100 font-poppins">
    <div class="max-w-[1200px] mx-auto px-4 md:px-6 py-6 md:py-8 bg-white">
        <!-- <PERSON> Header -->
        <div class="flex justify-between items-start gap-2 border-b border-gray-200 mb-4 pb-4">
            
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Billing Details</h1>
                    <p class="text-gray-600">Manage your subscription, view payment history, and track usage</p>
                </div>

                @if($user && $user->stripe_customer_id != null)
                <div class="mt-4 sm:mt-0">
                    <a href="{{ route('billing.portal') }}"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Manage Billing
                    </a>
                </div>
                @endif
            </div>
            <div class="mb-4 flex items-center">
                <a href="{{ route('business.dashboard') }}" class="flex items-center px-2 py-1 md:px-4 md:py-2 bg-white-600 text-indigo-600 border ring-2 ring-indigo-600 rounded-md text-sm font-medium hover:text-white hover:bg-indigo-600">
                    <i class="fas fa-arrow-left mr-2"></i> Back <span class="hidden md:block ml-1">to Business</span>
                </a>
            </div>
        </div>

        <!-- Billing Summary Cards -->
        @if(isset($billingData['billing_summary']))
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-dollar-sign text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Spent</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {{ $billingData['billing_summary']['currency_symbol'] }}{{ number_format($billingData['billing_summary']['total_spent'], 2) }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-receipt text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Subscriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $billingData['billing_summary']['subscription_count'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="fas fa-crown text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Current Plan</p>
                        <p class="text-lg font-bold text-gray-900">
                            @if($billingData['active_subscription'])
                            {{ $billingData['active_subscription']['plan_name'] }}
                            @else
                            No Active Plan
                            @endif
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <i class="fas fa-calendar text-orange-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Days Remaining</p>
                        <p class="text-2xl font-bold text-gray-900">
                            @if($billingData['active_subscription'])
                            {{ abs(number_format($billingData['active_subscription']['days_remaining'])) }}
                            @else
                            --
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Main Content Sections -->
        <div class="space-y-8">
            <!-- Active Subscription Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-star text-yellow-500 mr-3"></i>
                            Active Subscription
                        </h2>
                        @if($billingData['active_subscription'])
                        <div class="flex items-center space-x-2">
                            <button id="toggleSubscriptionDetails" class="text-gray-500 hover:text-gray-700 transition-colors">
                                <i class="fas fa-chevron-down transform transition-transform duration-200" id="toggleIcon"></i>
                            </button>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="p-6">
                    @if($billingData['active_subscription'])
                    <!-- Subscription Status Banner -->
                    @php
                    $daysRemaining = $billingData['active_subscription']['days_remaining'];
                    $isExpiringSoon = $daysRemaining <= 30;
                        $isExpiringSoonCritical=$daysRemaining <=7;
                        $bannerClass=$isExpiringSoonCritical ? 'from-red-50 to-orange-50 border-red-200' :
                        ($isExpiringSoon ? 'from-yellow-50 to-orange-50 border-yellow-200' : 'from-green-50 to-blue-50 border-green-200' );
                        @endphp
                        <div class="mb-6 p-4 bg-gradient-to-r {{ $bannerClass }} rounded-lg border hover:shadow-md transition-all duration-300">
                        @if($isExpiringSoon)
                        <div class="mb-3 p-2 bg-white bg-opacity-70 rounded-lg">
                            <div class="flex items-center text-sm">
                                <i class="fas fa-exclamation-triangle text-orange-500 mr-2"></i>
                                <span class="font-medium text-gray-900">
                                    @if($isExpiringSoonCritical)
                                    Your subscription expires in {{ abs(number_format($daysRemaining)) }} days!
                                    @else
                                    Your subscription expires soon.
                                    @endif
                                </span>
                                <button onclick="openUpgradeModal()" class="ml-2 text-indigo-600 hover:text-indigo-800 font-medium underline">
                                    Renew now
                                </button>
                            </div>
                        </div>
                        @endif

                        <div class="flex items-center justify-between flex-wrap gap-4">
                            <div class="flex items-center">
                                <div class="w-3 h-3 {{ $isExpiringSoonCritical ? 'bg-red-500' : ($isExpiringSoon ? 'bg-yellow-500' : 'bg-green-500') }} rounded-full mr-3 animate-pulse"></div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 flex items-center">
                                        {{ $billingData['active_subscription']['plan_name'] }}
                                        <span class="ml-2 px-2 py-1 bg-white bg-opacity-70 text-xs font-medium rounded-full">
                                            {{ $billingData['active_subscription']['status'] }}
                                        </span>
                                    </h3>
                                    <p class="text-sm text-gray-600">Active until {{ $billingData['active_subscription']['expiry_date'] }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="text-right">
                                    <p class="text-2xl font-bold {{ $isExpiringSoonCritical ? 'text-red-600' : ($isExpiringSoon ? 'text-yellow-600' : 'text-gray-900') }}" id="daysRemaining">
                                        {{ abs(number_format($billingData['active_subscription']['days_remaining'])) }}
                                    </p>
                                    <p class="text-xs text-gray-500">days left</p>
                                </div>
                                <div class="flex flex-col space-y-2">
                                    <button onclick="openUpgradeModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover-lift">
                                        <i class="fas fa-arrow-up mr-1"></i>
                                        Upgrade
                                    </button>
                                    @if($isExpiringSoon)
                                    <button onclick="renewSubscription()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover-lift">
                                        <i class="fas fa-refresh mr-1"></i>
                                        Renew
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Subscription Timeline Progress -->
                        <div class="mt-4">
                            <div class="flex justify-between text-xs text-gray-600 mb-1">
                                <span>{{ $billingData['active_subscription']['start_date'] }}</span>
                                <span>{{ $billingData['active_subscription']['expiry_date'] }}</span>
                            </div>
                            @php
                            $totalDays = $billingData['active_subscription']['duration_days'];
                            $daysRemaining = abs(number_format($billingData['active_subscription']['days_remaining']));
                            $daysUsed = $totalDays - $daysRemaining;
                            $progressPercentage = $totalDays > 0 ? ($daysUsed / $totalDays) * 100 : 0;
                            @endphp
                            <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                                    style="width: {{ $progressPercentage }}%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>{{ abs(number_format($daysUsed)) }} days used</span>
                                <span>{{ abs(number_format($daysRemaining)) }} days remaining</span>
                            </div>
                        </div>
                </div>

                <!-- Quick Actions -->
                <!-- <div class="mb-6 grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button onclick="manageSubscription()" class="flex items-center justify-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                        <i class="fas fa-cog text-gray-600 group-hover:text-gray-800 mr-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Manage</span>
                    </button>
                    <button onclick="viewInvoices()" class="flex items-center justify-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                        <i class="fas fa-file-invoice text-gray-600 group-hover:text-gray-800 mr-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Invoices</span>
                    </button>
                    <button onclick="downloadReceipt()" class="flex items-center justify-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                        <i class="fas fa-download text-gray-600 group-hover:text-gray-800 mr-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Receipt</span>
                    </button>
                    <button onclick="contactSupport()" class="flex items-center justify-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                        <i class="fas fa-headset text-gray-600 group-hover:text-gray-800 mr-2"></i>
                        <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Support</span>
                    </button>
                </div> -->

                <!-- Collapsible Details Section -->
                <div id="subscriptionDetails" class="transition-all duration-300 overflow-hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Subscription Details -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                Plan Details
                            </h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center p-2 hover:bg-white rounded transition-colors">
                                    <span class="text-gray-600">Plan Name:</span>
                                    <span class="font-medium">{{ $billingData['active_subscription']['plan_name'] }}</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-white rounded transition-colors">
                                    <span class="text-gray-600">Status:</span>
                                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        {{ $billingData['active_subscription']['status'] }}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-white rounded transition-colors">
                                    <span class="text-gray-600">Start Date:</span>
                                    <span class="font-medium">{{ $billingData['active_subscription']['start_date'] }}</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-white rounded transition-colors">
                                    <span class="text-gray-600">Expiry Date:</span>
                                    <span class="font-medium">{{ $billingData['active_subscription']['expiry_date'] }}</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-white rounded transition-colors">
                                    <span class="text-gray-600">Price:</span>
                                    <span class="font-medium text-lg">
                                        {{ $billingData['active_subscription']['currency_symbol'] }}{{ number_format($billingData['active_subscription']['price'], 2) }}
                                        <span class="text-sm text-gray-500">/ {{ $billingData['active_subscription']['duration_days'] }} days</span>
                                    </span>
                                </div>
                                @if($billingData['active_subscription']['payment_info'])
                                <div class="flex justify-between items-center p-2 hover:bg-white rounded transition-colors">
                                    <span class="text-gray-600">Payment Method:</span>
                                    <span class="font-medium">{{ $billingData['active_subscription']['payment_info']['payment_method'] ?? 'N/A' }}</span>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Interactive Plan Features -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-list-check text-green-500 mr-2"></i>
                                Plan Features
                                <button onclick="compareFeatures()" class="ml-auto text-sm text-indigo-600 hover:text-indigo-800 font-medium">
                                    Compare Plans
                                </button>
                            </h3>
                            <div class="space-y-2">
                                @foreach($billingData['active_subscription']['features'] as $index => $feature)
                                <div class="flex items-center p-2 hover:bg-white rounded transition-colors group cursor-pointer"
                                    onclick="toggleFeatureDetails({{ $index }})">
                                    <i class="fas fa-check text-green-500 mr-3 text-sm"></i>
                                    <span class="text-gray-700 flex-1">{{ $feature['title'] }}:
                                        <span class="font-medium">{{ $feature['value'] }}</span>
                                    </span>
                                    <i class="fas fa-info-circle text-gray-400 group-hover:text-gray-600 text-sm"></i>
                                </div>
                                @endforeach
                            </div>

                            <!-- Feature Usage Preview -->
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <button onclick="viewUsageDetails()" class="w-full text-center text-sm text-indigo-600 hover:text-indigo-800 font-medium py-2 hover:bg-indigo-50 rounded transition-colors">
                                    <i class="fas fa-chart-bar mr-1"></i>
                                    View Usage Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @else
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-circle text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                    <p class="text-gray-600 mb-4">You don't have an active subscription. Choose a plan to get started.</p>
                    <a href="{{ route('business.subscriptions') }}"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Choose a Plan
                    </a>
                </div>
                @endif
            </div>
        </div>

        <!-- Current Usage Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-chart-bar text-blue-500 mr-3"></i>
                    Current Usage
                </h2>
            </div>
            <div class="p-6">
                @if($billingData['current_usage']['has_subscription'])
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Business Connections -->
                    <div class="text-center">
                        <div class="mb-4">
                            <div class="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-building text-blue-600 text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Business Connections</h3>
                        <div class="text-3xl font-bold text-gray-900 mb-2">
                            {{ $billingData['current_usage']['businesses']['current'] }} / {{ $billingData['current_usage']['businesses']['limit'] }}
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $billingData['current_usage']['businesses']['percentage'] }}%"></div>
                        </div>
                        <p class="text-sm text-gray-600">{{ $billingData['current_usage']['businesses']['remaining'] }} remaining</p>
                    </div>

                    <!-- Team Members -->
                    <div class="text-center">
                        <div class="mb-4">
                            <div class="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-green-600 text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Team Members</h3>
                        <div class="text-3xl font-bold text-gray-900 mb-2">
                            {{ $billingData['current_usage']['team_members']['current'] }} / {{ $billingData['current_usage']['team_members']['limit'] }}
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: {{ $billingData['current_usage']['team_members']['percentage'] }}%"></div>
                        </div>
                        <p class="text-sm text-gray-600">{{ $billingData['current_usage']['team_members']['remaining'] }} remaining</p>
                    </div>

                    <!-- Monthly Replies -->
                    <div class="text-center">
                        <div class="mb-4">
                            <div class="w-20 h-20 mx-auto bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-reply text-purple-600 text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Monthly Replies</h3>
                        <div class="text-3xl font-bold text-gray-900 mb-2">
                            @if($billingData['current_usage']['replies']['is_unlimited'])
                            {{ $billingData['current_usage']['replies']['current'] }} / ∞
                            @else
                            {{ $billingData['current_usage']['replies']['current'] }} / {{ $billingData['current_usage']['replies']['limit'] }}
                            @endif
                        </div>
                        @if(!$billingData['current_usage']['replies']['is_unlimited'])
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: {{ $billingData['current_usage']['replies']['percentage'] }}%"></div>
                        </div>
                        <p class="text-sm text-gray-600">{{ $billingData['current_usage']['replies']['remaining'] }} remaining</p>
                        @else
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                        <p class="text-sm text-gray-600">Unlimited usage</p>
                        @endif
                        <p class="text-xs text-gray-500 mt-1">Resets: {{ $billingData['current_usage']['replies']['reset_date'] }}</p>
                    </div>
                </div>
                @else
                <div class="text-center py-8">
                    <i class="fas fa-chart-bar text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Usage Data</h3>
                    <p class="text-gray-600">Subscribe to a plan to track your usage.</p>
                </div>
                @endif
            </div>
        </div>
        <!-- Past Subscriptions Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-history text-gray-500 mr-3"></i>
                    Past Subscriptions
                </h2>
            </div>
            <div class="p-6">
                @if(count($billingData['past_subscriptions']) > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($billingData['past_subscriptions'] as $subscription)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $subscription['plan_name'] }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                                @if($subscription['status'] === 'EXPIRED') bg-red-100 text-red-800
                                                @elseif($subscription['status'] === 'CANCELLED') bg-yellow-100 text-yellow-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                        {{ $subscription['status'] }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $subscription['duration'] }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @if($subscription['payment_info'])
                                    {{ $subscription['currency_symbol'] }}{{ number_format($subscription['payment_info']['amount'], 2) }}
                                    @else
                                    --
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $subscription['start_date'] }} - {{ $subscription['expiry_date'] }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-8">
                    <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Past Subscriptions</h3>
                    <p class="text-gray-600">Your subscription history will appear here.</p>
                </div>
                @endif
            </div>
        </div>

        <!-- Payment History Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-credit-card text-green-500 mr-3"></i>
                    Payment History
                </h2>
            </div>
            <div class="p-6">
                @if(count($billingData['payment_history']) > 0)
                <div class="space-y-4">
                    @foreach($billingData['payment_history'] as $payment)
                    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <h3 class="text-lg font-medium text-gray-900 mr-3">{{ $payment['plan_name'] }}</h3>
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                                @if($payment['payment_status'] === 'SUCCESS') bg-green-100 text-green-800
                                                @elseif($payment['payment_status'] === 'FAILED') bg-red-100 text-red-800
                                                @elseif($payment['payment_status'] === 'REFUNDED') bg-yellow-100 text-yellow-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                        {{ $payment['payment_status'] }}
                                    </span>
                                </div>
                                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                                    <div>
                                        <span class="font-medium">Amount:</span>
                                        <span class="ml-1">{{ $payment['currency'] }} {{ number_format($payment['amount'], 2) }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium">Method:</span>
                                        <span class="ml-1">{{ $payment['payment_method'] ?? 'N/A' }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium">Gateway:</span>
                                        <span class="ml-1">{{ $payment['gateway_name'] }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium">Date:</span>
                                        <span class="ml-1">{{ $payment['payment_date'] }}</span>
                                    </div>
                                </div>
                                @if($payment['coupon_code'])
                                <div class="mt-2 text-sm text-green-600">
                                    <i class="fas fa-tag mr-1"></i>
                                    Coupon: {{ $payment['coupon_code'] }}
                                    @if($payment['discount_amount'] > 0)
                                    (Saved: {{ $payment['currency'] }} {{ number_format($payment['discount_amount'], 2) }})
                                    @endif
                                </div>
                                @endif
                                @if($payment['is_refunded'])
                                <div class="mt-2 text-sm text-yellow-600">
                                    <i class="fas fa-undo mr-1"></i>
                                    Refunded on {{ $payment['refund_date'] }}
                                </div>
                                @endif
                            </div>
                            <div class="mt-4 sm:mt-0 sm:ml-4">
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-900">
                                        {{ $payment['currency'] }} {{ number_format($payment['amount'], 2) }}
                                    </div>
                                    @if($payment['original_amount'] > $payment['amount'])
                                    <div class="text-sm text-gray-500 line-through">
                                        {{ $payment['currency'] }} {{ number_format($payment['original_amount'], 2) }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @if($payment['transaction_id'])
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <div class="text-xs text-gray-500">
                                Transaction ID: {{ $payment['transaction_id'] }}
                            </div>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-8">
                    <i class="fas fa-credit-card text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Payment History</h3>
                    <p class="text-gray-600">Your payment transactions will appear here.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
</div>

<!-- Feature Comparison Modal -->
<div id="featureComparisonModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center border-b px-6 py-4">
            <h3 class="text-xl font-semibold text-gray-900">Compare Subscription Plans</h3>
            <button onclick="closeFeatureModal()" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="text-center mb-6">
                <p class="text-gray-600">See how your current plan compares to other available options</p>
            </div>

            <!-- Plan Comparison Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feature</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Your Plan
                                @if($billingData['active_subscription'])
                                <div class="text-sm font-normal text-indigo-600 mt-1">{{ $billingData['active_subscription']['plan_name'] }}</div>
                                @endif
                            </th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Business Plan</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Enterprise Plan</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Business Connections</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                @if($billingData['active_subscription'])
                                @foreach($billingData['active_subscription']['features'] as $feature)
                                @if($feature['key'] === 'business_connections_limit')
                                <span class="text-indigo-600 font-medium">{{ $feature['value'] }}</span>
                                @endif
                                @endforeach
                                @else
                                <span class="text-gray-400">-</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">10</td>
                        </tr>
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Team Members</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                @if($billingData['active_subscription'])
                                @foreach($billingData['active_subscription']['features'] as $feature)
                                @if($feature['key'] === 'team_members_limit')
                                <span class="text-indigo-600 font-medium">{{ $feature['value'] }}</span>
                                @endif
                                @endforeach
                                @else
                                <span class="text-gray-400">-</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">5</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Monthly Replies</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                @if($billingData['active_subscription'])
                                @foreach($billingData['active_subscription']['features'] as $feature)
                                @if($feature['key'] === 'monthly_reply_limit')
                                <span class="text-indigo-600 font-medium">{{ $feature['value'] }}</span>
                                @endif
                                @endforeach
                                @else
                                <span class="text-gray-400">-</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">1,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">10,000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-6 flex justify-center space-x-4">
                <button onclick="closeFeatureModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Close
                </button>
                <button onclick="upgradeFromComparison()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                    Upgrade Plan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Management Quick Modal -->
<div id="subscriptionManagementModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div class="flex justify-between items-center border-b px-6 py-4">
            <h3 class="text-lg font-semibold text-gray-900">Subscription Management</h3>
            <button onclick="closeManagementModal()" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <a href="{{ route('billing.portal') }}" class="block w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-cog text-gray-600 mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">Billing Portal</div>
                            <div class="text-sm text-gray-600">Manage payment methods and billing info</div>
                        </div>
                    </div>
                </a>

                <button onclick="openUpgradeModal(); closeManagementModal();" class="block w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-up text-green-600 mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">Upgrade Plan</div>
                            <div class="text-sm text-gray-600">Get more features and higher limits</div>
                        </div>
                    </div>
                </button>

                <button onclick="showCancelConfirmation()" class="block w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-red-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-times-circle text-red-600 mr-3"></i>
                        <div>
                            <div class="font-medium text-red-900">Cancel Subscription</div>
                            <div class="text-sm text-red-600">End your subscription at period end</div>
                        </div>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Billing details page loaded');

        // Initialize interactive features
        initializeSubscriptionInteractions();
        initializeCountdown();
        initializeTooltips();
    });

    // Toggle subscription details visibility
    function initializeSubscriptionInteractions() {
        const toggleBtn = document.getElementById('toggleSubscriptionDetails');
        const detailsSection = document.getElementById('subscriptionDetails');
        const toggleIcon = document.getElementById('toggleIcon');

        if (toggleBtn && detailsSection) {
            // Initially collapsed
            detailsSection.style.maxHeight = '0px';
            detailsSection.style.opacity = '0';

            toggleBtn.addEventListener('click', function() {
                const isCollapsed = detailsSection.style.maxHeight === '0px';

                if (isCollapsed) {
                    // Expand
                    detailsSection.style.maxHeight = detailsSection.scrollHeight + 'px';
                    detailsSection.style.opacity = '1';
                    toggleIcon.style.transform = 'rotate(180deg)';
                } else {
                    // Collapse
                    detailsSection.style.maxHeight = '0px';
                    detailsSection.style.opacity = '0';
                    toggleIcon.style.transform = 'rotate(0deg)';
                }
            });
        }
    }

    // Real-time countdown for subscription expiry
    function initializeCountdown() {
        const daysElement = document.getElementById('daysRemaining');
        if (!daysElement) return;

        @if($billingData['active_subscription'])
        const expiryDate = new Date("{{ $billingData['active_subscription']['expiry_date'] }}");

        function updateCountdown() {
            const now = new Date();
            const timeDiff = expiryDate - now;
            const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

            if (daysRemaining > 0) {
                daysElement.textContent = Math.abs(daysRemaining);

                // Change color based on days remaining
                if (daysRemaining <= 7) {
                    daysElement.className = 'text-2xl font-bold text-red-600';
                } else if (daysRemaining <= 30) {
                    daysElement.className = 'text-2xl font-bold text-yellow-600';
                } else {
                    daysElement.className = 'text-2xl font-bold text-gray-900';
                }
            } else {
                daysElement.textContent = 'Expired';
                daysElement.className = 'text-2xl font-bold text-red-600';
            }
        }

        // Update immediately and then every hour
        updateCountdown();
        setInterval(updateCountdown, 3600000); // 1 hour
        @endif
    }

    // Initialize tooltips for interactive elements
    function initializeTooltips() {
        // Add hover effects and tooltips
        const featureItems = document.querySelectorAll('[onclick^="toggleFeatureDetails"]');
        featureItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(4px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0px)';
            });
        });
    }

    // Interactive function implementations
    function openUpgradeModal() {
        // Trigger the existing upgrade modal
        if (typeof window.upgradeModal !== 'undefined') {
            window.upgradeModal.show();
        } else {
            // Fallback to redirect
            window.location.href = '{{ route("business.subscriptions") }}';
        }
    }

    function manageSubscription() {
        // Show management modal instead of direct redirect
        const modal = document.getElementById('subscriptionManagementModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    function viewInvoices() {
        // Scroll to payment history section
        const paymentSection = document.querySelector('h2:contains("Payment History")');
        if (paymentSection) {
            paymentSection.scrollIntoView({
                behavior: 'smooth'
            });
        } else {
            // Fallback: show alert
            showNotification('Payment history is available below on this page.', 'info');
        }
    }

    function downloadReceipt() {
        @if($billingData['active_subscription'] && $billingData['active_subscription']['payment_info'])
        // In a real implementation, this would generate and download a receipt
        showNotification('Receipt download feature coming soon!', 'info');
        @else
        showNotification('No payment information available for receipt.', 'warning');
        @endif
    }

    function contactSupport() {
        // Open support contact modal or redirect
        showNotification('Support: <NAME_EMAIL> or call ******-REVIEW', 'info');
    }

    function compareFeatures() {
        // Open feature comparison modal
        const modal = document.getElementById('featureComparisonModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    // Modal management functions
    function closeFeatureModal() {
        const modal = document.getElementById('featureComparisonModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    function closeManagementModal() {
        const modal = document.getElementById('subscriptionManagementModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    function upgradeFromComparison() {
        closeFeatureModal();
        openUpgradeModal();
    }

    function showCancelConfirmation() {
        closeManagementModal();
        if (confirm('Are you sure you want to cancel your subscription? This action cannot be undone and you will lose access to all premium features at the end of your billing period.')) {
            showNotification('Subscription cancellation feature coming soon. Please contact support for now.', 'info');
        }
    }

    function renewSubscription() {
        // Redirect to subscription renewal
        window.location.href = '{{ route("business.subscriptions") }}?renew=true';
    }

    function toggleFeatureDetails(index) {
        // Show detailed information about a specific feature
        const features = @json($billingData['active_subscription']['features'] ?? []);
        if (features[index]) {
            const feature = features[index];
            showNotification(`${feature.title}: ${feature.value}`, 'info');
        }
    }

    function viewUsageDetails() {
        // Scroll to usage section
        const usageSection = document.querySelector('h2:contains("Current Usage")');
        if (usageSection) {
            usageSection.scrollIntoView({
                behavior: 'smooth'
            });
        }
    }

    // Utility function for notifications
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        // Set colors based on type
        switch (type) {
            case 'success':
                notification.className += ' bg-green-100 text-green-800 border border-green-200';
                break;
            case 'warning':
                notification.className += ' bg-yellow-100 text-yellow-800 border border-yellow-200';
                break;
            case 'error':
                notification.className += ' bg-red-100 text-red-800 border border-red-200';
                break;
            default:
                notification.className += ' bg-blue-100 text-blue-800 border border-blue-200';
        }

        notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-1">${message}</div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
</script>


<!-- Add some additional CSS for enhanced interactions -->
<style>
    .transition-all {
        transition: all 0.3s ease;
    }

    .group:hover .group-hover\:scale-105 {
        transform: scale(1.05);
    }

    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {

        0%,
        100% {
            opacity: 1;
        }

        50% {
            opacity: .5;
        }
    }

    /* Custom scrollbar for better UX */
    ::-webkit-scrollbar {
        width: 6px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Hover effects for interactive elements */
    .hover-lift:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Progress bar animation */
    .progress-bar {
        transition: width 1s ease-in-out;
    }

    /* Interactive subscription card animations */
    .subscription-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .subscription-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    /* Feature item hover effects */
    .feature-item {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .feature-item:hover {
        transform: translateX(4px);
        background-color: rgba(255, 255, 255, 0.8);
    }

    /* Quick action button effects */
    .quick-action-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .quick-action-btn:hover::before {
        left: 100%;
    }

    /* Modal animations */
    .modal-enter {
        animation: modalEnter 0.3s ease-out;
    }

    @keyframes modalEnter {
        from {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
        }

        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    /* Status indicator animations */
    .status-indicator {
        position: relative;
    }

    .status-indicator::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: inherit;
        transform: translate(-50%, -50%);
        animation: ripple 2s infinite;
        opacity: 0.3;
    }

    @keyframes ripple {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.3;
        }

        100% {
            transform: translate(-50%, -50%) scale(2);
            opacity: 0;
        }
    }

    /* Countdown number animation */
    .countdown-number {
        transition: all 0.3s ease;
    }

    .countdown-number.warning {
        animation: pulse-warning 2s infinite;
    }

    @keyframes pulse-warning {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }
</style>
@endpush
@endsection