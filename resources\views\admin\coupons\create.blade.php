@extends('admin.layouts.app')

@section('title', isset($coupon) ? 'Edit Coupon' : 'Create Coupon')

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">
                {{ isset($coupon) ? 'Edit Coupon' : 'Create New Coupon' }}
            </h1>
            <p class="text-gray-600 mt-1">
                {{ isset($coupon) ? 'Update coupon details and settings' : 'Create a new discount coupon for your customers' }}
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.coupons.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Coupons
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="{{ isset($coupon) ? route('admin.coupons.update', $coupon) : route('admin.coupons.store') }}" method="POST" class="p-6 space-y-6">
            @csrf
            @if(isset($coupon))
                @method('PUT')
            @endif
            @csrf

            <!-- Basic Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Coupon Code -->
                    <div>
                        <label for="coupon_code" class="block text-sm font-medium text-gray-700 mb-1">
                            Coupon Code <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="coupon_code" id="coupon_code" value="{{ old('coupon_code', $coupon->coupon_code ?? '') }}"
                               placeholder="e.g., SAVE20, WELCOME50" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('coupon_code') border-red-500 @enderror">
                        @error('coupon_code')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                            Display Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" 
                               placeholder="e.g., Welcome Discount, Holiday Sale" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                        @error('name')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea name="description" id="description" rows="3" placeholder="Optional description for internal use"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Discount Configuration -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Discount Configuration</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Discount Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
                            Discount Type <span class="text-red-500">*</span>
                        </label>
                        <select name="type" id="type" required onchange="updateDiscountFields()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('type') border-red-500 @enderror">
                            <option value="">Select Type</option>
                            <option value="percentage" {{ old('type') === 'percentage' ? 'selected' : '' }}>Percentage (%)</option>
                            <option value="fixed" {{ old('type') === 'fixed' ? 'selected' : '' }}>Fixed Amount (₹)</option>
                        </select>
                        @error('type')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Discount Value -->
                    <div>
                        <label for="value" class="block text-sm font-medium text-gray-700 mb-1">
                            Discount Value <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="number" name="value" id="value" value="{{ old('value') }}" 
                                   step="0.01" min="0" required placeholder="0.00"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('value') border-red-500 @enderror">
                            <div id="valueUnit" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm"></span>
                            </div>
                        </div>
                        @error('value')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Minimum Amount -->
                    <div>
                        <label for="minimum_amount" class="block text-sm font-medium text-gray-700 mb-1">Minimum Order Amount</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm">₹</span>
                            </span>
                            <input type="number" name="minimum_amount" id="minimum_amount" value="{{ old('minimum_amount') }}" 
                                   step="0.01" min="0" placeholder="0.00"
                                   class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('minimum_amount') border-red-500 @enderror">
                        </div>
                        @error('minimum_amount')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Leave empty for no minimum requirement</p>
                    </div>

                    <!-- Maximum Discount -->
                    <div id="maxDiscountField" style="display: none;">
                        <label for="maximum_discount" class="block text-sm font-medium text-gray-700 mb-1">Maximum Discount Amount</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm">₹</span>
                            </span>
                            <input type="number" name="maximum_discount" id="maximum_discount" value="{{ old('maximum_discount') }}" 
                                   step="0.01" min="0" placeholder="0.00"
                                   class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('maximum_discount') border-red-500 @enderror">
                        </div>
                        @error('maximum_discount')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">For percentage discounts only</p>
                    </div>
                </div>
            </div>

            <!-- Usage Limits -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Limits</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Total Usage Limit -->
                    <div>
                        <label for="usage_limit" class="block text-sm font-medium text-gray-700 mb-1">Total Usage Limit</label>
                        <input type="number" name="usage_limit" id="usage_limit" value="{{ old('usage_limit') }}" 
                               min="1" placeholder="Unlimited"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('usage_limit') border-red-500 @enderror">
                        @error('usage_limit')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Leave empty for unlimited usage</p>
                    </div>

                    <!-- Per User Usage Limit -->
                    <div>
                        <label for="usage_limit_per_user" class="block text-sm font-medium text-gray-700 mb-1">Usage Limit Per User</label>
                        <input type="number" name="usage_limit_per_user" id="usage_limit_per_user" value="{{ old('usage_limit_per_user') }}" 
                               min="1" placeholder="Unlimited"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('usage_limit_per_user') border-red-500 @enderror">
                        @error('usage_limit_per_user')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">How many times each user can use this coupon</p>
                    </div>
                </div>
            </div>

            <!-- Validity Period -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Validity Period</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Start Date -->
                    <div>
                        <label for="starts_at" class="block text-sm font-medium text-gray-700 mb-1">Start Date & Time</label>
                        <input type="datetime-local" name="starts_at" id="starts_at" value="{{ old('starts_at') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('starts_at') border-red-500 @enderror">
                        @error('starts_at')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Leave empty to start immediately</p>
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-1">Expiry Date & Time</label>
                        <input type="datetime-local" name="expires_at" id="expires_at" value="{{ old('expires_at') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('expires_at') border-red-500 @enderror">
                        @error('expires_at')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Leave empty for no expiry</p>
                    </div>
                </div>
            </div>

            <!-- Plan Restrictions -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Restrictions</h3>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Applicable Plans</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="allPlans" onchange="toggleAllPlans()" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">All Plans</span>
                        </label>
                        @foreach($plans as $plan)
                            <label class="flex items-center ml-6">
                                <input type="checkbox" name="applicable_plans[]" value="{{ $plan->id }}" 
                                       class="plan-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                       {{ in_array($plan->id, old('applicable_plans', [])) ? 'checked' : '' }}>
                                <span class="ml-2 text-sm text-gray-700">{{ $plan->name }} ({{ $plan->currency_symbol }}{{ number_format($plan->price, 2) }})</span>
                            </label>
                        @endforeach
                    </div>
                    @error('applicable_plans')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-2">Leave unchecked to apply to all plans</p>
                </div>
            </div>

            <!-- Status -->
            <div class="pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Status</h3>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Active (coupon can be used immediately)</span>
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.coupons.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium transition-colors text-center">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>Create Coupon
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateDiscountFields() {
    const type = document.getElementById('type').value;
    const valueUnit = document.getElementById('valueUnit').querySelector('span');
    const maxDiscountField = document.getElementById('maxDiscountField');
    
    if (type === 'percentage') {
        valueUnit.textContent = '%';
        maxDiscountField.style.display = 'block';
        document.getElementById('value').setAttribute('max', '100');
    } else if (type === 'fixed') {
        valueUnit.textContent = '₹';
        maxDiscountField.style.display = 'none';
        document.getElementById('value').removeAttribute('max');
    } else {
        valueUnit.textContent = '';
        maxDiscountField.style.display = 'none';
    }
}

function toggleAllPlans() {
    const allPlansCheckbox = document.getElementById('allPlans');
    const planCheckboxes = document.querySelectorAll('.plan-checkbox');
    
    planCheckboxes.forEach(checkbox => {
        checkbox.checked = allPlansCheckbox.checked;
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateDiscountFields();
    
    // Update "All Plans" checkbox when individual checkboxes change
    document.querySelectorAll('.plan-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allCheckboxes = document.querySelectorAll('.plan-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.plan-checkbox:checked');
            const allPlansCheckbox = document.getElementById('allPlans');
            
            if (checkedCheckboxes.length === allCheckboxes.length) {
                allPlansCheckbox.checked = true;
                allPlansCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length === 0) {
                allPlansCheckbox.checked = false;
                allPlansCheckbox.indeterminate = false;
            } else {
                allPlansCheckbox.checked = false;
                allPlansCheckbox.indeterminate = true;
            }
        });
    });
});
</script>
@endpush
