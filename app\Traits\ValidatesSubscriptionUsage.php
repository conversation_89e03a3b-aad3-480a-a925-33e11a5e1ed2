<?php

namespace App\Traits;

use App\Services\SubscriptionUsageTrigger;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

trait ValidatesSubscriptionUsage
{
    /**
     * Validate business connection before action
     */
    protected function validateBusinessConnectionUsage(): array
    {
        try {
            $userId = Auth::id();
            if (!$userId) {
                return ['allowed' => false, 'message' => 'User not authenticated'];
            }

            $trigger = app(SubscriptionUsageTrigger::class);
            return $trigger->triggerBusinessConnectionCheck($userId);
            
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Business connection validation failed', [
                'error' => $e->getMessage()
            ]);
            
            // Allow action if validation fails to prevent breaking existing functionality
            return ['allowed' => true, 'message' => 'Validation unavailable, proceeding'];
        }
    }

    /**
     * Validate reply sending before action
     */
    protected function validateReplySendUsage(): array
    {
        try {
            $userId = Auth::id();
            if (!$userId) {
                return ['allowed' => false, 'message' => 'User not authenticated'];
            }

            $trigger = app(SubscriptionUsageTrigger::class);
            return $trigger->triggerReplySendCheck($userId);
            
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Reply send validation failed', [
                'error' => $e->getMessage()
            ]);
            
            // Allow action if validation fails
            return ['allowed' => true, 'message' => 'Validation unavailable, proceeding'];
        }
    }

    /**
     * Validate team member invitation before action
     */
    protected function validateTeamMemberInviteUsage(int $businessId): array
    {
        try {
            $userId = Auth::id();
            if (!$userId) {
                return ['allowed' => false, 'message' => 'User not authenticated'];
            }

            $trigger = app(SubscriptionUsageTrigger::class);
            return $trigger->triggerTeamMemberInviteCheck($userId, $businessId);
            
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Team member invite validation failed', [
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            
            // Allow action if validation fails
            return ['allowed' => true, 'message' => 'Validation unavailable, proceeding'];
        }
    }

    /**
     * Validate feature access before action
     */
    protected function validateFeatureAccessUsage(string $feature): array
    {
        try {
            $userId = Auth::id();
            if (!$userId) {
                return ['allowed' => false, 'message' => 'User not authenticated'];
            }

            $trigger = app(SubscriptionUsageTrigger::class);
            return $trigger->triggerFeatureAccessCheck($userId, $feature);
            
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Feature access validation failed', [
                'feature' => $feature,
                'error' => $e->getMessage()
            ]);
            
            // Allow action if validation fails
            return ['allowed' => true, 'message' => 'Validation unavailable, proceeding'];
        }
    }

    /**
     * Get comprehensive usage status
     */
    protected function getComprehensiveUsageStatus(): array
    {
        try {
            $userId = Auth::id();
            if (!$userId) {
                return ['success' => false, 'has_alerts' => false, 'alerts' => []];
            }

            $trigger = app(SubscriptionUsageTrigger::class);
            return $trigger->triggerComprehensiveCheck($userId);
            
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Comprehensive usage check failed', [
                'error' => $e->getMessage()
            ]);
            
            return ['success' => false, 'has_alerts' => false, 'alerts' => []];
        }
    }

    /**
     * Handle subscription validation response for JSON requests
     */
    protected function handleSubscriptionValidationResponse(array $validation, string $action = 'action')
    {
        if (!$validation['allowed']) {
            return response()->json([
                'success' => false,
                'error' => 'Subscription limit exceeded',
                'message' => $validation['message'],
                'upgrade_required' => true,
                'current_count' => $validation['current_count'] ?? null,
                'limit' => $validation['limit'] ?? null,
                'metadata' => $validation['metadata'] ?? [],
                'action_type' => $action
            ], 403);
        }
        
        return null; // Continue with normal flow
    }

    /**
     * Handle subscription validation response for web requests
     */
    protected function handleSubscriptionValidationRedirect(array $validation, string $action = 'action')
    {
        if (!$validation['allowed']) {
            return redirect()->back()
                ->with('error', $validation['message'])
                ->with('upgrade_required', true)
                ->with('validation_data', $validation)
                ->with('action_type', $action);
        }
        
        return null; // Continue with normal flow
    }

    /**
     * Add usage validation to existing method without breaking it
     */
    protected function withUsageValidation(string $validationType, callable $originalMethod, array $params = [])
    {
        try {
            // Perform validation
            $validation = $this->performValidation($validationType, $params);
            
            // If validation fails, return appropriate response
            if (!$validation['allowed']) {
                if (request()->expectsJson()) {
                    return $this->handleSubscriptionValidationResponse($validation, $validationType);
                } else {
                    return $this->handleSubscriptionValidationRedirect($validation, $validationType);
                }
            }
            
            // If validation passes, execute original method
            return $originalMethod();
            
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Validation wrapper failed', [
                'validation_type' => $validationType,
                'error' => $e->getMessage()
            ]);
            
            // If validation fails, still execute original method to prevent breaking functionality
            return $originalMethod();
        }
    }

    /**
     * Perform specific validation based on type
     */
    private function performValidation(string $validationType, array $params = []): array
    {
        switch ($validationType) {
            case 'business_connection':
                return $this->validateBusinessConnectionUsage();
            
            case 'reply_send':
                return $this->validateReplySendUsage();
            
            case 'team_member_invite':
                $businessId = $params['business_id'] ?? 0;
                return $this->validateTeamMemberInviteUsage($businessId);
            
            case 'feature_access':
                $feature = $params['feature'] ?? '';
                return $this->validateFeatureAccessUsage($feature);
            
            default:
                return ['allowed' => true, 'message' => 'Unknown validation type'];
        }
    }

    /**
     * Check if user has any usage warnings (non-blocking)
     */
    protected function hasUsageWarnings(): bool
    {
        try {
            $status = $this->getComprehensiveUsageStatus();
            return $status['has_warnings'] ?? false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if user has any critical usage issues (blocking)
     */
    protected function hasCriticalUsageIssues(): bool
    {
        try {
            $status = $this->getComprehensiveUsageStatus();
            return $status['has_critical'] ?? false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get usage alerts for display
     */
    protected function getUsageAlerts(): array
    {
        try {
            $status = $this->getComprehensiveUsageStatus();
            return $status['alerts'] ?? [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Add usage data to view
     */
    protected function withUsageData(array $viewData = []): array
    {
        try {
            $usageStatus = $this->getComprehensiveUsageStatus();
            $viewData['usageStatus'] = $usageStatus;
            $viewData['hasUsageWarnings'] = $usageStatus['has_warnings'] ?? false;
            $viewData['hasCriticalUsage'] = $usageStatus['has_critical'] ?? false;
            $viewData['usageAlerts'] = $usageStatus['alerts'] ?? [];
            
            return $viewData;
        } catch (\Exception $e) {
            Log::error('ValidatesSubscriptionUsage: Failed to add usage data to view', [
                'error' => $e->getMessage()
            ]);
            
            return $viewData;
        }
    }

    /**
     * Validate and execute with automatic response handling
     */
    protected function validateAndExecute(string $validationType, callable $action, array $params = [])
    {
        $validation = $this->performValidation($validationType, $params);
        
        if (!$validation['allowed']) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Subscription limit exceeded',
                    'message' => $validation['message'],
                    'upgrade_required' => true,
                    'validation_data' => $validation
                ], 403);
            } else {
                return redirect()->back()
                    ->with('error', $validation['message'])
                    ->with('upgrade_required', true)
                    ->with('validation_data', $validation);
            }
        }
        
        return $action();
    }
}
