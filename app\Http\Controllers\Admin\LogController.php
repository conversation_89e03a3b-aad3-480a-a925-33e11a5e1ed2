<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExternalApiLog;
use App\Models\User;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LogController extends Controller
{
    /**
     * Display external API logs with filtering and pagination
     */
    public function index(Request $request)
    {
        $query = ExternalApiLog::with(['user', 'business'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('log_type')) {
            $query->where('log_type', $request->log_type);
        }

        if ($request->filled('status_code')) {
            $query->where('status_code', $request->status_code);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('endpoint', 'like', "%{$search}%")
                  ->orWhere('status_message', 'like', "%{$search}%")
                  ->orWhere('error_message', 'like', "%{$search}%");
            });
        }

        // Get statistics for dashboard
        $stats = $this->getLogStatistics($request);

        $logs = $query->paginate(20)->withQueryString();

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('admin.logs.index', compact('logs', 'stats', 'filterOptions'));
    }

    /**
     * Show detailed view of a specific log entry
     */
    public function show(ExternalApiLog $log)
    {
        $log->load(['user', 'business']);
        
        return view('admin.logs.show', compact('log'));
    }

    /**
     * Export logs based on current filters
     */
    public function export(Request $request)
    {
        $query = ExternalApiLog::with(['user', 'business'])
            ->orderBy('created_at', 'desc');

        // Apply same filters as index
        if ($request->filled('log_type')) {
            $query->where('log_type', $request->log_type);
        }

        if ($request->filled('status_code')) {
            $query->where('status_code', $request->status_code);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('endpoint', 'like', "%{$search}%")
                  ->orWhere('status_message', 'like', "%{$search}%")
                  ->orWhere('error_message', 'like', "%{$search}%");
            });
        }

        $logs = $query->limit(10000)->get(); // Limit export to 10k records

        $filename = 'api_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID', 'Log Type', 'Endpoint', 'Method', 'Status Code', 
                'Status Message', 'Duration (ms)', 'User Email', 'Business Name',
                'IP Address', 'Created At'
            ]);

            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->log_type,
                    $log->endpoint,
                    $log->method ?? 'GET',
                    $log->status_code,
                    $log->status_message,
                    $log->duration_ms,
                    $log->user->email ?? 'N/A',
                    $log->business->business_name ?? 'N/A',
                    $log->ip_address,
                    $log->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get log statistics for dashboard
     */
    private function getLogStatistics($request)
    {
        $baseQuery = ExternalApiLog::query();

        // Apply date filters if present
        if ($request->filled('date_from')) {
            $baseQuery->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $baseQuery->whereDate('created_at', '<=', $request->date_to);
        }

        return [
            'total_logs' => (clone $baseQuery)->count(),
            'success_logs' => (clone $baseQuery)->whereBetween('status_code', [200, 299])->count(),
            'error_logs' => (clone $baseQuery)->where('status_code', '>=', 400)->count(),
            'google_api_logs' => (clone $baseQuery)->where('log_type', 'google_api')->count(),
            'ai_logs' => (clone $baseQuery)->where('log_type', 'ai')->count(),
            'avg_duration' => (clone $baseQuery)->avg('duration_ms'),
            'logs_by_status' => (clone $baseQuery)
                ->select('status_code', DB::raw('count(*) as count'))
                ->groupBy('status_code')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'logs_by_type' => (clone $baseQuery)
                ->select('log_type', DB::raw('count(*) as count'))
                ->groupBy('log_type')
                ->get(),
        ];
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'log_types' => ExternalApiLog::select('log_type')
                ->distinct()
                ->orderBy('log_type')
                ->pluck('log_type'),
            'status_codes' => ExternalApiLog::select('status_code')
                ->distinct()
                ->whereNotNull('status_code')
                ->orderBy('status_code')
                ->pluck('status_code'),
            'recent_users' => User::select('id', 'name', 'email')
                ->whereIn('id', function ($query) {
                    $query->select('user_id')
                        ->from('external_api_logs')
                        ->whereNotNull('user_id')
                        ->distinct();
                })
                ->orderBy('name')
                ->get(),
            'recent_businesses' => Business::select('id', 'title')
                ->whereIn('id', function ($query) {
                    $query->select('business_id')
                        ->from('external_api_logs')
                        ->whereNotNull('business_id')
                        ->distinct();
                })
                ->orderBy('title')
                ->get(),
        ];
    }

    /**
     * Get logs analytics data for charts
     */
    public function analytics(Request $request)
    {
        $days = $request->input('days', 7);
        $startDate = Carbon::now()->subDays($days);

        $logsOverTime = ExternalApiLog::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 ELSE 0 END) as success'),
                DB::raw('SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as errors')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $errorsByEndpoint = ExternalApiLog::select(
                'endpoint',
                DB::raw('COUNT(*) as count')
            )
            ->where('status_code', '>=', 400)
            ->where('created_at', '>=', $startDate)
            ->groupBy('endpoint')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'logs_over_time' => $logsOverTime,
            'errors_by_endpoint' => $errorsByEndpoint,
        ]);
    }
}
