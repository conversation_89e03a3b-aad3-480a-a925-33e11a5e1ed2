@extends('admin.layouts.app')

@section('title', 'Create Plan')

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create New Plan</h1>
            <p class="text-gray-600 mt-1">Create a new subscription plan with features and pricing</p>
        </div>
        <div class="flex gap-3 mt-4 sm:mt-0">
            <a href="{{ route('admin.plans.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Plans
            </a>
        </div>
    </div>

    <!-- Create Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="{{ route('admin.plans.store') }}" method="POST" class="space-y-6">
            @csrf
            
            <div class="p-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="lg:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    </div>

                    <!-- Plan Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Plan Name *</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Currency -->
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Currency *</label>
                        <select name="currency" id="currency" required onchange="updateCurrencySymbol()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('currency') border-red-500 @enderror">
                            <option value="">Select Currency</option>
                            @foreach($currencies as $code => $info)
                                <option value="{{ $code }}" {{ old('currency') === $code ? 'selected' : '' }}>
                                    {{ $code }} ({{ $info['symbol'] }}) - {{ $info['name'] }}
                                </option>
                            @endforeach
                        </select>
                        @error('currency')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Short Description -->
                    <div class="lg:col-span-2">
                        <label for="short_description" class="block text-sm font-medium text-gray-700 mb-2">Short Description *</label>
                        <input type="text" name="short_description" id="short_description" value="{{ old('short_description') }}" required
                               placeholder="Brief description for plan cards"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('short_description') border-red-500 @enderror">
                        @error('short_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Long Description -->
                    <div class="lg:col-span-2">
                        <label for="long_description" class="block text-sm font-medium text-gray-700 mb-2">Long Description *</label>
                        <textarea name="long_description" id="long_description" rows="4" required
                                  placeholder="Detailed description for plan details page"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('long_description') border-red-500 @enderror">{{ old('long_description') }}</textarea>
                        @error('long_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="lg:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Information</h3>
                    </div>

                    <!-- Monthly Price -->
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Monthly Price *</label>
                        <div class="relative">
                            <span id="currency-symbol" class="absolute left-3 top-2 text-gray-500">₹</span>
                            <input type="number" name="price" id="price" value="{{ old('price') }}" step="0.01" min="0" required
                                   class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('price') border-red-500 @enderror">
                        </div>
                        @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Duration -->
                    <div>
                        <label for="duration_days" class="block text-sm font-medium text-gray-700 mb-2">Duration (Days) *</label>
                        <input type="number" name="duration_days" id="duration_days" value="{{ old('duration_days', 30) }}" min="1" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('duration_days') border-red-500 @enderror">
                        @error('duration_days')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Annual Discount -->
                    <div>
                        <label for="annual_discount_percentage" class="block text-sm font-medium text-gray-700 mb-2">Annual Discount (%)</label>
                        <input type="number" name="annual_discount_percentage" id="annual_discount_percentage"
                               value="{{ old('annual_discount_percentage') }}" min="0" max="100" onchange="calculateAnnualPrice()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('annual_discount_percentage') border-red-500 @enderror">
                        <p class="mt-1 text-xs text-gray-500">Leave empty for no annual discount</p>
                        @error('annual_discount_percentage')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tax Percentage -->
                    <div>
                        <label for="tax_percentage" class="block text-sm font-medium text-gray-700 mb-2">Tax Percentage (%)</label>
                        <input type="number" name="tax_percentage" id="tax_percentage" value="{{ old('tax_percentage') }}"
                               step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('tax_percentage') border-red-500 @enderror">
                        @error('tax_percentage')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Annual Price Preview -->
                    <div class="lg:col-span-2">
                        <div id="annual-price-preview" class="hidden p-4 bg-blue-50 rounded-lg">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Annual Pricing Preview</h4>
                            <div class="text-sm text-blue-800">
                                <div>Regular Annual Price: <span id="regular-annual-price">₹0.00</span></div>
                                <div>Discounted Annual Price: <span id="discounted-annual-price">₹0.00</span></div>
                                <div>Annual Savings: <span id="annual-savings">₹0.00</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Plan Features -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Plan Features</h3>
                        <button type="button" onclick="addFeature()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-plus mr-1"></i>Add Feature
                        </button>
                    </div>

                    <div id="features-container">
                        @if(old('features'))
                            @foreach(old('features') as $index => $feature)
                            <div class="feature-row grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-200 rounded-lg">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Feature Title *</label>
                                    <input type="text" name="features[{{ $index }}][title]" value="{{ $feature['title'] ?? '' }}" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Feature Key *</label>
                                    <input type="text" name="features[{{ $index }}][key]" value="{{ $feature['key'] ?? '' }}" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Feature Value *</label>
                                    <input type="text" name="features[{{ $index }}][value]" value="{{ $feature['value'] ?? '' }}" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div class="flex items-end">
                                    <div class="flex-1">
                                        <label class="flex items-center">
                                            <input type="checkbox" name="features[{{ $index }}][marketing_status]" value="1"
                                                   {{ isset($feature['marketing_status']) && $feature['marketing_status'] ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm text-gray-700">Show in marketing</span>
                                        </label>
                                    </div>
                                    <button type="button" onclick="removeFeature(this)" class="ml-2 text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach
                        @else
                            @foreach($defaultFeatures as $index => $feature)
                                <div class="feature-row grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-200 rounded-lg">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Feature Title *</label>
                                        <input type="text" name="features[{{ $index }}][title]" value="{{ $feature['title'] }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Feature Key *</label>
                                        <input type="text" name="features[{{ $index }}][key]" value="{{ $feature['key'] }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Feature Value *</label>
                                        <input type="text" name="features[{{ $index }}][value]" value="{{ $feature['value'] }}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div class="flex items-end">
                                        <div class="flex-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="features[{{ $index }}][marketing_status]" value="1"
                                                       {{ $feature['marketing_status'] ? 'checked' : '' }}
                                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-700">Show in marketing</span>
                                            </label>
                                        </div>
                                        <button type="button" onclick="removeFeature(this)" class="ml-2 text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>

                <!-- Plan Status -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Status</h3>
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <label for="is_active" class="ml-2 text-sm text-gray-700">Plan is active and available for subscription</label>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.plans.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg text-sm font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>Create Plan
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
let featureIndex = {{ old('features') ? count(old('features')) : count($defaultFeatures) }};

function updateCurrencySymbol() {
    const currency = document.getElementById('currency').value;
    const symbolElement = document.getElementById('currency-symbol');
    
    if (currency === 'USD') {
        symbolElement.textContent = '$';
    } else if (currency === 'INR') {
        symbolElement.textContent = '₹';
    } else {
        symbolElement.textContent = '₹';
    }
    
    calculateAnnualPrice();
}

function calculateAnnualPrice() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discount = parseFloat(document.getElementById('annual_discount_percentage').value) || 0;
    const currency = document.getElementById('currency').value;
    const symbol = currency === 'USD' ? '$' : '₹';
    
    if (price > 0 && discount > 0) {
        const regularAnnualPrice = price * 12;
        const discountAmount = (regularAnnualPrice * discount) / 100;
        const discountedAnnualPrice = regularAnnualPrice - discountAmount;
        
        document.getElementById('regular-annual-price').textContent = symbol + regularAnnualPrice.toFixed(2);
        document.getElementById('discounted-annual-price').textContent = symbol + discountedAnnualPrice.toFixed(2);
        document.getElementById('annual-savings').textContent = symbol + discountAmount.toFixed(2);
        document.getElementById('annual-price-preview').classList.remove('hidden');
    } else {
        document.getElementById('annual-price-preview').classList.add('hidden');
    }
}

function addFeature() {
    const container = document.getElementById('features-container');
    const featureRow = document.createElement('div');
    featureRow.className = 'feature-row grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-200 rounded-lg';
    featureRow.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Feature Title *</label>
            <input type="text" name="features[${featureIndex}][title]" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Feature Key *</label>
            <input type="text" name="features[${featureIndex}][key]" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Feature Value *</label>
            <input type="text" name="features[${featureIndex}][value]" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="flex items-end">
            <div class="flex-1">
                <label class="flex items-center">
                    <input type="checkbox" name="features[${featureIndex}][marketing_status]" value="1" checked
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-700">Show in marketing</span>
                </label>
            </div>
            <button type="button" onclick="removeFeature(this)" class="ml-2 text-red-600 hover:text-red-800">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(featureRow);
    featureIndex++;
}

function removeFeature(button) {
    button.closest('.feature-row').remove();
}

// Initialize currency symbol and calculations
document.addEventListener('DOMContentLoaded', function() {
    updateCurrencySymbol();
    
    // Add event listeners for price calculation
    document.getElementById('price').addEventListener('input', calculateAnnualPrice);
    document.getElementById('annual_discount_percentage').addEventListener('input', calculateAnnualPrice);
});
</script>
@endpush
