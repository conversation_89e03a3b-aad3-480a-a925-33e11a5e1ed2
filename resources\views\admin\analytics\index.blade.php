@extends('admin.layouts.app')

@section('title', 'Analytics & Reports')

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
            <p class="text-gray-600 mt-1">Comprehensive business insights and performance metrics</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-0">
            <a href="{{ route('admin.analytics.export') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-download mr-2"></i>Export Report
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
            <form method="GET" action="{{ route('admin.analytics') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Date Range -->
                <div>
                    <label for="date_range" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <select name="date_range" id="date_range" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="7_days" {{ $dateRange === '7_days' ? 'selected' : '' }}>Last 7 Days</option>
                        <option value="30_days" {{ $dateRange === '30_days' ? 'selected' : '' }}>Last 30 Days</option>
                        <option value="90_days" {{ $dateRange === '90_days' ? 'selected' : '' }}>Last 90 Days</option>
                        <option value="1_year" {{ $dateRange === '1_year' ? 'selected' : '' }}>Last Year</option>
                    </select>
                </div>

                <!-- Currency Filter -->
                <div>
                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                    <select name="currency" id="currency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all" {{ $currency === 'all' ? 'selected' : '' }}>All Currencies</option>
                        <option value="INR" {{ $currency === 'INR' ? 'selected' : '' }}>INR</option>
                        <option value="USD" {{ $currency === 'USD' ? 'selected' : '' }}>USD</option>
                    </select>
                </div>

                <!-- Plan Filter -->
                <div>
                    <label for="plan" class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                    <select name="plan" id="plan" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all" {{ $plan === 'all' ? 'selected' : '' }}>All Plans</option>
                        @foreach($filterOptions['plans'] as $planOption)
                            <option value="{{ $planOption->id }}" {{ $plan == $planOption->id ? 'selected' : '' }}>
                                {{ $planOption->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Filter Actions -->
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-search mr-1"></i>Apply Filters
                    </button>
                    <a href="{{ route('admin.analytics') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-times mr-1"></i>Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Revenue Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Revenue Overview -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Revenue Overview</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">
                            ${{ number_format($analytics['revenue_analytics']['total_revenue'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600">Total Revenue</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">
                            ${{ number_format($analytics['revenue_analytics']['average_revenue'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600">Average Revenue</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">
                            {{ number_format($analytics['revenue_analytics']['total_transactions']) }}
                        </div>
                        <div class="text-sm text-gray-600">Total Transactions</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">
                            {{ number_format($analytics['revenue_analytics']['growth_rate'], 1) }}%
                        </div>
                        <div class="text-sm text-gray-600">Growth Rate</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Chart -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Revenue Trends</h3>
            </div>
            <div class="p-6">
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- User Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- User Metrics -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">User Metrics</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">
                            {{ number_format($analytics['user_analytics']['total_users']) }}
                        </div>
                        <div class="text-sm text-gray-600">Total Users</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">
                            {{ number_format($analytics['user_analytics']['new_users']) }}
                        </div>
                        <div class="text-sm text-gray-600">New Users</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">
                            {{ number_format($analytics['user_analytics']['active_users']) }}
                        </div>
                        <div class="text-sm text-gray-600">Active Users</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">
                            {{ number_format($analytics['user_analytics']['conversion_rate'], 1) }}%
                        </div>
                        <div class="text-sm text-gray-600">Conversion Rate</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Growth Chart -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">User Growth</h3>
            </div>
            <div class="p-6">
                <canvas id="userChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Subscription Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Subscription Metrics -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Subscription Metrics</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">
                            {{ number_format($analytics['subscription_analytics']['total_subscriptions']) }}
                        </div>
                        <div class="text-sm text-gray-600">Total Subscriptions</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">
                            {{ number_format($analytics['subscription_analytics']['active_subscriptions']) }}
                        </div>
                        <div class="text-sm text-gray-600">Active Subscriptions</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">
                            {{ number_format($analytics['subscription_analytics']['cancelled_subscriptions']) }}
                        </div>
                        <div class="text-sm text-gray-600">Cancelled</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600">
                            {{ number_format($analytics['subscription_analytics']['churn_rate'], 1) }}%
                        </div>
                        <div class="text-sm text-gray-600">Churn Rate</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Distribution -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Plan Distribution</h3>
            </div>
            <div class="p-6">
                <canvas id="planChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Business Analytics -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Business Analytics</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ number_format($analytics['business_analytics']['total_businesses']) }}
                    </div>
                    <div class="text-sm text-gray-600">Total Businesses</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ number_format($analytics['business_analytics']['active_businesses']) }}
                    </div>
                    <div class="text-sm text-gray-600">Active Businesses</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ number_format($analytics['business_analytics']['total_reviews']) }}
                    </div>
                    <div class="text-sm text-gray-600">Total Reviews</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">
                        {{ number_format($analytics['business_analytics']['average_rating'], 1) }}
                    </div>
                    <div class="text-sm text-gray-600">Average Rating</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Performance Metrics</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metric</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Period</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Previous Period</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($analytics['performance_metrics'] as $metric)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $metric['name'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $metric['current'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $metric['previous'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    @php
                                        $changeClass = $metric['change'] >= 0 ? 'text-green-600' : 'text-red-600';
                                        $changeIcon = $metric['change'] >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                                    @endphp
                                    <span class="{{ $changeClass }}">
                                        <i class="fas {{ $changeIcon }} mr-1"></i>
                                        {{ abs($metric['change']) }}%
                                    </span>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($analytics['revenue_analytics']['chart_labels']) !!},
        datasets: [{
            label: 'Revenue',
            data: {!! json_encode($analytics['revenue_analytics']['chart_data']) !!},
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// User Chart
const userCtx = document.getElementById('userChart').getContext('2d');
new Chart(userCtx, {
    type: 'bar',
    data: {
        labels: {!! json_encode($analytics['user_analytics']['chart_labels']) !!},
        datasets: [{
            label: 'New Users',
            data: {!! json_encode($analytics['user_analytics']['chart_data']) !!},
            backgroundColor: 'rgba(59, 130, 246, 0.8)',
            borderColor: 'rgb(59, 130, 246)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Plan Distribution Chart
const planCtx = document.getElementById('planChart').getContext('2d');
new Chart(planCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($analytics['subscription_analytics']['plan_labels']) !!},
        datasets: [{
            data: {!! json_encode($analytics['subscription_analytics']['plan_data']) !!},
            backgroundColor: [
                'rgba(59, 130, 246, 0.8)',
                'rgba(34, 197, 94, 0.8)',
                'rgba(251, 191, 36, 0.8)',
                'rgba(239, 68, 68, 0.8)',
                'rgba(168, 85, 247, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
@endpush
