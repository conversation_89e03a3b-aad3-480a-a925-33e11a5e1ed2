<?php

namespace App\Http\Controllers;

use App\Models\BusinessActivityLog;
use App\Models\Business;
use App\Models\Subscription;
use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ActivityDashboardController extends Controller
{
    /**
     * Display the activity dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get user's businesses
        $businesses = Business::where('user_id', $user->id)->get();

        // Get selected business or default to first
        $selectedBusinessId = $request->input('business_id', $businesses->first()?->id);
        $selectedBusiness = $businesses->find($selectedBusinessId);

        // Get date range filter
        $dateRange = $request->input('date_range', '30_days');
        $startDate = $this->getStartDateFromRange($dateRange);
        $endDate = Carbon::now();

        // Get activity logs
        $activities = $this->getActivityLogs($user->id, $selectedBusinessId, $startDate, $endDate);

        // Get activity statistics
        $stats = $this->getActivityStats($user->id, $selectedBusinessId, $startDate, $endDate);

        // Get team analytics
        $teamAnalytics = $this->getTeamAnalytics($user->id, $selectedBusinessId, $startDate, $endDate);

        // Get subscription usage
        $subscription = Subscription::where('user_id', $user->id)
            ->where('status', 'ACTIVE')
            ->first();

        $usageStats = $this->getUsageStats($user->id, $subscription);

        // Get team member validation
        $teamValidation = $this->getTeamMemberValidation($user->id, $subscription);
        $connectedBusinesses = Business::where('user_id', $user->id)->get();
        $teamMembers = TeamMember::where('user_id', $user->id)
            ->with(['business', 'invitedBy', 'permissions'])
            ->get();
        $business = Business::where(['user_id' => $user->id, 'status' => 'active'])->with('setting')->first();

        return view('activity-dashboard', compact(
            'businesses',
            'selectedBusiness',
            'activities',
            'stats',
            'teamAnalytics',
            'usageStats',
            'teamValidation',
            'dateRange',
            'connectedBusinesses',
            'teamMembers',
            'business'
        ));
    }

    /**
     * Get activity logs for display
     */
    private function getActivityLogs(int $userId, ?int $businessId, Carbon $startDate, Carbon $endDate)
    {
        $query = BusinessActivityLog::forUser($userId)
            ->with(['business', 'performedByUser'])
            ->inDateRange($startDate, $endDate)
            ->orderBy('created_at', 'desc');

        if ($businessId) {
            $query->forBusiness($businessId);
        }

        return $query->paginate(20);
    }

    /**
     * Get activity statistics
     */
    private function getActivityStats(int $userId, ?int $businessId, Carbon $startDate, Carbon $endDate)
    {
        $query = BusinessActivityLog::forUser($userId)
            ->successful()
            ->inDateRange($startDate, $endDate);

        if ($businessId) {
            $query->forBusiness($businessId);
        }

        $totalActivities = $query->count();

        // Activity breakdown by type
        $activityBreakdown = $query->selectRaw('activity_type, COUNT(*) as count')
            ->groupBy('activity_type')
            ->pluck('count', 'activity_type')
            ->toArray();

        // Activity breakdown by category
        $categoryBreakdown = $query->selectRaw('activity_category, COUNT(*) as count')
            ->groupBy('activity_category')
            ->pluck('count', 'activity_category')
            ->toArray();

        // Activity breakdown by performer
        $performerBreakdown = $query->selectRaw('performed_by_type, COUNT(*) as count')
            ->groupBy('performed_by_type')
            ->pluck('count', 'performed_by_type')
            ->toArray();

        // Daily activity trend
        $dailyTrend = $query->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();

        return [
            'total_activities' => $totalActivities,
            'activity_breakdown' => $activityBreakdown,
            'category_breakdown' => $categoryBreakdown,
            'performer_breakdown' => $performerBreakdown,
            'daily_trend' => $dailyTrend
        ];
    }

    /**
     * Get subscription usage statistics
     */
    private function getUsageStats(int $userId, ?Subscription $subscription)
    {
        if (!$subscription) {
            return [
                'business_connections' => ['current' => 0, 'limit' => 0, 'percentage' => 0],
                'monthly_replies' => ['current' => 0, 'limit' => 0, 'percentage' => 0],
                'features' => []
            ];
        }

        // Business connections
        $businessCount = Business::where('user_id', $userId)->count();
        $businessLimit = $subscription->plan->getBusinessConnectionsLimit();

        // Monthly replies
        $replyCount = BusinessActivityLog::forUser($userId)
            ->byActivityType('send_reply')
            ->thisMonth()
            ->successful()
            ->countsTowardLimit()
            ->sum('activity_count');

        $replyLimit = $subscription->plan->getMonthlyReplyLimit();
        $hasUnlimitedReplies = $subscription->plan->hasUnlimitedReplies();

        return [
            'business_connections' => [
                'current' => $businessCount,
                'limit' => $businessLimit,
                'percentage' => $businessLimit > 0 ? ($businessCount / $businessLimit) * 100 : 0
            ],
            'monthly_replies' => [
                'current' => $replyCount,
                'limit' => $hasUnlimitedReplies ? 'Unlimited' : $replyLimit,
                'percentage' => $hasUnlimitedReplies ? 0 : ($replyLimit > 0 ? ($replyCount / $replyLimit) * 100 : 0),
                'unlimited' => $hasUnlimitedReplies
            ],
            'features' => $subscription->features ?? []
        ];
    }

    /**
     * Get start date from range string
     */
    private function getStartDateFromRange(string $range): Carbon
    {
        return match ($range) {
            '7_days' => Carbon::now()->subDays(7),
            '30_days' => Carbon::now()->subDays(30),
            '90_days' => Carbon::now()->subDays(90),
            '1_year' => Carbon::now()->subYear(),
            'all_time' => Carbon::now()->subYears(10),
            default => Carbon::now()->subDays(30)
        };
    }

    /**
     * Export activity data
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $businessId = $request->input('business_id');
        $dateRange = $request->input('date_range', '30_days');
        $format = $request->input('format', 'csv');

        $startDate = $this->getStartDateFromRange($dateRange);
        $endDate = Carbon::now();

        $query = BusinessActivityLog::forUser($user->id)
            ->with(['business', 'performedByUser'])
            ->inDateRange($startDate, $endDate)
            ->orderBy('created_at', 'desc');

        if ($businessId) {
            $query->forBusiness($businessId);
        }

        $activities = $query->get();

        if ($format === 'csv') {
            return $this->exportToCsv($activities);
        }

        return response()->json(['error' => 'Unsupported format'], 400);
    }

    /**
     * Export activities to CSV
     */
    private function exportToCsv($activities)
    {
        $filename = 'business_activities_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($activities) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Date',
                'Business',
                'Activity Type',
                'Category',
                'Description',
                'Performed By',
                'Status',
                'Count'
            ]);

            // CSV data
            foreach ($activities as $activity) {
                fputcsv($file, [
                    $activity->created_at->format('Y-m-d H:i:s'),
                    $activity->business?->title ?? 'N/A',
                    $activity->getActivityTypeDisplayAttribute(),
                    $activity->getActivityCategoryDisplayAttribute(),
                    $activity->activity_description,
                    $activity->getPerformedByDisplayNameAttribute(),
                    ucfirst($activity->status),
                    $activity->activity_count
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get team analytics
     */
    private function getTeamAnalytics(int $userId, ?int $businessId, Carbon $startDate, Carbon $endDate)
    {
        // Get team members for user's businesses
        $teamMembersQuery = TeamMember::whereHas('business', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });

        if ($businessId) {
            $teamMembersQuery->where('business_id', $businessId);
        }

        $teamMembers = $teamMembersQuery->with(['user', 'business'])->get();

        // Get team member activities
        $teamActivityQuery = BusinessActivityLog::forUser($userId)
            ->inDateRange($startDate, $endDate)
            ->successful();

        if ($businessId) {
            $teamActivityQuery->forBusiness($businessId);
        }

        $teamActivities = $teamActivityQuery->with('performedByUser')
            ->get()
            ->groupBy('performed_by_user_id');

        // Calculate team member statistics
        $teamStats = [];
        $totalActivities = 0;

        // Add owner statistics
        $owner = User::find($userId);
        $ownerActivities = $teamActivities->get($userId, collect());
        $ownerActivityCount = $ownerActivities->count();
        $totalActivities += $ownerActivityCount;

        $teamStats[] = [
            'user_id' => $userId,
            'name' => $owner->name,
            'email' => $owner->email,
            'role' => 'owner',
            'status' => 'active',
            'activity_count' => $ownerActivityCount,
            'activity_breakdown' => $ownerActivities->groupBy('activity_type')->map->count()->toArray(),
            'last_activity' => $ownerActivities->max('created_at'),
            'is_owner' => true
        ];

        // Add team member statistics
        foreach ($teamMembers as $member) {
            if ($member->user) {
                $memberActivities = $teamActivities->get($member->user_id, collect());
                $memberActivityCount = $memberActivities->count();
                $totalActivities += $memberActivityCount;

                $teamStats[] = [
                    'user_id' => $member->user_id,
                    'name' => $member->user->name,
                    'email' => $member->user->email,
                    'role' => $member->role,
                    'status' => $member->status,
                    'activity_count' => $memberActivityCount,
                    'activity_breakdown' => $memberActivities->groupBy('activity_type')->map->count()->toArray(),
                    'last_activity' => $memberActivities->max('created_at'),
                    'is_owner' => false,
                    'invited_at' => $member->created_at,
                    'business_name' => $member->business->title ?? 'Unknown'
                ];
            }
        }

        // Sort by activity count
        usort($teamStats, function ($a, $b) {
            return $b['activity_count'] <=> $a['activity_count'];
        });

        // Calculate team performance metrics
        $activeMembers = collect($teamStats)->where('status', 'active')->count();
        $avgActivitiesPerMember = $activeMembers > 0 ? $totalActivities / $activeMembers : 0;

        // Get most active team member
        $mostActiveMember = collect($teamStats)->where('activity_count', '>', 0)->first();

        // Get activity distribution by role
        $roleDistribution = collect($teamStats)->groupBy('role')->map(function ($members) {
            return [
                'count' => $members->count(),
                'total_activities' => $members->sum('activity_count'),
                'avg_activities' => $members->avg('activity_count')
            ];
        })->toArray();

        return [
            'team_members' => $teamStats,
            'summary' => [
                'total_members' => count($teamStats),
                'active_members' => $activeMembers,
                'total_activities' => $totalActivities,
                'avg_activities_per_member' => round($avgActivitiesPerMember, 1),
                'most_active_member' => $mostActiveMember,
                'role_distribution' => $roleDistribution
            ],
            'activity_timeline' => $this->getTeamActivityTimeline($userId, $businessId, $startDate, $endDate)
        ];
    }

    /**
     * Get team activity timeline
     */
    private function getTeamActivityTimeline(int $userId, ?int $businessId, Carbon $startDate, Carbon $endDate)
    {
        $query = BusinessActivityLog::forUser($userId)
            ->inDateRange($startDate, $endDate)
            ->successful()
            ->with('performedByUser');

        if ($businessId) {
            $query->forBusiness($businessId);
        }

        $activities = $query->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return $activities->map(function ($activity) {
            return [
                'date' => $activity->created_at->format('Y-m-d H:i:s'),
                'activity_type' => $activity->activity_type,
                'description' => $activity->activity_description,
                'performed_by' => $activity->performedByUser ? $activity->performedByUser->name : 'System',
                'performed_by_type' => $activity->performed_by_type
            ];
        })->toArray();
    }

    /**
     * Get team member validation based on subscription
     */
    private function getTeamMemberValidation(int $userId, ?Subscription $subscription)
    {
        if (!$subscription) {
            return [
                'can_invite' => false,
                'current_count' => 0,
                'limit' => 0,
                'remaining' => 0,
                'percentage' => 0,
                'message' => 'No active subscription found. Subscribe to a plan to invite team members.',
                'upgrade_required' => true
            ];
        }

        // Get current team member count (including owner)
        $currentCount = TeamMember::whereHas('business', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('status', 'active')->count() + 1; // +1 for owner

        $limit = $subscription->plan->getTeamMembersLimit();
        $remaining = max(0, $limit - $currentCount);
        $percentage = $limit > 0 ? ($currentCount / $limit) * 100 : 0;
        $canInvite = $currentCount < $limit;

        $message = '';
        $upgradeRequired = false;

        if (!$canInvite) {
            $message = "You have reached your team member limit of {$limit}. Upgrade your plan to invite more members.";
            $upgradeRequired = true;
        } elseif ($percentage >= 80) {
            $message = "You have {$remaining} team member slot(s) remaining. Consider upgrading soon.";
        } else {
            $message = "You can invite {$remaining} more team member(s).";
        }

        return [
            'can_invite' => $canInvite,
            'current_count' => $currentCount,
            'limit' => $limit,
            'remaining' => $remaining,
            'percentage' => round($percentage, 1),
            'message' => $message,
            'upgrade_required' => $upgradeRequired,
            'status' => $this->getUsageStatus($percentage)
        ];
    }

    /**
     * Get usage status based on percentage
     */
    private function getUsageStatus(float $percentage): string
    {
        if ($percentage >= 100) return 'exceeded';
        if ($percentage >= 90) return 'critical';
        if ($percentage >= 80) return 'warning';
        if ($percentage >= 60) return 'moderate';
        return 'normal';
    }
}
