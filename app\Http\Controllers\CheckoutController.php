<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Coupon;
use App\Models\CouponUsageLog;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\TeamMember;
use App\Services\SubscriptionDisplayService;
use App\Services\IpLocationService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Checkout\Session as StripeSession;
use Stripe\Customer as StripeCustomer;
use Razorpay\Api\Api as RazorpayApi;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;

class CheckoutController extends Controller
{
    /**
     * Show the checkout page with plan selection
     */
    public function index(Request $request, IpLocationService $ipLocationService, SubscriptionService $subscriptionService)
    {
        $user = Auth::user();

        $teamMember = TeamMember::where('user_id', $user->id)->first();
        if ($teamMember && $teamMember->business && $teamMember->invitedBy) {
            // This is an invited team member with an active business
            $googleTokens = getGoogleTokens($teamMember->invitedBy->id);

            if (!$googleTokens) {
                // Handle the case where tokens can't be retrieved
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect()->route('login')->with('error', 'Unable to access business information. Please contact the business owner.');
            }

            Session::put('businessId', $teamMember->business->id);
            Session::put('business_id', $teamMember->invitedBy->google_id);
            Session::put('selected_location', $teamMember->business->location_name);
            Session::put('business_google_token', $googleTokens->business_google_token);
            Session::put('business_google_refresh_token', $googleTokens->business_refresh_token);
            // Mark as team member in session
            Session::put('is_team_member', true);
            Session::put('business_owner_id', $teamMember->invitedBy->id);
            return redirect()->route('business.dashboard', ['fresh' => 'true']);
        }

        // Detect user's currency based on IP location
        $currency = $this->detectUserCurrency($request, $ipLocationService);

        // Get current user's subscription to determine if this is an upgrade
        $currentSubscription = $subscriptionService->getActiveSubscription($user->id);
        $isUpgrade = $currentSubscription !== null;

        // Get filtered plans based on currency and upgrade status
        $plans = $this->getFilteredPlans($currency, $currentSubscription, $isUpgrade);

        // Auto-select appropriate plan
        $selectedPlanId = $this->getDefaultSelectedPlan($request, $plans, $currentSubscription, $isUpgrade);

        return view('checkout.index', compact('plans', 'selectedPlanId', 'currency', 'isUpgrade', 'currentSubscription'));
    }

    /**
     * Validate and apply coupon code
     */
    public function validateCoupon(Request $request)
    {
        $request->validate([
            'coupon_code' => 'required|string|max:50',
            'plan_id' => 'required|exists:plans,id',
            'amount' => 'nullable|numeric|min:0',
            'billing_cycle' => 'nullable|in:monthly,annual',
        ]);

        $coupon = Coupon::where('coupon_code', strtoupper($request->coupon_code))->first();

        if (!$coupon) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid coupon code.'
            ], 422);
        }

        $plan = Plan::findOrFail($request->plan_id);
        $userId = Auth::id();
        $isAnnual = $request->billing_cycle === 'annual';
        $amount = $request->amount ?? $plan->getEffectivePrice($isAnnual);

        $validation = $coupon->isValid($userId, $plan->id, $amount);

        if (!$validation['valid']) {
            return response()->json($validation, 422);
        }

        $discountAmount = $coupon->calculateDiscount($amount);
        $amountAfterDiscount = max(0, $amount - $discountAmount);

        // Calculate tax on the amount after discount
        $taxAmount = 0;
        if ($plan->tax_percentage && $plan->tax_percentage > 0) {
            $taxAmount = ($amountAfterDiscount * $plan->tax_percentage) / 100;
        }

        $finalAmount = $amountAfterDiscount + $taxAmount;

        return response()->json([
            'valid' => true,
            'coupon' => [
                'id' => $coupon->id,
                'code' => $coupon->coupon_code,
                'name' => $coupon->name,
                'type' => $coupon->type,
                'value' => $coupon->value,
                'discount_display' => $coupon->discount_display,
            ],
            'pricing' => [
                'original_amount' => $amount,
                'discount_amount' => $discountAmount,
                'tax_amount' => $taxAmount,
                'final_amount' => $finalAmount,
                'currency' => $plan->currency,
                'currency_symbol' => $plan->currency_symbol,
            ],
            'message' => 'Coupon applied successfully!'
        ]);
    }

    /**
     * Process checkout and create payment
     */
    public function processCheckout(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
            'coupon_code' => 'nullable|string|max:50',
            'billing_cycle' => 'nullable|in:monthly,annual',
        ]);

        $user = Auth::user();
        $plan = Plan::findOrFail($request->plan_id);
        $isAnnual = $request->billing_cycle === 'annual';
        $originalAmount = $plan->getEffectivePrice($isAnnual);
        $finalAmount = $originalAmount;
        $discountAmount = 0;
        $taxAmount = 0;
        $coupon = null;
        $couponUsageLog = null;


        DB::beginTransaction();

        try {
            // Apply coupon if provided
            if ($request->coupon_code) {
                $coupon = Coupon::where('coupon_code', strtoupper($request->coupon_code))->first();

                if (!$coupon) {
                    return back()->withErrors(['coupon_code' => 'Invalid coupon code.']);
                }

                $couponResult = $coupon->apply($user->id, $originalAmount, $plan->id);

                if (!$couponResult['valid']) {
                    return back()->withErrors(['coupon_code' => $couponResult['message']]);
                }

                $discountAmount = $couponResult['discount_amount'];
                $finalAmount = $couponResult['final_amount'];

                // Get the usage log that was just created
                $couponUsageLog = CouponUsageLog::where('coupon_id', $coupon->id)
                    ->where('user_id', $user->id)
                    ->latest()
                    ->first();
            }

            // Calculate tax on the amount after discount
            $amountAfterDiscount = $finalAmount;
            if ($plan->tax_percentage && $plan->tax_percentage > 0) {
                $taxAmount = ($amountAfterDiscount * $plan->tax_percentage) / 100;
                $finalAmount = $amountAfterDiscount + $taxAmount;
            }

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'payment_type' => $coupon && $finalAmount == 0 ? 'COUPON_CODE' : 'ONE_TIME_PAYMENT',
                'gateway_name' => $finalAmount == 0 ? 'NONE' : 'STRIPE',
                'amount' => $finalAmount,
                'original_amount' => $originalAmount,
                'discount_amount' => $discountAmount,
                'tax_amount' => $taxAmount,
                'coupon_code' => $coupon ? $coupon->coupon_code : null,
                'currency' => $plan->currency,
                'payment_status' => $finalAmount == 0 ? 'SUCCESS' : 'PENDING',
                'billing_cycle' => $isAnnual ? 'annual' : 'monthly',
                'payment_date' => now(),
                'metadata' => [
                    'billing_cycle' => $isAnnual ? 'annual' : 'monthly',
                    'annual_discount_applied' => $isAnnual && $plan->annual_discount_percentage > 0,
                    'annual_discount_percentage' => $isAnnual ? $plan->annual_discount_percentage : 0,
                    'tax_percentage' => $plan->tax_percentage ?? 0,
                    'amount_before_tax' => $amountAfterDiscount,
                    'plan_name' => $plan->name,
                    'plan_features' => $plan->features
                ]
            ]);
            // Update coupon usage log with payment ID
            if ($couponUsageLog) {
                $couponUsageLog->update(['payment_id' => $payment->id]);
            }

            // If final amount is 0 (free with coupon), create subscription immediately
            if ($finalAmount == 0) {
                // Calculate expiry date based on billing cycle
                $durationDays = $isAnnual ? 365 : $plan->duration_days;

                // Check if user already has an active subscription to prevent duplicates
                $existingSubscription = Subscription::where('user_id', $user->id)
                    ->where('status', 'ACTIVE')
                    ->where('expiry_date', '>=', now())
                    ->where('plan_id', $plan->id)
                    ->where('created_at', '>=', Carbon::now()->subHour())
                    ->first();

                if (!$existingSubscription) {
                    Subscription::where('user_id', $user->id)
                        ->where('status', 'ACTIVE')
                        ->where('expiry_date', '>=', now())
                        ->update([
                            'status' => 'CANCELLED'
                        ]);

                    $subscription = Subscription::create([
                        'user_id' => $user->id,
                        'plan_id' => $plan->id,
                        'payment_id' => $payment->id,
                        'status' => 'ACTIVE',
                        'start_date' => now(),
                        'expiry_date' => now()->addDays($durationDays),
                        'features' => $plan->features, // Copy plan features to subscription
                    ]);
                } else {
                    // Use existing subscription
                    $subscription = $existingSubscription;
                }

                if ($couponUsageLog) {
                    $couponUsageLog->update([
                        'subscription_id' => $subscription->id,
                        'status' => 'used'
                    ]);
                }

                DB::commit();
                return redirect()->route('payment.success', [
                    'payment_id' => $payment->id,
                    'plan_id' => $plan->id,
                    'business_id' => 1
                ])->with('success', 'Subscription activated successfully!');
            }

            // Process payment through Stripe (default gateway)
            $result = $this->processStripePayment($payment, $plan, $user);
            DB::commit();

            Cache::forget("upgrade_modal_content_{$user->id}");
            return $result;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Checkout process failed: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Payment processing failed. Please try again.']);
        }
    }

    /**
     * Process Stripe payment
     */
    private function processStripePayment($payment, $plan, $user)
    {
        \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

        // Create or get Stripe customer
        if (!$user->stripe_customer_id) {
            $customer = StripeCustomer::create([
                'email' => $user->email,
                'name' => $user->name,
            ]);
            $user->update(['stripe_customer_id' => $customer->id]);
        } else {
            $customer = StripeCustomer::retrieve($user->stripe_customer_id);
        }

        $successUrl = route('payment.success') . '?session_id={CHECKOUT_SESSION_ID}&payment_id=' . $payment->id . '&plan_id=' . $plan->id . '&business_id=1';

        $session = StripeSession::create([
            'customer' => $customer->id,
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => strtolower($payment->currency),
                    'product_data' => [
                        'name' => $plan->name,
                        'description' => $plan->short_description,
                    ],
                    'unit_amount' => round($payment->amount * 100),
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => $successUrl,
            'cancel_url' => route('checkout.index', ['plan' => $plan->id]),
            'metadata' => [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'plan_id' => $plan->id,
            ],
        ]);

        return redirect()->to($session->url);
    }

    /**
     * Handle free plan signup
     */
    public function signupFreePlan(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
        ]);

        $user = Auth::user();
        $plan = Plan::findOrFail($request->plan_id);

        // Verify this is actually a free plan
        if (!$plan->isFree()) {
            return redirect()->back()->with('error', 'This is not a free plan.');
        }

        // Check if user already has an active subscription
        $existingSubscription = Subscription::where('user_id', $user->id)
            ->where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->first();

        if ($existingSubscription) {
            return redirect()->back()->with('error', 'You already have an active subscription.');
        }

        DB::beginTransaction();

        try {
            // Create payment record for free plan
            $payment = Payment::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'payment_type' => 'COUPON_CODE',
                'gateway_name' => 'NONE',
                'amount' => 0.00,
                'original_amount' => 0.00,
                'discount_amount' => 0.00,
                'currency' => $plan->currency,
                'payment_status' => 'SUCCESS',
                'billing_cycle' => 'monthly',
                'payment_date' => now(),
                'metadata' => [
                    'billing_cycle' => 'monthly',
                    'plan_name' => $plan->name,
                    'plan_features' => $plan->features,
                    'free_plan' => true
                ]
            ]);

            // Create subscription
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'payment_id' => $payment->id,
                'status' => 'ACTIVE',
                'start_date' => now(),
                'expiry_date' => now()->addDays($plan->duration_days),
                'features' => $plan->features, // Copy plan features to subscription
            ]);

            DB::commit();

            return redirect()->route('business.dashboard')
                ->with('success', 'Welcome to ' . $plan->name . '! Your free plan is now active.');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Free plan signup failed', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'Failed to activate free plan. Please try again.');
        }
    }

    /**
     * Display subscription details and usage
     */
    public function showSubscription(SubscriptionDisplayService $displayService)
    {
        $user = Auth::user();
        $subscriptionData = $displayService->getSubscriptionDisplay($user->id);

        return view('subscription.details', compact('subscriptionData'));
    }

    /**
     * Get subscription data as JSON for AJAX requests
     */
    public function getSubscriptionData(SubscriptionDisplayService $displayService)
    {
        $user = Auth::user();
        $subscriptionData = $displayService->getSubscriptionDisplay($user->id);

        return response()->json($subscriptionData);
    }

    /**
     * Get upgrade modal content for AJAX requests
     */
    public function getUpgradeModalContent(Request $request, IpLocationService $ipLocationService)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {

            $cacheKey = "upgrade_modal_content_{$user->id}";
            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            // Detect user's currency based on IP location
            $currency = $this->detectUserCurrency($request, $ipLocationService);

            // Get current subscription and plan
            $currentSubscription = Subscription::where('user_id', $user->id)
                ->where('status', 'ACTIVE')
                ->where('expiry_date', '>=', now())
                ->with('plan')
                ->first();

            $currentPlan = null;
            if ($currentSubscription && $currentSubscription->plan) {
                $currentPlan = [
                    'id' => $currentSubscription->plan->id,
                    'name' => $currentSubscription->plan->name,
                    'short_description' => $currentSubscription->plan->short_description,
                    'price' => $currentSubscription->plan->price,
                    'currency' => $currentSubscription->plan->currency,
                    'currency_symbol' => $currentSubscription->plan->currency_symbol,
                    'duration_days' => $currentSubscription->plan->duration_days,
                    'features' => $currentSubscription->plan->features ?? []
                ];
            }

            // Get plans filtered by detected currency
            $allPlans = Plan::where('is_active', 1)
                ->where('currency', $currency)
                ->get();

            // Filter upgrade plans based on current plan and higher price
            $upgradePlans = [];
            if ($currentPlan) {
                $currentPrice = (float) $currentPlan['price'];

                $upgradePlans = $allPlans->filter(function ($plan) use ($currentPrice, $currentPlan) {
                    return (float) $plan->price > $currentPrice &&
                        $plan->id !== $currentPlan['id'];
                })->map(function ($plan) {
                    return [
                        'id' => $plan->id,
                        'name' => $plan->name,
                        'short_description' => $plan->short_description,
                        'price' => $plan->price,
                        'currency' => $plan->currency,
                        'currency_symbol' => $plan->currency_symbol,
                        'duration_days' => $plan->duration_days,
                        'features' => $plan->features ?? []
                    ];
                })->values()->toArray();
            } else {
                // If no current plan, show all plans for detected currency
                $upgradePlans = $allPlans->map(function ($plan) {
                    return [
                        'id' => $plan->id,
                        'name' => $plan->name,
                        'short_description' => $plan->short_description,
                        'price' => $plan->price,
                        'currency' => $plan->currency,
                        'currency_symbol' => $plan->currency_symbol,
                        'duration_days' => $plan->duration_days,
                        'features' => $plan->features ?? []
                    ];
                })->toArray();
            }

            $data = [
                'current_plan' => $currentPlan,
                'upgrade_plans' => $upgradePlans,
                'detected_currency' => $currency
            ];

            // Return rendered HTML for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                $html = view('subscription.upgrade-modal-content', compact('data'))->render();
                Cache::put($cacheKey, $html, 3600); // Cache for 1 hour
                return $html;
            }

            // For non-AJAX requests, return JSON
            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('Error loading upgrade modal content: ' . $e->getMessage());

            if ($request->ajax() || $request->wantsJson()) {
                return response('<div class="text-center py-8"><p class="text-red-600">Failed to load upgrade options</p></div>', 500);
            }

            return response()->json(['error' => 'Failed to load upgrade options'], 500);
        }
    }

    /**
     * Get plans with pricing display for plan selection
     */
    public function getPlansWithPricing(Request $request)
    {
        $currency = $request->get('currency', 'INR');
        $plans = Plan::where('currency', $currency)
            ->where('is_active', true)
            ->orderBy('price')
            ->get();

        $plansData = [];
        foreach ($plans as $plan) {
            $plansData[] = [
                'id' => $plan->id,
                'name' => $plan->name,
                'short_description' => $plan->short_description,
                'long_description' => $plan->long_description,
                'features' => $plan->features,
                'pricing' => $plan->getPricingDisplay(),
                'is_free' => $plan->isFree(),
                'tier' => $plan->getTier()
            ];
        }

        return response()->json($plansData);
    }

    /**
     * Process Razorpay payment
     */
    private function processRazorpayPayment($payment, $plan, $user)
    {
        $api = new RazorpayApi(env('RAZORPAY_KEY'), env('RAZORPAY_SECRET'));

        $orderData = [
            'amount' => $payment->amount * 100, // Amount in paise
            'currency' => $payment->currency,
            'payment_capture' => 1,
        ];

        $order = $api->order->create($orderData);

        $payment->update(['order_id' => $order->id]);

        return view('checkout.razorpay', [
            'user' => $user,
            'plan' => $plan,
            'payment' => $payment,
            'order_id' => $order->id,
            'amount' => $payment->amount * 100,
            'razorpay_key' => env('RAZORPAY_KEY'),
        ]);
    }

    /**
     * Detect user's currency based on IP location
     */
    private function detectUserCurrency(Request $request, IpLocationService $ipLocationService)
    {
        // Allow manual currency override via URL parameter
        if ($request->has('currency') && in_array($request->get('currency'), ['INR', 'USD'])) {
            return $request->get('currency');
        }

        try {
            // Get user's IP location
            $clientIp = $ipLocationService->getClientIpAddress($request);
            $locationData = $ipLocationService->getIpLocation($clientIp);

            // If user is not from India, use USD, otherwise INR
            if (isset($locationData['countryCode']) && $locationData['countryCode'] != 'IN') {
                return 'USD';
            }

            return 'INR';
        } catch (\Exception $e) {
            // Fallback to USD if IP detection fails
            Log::warning('IP location detection failed for currency selection', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);
            return 'INR';
        }
    }

    /**
     * Get filtered plans based on currency and upgrade status
     */
    private function getFilteredPlans($currency, $currentSubscription, $isUpgrade)
    {
        $query = Plan::where('is_active', true)
            ->where('currency', $currency);

        if ($isUpgrade && $currentSubscription) {
            // For upgrades, only show plans with higher price than current plan
            $currentPrice = $currentSubscription->plan->price;
            $query->where('price', '>', $currentPrice);
        }

        return $query->orderBy('price')->get();
    }

    /**
     * Get default selected plan
     */
    private function getDefaultSelectedPlan(Request $request, $plans, $currentSubscription, $isUpgrade)
    {
        // If plan ID is provided in request, use it (if valid)
        if ($request->has('plan')) {
            $requestedPlanId = $request->get('plan');
            $requestedPlan = $plans->where('id', $requestedPlanId)->first();
            if ($requestedPlan) {
                return $requestedPlanId;
            }
        }

        // For first-time users, auto-select Free plan
        if (!$isUpgrade) {
            $freePlan = $plans->where('price', 0)->first();
            if ($freePlan) {
                return $freePlan->id;
            }
        }

        // For upgrades, select the lowest priced upgrade option
        if ($isUpgrade && $plans->isNotEmpty()) {
            return $plans->first()->id;
        }

        // Fallback to first available plan
        return $plans->isNotEmpty() ? $plans->first()->id : null;
    }
}
