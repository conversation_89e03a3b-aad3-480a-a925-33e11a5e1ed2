<?php

namespace App\Services;

use App\Models\AdminEmailTemplate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailTemplateService
{
    /**
     * Send email using template
     */
    public function sendEmail($templateType, $toEmail, $variables = [], $options = [])
    {
        try {
            // Get the template
            $template = AdminEmailTemplate::getDefaultTemplate($templateType);
            
            if (!$template) {
                throw new \Exception("No template found for type: {$templateType}");
            }

            if (!$template->is_active) {
                throw new \Exception("Template is not active: {$templateType}");
            }

            // Validate required variables
            $validation = $template->validateVariables($variables);
            if (!$validation['valid']) {
                throw new \Exception("Missing required variables: " . implode(', ', $validation['missing']));
            }

            // Add global variables
            $globalVariables = $this->getGlobalVariables();
            $variables = array_merge($globalVariables, $variables);

            // Render the template
            $rendered = $template->renderContent($variables);

            // Send the email
            Mail::send([], [], function ($message) use ($rendered, $toEmail, $options) {
                $message->to($toEmail)
                    ->subject($rendered['subject'])
                    ->html($rendered['html']);
                
                if ($rendered['text']) {
                    $message->text($rendered['text']);
                }

                // Apply additional options
                if (isset($options['from_email'])) {
                    $message->from($options['from_email'], $options['from_name'] ?? null);
                }

                if (isset($options['reply_to'])) {
                    $message->replyTo($options['reply_to']);
                }

                if (isset($options['cc'])) {
                    $message->cc($options['cc']);
                }

                if (isset($options['bcc'])) {
                    $message->bcc($options['bcc']);
                }
            });

            Log::info("Email sent successfully", [
                'template_type' => $templateType,
                'template_id' => $template->id,
                'to_email' => $toEmail,
                'subject' => $rendered['subject']
            ]);

            return [
                'success' => true,
                'template_id' => $template->id,
                'subject' => $rendered['subject']
            ];

        } catch (\Exception $e) {
            Log::error("Failed to send email", [
                'template_type' => $templateType,
                'to_email' => $toEmail,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get global variables for templates
     */
    private function getGlobalVariables()
    {
        return [
            'app_name' => config('app.name', 'ReviewBiz'),
            'app_url' => config('app.url'),
            'current_date' => now()->format('F j, Y'),
            'current_year' => now()->year,
            'support_email' => config('mail.support_address', '<EMAIL>'),
            'company_name' => config('app.company_name', 'ReviewBiz'),
            'company_address' => config('app.company_address', ''),
        ];
    }

    /**
     * Create default system templates
     */
    public function createDefaultTemplates()
    {
        $templates = [
            [
                'name' => 'Welcome Email',
                'subject' => 'Welcome to {{app_name}}!',
                'type' => 'welcome',
                'category' => 'system',
                'html_content' => $this->getWelcomeTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'user_email'],
                'required_variables' => ['user_name', 'user_email'],
                'is_default' => true,
            ],
            [
                'name' => 'User Registration',
                'subject' => 'Welcome to {{app_name}} - Registration Successful!',
                'type' => 'user_registration',
                'category' => 'system',
                'html_content' => $this->getUserRegistrationTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'user_email'],
                'required_variables' => ['user_name', 'user_email'],
                'is_default' => true,
            ],
            [
                'name' => 'Welcome User with Credentials',
                'subject' => 'Welcome to {{app_name}} - Your Account Details',
                'type' => 'welcome_user',
                'category' => 'system',
                'html_content' => $this->getWelcomeUserTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'user_email', 'password', 'reset_token'],
                'required_variables' => ['user_name', 'user_email', 'password'],
                'is_default' => true,
            ],
            [
                'name' => 'Team Invitation',
                'subject' => 'Invitation to Join {{business_name}} - {{app_name}}',
                'type' => 'team_invitation',
                'category' => 'system',
                'html_content' => $this->getTeamInvitationTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'business_name', 'inviter_name', 'role', 'invitation_url', 'reset_token'],
                'required_variables' => ['user_name', 'business_name', 'inviter_name', 'invitation_url'],
                'is_default' => true,
            ],
            [
                'name' => 'Password Reset',
                'subject' => 'Reset Your Password - {{app_name}}',
                'type' => 'password_reset',
                'category' => 'system',
                'html_content' => $this->getPasswordResetTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'reset_url', 'reset_token'],
                'required_variables' => ['user_name', 'reset_url'],
                'is_default' => true,
            ],
            [
                'name' => 'Email Verification',
                'subject' => 'Verify Your Email - {{app_name}}',
                'type' => 'email_verification',
                'category' => 'system',
                'html_content' => $this->getEmailVerificationTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'verification_url'],
                'required_variables' => ['user_name', 'verification_url'],
                'is_default' => true,
            ],
            [
                'name' => 'Subscription Created',
                'subject' => 'Your Subscription is Active - {{app_name}}',
                'type' => 'subscription_created',
                'category' => 'system',
                'html_content' => $this->getSubscriptionCreatedTemplate(),
                'available_variables' => ['app_name', 'app_url', 'user_name', 'plan_name', 'amount', 'currency'],
                'required_variables' => ['user_name', 'plan_name'],
                'is_default' => true,
            ],
        ];

        foreach ($templates as $templateData) {
            AdminEmailTemplate::updateOrCreate(
                ['type' => $templateData['type'], 'category' => 'system'],
                $templateData
            );
        }
    }

    /**
     * Get welcome email template HTML
     */
    private function getWelcomeTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{app_name}}</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f3f4f6; padding: 20px;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
        <div style="background-color: #1a1e2e; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Welcome to {{app_name}}</h1>
        </div>
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{user_name}}!</h2>
            <p style="color: #666; line-height: 1.6;">
                Welcome to {{app_name}}! We\'re excited to have you on board. Your account has been successfully created and you can now start managing your business reviews with our AI-powered platform.
            </p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{app_url}}" style="background-color: #4f46e5; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Get Started
                </a>
            </div>
            <p style="color: #666; line-height: 1.6;">
                If you have any questions, feel free to reach out to our support team at {{support_email}}.
            </p>
            <p style="color: #666; line-height: 1.6;">
                Best regards,<br>
                The {{app_name}} Team
            </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>&copy; {{current_year}} {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get password reset template HTML
     */
    private function getPasswordResetTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f3f4f6; padding: 20px;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
        <div style="background-color: #1a1e2e; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Password Reset</h1>
        </div>
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{user_name}},</h2>
            <p style="color: #666; line-height: 1.6;">
                You recently requested to reset your password for your {{app_name}} account. Click the button below to reset it.
            </p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{reset_url}}" style="background-color: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Reset Password
                </a>
            </div>
            <p style="color: #666; line-height: 1.6;">
                If you did not request a password reset, please ignore this email or contact support if you have questions.
            </p>
            <p style="color: #666; line-height: 1.6;">
                This password reset link will expire in 60 minutes.
            </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>&copy; {{current_year}} {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get email verification template HTML
     */
    private function getEmailVerificationTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f3f4f6; padding: 20px;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
        <div style="background-color: #1a1e2e; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Verify Your Email</h1>
        </div>
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{user_name}},</h2>
            <p style="color: #666; line-height: 1.6;">
                Thank you for signing up for {{app_name}}! Please verify your email address by clicking the button below.
            </p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{verification_url}}" style="background-color: #059669; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Verify Email Address
                </a>
            </div>
            <p style="color: #666; line-height: 1.6;">
                If you did not create an account, no further action is required.
            </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>&copy; {{current_year}} {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get subscription created template HTML
     */
    private function getSubscriptionCreatedTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Active</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f3f4f6; padding: 20px;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
        <div style="background-color: #1a1e2e; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Subscription Active</h1>
        </div>
        <div style="padding: 30px;">
            <h2 style="color: #333; margin-top: 0;">Hello {{user_name}},</h2>
            <p style="color: #666; line-height: 1.6;">
                Great news! Your {{plan_name}} subscription is now active and ready to use.
            </p>
            <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #0369a1; margin-top: 0;">Subscription Details</h3>
                <p style="color: #0369a1; margin: 5px 0;"><strong>Plan:</strong> {{plan_name}}</p>
                <p style="color: #0369a1; margin: 5px 0;"><strong>Amount:</strong> {{currency}}{{amount}}</p>
            </div>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{app_url}}" style="background-color: #4f46e5; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Access Your Dashboard
                </a>
            </div>
            <p style="color: #666; line-height: 1.6;">
                You can now enjoy all the features included in your plan. If you have any questions, our support team is here to help.
            </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>&copy; {{current_year}} {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get user registration template HTML
     */
    private function getUserRegistrationTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{app_name}}</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f6f9fc; font-family: Arial, sans-serif;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05); margin-top: 40px;">
        <div style="background-color: #1a1e2e; padding: 20px; text-align: center;">
            <img src="{{app_url}}/logo.png" alt="{{app_name}} Logo" style="width: 200px; height: auto;" />
        </div>
        <div style="padding: 40px;">
            <h1 style="color: #333; margin-top: 0; font-size: 24px;">Welcome {{user_name}}!</h1>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">Thank you for registering with us!</p>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">Your account has been created successfully.</p>

            <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h2 style="color: #333; margin-top: 0; font-size: 18px;">Your Account Details:</h2>
                <p style="color: #666; margin: 5px 0;"><strong>Name:</strong> {{user_name}}</p>
                <p style="color: #666; margin: 5px 0;"><strong>Email:</strong> {{user_email}}</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{app_url}}/login" style="background-color: #4f46e5; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 16px;">
                    Login to Your Account
                </a>
            </div>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>Empowering your businesses to manage and improve the online reputation with AI</p>
            <p>&copy; {{current_year}} {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get welcome user template HTML (with credentials)
     */
    private function getWelcomeUserTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{app_name}}</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f3f4f6; padding: 20px;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
        <div style="background-color: #1a1e2e; padding: 20px; text-align: center;">
            <h2 style="color: white; margin: 0;">Welcome to {{app_name}}</h2>
        </div>
        <div style="padding: 30px;">
            <p style="color: #333; font-size: 16px;">Hello {{user_name}},</p>
            <p style="color: #666; line-height: 1.6;">We\'ve created your account on <strong>{{app_name}}</strong>.</p>

            <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <p style="color: #0369a1; margin-top: 0; font-weight: bold;">Temporary Login Credentials:</p>
                <ul style="color: #0369a1; margin: 10px 0; padding-left: 20px;">
                    <li><strong>Email:</strong> {{user_email}}</li>
                    <li><strong>Password:</strong> {{password}}</li>
                </ul>
            </div>

            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <p style="color: #92400e; margin: 0; font-size: 14px;">
                    <strong>Important:</strong> Please change your password after your first login for security purposes.
                </p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{app_url}}/login" style="background-color: #4f46e5; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Login Now
                </a>
            </div>

            <p style="color: #666; line-height: 1.6;">
                If you have any questions or need assistance, please don\'t hesitate to contact our support team.
            </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>&copy; {{current_year}} {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get team invitation template HTML
     */
    private function getTeamInvitationTemplate()
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Invitation</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f6f9fc; font-family: Arial, sans-serif;">
    <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f6f9fc; padding: 40px 0;">
        <tr>
            <td align="center">
                <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; padding: 40px;">
                    <!-- Logo -->
                    <tr>
                        <td style="padding-bottom: 20px;">
                            <img src="{{app_url}}/logo-black.png" alt="{{app_name}} Logo" style="width: 200px;" />
                        </td>
                    </tr>

                    <!-- Heading -->
                    <tr>
                        <td style="font-size: 22px; font-weight: bold; color: #111; padding-bottom: 20px;">
                            You\'re invited to join <span style="color: #4f46e5;">{{business_name}}</span>
                        </td>
                    </tr>

                    <!-- Greeting -->
                    <tr>
                        <td style="font-size: 14px; color: #444; padding-bottom: 20px;">
                            Hello <strong>{{user_name}}</strong>,<br><br>
                            <strong>{{inviter_name}}</strong> has invited you to join their team on <strong>{{business_name}}</strong>.
                        </td>
                    </tr>

                    <tr>
                        <td style="font-size: 14px; color: #444; padding-bottom: 20px;">
                            Click the button below to reset your password and accept your invitation to get started.
                        </td>
                    </tr>

                    <!-- CTA Button -->
                    <tr>
                        <td style="text-align: center; padding: 30px 0;">
                            <a href="{{invitation_url}}" style="background-color: #4f46e5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                                Accept Invitation
                            </a>
                        </td>
                    </tr>

                    <!-- Additional Info -->
                    <tr>
                        <td style="font-size: 14px; color: #666; padding-bottom: 20px; line-height: 1.6;">
                            Once you accept this invitation, you\'ll have access to manage reviews and collaborate with the team on {{business_name}}.
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="font-size: 12px; color: #999; padding-top: 30px; border-top: 1px solid #eee; text-align: center;">
                            If you didn\'t expect this invitation, you can safely ignore this email.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>';
    }
}
