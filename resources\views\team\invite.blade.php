@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-3xl mx-auto">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4 flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-semibold text-gray-800">Business Location</h2>
                    <p class="text-sm text-gray-600 mt-1">{{ $business->title }}</p>
                </div>

                <div>
                    <h2 class="text-xl font-semibold text-gray-800">Owner</h2>
                    <p class="text-sm text-gray-600 mt-1">{{ $business->user->name }}</p>
                </div>
            </div>

            <div class="p-6">
                @if (session('error'))
                <div class="mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
                @endif

                <form method="POST" action="{{ route('team.invite', $locationId) }}">
                    @csrf

                    <div class="mb-6">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input id="email" type="email" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('email') border-red-500 @enderror" name="email" value="{{ old('email') }}" required autocomplete="email">
                        @error('email')
                        <p class="mt-1 text-sm text-red-600">
                            {{ $message }}
                        </p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            Enter the email address of the person you want to invite.
                        </p>
                    </div>

                    <div class="mb-6">
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select id="role" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('role') border-red-500 @enderror" name="role" required>
                            <option value="member" {{ old('role') === 'member' ? 'selected' : '' }}>Member</option>
                            <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                        </select>
                        @error('role')
                        <p class="mt-1 text-sm text-red-600">
                            {{ $message }}
                        </p>
                        @enderror
                        <div class="mt-2 text-sm text-gray-500 space-y-1">
                            <p><span class="font-medium text-gray-700">Admin:</span> Has full access to manage all aspects of this business account.</p>
                            <p><span class="font-medium text-gray-700">Member:</span> Access is limited to the permissions you select below.</p>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Permissions</label>
                        <div class="bg-gray-50 border border-gray-200 rounded-md">
                            <div class="p-4">
                                <div id="permissions-container" class="space-y-3">
                                    @foreach ($availablePermissions as $key => $label)
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 permission-checkbox" type="checkbox" name="permissions[]" value="{{ $key }}" id="perm_{{ $key }}" {{ old('permissions') && in_array($key, old('permissions')) ? 'checked' : '' }}>
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label class="font-medium text-gray-700" for="perm_{{ $key }}">
                                                {{ $label }}
                                            </label>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                <p class="mt-3 text-xs text-gray-500 italic">
                                    These permissions will be ignored if the role is set to Admin.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4">
                        <a href="{{ route('team.index', $locationId) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                            </svg>
                            Send Invitation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const roleSelect = document.getElementById('role');
        const permissionsContainer = document.getElementById('permissions-container');

        function togglePermissions() {
            const isAdmin = roleSelect.value === 'admin';
            const checkboxes = document.querySelectorAll('.permission-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.disabled = isAdmin;
                if (isAdmin) {
                    checkbox.checked = true;
                }
            });

            permissionsContainer.style.opacity = isAdmin ? '0.6' : '1';
        }

        roleSelect.addEventListener('change', togglePermissions);

        // Initial state
        togglePermissions();
    });
</script>
@endpush
@endsection