<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Business;
use App\Models\BusinessAccount;
use App\Jobs\FetchGoogleReviewsJob;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;


class FetchGoogleReviewsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'google:fetch-reviews {--user=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $userId = $this->option('user');

        if (!$userId) {
            $this->error('Please provide a user ID using --user option.');
            return;
        }


        $business = Business::where('user_id', $userId)->first();
        $businessAccount = BusinessAccount::where('user_id', $userId)->first();

        if (!$business || !$businessAccount) {
            $this->error('Business or business account not found for user ID: ' . $userId);
            return;
        }

        FetchGoogleReviewsJob::dispatch($business->location_name, $businessAccount->business_google_id);
        $this->info('Dispatched review fetching jobs for all businesses.');
    }
}
