<?php

namespace App\Http\Controllers;

use App\Models\GoogleReview;
use App\Models\Template;
use App\Services\GoogleAuthService;
use App\Services\GoogleBusinessService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class ReviewController extends Controller
{
    protected $googleAuthService;
    protected $googleBusinessService;

    public function __construct(GoogleAuthService $googleAuthService, GoogleBusinessService $googleBusinessService)
    {
        // $this->middleware('auth.google');
        $this->googleAuthService = $googleAuthService;
        $this->googleBusinessService = $googleBusinessService;
    }

    public function index(Request $request)
    {
        $locationName = $request->query('location');

        if (!$locationName) {
            return redirect()->route('dashboard');
        }

        // Refresh token if needed
        $accessToken = Session::get('access_token.access_token');
        if (!$this->googleAuthService->isTokenValid($accessToken)) {
            if (Session::has('access_token.refresh_token')) {
                try {
                    $newToken = $this->googleAuthService->refreshAccessToken(Session::get('access_token.refresh_token'));
                    Session::put('access_token', $newToken);
                    $accessToken = $newToken['access_token'];
                    $this->googleBusinessService->setAccessToken($accessToken);
                } catch (Exception $e) {
                    Session::flush();
                    return redirect()->route('login');
                }
            } else {
                Session::flush();
                return redirect()->route('login');
            }
        }

        // Get user info
        $userInfo = Session::get('user_info') ?? $this->googleAuthService->getUserInfo();

        // Initialize variables
        $reviews = [];
        $error = null;
        $rawResponse = null;
        $successMessage = null;
        $averageRating = 0;
        $totalReviewCount = 0;

        // Fetch reviews
        try {
            if (config('app.debug')) {
                Log::info("Attempting to fetch reviews for location: {$locationName}");
            }

            $reviewsResponse = $this->googleBusinessService->getReviews($locationName);

            if (config('app.debug')) {
                Log::info("Reviews response: " . json_encode($reviewsResponse));
            }

            $reviews = $reviewsResponse['reviews'] ?? [];
            $averageRating = $reviewsResponse['averageRating'] ?? 0;
            $totalReviewCount = $reviewsResponse['totalReviewCount'] ?? 0;
            $rawResponse = json_encode($reviewsResponse, JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            $error = "Error fetching reviews: " . $e->getMessage();
            if (config('app.debug')) {
                Log::error("Error in reviews: " . $e->getMessage());
            }
        }

        // Get location title
        $locationTitle = '';
        $locations = Session::get('locations', []);
        foreach ($locations as $location) {
            if ($location['name'] === $locationName) {
                $locationTitle = $location['title'];
                break;
            }
        }

        return view('reviews', compact(
            'reviews',
            'error',
            'successMessage',
            'rawResponse',
            'userInfo',
            'locationName',
            'locationTitle',
            'averageRating',
            'totalReviewCount'
        ));
    }

    public function reply(Request $request)
    {
        $request->validate([
            'review_id' => 'required',
            'reply' => 'required|string',
            'location' => 'required',
        ]);

        $reviewId = $request->input('review_id');
        $reply = $request->input('reply');
        $locationName = $request->input('location');

        try {
            $replyResponse = $this->googleBusinessService->replyToReview($locationName, $reviewId, $reply);
            $successMessage = "Reply submitted successfully!";
            $rawResponse = json_encode($replyResponse, JSON_PRETTY_PRINT);
            return redirect()->route('reviews', ['location' => $locationName])
                ->with('success', $successMessage)
                ->with('rawResponse', $rawResponse);
        } catch (Exception $e) {
            return redirect()->route('reviews', ['location' => $locationName])
                ->with('error', "Error submitting reply: " . $e->getMessage());
        }
    }

    public function deleteReply(Request $request)
    {
        $locationName = $request->query('location');
        $reviewId = $request->query('review_id');

        if (!$locationName || !$reviewId) {
            return redirect()->route('dashboard');
        }

        try {
            $deleteResponse = $this->googleBusinessService->deleteReviewReply($locationName, $reviewId);
            $successMessage = "Reply deleted successfully!";
            $rawResponse = json_encode($deleteResponse, JSON_PRETTY_PRINT);
            return redirect()->route('reviews', ['location' => $locationName])
                ->with('success', $successMessage)
                ->with('rawResponse', $rawResponse);
        } catch (Exception $e) {
            return redirect()->route('reviews', ['location' => $locationName])
                ->with('error', "Error deleting reply: " . $e->getMessage());
        }
    }

    public function generateReply(Request $request)
    {
        $prompt = $request->input('prompt');
        $model = $request->input('model', 'gpt-3.5-turbo');
        $customInstruction = $request->input('custom_instruction');

        // Combine custom_instruction with prompt if provided
        $finalPrompt = $prompt;
        if (!empty($customInstruction)) {
            $finalPrompt = $customInstruction . "\n\n" . $prompt;
        }

        Log::info('AI Final Prompt:', ['finalPrompt' => $finalPrompt]);

        $start = microtime(true);
        $requestData = [
            'model' => $model,
            'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant that generates professional replies for Google reviews. IMPORTANT: Always strictly adhere to the specified sentiment in your responses and do not include any emojis. If asked for a negative sentiment, acknowledge problems and offer solutions. If positive, be appreciative and upbeat. If neutral, be balanced and factual. The sentiment parameter must be the primary factor determining the tone and content of your response.'],
                ['role' => 'user', 'content' => $finalPrompt],
            ],
            'max_tokens' => 500,
            'temperature' => 0.7,
        ];
        $url = 'https://api.openai.com/v1/chat/completions';
        $response = Http::withToken(env('OPEN_AI_KEY'))->post($url, $requestData);

        try {
            \App\Services\ExternalApiLogger::logAi(
                $url,
                $requestData,
                $response->json(),
                $response->status(),
                ($response->failed() ? 'Failed' : 'Success'),
                (int)((microtime(true) - $start) * 1000)
            );
        } catch (\Exception $e) {
            Log::error('Failed to log AI call generateReply:', ['error' => $e->getMessage()]);
        }
        Log::info('AI Reply Response:', ['response' => $response->json()]);
        return response()->json($response->json(), $response->status());
    }

    /**
     * Show the generate reply page interface
     *
     * @return \Illuminate\View\View
     */
    public function showGenerateReplyPage()
    {
        return view('generate-reply-page');
    }

    public function getDefaultTemplate(Request $request)
    {
        $businessId = $request->input('business_id');
        $template = Template::where('business_id', $businessId)->first();
        return response()->json(['data' => $template]);
    }

    public function getReply(Request $request)
    {
        $reviewId = $request->input('reviewId');
        $result = GoogleReview::where('parent_id', $reviewId)->first();
        return response()->json(['data' => $result]);
    }

    public function improvedText(Request $request)
    {
        $response = Http::withToken(env('OPEN_AI_KEY'))->post('https://api.openai.com/v1/chat/completions', [
            'model' => 'gpt-4',
            'messages' => [
                ['role' => 'system', 'content' => 'You are a grammar and readability expert.'],
                ['role' => 'user', 'content' => "Improve the following text:\n\n" . $request->input('text')],
            ],
            'temperature' => 0.7,
        ]);

        $improvedText = $response['choices'][0]['message']['content'];
        // $improvedText = $response['choices'];

        return response()->json(['improvedText' => $improvedText]);
    }

    public function exportReviews($businessName)
    {
        $reviews = GoogleReview::whereNull('parent_id')
            ->with('repliesWithUser.user')
            ->get();

        $handle = fopen('php://temp', 'r+');

        // Header row
        fputcsv($handle, [
            'Reviewer Name',
            'Comment',
            'Day Of Review',
            'Reply Comment',
            'Reply By',
            'Reply On',
        ]);

        foreach ($reviews as $review) {
            $reply = $review->repliesWithUser->first();

            // Use null safe operator and fallback values
            $reviewDate = $review->created_at_google
                ? Carbon::parse($review->created_at_google)->format('l, j F Y')
                : '-';

            $replyComment = $reply->comment ?? '-';
            $replyDate = $reply?->updated_at_google
                ? Carbon::parse($reply->updated_at_google)->format('j F Y')
                : '-';

            // Handle reply_by_name
            $replyByName = $reply?->reply_by === 0
                ? 'Smart Reply System'
                : ($reply?->user?->name ?? '-');

            fputcsv($handle, [
                $review->reviewer_name ?? '-',
                $review->comment ?? '-',
                $reviewDate,
                $replyComment,
                $replyByName,
                $replyDate,
            ]);
        }

        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        return Response::make($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $businessName . '_reviews_export.csv"',
        ]);
    }

    public function exportReviewsAsPdf($businessName)
    {
        $reviews = GoogleReview::whereNull('parent_id')->with('repliesWithUser.user')->get();
        $pdf = PDF::loadView('partials.reviews_pdf', compact('reviews'));
        return $pdf->download($businessName . '_reviews_export.pdf');
    }
}
