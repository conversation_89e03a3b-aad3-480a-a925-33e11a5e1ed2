<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of subscriptions
     */
    public function index(Request $request)
    {
        $query = Subscription::with(['user', 'plan']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('plan', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('plan')) {
            $query->where('plan_id', $request->plan);
        }

        $subscriptions = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.subscriptions.index', compact('subscriptions'));
    }

    /**
     * Display the specified subscription
     */
    public function show(Subscription $subscription)
    {
        $subscription->load(['user', 'plan', 'payment']);

        return view('admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Cancel subscription
     */
    public function cancel(Subscription $subscription)
    {
        try {
            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'notes' => 'Cancelled by admin'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription cancelled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error cancelling subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extend subscription
     */
    public function extend(Request $request, Subscription $subscription)
    {
        $request->validate([
            'extend_days' => 'required|integer|min:1|max:365',
            'reason' => 'nullable|string|max:500'
        ]);

        try {
            $currentExpiry = $subscription->expires_at ?: now();
            $newExpiry = Carbon::parse($currentExpiry)->addDays($request->extend_days);

            $subscription->update([
                'expires_at' => $newExpiry,
                'notes' => $request->reason ?: 'Extended by admin for ' . $request->extend_days . ' days'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription extended successfully',
                'new_expiry' => $newExpiry->format('M d, Y')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error extending subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export subscriptions data
     */
    public function export(Request $request)
    {
        $query = Subscription::with(['user', 'plan']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('plan', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('plan')) {
            $query->where('plan_id', $request->plan);
        }

        $subscriptions = $query->orderBy('created_at', 'desc')->get();

        $filename = 'subscriptions_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($subscriptions) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'User Name', 'User Email', 'Plan Name', 'Status',
                'Amount', 'Start Date', 'Expiry Date', 'Created At'
            ]);

            // CSV data
            foreach ($subscriptions as $subscription) {
                fputcsv($file, [
                    $subscription->id,
                    $subscription->user->name,
                    $subscription->user->email,
                    $subscription->plan->name,
                    $subscription->status,
                    $subscription->amount ?? $subscription->plan->price,
                    $subscription->created_at->format('Y-m-d'),
                    $subscription->expires_at ? $subscription->expires_at->format('Y-m-d') : 'Never',
                    $subscription->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
