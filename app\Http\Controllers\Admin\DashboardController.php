<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Subscription;
use App\Models\Payment;
use App\Models\Plan;
use App\Models\Coupon;
use App\Models\CouponUsageLog;
use App\Models\Business;
use App\Models\TeamMember;
use App\Models\BusinessActivityLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show admin dashboard
     */
    public function index(Request $request)
    {
        // Get filters
        $dateRange = $request->input('date_range', '30_days');
        $currency = $request->input('currency', 'all');
        $plan = $request->input('plan', 'all');

        $startDate = $this->getStartDateFromRange($dateRange);
        $endDate = Carbon::now();

        // Get key metrics
        $metrics = $this->getKeyMetrics($startDate, $endDate, $currency, $plan);

        // Get chart data
        $chartData = $this->getChartData($startDate, $endDate, $currency, $plan);

        // Get recent activities
        $recentUsers = $this->getRecentUsers();
        $recentPayments = $this->getRecentPayments();
        $expiringSubscriptions = $this->getExpiringSubscriptions();

        // Get additional analytics
        $businessMetrics = $this->getBusinessMetrics($startDate, $endDate);
        $teamMetrics = $this->getTeamMetrics($startDate, $endDate);
        $activityMetrics = $this->getActivityMetrics($startDate, $endDate);

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('admin.dashboard.index', compact(
            'metrics',
            'chartData',
            'recentUsers',
            'recentPayments',
            'expiringSubscriptions',
            'businessMetrics',
            'teamMetrics',
            'activityMetrics',
            'filterOptions',
            'dateRange',
            'currency',
            'plan'
        ));
    }

    /**
     * Get key metrics for dashboard
     */
    private function getKeyMetrics($startDate, $endDate, $currency = 'all', $plan = 'all')
    {
        // Active Users
        $activeUsersQuery = User::whereHas('subscriptions', function($query) use ($plan) {
            $query->where('status', 'ACTIVE')
                  ->where('expiry_date', '>=', now());
            if ($plan !== 'all') {
                $query->where('plan_id', $plan);
            }
        });
        $activeUsers = $activeUsersQuery->count();

        // Active Subscriptions
        $activeSubscriptionsQuery = Subscription::where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now());
        if ($plan !== 'all') {
            $activeSubscriptionsQuery->where('plan_id', $plan);
        }
        $activeSubscriptions = $activeSubscriptionsQuery->count();

        // Subscriptions Expiring (next 30 days)
        $expiringSubscriptionsQuery = Subscription::where('status', 'ACTIVE')
            ->whereBetween('expiry_date', [now(), now()->addDays(30)]);
        if ($plan !== 'all') {
            $expiringSubscriptionsQuery->where('plan_id', $plan);
        }
        $expiringSubscriptions = $expiringSubscriptionsQuery->count();

        // New Signups in date range
        $newSignups = User::whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Total Revenue in date range
        $revenueQuery = Payment::where('payment_status', 'completed')
            ->whereBetween('payment_date', [$startDate, $endDate]);
        if ($currency !== 'all') {
            $revenueQuery->where('currency', $currency);
        }
        if ($plan !== 'all') {
            $revenueQuery->where('plan_id', $plan);
        }
        $totalRevenue = $revenueQuery->sum('amount');

        // Coupon Usage in date range
        $couponUsage = CouponUsageLog::where('status', 'used')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Total Businesses Connected
        $totalBusinesses = Business::count();

        // Active Team Members
        $activeTeamMembers = TeamMember::where('status', 'active')->count();

        // Growth calculations (compared to previous period)
        $previousStartDate = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $previousEndDate = $startDate->copy()->subDay();

        $previousSignups = User::whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $previousRevenueQuery = Payment::where('payment_status', 'completed')
            ->whereBetween('payment_date', [$previousStartDate, $previousEndDate]);
        if ($currency !== 'all') {
            $previousRevenueQuery->where('currency', $currency);
        }
        if ($plan !== 'all') {
            $previousRevenueQuery->where('plan_id', $plan);
        }
        $previousRevenue = $previousRevenueQuery->sum('amount');

        $signupGrowth = $previousSignups > 0 ?
            (($newSignups - $previousSignups) / $previousSignups) * 100 :
            ($newSignups > 0 ? 100 : 0);

        $revenueGrowth = $previousRevenue > 0 ?
            (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 :
            ($totalRevenue > 0 ? 100 : 0);

        // Average Revenue Per User (ARPU)
        $arpu = $activeUsers > 0 ? $totalRevenue / $activeUsers : 0;

        return [
            'active_users' => $activeUsers,
            'active_subscriptions' => $activeSubscriptions,
            'expiring_subscriptions' => $expiringSubscriptions,
            'new_signups' => $newSignups,
            'total_revenue' => $totalRevenue,
            'coupon_usage' => $couponUsage,
            'total_businesses' => $totalBusinesses,
            'active_team_members' => $activeTeamMembers,
            'signup_growth' => round($signupGrowth, 1),
            'revenue_growth' => round($revenueGrowth, 1),
            'arpu' => round($arpu, 2),
        ];
    }

    /**
     * Get chart data for dashboard
     */
    private function getChartData($startDate, $endDate, $currency = 'all', $plan = 'all')
    {
        // Revenue chart data (daily)
        $revenueQuery = Payment::where('payment_status', 'SUCCESS')
            ->whereBetween('payment_date', [$startDate, $endDate]);
        if ($currency !== 'all') {
            $revenueQuery->where('currency', $currency);
        }
        if ($plan !== 'all') {
            $revenueQuery->where('plan_id', $plan);
        }
        $revenueData = $revenueQuery
            ->selectRaw('DATE(payment_date) as date, SUM(amount) as revenue, currency')
            ->groupBy('date', 'currency')
            ->orderBy('date')
            ->get();

        // Signups chart data (daily)
        $signupData = User::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as signups')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Plan distribution
        $planDistributionQuery = Subscription::where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->join('plans', 'subscriptions.plan_id', '=', 'plans.id');
        if ($plan !== 'all') {
            $planDistributionQuery->where('plan_id', $plan);
        }
        $planDistribution = $planDistributionQuery
            ->selectRaw('plans.name, COUNT(*) as count, plans.currency')
            ->groupBy('plans.name', 'plans.currency')
            ->get();

        // Subscription status distribution
        $subscriptionStatus = Subscription::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Monthly revenue trend (last 12 months)
        $monthlyRevenueQuery = Payment::where('payment_status', 'completed')
            ->where('payment_date', '>=', now()->subMonths(12));
        if ($currency !== 'all') {
            $monthlyRevenueQuery->where('currency', $currency);
        }
        $monthlyRevenue = $monthlyRevenueQuery
            ->selectRaw('YEAR(payment_date) as year, MONTH(payment_date) as month, SUM(amount) as revenue')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // User growth trend (last 12 months)
        $userGrowth = User::where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as users')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'revenue' => $revenueData,
            'signups' => $signupData,
            'plan_distribution' => $planDistribution,
            'subscription_status' => $subscriptionStatus,
            'monthly_revenue' => $monthlyRevenue,
            'user_growth' => $userGrowth,
        ];
    }

    /**
     * Get recent users
     */
    private function getRecentUsers($limit = 5)
    {
        return User::with(['subscriptions.plan'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent payments
     */
    private function getRecentPayments($limit = 5)
    {
        return Payment::with(['user', 'plan'])
            ->where('payment_status', 'SUCCESS')
            ->latest('payment_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Get expiring subscriptions
     */
    private function getExpiringSubscriptions($limit = 5)
    {
        return Subscription::with(['user', 'plan'])
            ->where('status', 'ACTIVE')
            ->whereBetween('expiry_date', [now(), now()->addDays(30)])
            ->orderBy('expiry_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Get business metrics
     */
    private function getBusinessMetrics($startDate, $endDate)
    {
        $totalBusinesses = Business::count();
        $newBusinesses = Business::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeBusinesses = Business::whereHas('user.subscriptions', function($q) {
            $q->where('status', 'ACTIVE')->where('expiry_date', '>=', now());
        })->count();

        $businessesWithTeams = Business::whereHas('teamMembers')->count();
        $averageTeamSize = Business::withCount('teamMembers')->get()->avg('team_members_count');

        return [
            'total_businesses' => $totalBusinesses,
            'new_businesses' => $newBusinesses,
            'active_businesses' => $activeBusinesses,
            'businesses_with_teams' => $businessesWithTeams,
            'average_team_size' => round($averageTeamSize, 1),
        ];
    }

    /**
     * Get team metrics
     */
    private function getTeamMetrics($startDate, $endDate)
    {
        $totalTeamMembers = TeamMember::count();
        $activeTeamMembers = TeamMember::where('status', 'active')->count();
        $pendingInvitations = TeamMember::where('status', 'pending')->count();
        $newTeamMembers = TeamMember::whereBetween('created_at', [$startDate, $endDate])->count();

        $teamMembersByRole = TeamMember::where('status', 'active')
            ->selectRaw('role, COUNT(*) as count')
            ->groupBy('role')
            ->get();

        return [
            'total_team_members' => $totalTeamMembers,
            'active_team_members' => $activeTeamMembers,
            'pending_invitations' => $pendingInvitations,
            'new_team_members' => $newTeamMembers,
            'team_members_by_role' => $teamMembersByRole,
        ];
    }

    /**
     * Get activity metrics
     */
    private function getActivityMetrics($startDate, $endDate)
    {
        $totalActivities = BusinessActivityLog::whereBetween('created_at', [$startDate, $endDate])->count();

        $activitiesByCategory = BusinessActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('activity_category, COUNT(*) as count')
            ->groupBy('activity_category')
            ->get();

        $activitiesByPerformer = BusinessActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('performed_by_type, COUNT(*) as count')
            ->groupBy('performed_by_type')
            ->get();

        $dailyActivities = BusinessActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as activities')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_activities' => $totalActivities,
            'activities_by_category' => $activitiesByCategory,
            'activities_by_performer' => $activitiesByPerformer,
            'daily_activities' => $dailyActivities,
        ];
    }

    /**
     * Get filter options
     */
    private function getFilterOptions()
    {
        $currencies = Payment::distinct()->pluck('currency')->filter()->values();
        $plans = Plan::select('id', 'name', 'currency')->get();

        return [
            'currencies' => $currencies,
            'plans' => $plans,
        ];
    }

    /**
     * Get start date from range string
     */
    private function getStartDateFromRange($range)
    {
        return match($range) {
            '7_days' => Carbon::now()->subDays(7),
            '30_days' => Carbon::now()->subDays(30),
            '90_days' => Carbon::now()->subDays(90),
            '1_year' => Carbon::now()->subYear(),
            default => Carbon::now()->subDays(30),
        };
    }

    /**
     * Analytics page with detailed reports
     */
    public function analytics(Request $request)
    {
        $dateRange = $request->input('date_range', '30_days');
        $currency = $request->input('currency', 'all');
        $plan = $request->input('plan', 'all');

        $startDate = $this->getStartDateFromRange($dateRange);
        $endDate = Carbon::now();

        // Get comprehensive analytics
        $analytics = [
            'revenue_analytics' => $this->getRevenueAnalytics($startDate, $endDate, $currency, $plan),
            'user_analytics' => $this->getUserAnalytics($startDate, $endDate, $plan),
            'subscription_analytics' => $this->getSubscriptionAnalytics($startDate, $endDate, $plan),
            'business_analytics' => $this->getBusinessAnalytics($startDate, $endDate),
            'performance_metrics' => $this->getPerformanceMetrics($startDate, $endDate),
        ];

        $filterOptions = $this->getFilterOptions();

        return view('admin.analytics.index', compact(
            'analytics',
            'filterOptions',
            'dateRange',
            'currency',
            'plan'
        ));
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        // Implementation for exporting analytics data
        // This would generate CSV/Excel files with analytics data
        return response()->json(['message' => 'Export functionality to be implemented']);
    }

    /**
     * Get revenue analytics data
     */
    private function getRevenueAnalytics($startDate, $endDate, $currency = 'all', $plan = 'all')
    {
        $query = Payment::whereBetween('created_at', [$startDate, $endDate])
                       ->where('payment_status', 'SUCCESS');

        if ($currency !== 'all') {
            $query->where('currency', $currency);
        }

        if ($plan !== 'all') {
            $query->whereHas('subscription', function ($q) use ($plan) {
                $q->where('plan_id', $plan);
            });
        }

        $payments = $query->get();
        $totalRevenue = $payments->sum('amount');
        $totalTransactions = $payments->count();
        $averageRevenue = $totalTransactions > 0 ? $totalRevenue / $totalTransactions : 0;

        // Calculate growth rate (simplified)
        $previousPeriod = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        $previousRevenue = Payment::whereBetween('created_at', [$previousPeriod, $startDate])
                                 ->where('payment_status', 'SUCCESS')
                                 ->sum('amount');

        $growthRate = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        // Chart data
        $chartLabels = [];
        $chartData = [];
        $days = $endDate->diffInDays($startDate);

        for ($i = 0; $i <= $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $chartLabels[] = $date->format('M d');
            $dayRevenue = $payments->filter(function ($payment) use ($date) {
                return $payment->created_at->format('Y-m-d') === $date->format('Y-m-d');
            })->sum('amount');
            $chartData[] = $dayRevenue;
        }

        return [
            'total_revenue' => $totalRevenue,
            'average_revenue' => $averageRevenue,
            'total_transactions' => $totalTransactions,
            'growth_rate' => $growthRate,
            'chart_labels' => $chartLabels,
            'chart_data' => $chartData
        ];
    }

    /**
     * Get user analytics data
     */
    private function getUserAnalytics($startDate, $endDate, $plan = 'all')
    {
        $totalUsers = User::count();
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();

        $activeUsersQuery = User::whereHas('subscriptions', function ($q) use ($startDate, $endDate) {
            $q->where('status', 'active')
              ->whereBetween('created_at', [$startDate, $endDate]);
        });

        if ($plan !== 'all') {
            $activeUsersQuery->whereHas('subscriptions', function ($q) use ($plan) {
                $q->where('plan_id', $plan);
            });
        }

        $activeUsers = $activeUsersQuery->count();
        $conversionRate = $newUsers > 0 ? ($activeUsers / $newUsers) * 100 : 0;

        // Chart data
        $chartLabels = [];
        $chartData = [];
        $days = $endDate->diffInDays($startDate);

        for ($i = 0; $i <= $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $chartLabels[] = $date->format('M d');
            $dayUsers = User::whereDate('created_at', $date)->count();
            $chartData[] = $dayUsers;
        }

        return [
            'total_users' => $totalUsers,
            'new_users' => $newUsers,
            'active_users' => $activeUsers,
            'conversion_rate' => $conversionRate,
            'chart_labels' => $chartLabels,
            'chart_data' => $chartData
        ];
    }

    /**
     * Get subscription analytics data
     */
    private function getSubscriptionAnalytics($startDate, $endDate, $plan = 'all')
    {
        $query = Subscription::whereBetween('created_at', [$startDate, $endDate]);

        if ($plan !== 'all') {
            $query->where('plan_id', $plan);
        }

        $subscriptions = $query->get();
        $totalSubscriptions = $subscriptions->count();
        $activeSubscriptions = $subscriptions->where('status', 'active')->count();
        $cancelledSubscriptions = $subscriptions->where('status', 'cancelled')->count();

        $churnRate = $totalSubscriptions > 0 ? ($cancelledSubscriptions / $totalSubscriptions) * 100 : 0;

        // Plan distribution
        $planDistribution = Subscription::with('plan')
                                      ->whereBetween('created_at', [$startDate, $endDate])
                                      ->get()
                                      ->groupBy('plan.name')
                                      ->map->count();

        return [
            'total_subscriptions' => $totalSubscriptions,
            'active_subscriptions' => $activeSubscriptions,
            'cancelled_subscriptions' => $cancelledSubscriptions,
            'churn_rate' => $churnRate,
            'plan_labels' => $planDistribution->keys()->toArray(),
            'plan_data' => $planDistribution->values()->toArray()
        ];
    }

    /**
     * Get business analytics data
     */
    private function getBusinessAnalytics($startDate, $endDate)
    {
        $totalBusinesses = \App\Models\Business::count();
        $activeBusinesses = \App\Models\Business::whereHas('user.subscriptions', function ($q) {
            $q->where('status', 'active');
        })->count();

        // Assuming you have a reviews table
        $totalReviews = \App\Models\GoogleReview::count();
        $averageRating = \App\Models\GoogleReview::avg('star_rating') ?? 0;

        return [
            'total_businesses' => $totalBusinesses,
            'active_businesses' => $activeBusinesses,
            'total_reviews' => $totalReviews,
            'average_rating' => $averageRating
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics($startDate, $endDate)
    {
        $currentPeriodDays = $endDate->diffInDays($startDate);
        $previousStart = $startDate->copy()->subDays($currentPeriodDays);

        $metrics = [
            [
                'name' => 'Revenue',
                'current' => '$' . number_format(Payment::whereBetween('created_at', [$startDate, $endDate])->sum('amount'), 2),
                'previous' => '$' . number_format(Payment::whereBetween('created_at', [$previousStart, $startDate])->sum('amount'), 2),
                'change' => 15.2
            ],
            [
                'name' => 'New Users',
                'current' => number_format(User::whereBetween('created_at', [$startDate, $endDate])->count()),
                'previous' => number_format(User::whereBetween('created_at', [$previousStart, $startDate])->count()),
                'change' => 8.7
            ],
            [
                'name' => 'Subscriptions',
                'current' => number_format(Subscription::whereBetween('created_at', [$startDate, $endDate])->count()),
                'previous' => number_format(Subscription::whereBetween('created_at', [$previousStart, $startDate])->count()),
                'change' => -2.1
            ],
            [
                'name' => 'Active Businesses',
                'current' => number_format(\App\Models\Business::count()),
                'previous' => number_format(\App\Models\Business::where('created_at', '<', $startDate)->count()),
                'change' => 12.5
            ]
        ];

        return $metrics;
    }

}
