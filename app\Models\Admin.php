<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Admin extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $table = 'admins';

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_active',
        'last_login_at',
        'created_by',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Check if admin has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if admin is super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if admin is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Get the admin who created this admin
     */
    public function creator()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * Get admins created by this admin
     */
    public function createdAdmins()
    {
        return $this->hasMany(Admin::class, 'created_by');
    }

    /**
     * Available admin roles
     */
    public static function availableRoles(): array
    {
        return [
            'super_admin' => 'Super Admin',
            'admin' => 'Admin',
            'moderator' => 'Moderator',
        ];
    }

    /**
     * Get role permissions
     */
    public function getPermissions(): array
    {
        $permissions = [
            'super_admin' => [
                'dashboard.view',
                'users.view', 'users.create', 'users.edit', 'users.delete',
                'subscriptions.view', 'subscriptions.edit',
                'plans.view', 'plans.create', 'plans.edit', 'plans.delete',
                'coupons.view', 'coupons.create', 'coupons.edit', 'coupons.delete',
                'payments.view', 'payments.refund',
                'analytics.view', 'analytics.export',
                'admins.view', 'admins.create', 'admins.edit', 'admins.delete',
                'settings.view', 'settings.edit',
                // New modules permissions
                'logs.view', 'logs.export',
                'email_templates.view', 'email_templates.create', 'email_templates.edit', 'email_templates.delete',
                'site_settings.view', 'site_settings.edit',
            ],
            'admin' => [
                'dashboard.view',
                'users.view', 'users.edit',
                'subscriptions.view', 'subscriptions.edit',
                'plans.view', 'plans.edit',
                'coupons.view', 'coupons.create', 'coupons.edit',
                'payments.view',
                'analytics.view', 'analytics.export',
                // New modules permissions (limited access)
                'logs.view',
                'email_templates.view', 'email_templates.create', 'email_templates.edit',
                'site_settings.view',
            ],
            'moderator' => [
                'dashboard.view',
                'users.view',
                'subscriptions.view',
                'plans.view',
                'coupons.view',
                'payments.view',
                'analytics.view',
                // New modules permissions (view only)
                'logs.view',
                'email_templates.view',
            ],
        ];

        return $permissions[$this->role] ?? [];
    }

    /**
     * Check if admin has specific permission
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissions());
    }
}
