<div id="sidebar" class="bg-white w-64 border-r border-gray-200 flex-shrink-0 transform transition-all duration-500 ease-in-out motion-reduce:transition-none h-screen fixed md:static z-10 overflow-y-auto top-[46px]">
    <div class="flex flex-col relative h-full">
        <!-- <button id="closeSidebar" class="md:hidden text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
        </button> -->
        <div class="h-[calc(100%-46px)] md:h-[calc(100vh-122px)] overflow-hidden" id="filtersTour">
            <div class="flex flex-col">
                <div class="flex justify-between items-center px-4 py-2 border-b border-gray-200">
                    <span class="text-gray-500 block uppercase text-sm font-semibold py-[3px]">FILTERS</span>
                    <div class="flex gap-2 items-center">
                        <button
                            class="w-auto flex-shrink-0 flex items-center justify-center px-2 p-1 text-xs border-0 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 transition-colors duration-200 rounded-md refreshFilters" title="Reset Filters" data-business="<?php echo e($business['id']); ?>">
                            <i class="fas fa-sync mr-2"></i> Reset
                        </button>
                        <button
                            class="filter-close w-[24px] h-[24px] flex md:hidden text-xs items-center justify-center p-2 bg-indigo-600 text-white ring-indigo-600 rounded-md" title="Close Filters">
                            <i class="fa-solid fa-xmark"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4 h-[calc(100vh-89px)] md:h-[calc(100vh-135px)] pb-12 overflow-auto">
                    <div class="flex flex-col gap-3">
                        <!-- Date Range Filter -->
                        <div>
                            <span class="block text-xs font-medium text-gray-700 mb-2">Date Range</span>
                            <select id="dateRangeFilter" class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 getFilter" data-business="<?php echo e($business['id']); ?>">
                                <option value="all">All Time</option>
                                <option value="last_week">Last Week</option>
                                <option value="last_month">Last Month</option>
                                <option value="last_3_months">Last 3 Months</option>
                            </select>
                        </div>

                        <!-- Rating Filter -->
                        <div>
                            <span class="block text-xs font-medium text-gray-700 mb-2">Ratings</span>
                            <div class="grid grid-cols-5 gap-1">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php $rating=['ONE', 'TWO' , 'THREE' , 'FOUR' , 'FIVE' ][$i-1] ?>
                                    <button class="rating-btn flex flex-col items-center justify-center p-2 border border-gray-200 rounded hover:bg-gray-50 btnRating getFilter"
                                    data-rating="<?php echo e($rating); ?>"
                                    data-business="<?php echo e($business['id']); ?>"
                                    data-location="<?php echo e($locationId); ?>"
                                    title="<?php echo e($i); ?> star">
                                    <i class="icon-star text-yellow-400"></i>
                                    <span class="text-xs mt-1"><?php echo e($i); ?></span>
                                    </button>
                                    <?php endfor; ?>
                            </div>
                        </div>

                        <!-- Type Filter -->
                        <div>
                            <span class="block text-xs font-medium text-gray-700 mb-2">Review Status</span>
                            <select id="typeFilter" class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 getFilter" data-business="<?php echo e($business['id']); ?>">
                                <option value="all">All Reviews</option>
                                <option value="replied">Replied</option>
                                <option value="not_replied">Not Replied</option>
                            </select>
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <span class="block text-xs font-medium text-gray-700 mb-2">Sort Order</span>
                            <div class="grid grid-cols-2 sm:grid-cols-2 gap-2 hidden">
                                <button type="button" class="w-full flex-shrink-0 rounded-md flex items-center justify-center px-2 p-1 text-xs border-0 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 transition-colors duration-200 sortOrderCls" data-value="desc" data-business="<?php echo e($business['id']); ?>">Newest First</button>
                                <button type="button" class="w-full flex-shrink-0 rounded-md flex items-center justify-center px-2 p-1 text-xs border-0 bg-indigo-50 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 transition-colors duration-200 sortOrderCls" data-value="asc" data-business="<?php echo e($business['id']); ?>">Oldest First</button>
                            </div>
                            <select id="showOrderFilter" class="w-full block border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500" data-business="<?php echo e($business['id']); ?>">
                                <option value="desc" <?php echo e(isset($currentOrder) && $currentOrder == 'desc' ? 'selected' : ''); ?>>Newest Review</option>
                                <option value="asc" <?php echo e(isset($currentOrder) && $currentOrder == 'asc' ? 'selected' : ''); ?>>Oldest Review</option>
                                <option value="desc-reply" <?php echo e(isset($currentOrder) && $currentOrder == 'desc-reply' ? 'selected' : ''); ?>>Newest Reply</option>
                                <option value="asc-reply" <?php echo e(isset($currentOrder) && $currentOrder == 'asc-reply' ? 'selected' : ''); ?>>Oldest Reply</option>
                            </select>
                            <input type="hidden" id="businessId" value="<?php echo e($businessId); ?>">
                            <input type="hidden" id="locationName" value="<?php echo e($selectedLocationName); ?>">
                        </div>
                        <!-- Actions -->
                        <div class="hidden flex md:hidden">
                            <select id="typeFilter" class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 getFilter" data-business="">
                                <option value="Fetch New Reviews">Fetch New Reviews</option>
                                <option value="Fetch New Reviews">Send Auto Reviews</option>
                                <option value="Sync Reviews">Sync Reviews</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex flex-col gap-3 mt-3 border-t border-gray-200 pt-3 md:hidden hidden">
                        <span class="text-gray-500 block uppercase text-sm font-semibold md:hidden">Actions</span>
                        <div class="flex flex-wrap gap-3 md:hidden">
                            <button id="fetchReviewsBtn"
                                class="w-auto text-xs flex items-center justify-center px-2 py-1 md:px-4 mdpy-2 border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors"
                                data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>">
                                <i class="fas fa-arrow-down mr-2" id="fetchReviewsIcon"></i>
                                <span>Fetch New Reviews</span>
                            </button>

                            <!-- <button onclick="sendAutoReplies('<?php echo e($businessId); ?>', '<?php echo e($selectedLocationName); ?>')"
                                class="w-auto text-xs flex items-center justify-center  px-2 py-1 md:px-4 mdpy-2 border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors">
                                <i class="fas fa-reply-all mr-2"></i> Send Auto Replies
                            </button> -->
                            <button class="w-auto text-sm flex items-center justify-center  px-2 py-1 md:px-4 mdpy-2 border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors sendAutoReplies
                                data-business-id=" <?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>">
                                <i class="fas fa-reply-all mr-2"></i> Send Auto Replies
                            </button>

                            <!-- Toast/Progress Container for AJAX feedback -->
                            <div id="reviewsActionToast" class="fixed top-4 right-4 z-50"></div>

                            <button id="syncReviewsBtn"
                                class="w-auto text-xs flex items-center justify-center  px-2 py-1 md:px-4 mdpy-2 border bg-indigo-600 border-indigo-600 text-white hover:text-indigo-600 rounded-md hover:bg-indigo-50 transition-colors"
                                data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>">
                                <i class="fas fa-sync mr-2" id="syncReviewsIcon"></i>
                                <span>Sync Reviews</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overlay for mobile -->
<!-- <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-0 hidden"></div> -->
<!-- <div class="backdrop fixed top-0 left-0 bg-black bg-opacity-30 w-full h-full"></div> --><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partials/reviews-filter.blade.php ENDPATH**/ ?>