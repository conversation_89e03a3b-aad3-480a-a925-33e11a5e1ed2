<?php $__env->startSection('title', 'Log Details'); ?>
<?php $__env->startSection('page-title', 'API Log Details'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .json-viewer {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
    }
    .status-badge {
        @apply px-2 py-1 text-xs font-medium rounded-full;
    }
    .status-success { @apply bg-green-100 text-green-800; }
    .status-error { @apply bg-red-100 text-red-800; }
    .status-warning { @apply bg-yellow-100 text-yellow-800; }
    .log-type-badge {
        @apply px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Log #<?php echo e($log->id); ?></h1>
                <p class="text-gray-600"><?php echo e($log->created_at->format('F j, Y \a\t g:i A')); ?></p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="<?php echo e(route('admin.logs.index')); ?>" 
                   class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Logs
                </a>
            </div>
        </div>
    </div>

    <!-- Basic Information -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Log Type</label>
                    <span class="log-type-badge"><?php echo e(ucfirst(str_replace('_', ' ', $log->log_type))); ?></span>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">HTTP Method</label>
                    <p class="text-sm text-gray-900"><?php echo e($log->method ?? 'GET'); ?></p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status Code</label>
                    <?php if($log->status_code): ?>
                        <span class="status-badge <?php echo e($log->status_code >= 200 && $log->status_code < 300 ? 'status-success' : ($log->status_code >= 400 ? 'status-error' : 'status-warning')); ?>">
                            <?php echo e($log->status_code); ?>

                        </span>
                    <?php else: ?>
                        <span class="text-gray-400">N/A</span>
                    <?php endif; ?>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Duration</label>
                    <p class="text-sm text-gray-900"><?php echo e($log->duration_ms ? number_format($log->duration_ms) . ' ms' : 'N/A'); ?></p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                    <p class="text-sm text-gray-900"><?php echo e($log->ip_address ?? 'N/A'); ?></p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Session ID</label>
                    <p class="text-sm text-gray-900 font-mono"><?php echo e($log->session_id ?? 'N/A'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Endpoint Information -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Endpoint Information</h3>
        </div>
        <div class="p-6">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Endpoint URL</label>
                <div class="bg-gray-50 border border-gray-200 rounded-md p-3">
                    <code class="text-sm text-gray-900 break-all"><?php echo e($log->endpoint); ?></code>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Status Message</label>
                <p class="text-sm text-gray-900"><?php echo e($log->status_message); ?></p>
            </div>

            <?php if($log->error_message): ?>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Error Message</label>
                    <div class="bg-red-50 border border-red-200 rounded-md p-3">
                        <p class="text-sm text-red-900"><?php echo e($log->error_message); ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($log->is_retry): ?>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Retry Information</label>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <p class="text-sm text-yellow-900">
                            This was a retry attempt (Retry count: <?php echo e($log->retry_count); ?>)
                        </p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- User & Business Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- User Information -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">User Information</h3>
            </div>
            <div class="p-6">
                <?php if($log->user): ?>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Name</label>
                            <p class="text-sm text-gray-900"><?php echo e($log->user->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="text-sm text-gray-900"><?php echo e($log->user->email); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">User ID</label>
                            <p class="text-sm text-gray-900"><?php echo e($log->user->id); ?></p>
                        </div>
                        <div class="pt-2">
                            <a href="<?php echo e(route('admin.users.show', $log->user)); ?>" 
                               class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                <i class="fas fa-external-link-alt mr-1"></i>View User Profile
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">No user associated with this log entry.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Business Information -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Business Information</h3>
            </div>
            <div class="p-6">
                <?php if($log->business): ?>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Business Name</label>
                            <p class="text-sm text-gray-900"><?php echo e($log->business->business_name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Business ID</label>
                            <p class="text-sm text-gray-900"><?php echo e($log->business->id); ?></p>
                        </div>
                        <?php if($log->business->location_id): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Location ID</label>
                                <p class="text-sm text-gray-900 font-mono"><?php echo e($log->business->location_id); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">No business associated with this log entry.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Request & Response Data -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Request Data -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Request Data</h3>
            </div>
            <div class="p-6">
                <?php if($log->request_headers): ?>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Request Headers</label>
                        <div class="json-viewer"><?php echo e(json_encode($log->request_headers, JSON_PRETTY_PRINT)); ?></div>
                    </div>
                <?php endif; ?>

                <?php if($log->request_payload): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Request Payload</label>
                        <div class="json-viewer"><?php echo e(json_encode($log->request_payload, JSON_PRETTY_PRINT)); ?></div>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">No request payload data available.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Response Data -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Response Data</h3>
            </div>
            <div class="p-6">
                <?php if($log->response_headers): ?>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Response Headers</label>
                        <div class="json-viewer"><?php echo e(json_encode($log->response_headers, JSON_PRETTY_PRINT)); ?></div>
                    </div>
                <?php endif; ?>

                <?php if($log->response_payload): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Response Payload</label>
                        <div class="json-viewer"><?php echo e(json_encode($log->response_payload, JSON_PRETTY_PRINT)); ?></div>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">No response payload data available.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <?php if($log->user_agent): ?>
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Additional Information</h3>
            </div>
            <div class="p-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">User Agent</label>
                    <div class="bg-gray-50 border border-gray-200 rounded-md p-3">
                        <code class="text-sm text-gray-900 break-all"><?php echo e($log->user_agent); ?></code>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/logs/show.blade.php ENDPATH**/ ?>