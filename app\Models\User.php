<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'google_id',
        'email',
        'image',
        'password',
        'password_created_at',
        'access_token',
        'refresh_token',
        'first_login_popup_shown',
        'phone_number',
        'country_code',
        'stripe_customer_id'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed'
        ];
    }

    public function businesses()
    {
        return $this->hasManyThrough(Business::class, Subscription::class, 'user_id', 'id', 'id', 'business_id');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function hasActiveSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->exists();
    }

    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class)->select('id', 'user_id')->where('status', 'ACTIVE');
    }


    public function getBusinessId()
    {
        return $this->hasOne(Business::class)->select('id', 'user_id');
    }

    public function businessAccounts()
    {
        return $this->hasMany(BusinessAccount::class);
    }

    public function businessAccount()
    {
        return $this->hasOne(BusinessAccount::class);
    }

    /**
     * Get the team memberships for the user.
     */
    public function teamMemberships()
    {
        return $this->hasMany(TeamMember::class);
    }

    public function invitedTeamMembers()
    {
        return $this->hasMany(TeamMember::class, 'invited_by')
            ->where('status', 'active');
    }

    /**
     * Get the team invitations sent by this user.
     */
    public function sentInvitations()
    {
        return $this->hasMany(TeamMember::class, 'invited_by');
    }

    public function associateBusinesses()
    {
        return $this->hasMany(AssociateBusiness::class);
    }

    public function invitedMembers()
    {
        return $this->hasMany(TeamMember::class);
    }

    public function pendingInvitations()
    {
        return $this->hasMany(TeamMember::class, 'invited_by')
            ->where('status', 'pending');
    }

    public function businessPreferences()
    {
        return $this->hasMany(UserBusinessPreference::class);
    }

    public function preference()
    {
        return $this->hasOne(UserBusinessPreference::class)->where('is_active', 1);
    }
}
