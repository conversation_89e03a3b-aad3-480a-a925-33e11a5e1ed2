<!DOCTYPE html>
<html>

<head>
    <title>Google Reviews</title>
    <style>
        body {
            font-family: sans-serif;
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 6px;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>

<body>
    <h2>Google Reviews Export</h2>
    <table>
        <thead>
            <tr>
                <th>Reviewer Name</th>
                <th>Comment</th>
                <th>Day of review</th>
                <th>Reply Comment</th>
                <th>Replied By</th>
                <th>Replied On</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($reviews as $review)
            @php
            $reply = $review->repliesWithUser->first();
            $reviewDate = $review->created_at_google
            ? \Carbon\Carbon::parse($review->created_at_google)->format('j F Y')
            : '-';
            $replyComment = $reply->comment ?? '-';
            $repliedBy = $reply?->reply_by === 0
            ? 'Smart Reply System'
            : ($reply?->user?->name ?? '-');
            $repliedOn = $reply?->updated_at_google
            ? \Carbon\Carbon::parse($reply->updated_at_google)->format('j F Y')
            : '-';
            @endphp
            <tr>
                <td>{{ $review->reviewer_name ?? '-' }}</td>
                <td>{{ $review->comment ?? '-' }}</td>
                <td>{{ \Carbon\Carbon::parse($review->created_at_google)->format('l, j F Y') }}</td>
                <td>{{ $replyComment }}</td>
                <td>{{ $repliedBy }}</td>
                <td>{{ $repliedOn }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</body>

</html>