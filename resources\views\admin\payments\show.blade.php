@extends('admin.layouts.app')

@section('title', 'Payment Details - #' . $payment->id)

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <div class="flex items-center">
                <a href="{{ route('admin.payments.index') }}" class="text-gray-500 hover:text-gray-700 mr-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-bold text-gray-900">Payment Details</h1>
            </div>
            <p class="text-gray-600 mt-1">Payment ID: #{{ $payment->id }}</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-0">
            @if($payment->payment_status === 'SUCCESS' && auth('admin')->user()->hasPermission('payments.refund'))
                <button onclick="refundPayment({{ $payment->id }})" 
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-undo mr-2"></i>Process Refund
                </button>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Payment Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Payment Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                {{ $payment->payment_status === 'SUCCESS' ? 'bg-green-100 text-green-800' : 
                                   ($payment->payment_status === 'FAILED' ? 'bg-red-100 text-red-800' : 
                                   ($payment->payment_status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 
                                   ($payment->payment_status === 'REFUNDED' ? 'bg-gray-100 text-gray-800' : 'bg-gray-100 text-gray-800'))) }}">
                                {{ ucfirst(strtolower($payment->payment_status)) }}
                            </span>
                        </div>

                        <!-- Payment Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Type</label>
                            <p class="text-sm text-gray-900">{{ $payment->payment_type }}</p>
                        </div>

                        <!-- Gateway -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Gateway</label>
                            <p class="text-sm text-gray-900">{{ $payment->gateway_name }}</p>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                            <p class="text-sm text-gray-900">{{ $payment->payment_method ?? 'N/A' }}</p>
                        </div>

                        <!-- Transaction ID -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Transaction ID</label>
                            <p class="text-sm text-gray-900 font-mono">{{ $payment->transaction_id ?? 'N/A' }}</p>
                        </div>

                        <!-- Order ID -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Order ID</label>
                            <p class="text-sm text-gray-900 font-mono">{{ $payment->order_id ?? 'N/A' }}</p>
                        </div>

                        <!-- Payment Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Date</label>
                            <p class="text-sm text-gray-900">
                                {{ $payment->payment_date ? $payment->payment_date->format('M d, Y g:i A') : 'N/A' }}
                            </p>
                        </div>

                        <!-- Billing Cycle -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Billing Cycle</label>
                            <p class="text-sm text-gray-900">{{ ucfirst($payment->billing_cycle) }}</p>
                        </div>

                        <!-- Currency -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                            <p class="text-sm text-gray-900">{{ $payment->currency }}</p>
                        </div>

                        <!-- Coupon Code -->
                        @if($payment->coupon_code)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Coupon Code</label>
                            <p class="text-sm text-gray-900 font-mono">{{ $payment->coupon_code }}</p>
                        </div>
                        @endif

                        <!-- Refund Information -->
                        @if($payment->payment_status === 'REFUNDED')
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Refund Information</label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-xs text-gray-500">Refund ID</p>
                                        <p class="text-sm text-gray-900 font-mono">{{ $payment->refund_id ?? 'N/A' }}</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Refund Date</p>
                                        <p class="text-sm text-gray-900">
                                            {{ $payment->refund_date ? $payment->refund_date->format('M d, Y g:i A') : 'N/A' }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Amount Breakdown -->
            <div class="bg-white rounded-lg shadow mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Amount Breakdown</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Original Amount -->
                        @if($payment->original_amount)
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Original Amount</span>
                            <span class="text-sm font-medium text-gray-900">
                                {{ $payment->currency }} {{ number_format($payment->original_amount, 2) }}
                            </span>
                        </div>
                        @endif

                        <!-- Discount Amount -->
                        @if($payment->discount_amount > 0)
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Discount</span>
                            <span class="text-sm font-medium text-green-600">
                                -{{ $payment->currency }} {{ number_format($payment->discount_amount, 2) }}
                            </span>
                        </div>
                        @endif

                        <!-- Tax Amount -->
                        @if($payment->tax_amount > 0)
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Tax</span>
                            <span class="text-sm font-medium text-gray-900">
                                {{ $payment->currency }} {{ number_format($payment->tax_amount, 2) }}
                            </span>
                        </div>
                        @endif

                        <hr class="border-gray-200">

                        <!-- Final Amount -->
                        <div class="flex justify-between items-center">
                            <span class="text-base font-medium text-gray-900">Total Amount</span>
                            <span class="text-lg font-bold text-gray-900">
                                {{ $payment->currency }} {{ number_format($payment->amount, 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            @if($payment->metadata)
            <div class="bg-white rounded-lg shadow mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Additional Information</h3>
                </div>
                <div class="p-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <pre class="text-sm text-gray-700 whitespace-pre-wrap">{{ json_encode($payment->metadata, JSON_PRETTY_PRINT) }}</pre>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- User Information -->
            @if($payment->user)
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">User Information</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-900">{{ $payment->user->name }}</h4>
                            <p class="text-sm text-gray-500">{{ $payment->user->email }}</p>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</label>
                            <p class="text-sm text-gray-900">#{{ $payment->user->id }}</p>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider">Member Since</label>
                            <p class="text-sm text-gray-900">{{ $payment->user->created_at->format('M d, Y') }}</p>
                        </div>
                        <div class="pt-3">
                            <a href="{{ route('admin.users.show', $payment->user) }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View User Profile →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Plan Information -->
            @if($payment->plan)
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Plan Information</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</label>
                            <p class="text-sm font-medium text-gray-900">{{ $payment->plan->name }}</p>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider">Plan ID</label>
                            <p class="text-sm text-gray-900">#{{ $payment->plan->id }}</p>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Price</label>
                            <p class="text-sm text-gray-900">
                                {{ $payment->plan->currency }} {{ number_format($payment->plan->price, 2) }}
                                @if($payment->billing_cycle === 'annual' && $payment->plan->annual_discount_percentage > 0)
                                    <span class="text-xs text-green-600">({{ $payment->plan->annual_discount_percentage }}% annual discount)</span>
                                @endif
                            </p>
                        </div>
                        <div class="pt-3">
                            <a href="{{ route('admin.plans.show', $payment->plan) }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View Plan Details →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <a href="{{ route('admin.payments.index', ['search' => $payment->user->email ?? '']) }}" 
                           class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200">
                            <i class="fas fa-search mr-2"></i>View User's Other Payments
                        </a>
                        @if($payment->plan)
                        <a href="{{ route('admin.payments.index', ['plan_id' => $payment->plan->id]) }}" 
                           class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200">
                            <i class="fas fa-list mr-2"></i>View Plan Payments
                        </a>
                        @endif
                        <a href="{{ route('admin.payments.index', ['gateway' => $payment->gateway_name]) }}" 
                           class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200">
                            <i class="fas fa-credit-card mr-2"></i>View {{ $payment->gateway_name }} Payments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refundPayment(paymentId) {
    if (confirm('Are you sure you want to refund this payment? This action cannot be undone.')) {
        fetch(`/admin/payments/${paymentId}/refund`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error processing refund');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error processing refund');
        });
    }
}
</script>
@endsection
