<?php

namespace App\Http\Controllers;

use App\Models\BusinessAccount;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PermissionController extends Controller
{
    /**
     * Get the current user's permissions for a specific business account
     *
     * @param int $businessAccountId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserPermissions($businessAccountId)
    {
        $businessAccount = BusinessAccount::findOrFail($businessAccountId);
        $user = Auth::user();
        
        // Check if user is the owner of the business account
        $isOwner = ($user->id === $businessAccount->user_id);
        
        if ($isOwner) {
            // Business owner has all permissions
            return response()->json([
                'isOwner' => true,
                'role' => 'owner',
                'permissions' => array_keys(\App\Models\Permission::availablePermissions())
            ]);
        }
        
        // Get team member record
        $teamMember = TeamMember::where('user_id', $user->id)
            ->where('business_account_id', $businessAccountId)
            ->where('status', 'active')
            ->with('permissions')
            ->first();
        
        if (!$teamMember) {
            return response()->json([
                'isOwner' => false,
                'role' => null,
                'permissions' => []
            ]);
        }
        
        // For admin role, return all permissions
        if ($teamMember->isAdmin()) {
            return response()->json([
                'isOwner' => false,
                'role' => 'admin',
                'permissions' => array_keys(\App\Models\Permission::availablePermissions())
            ]);
        }
        
        // For regular members, return only granted permissions
        $grantedPermissions = $teamMember->permissions
            ->where('is_granted', true)
            ->pluck('permission_name')
            ->toArray();
        
        return response()->json([
            'isOwner' => false,
            'role' => 'member',
            'permissions' => $grantedPermissions
        ]);
    }
}
