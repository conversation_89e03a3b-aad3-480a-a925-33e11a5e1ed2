<?php

namespace App\Jobs;

use App\Helpers\ReviewHelper;
use App\Services\GoogleBusinessService;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Models\Business;
use App\Models\BusinessAccount;
use App\Models\GoogleReview;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class FetchGoogleReviewsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $location;
    protected $businessId;

    /**
     * Create a new job instance.
     */
    public function __construct($location, $businessId)
    {
        $this->location = $location;
        $this->businessId = $businessId;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        // Initialize response counters
        $response = [
            'success' => true,
            'new_reviews_added' => 0,
            'reviews_updated' => 0,
            'new_replies_added' => 0,
            'replies_updated' => 0,
            'message' => ''
        ];

        $allReviews = [];
        $nextPageToken = null;
        $business = Business::where('location_name', $this->location)->first();

        if (!$business) {
            Log::error('Business not found for location: ' . $this->location);
            return response()->json([
                'success' => false,
                'message' => 'Business not found for location: ' . $this->location,
                'status' => 404
            ]);
        }

        $businessGoogleId = $this->businessId;
        $businessAccount = BusinessAccount::where('business_google_id', $businessGoogleId)->first();

        if (!$businessAccount || !$businessAccount->business_google_token) {
            Log::error('Business account or token not found for ID: ' . $businessGoogleId);
            return response()->json([
                'success' => false,
                'message' => 'Business account or token not found',
                'status' => 404
            ]);
        }

        $locationId = explode('/', $this->location)[1];
        $nextPageToken = $business->next_token;

        try {
            // Initialize GoogleBusinessService
            $googleBusinessService = new GoogleBusinessService($businessAccount->business_google_token);
            $istokenvalid = $googleBusinessService->isTokenValid($businessAccount->business_google_token);
            if(!$istokenvalid) {
                $newToken = $googleBusinessService->refreshAccessToken($businessAccount);
                if ($newToken) {
                    $googleBusinessService->setAccessToken($newToken);
                }
            }

            do {
                $data = $googleBusinessService->getReviewsWithPagination($locationId, $businessGoogleId, $businessAccount->business_google_token, $nextPageToken, 50);

                if (isset($data['error'])) {
                    Log::error('Google API error: ' . $data['error']['message']);
                    return response()->json([
                        'success' => false,
                        'message' => 'Google API error: ' . $data['error']['message'],
                        'status' => 500
                    ]);
                }

                $reviews = $data['reviews'] ?? [];
                $allReviews = array_merge($allReviews, $reviews);

                $nextPageToken = $data['nextPageToken'] ?? null;
                // Update token
                $business->update(['next_token' => $nextPageToken]);

                // Process reviews and track operations
                foreach ($reviews as $review) {
                    $reviewId = $review['reviewId'];
                    $existingReview = GoogleReview::where('review_id', $reviewId)
                        ->where('location_id', $locationId)
                        ->where('account_id', $businessGoogleId)
                        ->first();

                    if ($existingReview) {
                        // Check if anything needs update
                        $shouldUpdate = false;
                        $updateData = [];

                        $reviewData = [
                            'reviewer_name'     => $review['reviewer']['displayName'] ?? null,
                            'reviewer_photo'    => $review['reviewer']['profilePhotoUrl'] ?? null,
                            'star_rating'       => $review['starRating'] ?? null,
                            'comment'           => $review['comment'] ?? null,
                            'created_at_google' => isset($review['createTime']) ? Carbon::parse($review['createTime'])->toDateTimeString() : null,
                            'updated_at_google' => isset($review['updateTime']) ? Carbon::parse($review['updateTime'])->toDateTimeString() : null,
                        ];

                        foreach ($reviewData as $key => $value) {
                            if ($existingReview->$key !== $value) {
                                $shouldUpdate = true;
                                $updateData[$key] = $value;
                            }
                        }

                        if ($shouldUpdate) {
                            $existingReview->update($updateData);
                            $response['reviews_updated']++;
                            Log::info('Updated existing review', [
                                'review_id' => $reviewId,
                                'changes' => $updateData
                            ]);
                        }

                        $mainReview = $existingReview;
                    } else {
                        // Create new review
                        $photoUrls = []; // Initialize this variable to avoid undefined variable error
                        $mainReview = GoogleReview::create([
                            'review_id'         => $review['reviewId'],
                            'location_id'       => $locationId,
                            'account_id'        => $businessGoogleId,
                            'parent_id'         => null,
                            'reviewer_name'     => $review['reviewer']['displayName'] ?? null,
                            'reviewer_photo'    => $review['reviewer']['profilePhotoUrl'] ?? null,
                            'star_rating'       => $review['starRating'] ?? null,
                            'comment'           => $review['comment'] ?? null,
                            'review_photos'     => !empty($photoUrls) ? $photoUrls : null,
                            'created_at_google' => isset($review['createTime']) ? Carbon::parse($review['createTime'])->toDateTimeString() : null,
                            'updated_at_google' => isset($review['updateTime']) ? Carbon::parse($review['updateTime'])->toDateTimeString() : null,
                        ]);
                        $response['new_reviews_added']++;
                        Log::info('New review added: ' . $mainReview->id);
                    }

                    // Process review reply if exists
                    if (isset($review['reviewReply']) && !empty($review['reviewReply']['comment'])) {
                        $existingReply = GoogleReview::where('parent_id', $mainReview->id)
                            ->whereNull('review_id')
                            ->first();

                        if ($existingReply) {
                            // Check if reply needs update
                            $replyComment = $review['reviewReply']['comment'];
                            $replyUpdateTime = isset($review['reviewReply']['updateTime']) ?
                                Carbon::parse($review['reviewReply']['updateTime'])->toDateTimeString() : null;

                            $updateData = [];
                            $shouldUpdate = false;

                            if ($existingReply->comment !== $replyComment) {
                                $updateData['comment'] = $replyComment;
                                $shouldUpdate = true;
                            }

                            if ($existingReply->updated_at_google !== $replyUpdateTime) {
                                $updateData['updated_at_google'] = $replyUpdateTime;
                                $shouldUpdate = true;
                            }

                            if ($shouldUpdate) {
                                $existingReply->update($updateData);
                                $response['replies_updated']++;
                                Log::info('Updated existing reply', [
                                    'review_id' => $mainReview->id,
                                    'changes' => $updateData
                                ]);
                            }
                        } else {
                            // Create new reply
                            GoogleReview::create([
                                'review_id'         => null,
                                'location_id'       => $locationId,
                                'account_id'        => $businessGoogleId,
                                'parent_id'         => $mainReview->id,
                                'reviewer_name'     => 'Admin',
                                'reviewer_photo'    => null,
                                'star_rating'       => null,
                                'comment'           => $review['reviewReply']['comment'],
                                'created_at_google' => null,
                                'updated_at_google' => isset($review['reviewReply']['updateTime']) ?
                                    Carbon::parse($review['reviewReply']['updateTime'])->toDateTimeString() : null,
                            ]);
                            $response['new_replies_added']++;
                            Log::info('New reply added for review: ' . $mainReview->id);
                        }
                    }
                }
            } while ($nextPageToken);

            $business->update(['next_token' => null]);
            Log::info('All reviews fetched. next_token reset to null.');

            // Generate appropriate message based on operations
            if (
                $response['new_reviews_added'] > 0 || $response['reviews_updated'] > 0 ||
                $response['new_replies_added'] > 0 || $response['replies_updated'] > 0
            ) {
                $message = [];

                if ($response['new_reviews_added'] > 0) {
                    $message[] = $response['new_reviews_added'] . ' new review(s) added';
                }
                if ($response['reviews_updated'] > 0) {
                    $message[] = $response['reviews_updated'] . ' review(s) updated';
                }
                if ($response['new_replies_added'] > 0) {
                    $message[] = $response['new_replies_added'] . ' new reply(s) added';
                }
                if ($response['replies_updated'] > 0) {
                    $message[] = $response['replies_updated'] . ' reply(s) updated';
                }

                $response['message'] = implode(', ', $message);
            } else {
                $response['message'] = 'Already latest reviews and replies added';
            }

            return response()->json([
                'message' => $response['message'],
                'success' => true,
                'status' => 200,
                'data' => $response
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch reviews', [
                'message' => $e->getMessage(),
                'location_id' => $locationId
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch reviews: ' . $e->getMessage(),
                'status' => 500
            ]);
        }
    }
}
