<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use App\Models\Business;
use App\Models\TeamMember;
use App\Models\ReplyLog;
use App\Models\BusinessActivityLog;
use Carbon\Carbon;

class SubscriptionService
{
    /**
     * Get user's active subscription
     */
    public function getActiveSubscription(int $userId): ?Subscription
    {
        return Subscription::with('plan')
            ->where('user_id', $userId)
            ->where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->first();
    }

    /**
     * Check if user can connect more businesses
     */
    public function canConnectBusiness(int $userId): array
    {
        $subscription = $this->getActiveSubscription($userId);
        
        if (!$subscription) {
            return [
                'allowed' => false,
                'message' => 'No active subscription found. Please subscribe to a plan.',
                'current_count' => 0,
                'limit' => 0
            ];
        }

        $currentCount = Business::where('user_id', $userId)->count();
        $limit = $subscription->plan->getBusinessConnectionsLimit();

        return [
            'allowed' => $currentCount < $limit,
            'message' => $currentCount >= $limit ? 
                "You have reached your business connection limit of {$limit}. Please upgrade your plan." : 
                "You can connect " . ($limit - $currentCount) . " more business(es).",
            'current_count' => $currentCount,
            'limit' => $limit
        ];
    }

    /**
     * Check if user can invite more team members
     */
    public function canInviteTeamMember(int $userId, int $businessId): array
    {
        $subscription = $this->getActiveSubscription($userId);
        
        if (!$subscription) {
            return [
                'allowed' => false,
                'message' => 'No active subscription found. Please subscribe to a plan.',
                'current_count' => 0,
                'limit' => 0
            ];
        }

        $currentCount = TeamMember::where('business_id', $businessId)
            ->where('status', 'active')
            ->count();
        
        // Add 1 for the business owner
        $totalMembers = $currentCount + 1;
        $limit = $subscription->plan->getTeamMembersLimit();

        return [
            'allowed' => $totalMembers < $limit,
            'message' => $totalMembers >= $limit ? 
                "You have reached your team member limit of {$limit}. Please upgrade your plan." : 
                "You can invite " . ($limit - $totalMembers) . " more team member(s).",
            'current_count' => $totalMembers,
            'limit' => $limit
        ];
    }

    /**
     * Check if user can send more replies this month
     */
    public function canSendReply(int $userId): array
    {
        $subscription = $this->getActiveSubscription($userId);

        if (!$subscription) {
            return [
                'allowed' => false,
                'message' => 'No active subscription found. Please subscribe to a plan.',
                'current_count' => 0,
                'limit' => 0,
                'remaining' => 0
            ];
        }

        if ($subscription->plan->hasUnlimitedReplies()) {
            $currentCount = $this->getMonthlyReplyCount($userId);
            return [
                'allowed' => true,
                'message' => 'Unlimited replies available.',
                'current_count' => $currentCount,
                'limit' => -1,
                'remaining' => -1
            ];
        }

        $currentCount = $this->getMonthlyReplyCount($userId);
        $limit = $subscription->plan->getMonthlyReplyLimit();
        $remaining = max(0, $limit - $currentCount);

        return [
            'allowed' => $currentCount < $limit,
            'message' => $currentCount >= $limit ?
                "You have reached your monthly reply limit of {$limit}. Please upgrade your plan or wait for next month." :
                "You have {$remaining} replies remaining this month.",
            'current_count' => $currentCount,
            'limit' => $limit,
            'remaining' => $remaining
        ];
    }

    /**
     * Check if user has access to specific feature
     */
    public function hasFeatureAccess(int $userId, string $feature): array
    {
        $subscription = $this->getActiveSubscription($userId);
        
        if (!$subscription) {
            return [
                'allowed' => false,
                'message' => 'No active subscription found. Please subscribe to a plan.'
            ];
        }

        $hasAccess = $subscription->plan->hasFeature($feature);

        return [
            'allowed' => $hasAccess,
            'message' => $hasAccess ? 
                'Feature access granted.' : 
                'This feature is not available in your current plan. Please upgrade to access this feature.'
        ];
    }

    /**
     * Check API access level
     */
    public function hasApiAccess(int $userId, string $level = 'basic'): array
    {
        $subscription = $this->getActiveSubscription($userId);
        
        if (!$subscription) {
            return [
                'allowed' => false,
                'message' => 'No active subscription found. Please subscribe to a plan.',
                'current_level' => 'none'
            ];
        }

        $hasAccess = $subscription->plan->hasApiAccess($level);

        return [
            'allowed' => $hasAccess,
            'message' => $hasAccess ? 
                'API access granted.' : 
                "Your plan does not include {$level} API access. Please upgrade your plan.",
            'current_level' => $subscription->plan->api_access_level
        ];
    }

    /**
     * Get subscription usage summary
     */
    public function getUsageSummary(int $userId): array
    {
        $subscription = $this->getActiveSubscription($userId);
        
        if (!$subscription) {
            return [
                'subscription' => null,
                'businesses' => ['current' => 0, 'limit' => 0],
                'team_members' => ['current' => 0, 'limit' => 0],
                'replies' => ['current' => 0, 'limit' => 0, 'remaining' => 0],
                'features' => []
            ];
        }

        $businessCount = Business::where('user_id', $userId)->count();
        $replyCount = $this->getMonthlyReplyCount($userId);
        $remaining = $subscription->plan->hasUnlimitedReplies() ? -1 :
            max(0, $subscription->plan->getMonthlyReplyLimit() - $replyCount);

        return [
            'subscription' => [
                'plan_name' => $subscription->plan->name,
                'status' => $subscription->status,
                'expires_at' => $subscription->expiry_date,
                'days_remaining' => Carbon::parse($subscription->expiry_date)->diffInDays(now())
            ],
            'businesses' => [
                'current' => $businessCount,
                'limit' => $subscription->plan->getBusinessConnectionsLimit()
            ],
            'team_members' => [
                'current' => 1, // Owner + team members
                'limit' => $subscription->plan->getTeamMembersLimit()
            ],
            'replies' => [
                'current' => $replyCount,
                'limit' => $subscription->plan->getMonthlyReplyLimit(),
                'remaining' => $remaining
            ],
            'features' => [
                'data_export' => $subscription->plan->hasFeature('data_export_enabled'),
                'smart_replies' => $subscription->plan->hasFeature('smart_replies_enabled'),
                'scheduled_auto_replies' => $subscription->plan->hasFeature('scheduled_auto_replies_enabled'),
                'api_access' => $subscription->plan->getFeatureValue('api_access_level'),
                'analytics_level' => $subscription->plan->getFeatureValue('analytics_level'),
                'ai_customization' => $subscription->plan->getFeatureValue('ai_customization_level'),
                'template_access' => $subscription->plan->getFeatureValue('template_access'),
                'settings_access' => $subscription->plan->hasFeature('settings_access_enabled'),
                'beta_features' => $subscription->plan->hasFeature('beta_features_enabled'),
                'support_level' => $subscription->plan->getSupportLevel()
            ]
        ];
    }

    /**
     * Log a reply for tracking
     */
    public function logReply(int $userId, int $businessId, array $data = []): bool
    {
        $subscription = $this->getActiveSubscription($userId);
        
        if (!$subscription) {
            return false;
        }

        ReplyLog::logReply($userId, $subscription->id, $businessId, $data);
        return true;
    }

    /**
     * Check if user needs to upgrade for a specific action
     */
    public function requiresUpgrade(int $userId, string $action, array $params = []): array
    {
        switch ($action) {
            case 'connect_business':
                return $this->canConnectBusiness($userId);

            case 'invite_team_member':
                $businessId = $params['business_id'] ?? 0;
                return $this->canInviteTeamMember($userId, $businessId);

            case 'send_reply':
                return $this->canSendReply($userId);

            case 'feature_access':
                $feature = $params['feature'] ?? '';
                return $this->hasFeatureAccess($userId, $feature);

            case 'api_access':
                $level = $params['level'] ?? 'basic';
                return $this->hasApiAccess($userId, $level);

            default:
                return [
                    'allowed' => false,
                    'message' => 'Unknown action specified.'
                ];
        }
    }

    /**
     * Get monthly reply count from business activity logs
     */
    public function getMonthlyReplyCount(int $userId): int
    {
        return BusinessActivityLog::forUser($userId)
                                 ->byActivityType('send_reply')
                                 ->successful()
                                 ->countsTowardLimit()
                                 ->thisMonth()
                                 ->sum('activity_count');
    }

    /**
     * Get business connection count from business activity logs
     */
    public function getBusinessConnectionCount(int $userId): int
    {
        return BusinessActivityLog::forUser($userId)
                                 ->byActivityType('connect_business')
                                 ->successful()
                                 ->countsTowardLimit()
                                 ->count();
    }

    /**
     * Get team member invitation count from business activity logs
     */
    public function getTeamMemberInvitationCount(int $userId): int
    {
        return BusinessActivityLog::forUser($userId)
                                 ->byActivityType('invite_team_member')
                                 ->successful()
                                 ->countsTowardLimit()
                                 ->count();
    }

    /**
     * Get activity count for specific type and time period
     */
    public function getActivityCount(int $userId, string $activityType, ?Carbon $startDate = null, ?Carbon $endDate = null): int
    {
        $query = BusinessActivityLog::forUser($userId)
                                   ->byActivityType($activityType)
                                   ->successful()
                                   ->countsTowardLimit();

        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }

        return $query->sum('activity_count');
    }

    /**
     * Get comprehensive usage statistics including activity logs
     */
    public function getEnhancedUsageSummary(int $userId): array
    {
        $subscription = $this->getActiveSubscription($userId);

        if (!$subscription) {
            return [
                'subscription' => null,
                'businesses' => ['current' => 0, 'limit' => 0, 'percentage' => 0],
                'team_members' => ['current' => 0, 'limit' => 0, 'percentage' => 0],
                'replies' => ['current' => 0, 'limit' => 0, 'remaining' => 0, 'percentage' => 0],
                'activities' => [],
                'features' => [],
                'upgrade_suggestions' => []
            ];
        }

        // Get current counts
        $businessCount = Business::where('user_id', $userId)->count();
        $replyCount = $this->getMonthlyReplyCount($userId);
        $teamMemberCount = TeamMember::whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('status', 'active')->count() + 1; // +1 for owner

        // Get limits
        $businessLimit = $subscription->plan->getBusinessConnectionsLimit();
        $replyLimit = $subscription->plan->getMonthlyReplyLimit();
        $teamLimit = $subscription->plan->getTeamMembersLimit();
        $hasUnlimitedReplies = $subscription->plan->hasUnlimitedReplies();

        // Calculate percentages
        $businessPercentage = $businessLimit > 0 ? ($businessCount / $businessLimit) * 100 : 0;
        $replyPercentage = $hasUnlimitedReplies ? 0 : ($replyLimit > 0 ? ($replyCount / $replyLimit) * 100 : 0);
        $teamPercentage = $teamLimit > 0 ? ($teamMemberCount / $teamLimit) * 100 : 0;

        // Get activity breakdown
        $activityBreakdown = $this->getActivityBreakdown($userId);

        return [
            'subscription' => [
                'plan_name' => $subscription->plan->name,
                'status' => $subscription->status,
                'expires_at' => $subscription->expiry_date,
                'days_remaining' => Carbon::parse($subscription->expiry_date)->diffInDays(now())
            ],
            'businesses' => [
                'current' => $businessCount,
                'limit' => $businessLimit,
                'percentage' => $businessPercentage
            ],
            'team_members' => [
                'current' => $teamMemberCount,
                'limit' => $teamLimit,
                'percentage' => $teamPercentage
            ],
            'replies' => [
                'current' => $replyCount,
                'limit' => $hasUnlimitedReplies ? 'Unlimited' : $replyLimit,
                'remaining' => $hasUnlimitedReplies ? -1 : max(0, $replyLimit - $replyCount),
                'percentage' => $replyPercentage,
                'unlimited' => $hasUnlimitedReplies
            ],
            'activities' => $activityBreakdown,
            'features' => $subscription->features ?? [],
            'upgrade_suggestions' => $this->getUpgradeSuggestions($userId, $subscription)
        ];
    }

    /**
     * Get activity breakdown for user
     */
    private function getActivityBreakdown(int $userId): array
    {
        // Get this month's activities
        $thisMonth = BusinessActivityLog::forUser($userId)
                                       ->thisMonth()
                                       ->successful()
                                       ->selectRaw('activity_type, COUNT(*) as count, SUM(activity_count) as total_count')
                                       ->groupBy('activity_type')
                                       ->get()
                                       ->keyBy('activity_type');

        // Get all-time activities
        $allTime = BusinessActivityLog::forUser($userId)
                                     ->successful()
                                     ->selectRaw('activity_type, COUNT(*) as count, SUM(activity_count) as total_count')
                                     ->groupBy('activity_type')
                                     ->get()
                                     ->keyBy('activity_type');

        return [
            'this_month' => $thisMonth->toArray(),
            'all_time' => $allTime->toArray()
        ];
    }

    /**
     * Get upgrade suggestions based on usage
     */
    private function getUpgradeSuggestions(int $userId, Subscription $subscription): array
    {
        $suggestions = [];
        $summary = $this->getEnhancedUsageSummary($userId);

        // Business limit suggestions
        if ($summary['businesses']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'business_limit',
                'message' => 'You\'re approaching your business connection limit. Consider upgrading for more connections.',
                'urgency' => $summary['businesses']['percentage'] > 95 ? 'high' : 'medium',
                'current' => $summary['businesses']['current'],
                'limit' => $summary['businesses']['limit']
            ];
        }

        // Reply limit suggestions
        if (!$summary['replies']['unlimited'] && $summary['replies']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'reply_limit',
                'message' => 'You\'re approaching your monthly reply limit. Upgrade for more replies or unlimited access.',
                'urgency' => $summary['replies']['percentage'] > 95 ? 'high' : 'medium',
                'current' => $summary['replies']['current'],
                'limit' => $summary['replies']['limit']
            ];
        }

        // Team member limit suggestions
        if ($summary['team_members']['percentage'] > 80) {
            $suggestions[] = [
                'type' => 'team_limit',
                'message' => 'You\'re approaching your team member limit. Upgrade to invite more team members.',
                'urgency' => $summary['team_members']['percentage'] > 95 ? 'high' : 'medium',
                'current' => $summary['team_members']['current'],
                'limit' => $summary['team_members']['limit']
            ];
        }

        return $suggestions;
    }
}
