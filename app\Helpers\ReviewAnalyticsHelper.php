<?php

namespace App\Helpers;

use App\Models\Review;

class ReviewAnalyticsHelper
{
    public static function getAnalytics($reviews)
    {
        $totalReviews = count($reviews);
        $totalReplies = 0;
        $totalRating = 0;
        $responseTimes = [];

        foreach ($reviews as $review) {
            $totalRating += $review->rating;

            if ($review->reply) {
                $totalReplies++;
                $responseTimes[] = strtotime($review->reply->created_at) - strtotime($review->created_at);
            }
        }

        $stats = [
            'overall_rating' => $totalReviews > 0 ? round($totalRating / $totalReviews, 1) : 0,
            'total_reviews' => $totalReviews,
            'response_rate' => $totalReviews > 0 ? round(($totalReplies / $totalReviews) * 100) : 0,
            'avg_response_time' => $responseTimes ? self::formatTime(array_sum($responseTimes) / count($responseTimes)) : 'N/A',
            'rating_distribution' => self::calculateRatingDistribution($reviews),
            'sentiment_analysis' => self::calculateSentimentAnalysis($reviews),
        ];

        return $stats;
    }

    private static function calculateRatingDistribution($reviews)
    {
        $distribution = [
            1 => 0,
            2 => 0,
            3 => 0,
            4 => 0,
            5 => 0,
        ];

        foreach ($reviews as $review) {
            $distribution[$review->rating]++;
        }

        return $distribution;
    }

    private static function calculateSentimentAnalysis($reviews)
    {
        $positive = 0;
        $neutral = 0;
        $negative = 0;

        foreach ($reviews as $review) {
            // Simple sentiment analysis based on rating
            if ($review->rating >= 4) {
                $positive++;
            } elseif ($review->rating == 3) {
                $neutral++;
            } else {
                $negative++;
            }
        }

        $total = count($reviews);
        return [
            'positive' => $total > 0 ? round(($positive / $total) * 100) : 0,
            'neutral' => $total > 0 ? round(($neutral / $total) * 100) : 0,
            'negative' => $total > 0 ? round(($negative / $total) * 100) : 0,
        ];
    }

    private static function formatTime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return $hours . 'h ' . $minutes . 'm';
    }
}
