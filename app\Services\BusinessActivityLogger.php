<?php

namespace App\Services;

use App\Models\BusinessActivityLog;
use App\Models\Business;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Exception;

class BusinessActivityLogger
{
    /**
     * Log a business activity
     * 
     * @param array $data Activity data
     * @return BusinessActivityLog|null
     */
    public static function log(array $data): ?BusinessActivityLog
    {
        try {
            // Validate required fields
            if (!self::validateRequiredFields($data)) {
                Log::warning('BusinessActivityLogger: Missing required fields', $data);
                return null;
            }

            // Prepare activity data
            $activityData = self::prepareActivityData($data);

            // Create the log entry
            return BusinessActivityLog::create($activityData);

        } catch (Exception $e) {
            // Log the error but don't break the main flow
            Log::error('BusinessActivityLogger: Failed to log activity', [
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Log business connection activity
     */
    public static function logBusinessConnection(int $businessId, int $userId, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => 'connect_business',
            'activity_category' => 'business_management',
            'activity_description' => 'Business location connected to account',
            'counts_toward_limit' => true,
            'metadata' => $metadata
        ]);
    }

    /**
     * Log business disconnection activity
     */
    public static function logBusinessDisconnection(int $businessId, int $userId, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => 'disconnect_business',
            'activity_category' => 'business_management',
            'activity_description' => 'Business location disconnected from account',
            'counts_toward_limit' => false,
            'metadata' => $metadata
        ]);
    }

    /**
     * Log reply sent activity
     */
    public static function logReplySent(int $businessId, int $userId, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => 'send_reply',
            'activity_category' => 'review_management',
            'activity_description' => 'Reply sent to customer review',
            'counts_toward_limit' => true,
            'metadata' => $metadata
        ]);
    }

    /**
     * Log reviews fetch activity
     */
    public static function logReviewsFetch(int $businessId, int $userId, int $reviewCount = 0, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => 'fetch_reviews',
            'activity_category' => 'api_usage',
            'activity_description' => "Fetched {$reviewCount} reviews from Google Business",
            'counts_toward_limit' => false,
            'activity_count' => $reviewCount,
            'metadata' => array_merge($metadata, ['review_count' => $reviewCount])
        ]);
    }

    /**
     * Log template management activity
     */
    public static function logTemplateActivity(string $action, int $businessId, int $userId, array $metadata = []): ?BusinessActivityLog
    {
        $descriptions = [
            'create' => 'New reply template created',
            'update' => 'Reply template updated',
            'delete' => 'Reply template deleted'
        ];

        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => $action . '_template',
            'activity_category' => 'template_management',
            'activity_description' => $descriptions[$action] ?? 'Template action performed',
            'counts_toward_limit' => false,
            'metadata' => $metadata
        ]);
    }

    /**
     * Log team member activity
     */
    public static function logTeamMemberActivity(string $action, int $businessId, int $userId, array $metadata = []): ?BusinessActivityLog
    {
        $descriptions = [
            'invite' => 'Team member invited to business',
            'remove' => 'Team member removed from business',
            'activate' => 'Team member activated',
            'deactivate' => 'Team member deactivated'
        ];

        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => $action . '_team_member',
            'activity_category' => 'team_management',
            'activity_description' => $descriptions[$action] ?? 'Team member action performed',
            'counts_toward_limit' => $action === 'invite',
            'metadata' => $metadata
        ]);
    }

    /**
     * Log API usage activity
     */
    public static function logApiUsage(int $businessId, int $userId, string $endpoint, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => 'api_call',
            'activity_category' => 'api_usage',
            'activity_description' => "API call made to {$endpoint}",
            'counts_toward_limit' => false,
            'metadata' => array_merge($metadata, ['endpoint' => $endpoint])
        ]);
    }

    /**
     * Log data export activity
     */
    public static function logDataExport(int $businessId, int $userId, string $exportType, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => 'export_data',
            'activity_category' => 'data_export',
            'activity_description' => "Data exported: {$exportType}",
            'counts_toward_limit' => false,
            'metadata' => array_merge($metadata, ['export_type' => $exportType])
        ]);
    }

    /**
     * Log system/automated activity
     */
    public static function logSystemActivity(int $businessId, int $userId, string $activityType, string $description, array $metadata = []): ?BusinessActivityLog
    {
        return self::log([
            'business_id' => $businessId,
            'user_id' => $userId,
            'activity_type' => $activityType,
            'activity_category' => 'automation',
            'activity_description' => $description,
            'performed_by_type' => 'system',
            'counts_toward_limit' => in_array($activityType, ['auto_reply_sent', 'scheduled_reply_sent']),
            'metadata' => $metadata
        ]);
    }

    /**
     * Validate required fields
     */
    private static function validateRequiredFields(array $data): bool
    {
        $required = ['business_id', 'user_id', 'activity_type', 'activity_category', 'activity_description'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Prepare activity data with defaults and context
     */
    private static function prepareActivityData(array $data): array
    {
        // Get current user context
        $currentUser = Auth::user();
        $request = request();

        // Set defaults
        $activityData = array_merge([
            'performed_by_type' => 'owner',
            'performed_by_user_id' => $currentUser?->id,
            'performed_by_name' => $currentUser?->name,
            'counts_toward_limit' => true,
            'activity_count' => 1,
            'status' => 'success',
            'ip_address' => $request?->ip(),
            'user_agent' => $request?->userAgent(),
            'metadata' => []
        ], $data);

        // Get subscription ID if not provided
        if (!isset($activityData['subscription_id'])) {
            $activityData['subscription_id'] = self::getActiveSubscriptionId($activityData['user_id']);
        }

        // Determine performed_by_type if not explicitly set
        if (!isset($data['performed_by_type']) && $currentUser) {
            $activityData['performed_by_type'] = self::determinePerformedByType(
                $activityData['user_id'], 
                $currentUser->id, 
                $activityData['business_id']
            );
        }

        return $activityData;
    }

    /**
     * Get active subscription ID for user
     */
    private static function getActiveSubscriptionId(int $userId): ?int
    {
        try {
            $subscription = Subscription::where('user_id', $userId)
                                      ->where('status', 'ACTIVE')
                                      ->first();
            return $subscription?->id;
        } catch (Exception $e) {
            Log::warning('BusinessActivityLogger: Could not get subscription ID', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Determine who performed the activity
     */
    private static function determinePerformedByType(int $businessOwnerId, int $currentUserId, int $businessId): string
    {
        if ($businessOwnerId === $currentUserId) {
            return 'owner';
        }

        // Check if current user is a team member of this business
        try {
            $business = Business::find($businessId);
            if ($business && $business->teamMembers()->where('user_id', $currentUserId)->exists()) {
                return 'member';
            }
        } catch (Exception $e) {
            Log::warning('BusinessActivityLogger: Could not determine performed_by_type', [
                'business_owner_id' => $businessOwnerId,
                'current_user_id' => $currentUserId,
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
        }

        return 'owner'; // Default fallback
    }
}
