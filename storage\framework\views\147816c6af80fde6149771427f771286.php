<div>
    <?php
    $autoReply = $business->setting->auto_reply;
    $autoReplySettings = json_decode($business->setting->auto_reply_settings ?? [], true);
    $anyStatusOn = collect($autoReplySettings)->contains(function($item) {
    return isset($item['status']) && $item['status'] === 'on';
    });
    ?>
    <?php if($autoReply == 1 && $anyStatusOn): ?>
    <div id="defaultSelectedReviewContainer">
        <div class="flex items-center justify-between border-b pb-2 border-gray-200 mb-4">
            <label class="inline-flex items-center space-x-2 text-sm text-gray-700">
                <input type="checkbox" id="selectAllCheckbox" class="accent-indigo-600 w-5 h-5" data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>">
                <span>Select All</span>
            </label>

            <input type="text" placeholder="search reviewer" id="searchUserByName" class="text-sm border border-gray-200 rounded-md px-2 py-1" />
        </div>

        <ul class="space-y-4" id="reviewListData">
            <?php $__currentLoopData = $getSelectedReviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
            $ratingMap = [
            'ONE' => 1,
            'TWO' => 2,
            'THREE' => 3,
            'FOUR' => 4,
            'FIVE' => 5,
            ];
            $ratingValue = $ratingMap[$review->star_rating] ?? 0;
            ?>

            <li class="flex items-start gap-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition">
                <input type="checkbox" class="review-checkbox mt-1 accent-indigo-600 w-5 h-5" name="selected_reviews[]" value="<?php echo e($review->id); ?>" data-name="<?php echo e($review->reviewer_name); ?>" <?php echo e($review->id); ?> data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>" />

                <div class="flex-1">
                    <p class="text-sm text-gray-600 flex items-center gap-2">
                        <span class="font-medium text-gray-800">Name:</span>
                        <?php echo e($review->reviewer_name); ?>


                        <!-- Stars -->
                        <span class="flex items-center gap-0.5 ml-3">
                            <?php for($i = 0; $i < $ratingValue; $i++): ?>
                                <i class="icon-star text-yellow-400"></i>
                                <?php endfor; ?>
                                <?php for($i = 0; $i < (5 - $ratingValue); $i++): ?>
                                    <i class="icon-star text-gray-300"></i>
                                    <?php endfor; ?>
                        </span>
                    </p>

                    <?php if($review->comment): ?>
                    <div class="mt-2 bg-gray-50 border border-gray-100 rounded-md p-3">
                        <span class="text-sm text-gray-700">
                            <span class="font-medium text-gray-800">Comment:</span>
                            <?php echo e($review->comment); ?>

                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <!-- <button id="generateReplyAutoId" data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>"
            class="w-full md:w-auto bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition">
            Generate Auto Reply
        </button> -->
    </div>
    <div id="defaultSelectedReplyContainer" class="hidden flex-col items-center justify-center gap-4 pt-4">
        <div id="autoReplyLoader" class="text-center py-4 hidden">
            <div class="loader border-4 border-blue-500 border-t-transparent rounded-full w-6 h-6 mx-auto animate-spin"></div>
            <p class="text-xs text-gray-500 mt-2">Generating replies...</p>
        </div>
        <div id="defaultSelectedReplyList"></div>
        <!-- <button id="sendReplyAutoId" data-business-id="<?php echo e($businessId); ?>" data-location="<?php echo e($selectedLocationName); ?>"
            class="w-full md:w-auto bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-400 transition sendReplyAutoId">
            Send Auto Reply
        </button> -->
    </div>
    <?php else: ?>
    <div class="flex flex-col items-center justify-center gap-4 pt-4">
        <p class="text-sm text-gray-500">Please enabled auto-reply from settings option and at least make any of template status ON</p>
    </div>
    <?php endif; ?>
</div><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partials/selected-reviews.blade.php ENDPATH**/ ?>