<div class="flex justify-between gap-4 ml-auto mb-4">
    <input type="hidden" id="activity_business_id" name="business_id" value="<?php echo e($business->id); ?>">
    <select id="analyticsTimeRange" class="bg-white border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
        <option value="all" <?php echo e($timeRange == 'all' ? 'selected' : ''); ?>>All</option>
        <option value="30days" <?php echo e($timeRange == '30days' ? 'selected' : ''); ?>>Last 30 Days</option>
        <option value="3months" <?php echo e($timeRange == '3months' ? 'selected' : ''); ?>>Last 3 Months</option>
        <option value="6months" <?php echo e($timeRange == '6months' ? 'selected' : ''); ?>>Last 6 Months</option>
        <option value="year" <?php echo e($timeRange == 'year' ? 'selected' : ''); ?>>Last Year</option>
    </select>
</div>
<!-- Analytics Summary -->
<div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow duration-300 border border-[#efefef]">
        <div class="text-sm text-gray-500 mb-1">Overall Rating</div>
        <div class="flex items-center">
            <span id="avgRatingValue" class="text-xl sm:text-2xl font-semibold mr-2"><?php echo e($avgRating ?? '0.0'); ?></span>
            <div id="ratingStars" class="flex text-yellow-400 text-sm sm:text-base">
                <?php
                $fullStars = floor($avgRating ?? 0);
                $hasHalfStar = ($avgRating - $fullStars) >= 0.5;
                $emptyStars = 5 - $fullStars - ($hasHalfStar ? 1 : 0);
                ?>

                <?php for($i = 0; $i < $fullStars; $i++): ?>
                    <i class="icon-star"></i>
                    <?php endfor; ?>

                    <?php if($hasHalfStar): ?>
                    <i class="icon-star-half"></i>
                    <?php endif; ?>

                    <?php for($i = 0; $i < $emptyStars; $i++): ?>
                        <i class="icon-star"></i>
                        <?php endfor; ?>
            </div>
        </div>
    </div>
    <div class="bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow duration-300 border border-[#efefef]">
        <div class="text-sm text-gray-500 mb-1">Total Reviews</div>
        <div id="totalReviewsValue" class="text-xl sm:text-2xl font-semibold"><?php echo e(number_format($totalReviews)); ?></div>
    </div>
    <div class="bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow duration-300 border border-[#efefef]">
        <div class="text-sm text-gray-500 mb-1">Response Rate</div>
        <div id="responseRateValue" class="text-xl sm:text-2xl font-semibold"><?php echo e($responseRate); ?>%</div>
        <!-- <div class="text-xs text-gray-400 mt-1">
                                    <?php echo e($reviewsWithReplies ?? 0); ?> of <?php echo e($totalReviews); ?> reviews have responses
                                </div> -->
    </div>
    <div class="bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow duration-300 border border-[#efefef]">
        <div class="text-sm text-gray-500 mb-1">Avg. Response Time</div>
        <div id="avgResponseTimeValue" class="text-xl sm:text-2xl font-semibold">
            <?php if($avgResponseTime > 0): ?>
            <?php if($avgResponseTime < 24): ?>
                <?php echo e($avgResponseTime); ?>h
                <?php else: ?>
                <?php echo e(round($avgResponseTime / 24)); ?>d
                <?php endif; ?>
                <?php else: ?>
                N/A
                <?php endif; ?>
                </div>
        </div>
    </div>


    <!-- Sentiment Trend Analysis -->
    <div class="bg-white rounded-xl shadow-sm p-4 mb-6 border border-[#efefef]">
        <div class="flex justify-between gap-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-chart-line text-indigo-600 mr-2"></i>
                <h3 class="text-lg font-semibold">Sentiment Trend Analysis</h3>
            </div>
            <div>
                <input type="hidden" id="sentiment_business_id" name="business_id" value="<?php echo e($business->id); ?>">
            </div>
        </div>
        <p class="text-sm text-gray-600 mb-4">Track sentiment changes over time to identify improvement areas</p>

        <div id="sentimentChart" class="h-64">
            <div id="sentimentChartLoading" class="hidden flex items-center justify-center h-full">
                <div class="text-gray-500">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    Loading chart data...
                </div>
            </div>
            <canvas id="sentimentChartCanvas"></canvas>
        </div>
    </div>

    <!-- Review Activity Chart -->
    <div class="bg-white rounded-xl shadow-sm p-4 mb-6 border border-[#efefef]">
        <div class="flex justify-between gap-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-chart-bar text-indigo-600 mr-2"></i>
                <h3 class="text-lg font-semibold">Review Activity</h3>
            </div>
        </div>
        <p class="text-sm text-gray-600 mb-4">Track new reviews and responses over the past month</p>

        <div id="activityChart" class="h-64">
            <div id="activityChartLoading" class="hidden flex items-center justify-center h-full">
                <div class="text-gray-500">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    Loading chart data...
                </div>
            </div>
            <canvas id="activityChartCanvas"></canvas>
        </div>
    </div>

    <!-- Topic Discovery -->
    <!-- <div class="bg-white rounded-xl shadow-sm p-4">
        <div class="flex items-center mb-4">
            <i class="fas fa-search text-indigo-600 mr-2"></i>
            <h3 class="text-lg font-semibold">Topic Discovery</h3>
        </div>
        <p class="text-sm text-gray-600 mb-4">Identify common themes and topics from customer reviews</p>
        <div id="topicVisualization" class="h-64">
            
        </div>
    </div> --><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/partials/analytics.blade.php ENDPATH**/ ?>