<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Business Reviews</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body class="bg-white">
    <div class="flex flex-col lg:flex-row w-full bg-white overflow-hidden shadow-2xl h-auto lg:h-screen">
        <!-- Left Section -->
        <div class="flex flex-col justify-center w-2/2 lg:w-1/2 p-12 bg-gradient-to-br from-[#3B3B3B] to-[#515153] text-white">
            <div class="max-w-[500px] mx-auto">
                <div class="flex items-center justify-center mb-20 text-center">
                    <img src="{{ url('logo.png') }}" alt="ReviewMaster AI Logo" width="285" height="43" class="mr-2">
                </div>
                <h2 class="text-3xl font-bold mb-4 text-center">Welcome Back!</h2>
                <p class="text-purple-100 mb-12 text-center">Log in to your account to manage your reviews, respond to customers, and grow your business with AI-powered insights.</p>

                <div class="bg-purple-400/20 p-6 rounded-xl mb-8">
                    <div class="flex items-center gap-3 mb-2">
                        <i class="fas fa-shield-alt text-xl"></i>
                        <h3 class="font-semibold">Enhanced Security</h3>
                    </div>
                    <p class="text-sm text-purple-100">Your account is protected with two-factor authentication. After logging in, you'll be asked to enter a verification code sent to your email or phone for new devices.</p>
                    <p class="text-sm text-purple-100 mt-4"><i class="fas fa-clock mr-2"></i>We'll remember your device for 30 days.</p>
                </div>
            </div>
        </div>

        <!-- Right Section -->
        <div class="w-2/2 lg:w-1/2 p-12 flex flex-col justify-center">
            <div class="max-w-[500px] mx-auto">
                <h2 class="text-2xl font-bold text-gray-800 mb-2">Reset Password</h2>
                <p class="text-gray-600 mb-8">Enter your new password</p>
                @if (session('error'))
                <div class="alert alert-danger text-red-500">
                    {{ session('error') }}
                </div>
                @endif
                @if ($errors->has('email'))
                <div class="alert alert-danger text-red-500">
                    {{ $errors->first('email') }}
                </div>
                @endif

                <form class="space-y-6" method="POST" action="{{ route('password.update') }}">
                    @csrf
                    <input type="hidden" name="token" value="{{ $token }}">
                    <div>
                        <label class="block text-gray-700 mb-2" for="email">Email Address</label>
                        <div class="relative">
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                            <i class="fas fa-envelope absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>

                    <div>
                        <div class="relative">
                            <input type="password" id="password" name="password" placeholder="Password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                            <i class="fas fa-eye absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer" id="togglePassword" onclick="togglePasswordVisibility()"></i>
                        </div>
                    </div>

                    <div>
                        <div class="relative">
                            <input type="password" id="password_confirmation" name="password_confirmation" placeholder="Confirm Password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition">
                            <i class="fas fa-eye absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer" id="togglePassword" onclick="togglePasswordVisibility()"></i>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-[#006AFF] hover:bg-blue-700  text-white font-semibold py-3 px-4 rounded-lg transition flex items-center justify-center gap-2">
                        Update Password
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</body>

</html>