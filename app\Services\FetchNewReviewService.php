<?php

namespace App\Services;

use App\Console\Commands\FetchGoogleReviews;
use App\Models\BusinessAccount;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class FetchNewReviewService
{

    public function getUserBusinessPreference($userId)
    {
        return User::where('id', $userId)->with('preference.business')->first();
    }

    public function getScheduleTime($userId)
    {
        $user = User::with('businessAccount.setting')->find($userId);

        $account = $user->businessAccount;
        $setting = $account?->setting;

        if ($account && $setting) {
            return [
                'businessId' => $user->businessAccount->business_google_id,
                'location' => $user->preference->business->location_name,
                'token' => $account->business_google_token,
                'schedule_time' => $setting->check_interval_minutes,
                'last_fetched_at' => $setting->last_review_fetched_at,
            ];
        }

        return null;
    }
}
