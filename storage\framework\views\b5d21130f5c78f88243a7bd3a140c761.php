<?php $__env->startSection('title', 'Coupon Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Coupon Management</h1>
            <p class="text-gray-600 mt-1">Manage discount coupons and track their usage</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-0">
            <a href="<?php echo e(route('admin.coupons.analytics')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-chart-bar mr-2"></i>Analytics
            </a>
            <a href="<?php echo e(route('admin.coupons.create')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>Create Coupon
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-tags text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Coupons</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['total_coupons'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Coupons</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['active_coupons'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Usage</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['total_usage'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Discount</dt>
                        <dd class="text-lg font-medium text-gray-900">₹<?php echo e(number_format($stats['total_discount_given'], 2)); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
            <form method="GET" action="<?php echo e(route('admin.coupons.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                               placeholder="Search by code, name..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Status</option>
                            <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                            <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            <option value="expired" <?php echo e(request('status') === 'expired' ? 'selected' : ''); ?>>Expired</option>
                            <option value="upcoming" <?php echo e(request('status') === 'upcoming' ? 'selected' : ''); ?>>Upcoming</option>
                        </select>
                    </div>

                    <!-- Type Filter -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select name="type" id="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Types</option>
                            <option value="percentage" <?php echo e(request('type') === 'percentage' ? 'selected' : ''); ?>>Percentage</option>
                            <option value="fixed" <?php echo e(request('type') === 'fixed' ? 'selected' : ''); ?>>Fixed Amount</option>
                        </select>
                    </div>

                    <!-- Sort -->
                    <div>
                        <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        <select name="sort_by" id="sort_by" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="created_at" <?php echo e(request('sort_by') === 'created_at' ? 'selected' : ''); ?>>Created Date</option>
                            <option value="coupon_code" <?php echo e(request('sort_by') === 'coupon_code' ? 'selected' : ''); ?>>Coupon Code</option>
                            <option value="usage_count" <?php echo e(request('sort_by') === 'usage_count' ? 'selected' : ''); ?>>Usage Count</option>
                            <option value="expires_at" <?php echo e(request('sort_by') === 'expires_at' ? 'selected' : ''); ?>>Expiry Date</option>
                        </select>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-search mr-2"></i>Apply Filters
                    </button>
                    <a href="<?php echo e(route('admin.coupons.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors text-center">
                        <i class="fas fa-times mr-2"></i>Clear Filters
                    </a>
                    <div class="flex-1"></div>
                    <button type="button" id="bulkActionBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors" style="display: none;">
                        <i class="fas fa-cog mr-2"></i>Bulk Actions
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Coupons Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coupon</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type & Value</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validity</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $coupons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <input type="checkbox" name="coupon_ids[]" value="<?php echo e($coupon->id); ?>" class="coupon-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($coupon->coupon_code); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($coupon->name); ?></div>
                                    <?php if($coupon->description): ?>
                                        <div class="text-xs text-gray-400 mt-1"><?php echo e(Str::limit($coupon->description, 50)); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        <?php echo e($coupon->type === 'percentage' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'); ?>">
                                        <?php echo e($coupon->discount_display); ?>

                                    </span>
                                </div>
                                <?php if($coupon->minimum_amount): ?>
                                    <div class="text-xs text-gray-500 mt-1">Min: ₹<?php echo e(number_format($coupon->minimum_amount, 2)); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e(number_format($coupon->usage_logs_count)); ?></div>
                                <div class="text-xs text-gray-500">
                                    <?php if($coupon->usage_limit): ?>
                                        of <?php echo e(number_format($coupon->usage_limit)); ?>

                                    <?php else: ?>
                                        Unlimited
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <?php if($coupon->starts_at): ?>
                                        <div class="text-xs text-gray-500">From: <?php echo e($coupon->starts_at->format('M d, Y')); ?></div>
                                    <?php endif; ?>
                                    <?php if($coupon->expires_at): ?>
                                        <div class="text-xs <?php echo e($coupon->expires_at->isPast() ? 'text-red-500' : 'text-gray-500'); ?>">
                                            Until: <?php echo e($coupon->expires_at->format('M d, Y')); ?>

                                        </div>
                                    <?php else: ?>
                                        <div class="text-xs text-gray-500">No expiry</div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <button onclick="toggleStatus(<?php echo e($coupon->id); ?>)" 
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium cursor-pointer transition-colors
                                        <?php echo e($coupon->is_active ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-red-100 text-red-800 hover:bg-red-200'); ?>">
                                    <?php echo e($coupon->is_active ? 'Active' : 'Inactive'); ?>

                                </button>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium space-x-2">
                                <a href="<?php echo e(route('admin.coupons.show', $coupon)); ?>" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('admin.coupons.edit', $coupon)); ?>" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if($coupon->usage_logs_count == 0): ?>
                                    <button onclick="deleteCoupon(<?php echo e($coupon->id); ?>)" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-tags text-4xl text-gray-300 mb-4"></i>
                                <p class="text-lg font-medium">No coupons found</p>
                                <p class="text-sm">Create your first coupon to get started</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($coupons->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($coupons->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div id="bulkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
                <div class="space-y-3">
                    <button onclick="performBulkAction('activate')" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-check mr-2"></i>Activate Selected
                    </button>
                    <button onclick="performBulkAction('deactivate')" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-pause mr-2"></i>Deactivate Selected
                    </button>
                    <button onclick="performBulkAction('delete')" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete Selected
                    </button>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button onclick="closeBulkModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Checkbox functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.coupon-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActionButton();
});

document.querySelectorAll('.coupon-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButton);
});

function updateBulkActionButton() {
    const checkedBoxes = document.querySelectorAll('.coupon-checkbox:checked');
    const bulkBtn = document.getElementById('bulkActionBtn');
    
    if (checkedBoxes.length > 0) {
        bulkBtn.style.display = 'block';
        bulkBtn.textContent = `Bulk Actions (${checkedBoxes.length})`;
    } else {
        bulkBtn.style.display = 'none';
    }
}

// Bulk actions
document.getElementById('bulkActionBtn').addEventListener('click', function() {
    document.getElementById('bulkModal').classList.remove('hidden');
});

function closeBulkModal() {
    document.getElementById('bulkModal').classList.add('hidden');
}

function performBulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.coupon-checkbox:checked');
    const couponIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (couponIds.length === 0) {
        alert('Please select at least one coupon');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${couponIds.length} coupon(s)?`)) {
        fetch('<?php echo e(route("admin.coupons.bulk-action")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({
                action: action,
                coupon_ids: couponIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
    
    closeBulkModal();
}

// Toggle status
function toggleStatus(couponId) {
    fetch(`/admin/coupons/${couponId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

// Delete coupon
function deleteCoupon(couponId) {
    if (confirm('Are you sure you want to delete this coupon? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/coupons/${couponId}`;
        
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '<?php echo e(csrf_token()); ?>';
        
        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\reviewbiz\resources\views/admin/coupons/index.blade.php ENDPATH**/ ?>