<?php

namespace App\Console\Commands;

use App\Helpers\GoogleTokenRefreshHelper;
use Illuminate\Console\Command;

class RefreshGoogleTokens extends Command
{
    protected $signature = 'google:tokens:refresh {--type=all : Type of tokens to refresh (all, user, business)}';
    protected $description = 'Refresh Google OAuth tokens for users and business accounts';

    public function handle()
    {
        $type = $this->option('type');

        $this->info('Starting Google token refresh...');

        if ($type === 'all' || $type === 'user') {
            $this->info('Refreshing user tokens...');
            $userStats = GoogleTokenRefreshHelper::refreshUserTokens();
            $this->info("User tokens processed: {$userStats['total']} total, {$userStats['success']} successful, {$userStats['failed']} failed");
        }

        if ($type === 'all' || $type === 'business') {
            $this->info('Refreshing business account tokens...');
            $businessStats = GoogleTokenRefreshHelper::refreshBusinessTokens();
            $this->info("Business tokens processed: {$businessStats['total']} total, {$businessStats['success']} successful, {$businessStats['failed']} failed");
        }

        $this->info('Token refresh completed');

        return 0;
    }
}
