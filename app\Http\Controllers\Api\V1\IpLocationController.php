<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\IpLocationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class IpLocationController extends Controller
{
    private $ipLocationService;

    public function __construct(IpLocationService $ipLocationService)
    {
        $this->ipLocationService = $ipLocationService;
    }

    /**
     * Get IP location data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getLocation(Request $request): JsonResponse
    {
        try {
            // Get IP address from request parameter or detect client IP
            $ipAddress = $request->get('ip');

            if (!$ipAddress) {
                $ipAddress = $this->ipLocationService->getClientIpAddress($request);
            }

            // Validate IP address
            $validator = Validator::make(['ip' => $ipAddress], [
                'ip' => 'required|ip'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid IP address format',
                    'errors' => $validator->errors(),
                    'code' => 400
                ], 400);
            }

            // Get location data
            $locationData = $this->ipLocationService->getIpLocation($ipAddress);

            return response()->json([
                'status' => true,
                'message' => 'Location data retrieved successfully',
                'data' => $locationData,
                'code' => 200
            ], 200);

        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'code' => 400
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve location data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
                'code' => 500
            ], 500);
        }
    }

    /**
     * Get location by specific IP address
     *
     * @param string $ipAddress
     * @return JsonResponse
     */
    public function getLocationByIp(string $ipAddress): JsonResponse
    {
        try {
            // Validate IP address
            $validator = Validator::make(['ip' => $ipAddress], [
                'ip' => 'required|ip'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid IP address format',
                    'errors' => $validator->errors(),
                    'code' => 400
                ], 400);
            }

            // Get location data
            $locationData = $this->ipLocationService->getIpLocation($ipAddress);

            return response()->json([
                'status' => true,
                'message' => 'Location data retrieved successfully',
                'data' => $locationData,
                'code' => 200
            ], 200);

        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
                'code' => 400
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve location data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
                'code' => 500
            ], 500);
        }
    }
}
