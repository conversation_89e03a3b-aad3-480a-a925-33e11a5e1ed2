stages:
  - deploy

variables:
  GIT_STRATEGY: none
  MAIN: "${DOMAIN}"
  BRANCH1: "DEVELOPMENT"
  BRANCH2: "LIVE"

.deploy_template: &deploy_template
  stage: deploy
  tags:
    - shell-exec
  before_script:
    - |
      if [[ "$CI_COMMIT_BRANCH" == "main" ]]; then
        DOMAIN=$MAIN
      elif [[ "$CI_COMMIT_BRANCH" == "branch1" ]]; then
        DOMAIN=$BRANCH1
      elif [[ "$CI_COMMIT_BRANCH" == "branch2" ]]; then
        DOMAIN=$BRANCH2
      else
        DOMAIN=$(echo "$CI_COMMIT_BRANCH" | tr '[:lower:]' '[:upper:]')
      fi

      SERVER_ACCESS_KEY_VAR="${DOMAIN}_SERVER_ACCESS"
      SERVER_IP_VAR="${DOMAIN}_SERVER_IP"
      SERVER_USER_VAR="${DOMAIN}_SERVER_USER"
      DEPLOY_PATH_VAR="${DOMAIN}_DEPLOY_PATH"

      echo "Resolved DOMAIN: $DOMAIN"
      echo "Deploying to: ${!SERVER_USER_VAR}@${!SERVER_IP_VAR}"
      echo "Target path: ${!DEPLOY_PATH_VAR}"

      command -v ssh-agent >/dev/null || (apt-get update -qq && apt-get install -qq openssh-client)
      eval $(ssh-agent -s)
      echo "${!SERVER_ACCESS_KEY_VAR}" > /tmp/ssh_key
      chmod 600 /tmp/ssh_key
      ssh-add /tmp/ssh_key

      mkdir -p ~/.ssh
      echo -e "Host remote-server\n  HostName ${!SERVER_IP_VAR}\n  StrictHostKeyChecking accept-new\n  User ${!SERVER_USER_VAR}\n  UserKnownHostsFile ~/.ssh/known_hosts" > ~/.ssh/config

  script:
    - |
      if ! ssh remote-server "cd ${!DEPLOY_PATH_VAR} && git pull origin $CI_COMMIT_BRANCH"; then
        echo "❌ Deployment failed for $DOMAIN"
        exit 1
      fi
      echo "✅ Deployment successful to ${!DEPLOY_PATH_VAR} on $DOMAIN"

  after_script:
    - rm -f ~/.ssh/config ~/.ssh/known_hosts /tmp/ssh_key

  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

deploy_main:
  <<: *deploy_template
  only:
    - main
  environment:
    name: production
    url: https://${MAIN_SERVER_IP}

deploy_branch:
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_MESSAGE =~ /\[DEPLOY\]/
  environment:
    name: $CI_COMMIT_BRANCH
    url: https://${CI_COMMIT_BRANCH}