<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessAccount extends Model
{
    protected $fillable = [
        'user_id',
        'business_email',
        'business_google_id',
        'business_google_token',
        'business_refresh_token',
        'token_expires_at'
    ];

    protected $casts = [
        'token_expires_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the team members for this business account.
     */
    public function teamMembers()
    {
        return $this->hasMany(TeamMember::class);
    }

    /**
     * Get all businesses that belong to the same user as this business account
     * (since businesses share the BusinessAccount tokens)
     */
    public function businesses()
    {
        return $this->hasMany(\App\Models\Business::class, 'user_id', 'user_id');
    }

    /**
     * Check if a user is a member of this business account.
     *
     * @param int $userId
     * @return bool
     */
    public function hasMember(int $userId): bool
    {
        return $this->teamMembers()
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->exists();
    }

    public function setting()
    {
        return $this->hasOne(Setting::class, 'business_id', 'id');
    }
}
