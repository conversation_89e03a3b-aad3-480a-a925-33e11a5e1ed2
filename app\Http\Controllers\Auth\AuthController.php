<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\PasswordResetNotification;
use App\Mail\UserRegistrationNotification;
use App\Models\Business;
use App\Models\BusinessAccount;
use App\Models\Coupon;
use App\Models\GoogleReview;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\TeamMember;
use App\Services\GoogleBusinessService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    protected $googleBusinessService;

    public function __construct(GoogleBusinessService $googleBusinessService)
    {
        $this->googleBusinessService = $googleBusinessService;
    }
    public function showLoginForm(Request $request)
    {
        $plan = $request->get('plan');
        if(!empty($plan)) {
            $plan = base64_decode($plan);
            return view('auth.login', compact('plan'));
        }
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');
        $remember = $request->has('remember');

        if (Auth::attempt($credentials, $remember)) {

            $user = Auth::user();

            // $passwordExpired = $user->password_created_at
            //     && $user->password_created_at->addMinutes(1)->isPast()
            //     && $user->password_updated_at->eq($user->password_created_at);

            // if ($passwordExpired) {
            //     Auth::logout();
            //     $request->session()->invalidate();
            //     $request->session()->regenerateToken();

            //     throw \Illuminate\Validation\ValidationException::withMessages([
            //         'email' => ['Your temporary password has expired. Please reset your password.'],
            //     ]);
            // }

            // Clear any previous session data to prevent redirect loops
            Session::forget([
                'businessId',
                'business_id',
                'selected_location',
                'business_google_token',
                'business_google_refresh_token',
                'is_team_member',
                'business_owner_id'
            ]);

            $userData = User::find($user->id);
            // Check if user has associated business

            // Check if the user is an invited team member
            $teamMember = TeamMember::where('user_id', $user->id)
                ->where('business_status', 'active')
                ->where('status', 'active') // Ensure team member is active
                ->with(['business', 'invitedBy', 'permissions'])
                ->first();

            if ($teamMember && $teamMember->business && $teamMember->invitedBy) {
                // This is an invited team member with an active business
                $googleTokens = getGoogleTokens($teamMember->invitedBy->id);

                if (!$googleTokens) {
                    // Handle the case where tokens can't be retrieved
                    Auth::logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                    return redirect()->route('login')->with('error', 'Unable to access business information. Please contact the business owner.');
                }

                Session::put('businessId', $teamMember->business->id);
                Session::put('business_id', $teamMember->invitedBy->google_id);
                Session::put('selected_location', $teamMember->business->location_name);
                Session::put('business_google_token', $googleTokens->business_google_token);
                Session::put('business_google_refresh_token', $googleTokens->business_refresh_token);
                // Mark as team member in session
                Session::put('is_team_member', true);
                Session::put('business_owner_id', $teamMember->invitedBy->id);
                return redirect()->route('business.dashboard', ['fresh' => 'true']);
            }

            if (!$userData->hasActiveSubscription()) {
                return redirect()->route('business.subscriptions')->with('info', 'No active subscription found');
            }

            $checkBusinessAccount = BusinessAccount::where('user_id', $user->id)->first();
            if (!$checkBusinessAccount) {
                return redirect()->route('business.setup');
            }

            $isTokenValid = $this->googleBusinessService->isTokenValid($checkBusinessAccount->business_google_token);
            $refreshToken = $this->googleBusinessService->refreshAccessToken();
            $businessAccountId = BusinessAccount::where('business_google_token', $refreshToken)->pluck('business_google_id')->first();
            $location = Business::where('user_id', $user->id)->pluck('location_name')->first();
            $locationExists = Business::where('user_id', $user->id)->exists();

            if ($locationExists) {
                $locationId = explode('/', $location)[1];
                $checkReviews = GoogleReview::where(['location_id' => $locationId, 'account_id' => $businessAccountId])->exists();
                if ($refreshToken && $checkReviews) {
                    return redirect()->route('business.dashboard');
                }
            }


            Session::put('business_google_token', $checkBusinessAccount->business_google_token);
            Session::put('business_google_refresh_token', $checkBusinessAccount->business_refresh_token);

            return redirect()->route('business.dashboard', ['fresh' => 'true']);
        }

        return back()->withErrors(['email' => 'Invalid credentials']);
    }


    public function showRegister()
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'email' => 'required|email|unique:users,email',
            'password' => 'required',
            'confirm_password' => 'required|same:password',
        ]);

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);
            Mail::to($user->email)->send(new UserRegistrationNotification($user));
            return redirect()->route('login')->with('success', 'Registration successful! Please log in.');
        } catch (\Exception $e) {
            return back()->with('error', 'Registration failed. Please try again.');
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('login');
    }

    public function showSetPasswordForm()
    {
        return view('auth.set-password');
    }

    public function setPassword(Request $request)
    {
        $request->validate([
            'password' => 'required|min:8|confirmed',
        ]);

        // Retrieve the user from the database to ensure it's a proper User model instance
        $userId = Auth::id();
        $user = User::find($userId);
        if ($user) {
            $user->password = Hash::make($request->password);
            $user->save();
        }

        return redirect()->route('business.dashboard')->with('success', 'Password set successfully!');
    }

    public function dashboard()
    {
        $user = Auth::user();
        $userData = User::find($user->id);

        // Clear session data if this is a redirect from login to avoid stale data
        if (request()->query('fresh') === 'true') {
            Session::forget([
                'businessId',
                'business_id',
                'selected_location',
                'business_google_token',
                'business_google_refresh_token',
                'is_team_member',
                'business_owner_id'
            ]);
        }

        // If user was previously a team member, verify their status
        if (Session::get('is_team_member')) {
            $teamMember = TeamMember::where('user_id', $user->id)
                ->where('status', 'active')
                ->where('business_status', 'active')
                ->first();

            if (!$teamMember) {
                // User is no longer an active team member
                // Clear all session data to prevent redirect loops
                Session::flush();
                Auth::logout();

                return redirect()->route('login')
                    ->with('error', 'Your access to this business has been removed. Please contact the business owner for assistance.');
            }
        }

        if (!$userData->hasActiveSubscription()) {
            return redirect()->route('business.subscriptions');
        }

        $checkBusinessAccount = BusinessAccount::where('user_id', $user->id)->first();
        if (!$checkBusinessAccount) {
            return redirect()->route('business.setup');
        }
        return redirect()->route('business.dashboard');
    }

    public function showForgotPasswordForm()
    {
        return view('auth.forgot-password');
    }

    public function sendResetLink(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );
        return $status === Password::RESET_LINK_SENT
            ? back()->with(['status' => __($status)])
            : back()->withErrors(['email' => __($status)]);
    }

    public function showResetPasswordForm($token)
    {
        return view('auth.reset-password', ['token' => $token]);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email|exists:users,email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                    'remember_token' => Str::random(60),
                ])->save();
            }
        );
        Mail::to($request->email)->send(new PasswordResetNotification($request->email, $request->token));
        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('status', __($status))
            : back()->withErrors(['email' => [__($status)]]);
    }

    public function submitCouponCode(Request $request)
    {
        $request->validate([
            'coupon_code' => 'required'
        ]);

        $coupon = Coupon::where('coupon_code', $request->coupon_code)->first();

        if (!$coupon) {
            return redirect()->back()->with('error', 'Coupon code does not exist.');
        }

        // Get the latest plan
        $plan = Plan::orderBy('created_at', 'desc')->first();

        if (!$plan) {
            return redirect()->back()->with('error', 'No available plan to apply the coupon to.');
        }

        $currentUserId = Auth::id() ? Auth::id() : Auth::user()->id;

        // Create Payment Entry
        $payment = Payment::create([
            'user_id'        => $currentUserId,
            'payment_type'   => 'COUPON_CODE',
            'gateway_name'   => 'NONE',
            'coupon_code'    => $coupon->coupon_code,
            'amount'         => 0.00,
            'currency'       => 'INR',
            'payment_status' => 'SUCCESS',
            'payment_method' => 'COUPON',
            'payment_date'   => now(),
        ]);

        $startDate = now();
        $expiryDate = now()->addDays(30);

        Subscription::create([
            'user_id'     => $currentUserId,
            'plan_id'     => $plan->id,
            'payment_id'  => $payment->id,
            'status'      => 'ACTIVE',
            'start_date'  => $startDate,
            'expiry_date' => $expiryDate,
        ]);

        return redirect()->route('business.setup')->with('success', 'Coupon applied successfully and subscription activated.');
    }
}
