<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->unsignedBigInteger('business_id')->nullable();
            $table->string('title', 100);
            $table->string('description', 255);
            $table->text('template_text');
            $table->unsignedTinyInteger('min_rating');
            $table->unsignedTinyInteger('max_rating');
            $table->enum('sentiment', ['POSITIVE', 'NEGATIVE', 'NEUTRAL', 'ANY']);
            $table->enum('length', ['SHORT', 'MEDIUM', 'LONG', 'ANY']);
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';

            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->index('business_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('templates');
    }
};
