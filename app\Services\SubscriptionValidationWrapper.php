<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Closure;

class SubscriptionValidationWrapper
{
    protected $usageTrigger;

    public function __construct(SubscriptionUsageTrigger $usageTrigger)
    {
        $this->usageTrigger = $usageTrigger;
    }

    /**
     * Wrap any service method with subscription validation
     */
    public function wrapWithValidation(string $validationType, Closure $originalMethod, array $params = [])
    {
        try {
            // Perform validation before executing the original method
            $validation = $this->performValidation($validationType, $params);
            
            if (!$validation['allowed']) {
                return [
                    'success' => false,
                    'error' => 'Subscription limit exceeded',
                    'message' => $validation['message'],
                    'validation_data' => $validation,
                    'upgrade_required' => true
                ];
            }
            
            // Execute the original method if validation passes
            $result = $originalMethod();
            
            // If the original method returns a result, wrap it with validation success info
            if (is_array($result)) {
                $result['validation_passed'] = true;
                $result['validation_data'] = $validation;
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('SubscriptionValidationWrapper: Validation failed', [
                'validation_type' => $validationType,
                'error' => $e->getMessage(),
                'params' => $params
            ]);
            
            // If validation fails, still execute the original method to prevent breaking functionality
            try {
                $result = $originalMethod();
                
                if (is_array($result)) {
                    $result['validation_warning'] = 'Subscription validation failed but action was allowed';
                }
                
                return $result;
            } catch (\Exception $originalException) {
                return [
                    'success' => false,
                    'error' => 'Both validation and original method failed',
                    'validation_error' => $e->getMessage(),
                    'original_error' => $originalException->getMessage()
                ];
            }
        }
    }

    /**
     * Validate business connection with real-time data
     */
    public function validateBusinessConnection(int $userId, Closure $originalMethod)
    {
        return $this->wrapWithValidation('business_connection', $originalMethod, ['user_id' => $userId]);
    }

    /**
     * Validate reply sending with real-time data
     */
    public function validateReplySend(int $userId, Closure $originalMethod)
    {
        return $this->wrapWithValidation('reply_send', $originalMethod, ['user_id' => $userId]);
    }

    /**
     * Validate team member invitation with real-time data
     */
    public function validateTeamMemberInvite(int $userId, int $businessId, Closure $originalMethod)
    {
        return $this->wrapWithValidation('team_member_invite', $originalMethod, [
            'user_id' => $userId,
            'business_id' => $businessId
        ]);
    }

    /**
     * Validate feature access with real-time data
     */
    public function validateFeatureAccess(int $userId, string $feature, Closure $originalMethod)
    {
        return $this->wrapWithValidation('feature_access', $originalMethod, [
            'user_id' => $userId,
            'feature' => $feature
        ]);
    }

    /**
     * Perform validation based on type
     */
    private function performValidation(string $validationType, array $params): array
    {
        $userId = $params['user_id'] ?? 0;
        
        switch ($validationType) {
            case 'business_connection':
                return $this->usageTrigger->triggerBusinessConnectionCheck($userId);
            
            case 'reply_send':
                return $this->usageTrigger->triggerReplySendCheck($userId);
            
            case 'team_member_invite':
                $businessId = $params['business_id'] ?? 0;
                return $this->usageTrigger->triggerTeamMemberInviteCheck($userId, $businessId);
            
            case 'feature_access':
                $feature = $params['feature'] ?? '';
                return $this->usageTrigger->triggerFeatureAccessCheck($userId, $feature);
            
            default:
                return ['allowed' => true, 'message' => 'Unknown validation type'];
        }
    }

    /**
     * Create a validation decorator for existing services
     */
    public static function decorateService($serviceInstance, array $validationRules = [])
    {
        return new class($serviceInstance, $validationRules) {
            private $service;
            private $validationRules;
            private $wrapper;

            public function __construct($service, array $validationRules)
            {
                $this->service = $service;
                $this->validationRules = $validationRules;
                $this->wrapper = app(SubscriptionValidationWrapper::class);
            }

            public function __call($method, $arguments)
            {
                // Check if this method needs validation
                if (isset($this->validationRules[$method])) {
                    $validationType = $this->validationRules[$method]['type'];
                    $params = $this->validationRules[$method]['params'] ?? [];
                    
                    // Extract dynamic parameters from arguments if needed
                    foreach ($params as $key => $value) {
                        if (is_string($value) && strpos($value, 'arg:') === 0) {
                            $argIndex = (int) substr($value, 4);
                            $params[$key] = $arguments[$argIndex] ?? null;
                        }
                    }
                    
                    return $this->wrapper->wrapWithValidation(
                        $validationType,
                        function() use ($method, $arguments) {
                            return $this->service->$method(...$arguments);
                        },
                        $params
                    );
                }
                
                // If no validation needed, call the original method
                return $this->service->$method(...$arguments);
            }
        };
    }

    /**
     * Add validation to GoogleBusinessService methods
     */
    public function decorateGoogleBusinessService($googleBusinessService)
    {
        return self::decorateService($googleBusinessService, [
            'replyToReview' => [
                'type' => 'reply_send',
                'params' => ['user_id' => 'arg:0'] // Assuming user_id is first argument
            ],
            'fetchReviews' => [
                'type' => 'feature_access',
                'params' => ['user_id' => 'arg:0', 'feature' => 'api_access_level']
            ]
        ]);
    }

    /**
     * Add validation to TemplateService methods
     */
    public function decorateTemplateService($templateService)
    {
        return self::decorateService($templateService, [
            'createTemplate' => [
                'type' => 'feature_access',
                'params' => ['user_id' => 'arg:0', 'feature' => 'template_access']
            ]
        ]);
    }

    /**
     * Check if validation should be bypassed for specific conditions
     */
    public function shouldBypassValidation(array $conditions = []): bool
    {
        // Allow bypassing validation in specific scenarios
        if (isset($conditions['bypass_for_admin']) && $conditions['bypass_for_admin']) {
            return auth()->user()?->hasRole('admin') ?? false;
        }
        
        if (isset($conditions['bypass_for_testing']) && $conditions['bypass_for_testing']) {
            return app()->environment('testing');
        }
        
        if (isset($conditions['bypass_for_migration']) && $conditions['bypass_for_migration']) {
            return app()->runningInConsole();
        }
        
        return false;
    }

    /**
     * Get validation status without executing any action
     */
    public function checkValidationStatus(string $validationType, array $params = []): array
    {
        try {
            return $this->performValidation($validationType, $params);
        } catch (\Exception $e) {
            Log::error('SubscriptionValidationWrapper: Status check failed', [
                'validation_type' => $validationType,
                'error' => $e->getMessage()
            ]);
            
            return [
                'allowed' => true, // Default to allowing action if check fails
                'message' => 'Validation status check failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Batch validate multiple actions
     */
    public function batchValidate(array $validations): array
    {
        $results = [];
        
        foreach ($validations as $key => $validation) {
            $results[$key] = $this->checkValidationStatus(
                $validation['type'],
                $validation['params'] ?? []
            );
        }
        
        return $results;
    }
}
