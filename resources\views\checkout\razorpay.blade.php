<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Payment - ReviewBiz</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body class="font-inter bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Payment Card -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-credit-card text-2xl text-blue-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Complete Your Payment</h1>
                <p class="text-gray-600">Secure payment powered by Razorpay</p>
            </div>

            <!-- Order Summary -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 class="font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">{{ $plan->name }}</span>
                        <span class="font-medium">{{ $plan->currency_symbol }}{{ number_format($payment->original_amount ?? $payment->amount, 2) }}</span>
                    </div>
                    
                    @if($payment->discount_amount > 0)
                    <div class="flex justify-between text-green-600">
                        <span>Discount ({{ $payment->coupon_code }})</span>
                        <span>-{{ $plan->currency_symbol }}{{ number_format($payment->discount_amount, 2) }}</span>
                    </div>
                    @endif
                    
                    <div class="border-t pt-3">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span>{{ $plan->currency_symbol }}{{ number_format($payment->amount, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Button -->
            <button id="rzp-button" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-lock mr-2"></i>
                Pay {{ $plan->currency_symbol }}{{ number_format($payment->amount, 2) }}
            </button>

            <!-- Security Info -->
            <div class="mt-6 text-center">
                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt mr-1"></i>
                        <span>Secure</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-lock mr-1"></i>
                        <span>Encrypted</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-1"></i>
                        <span>Verified</span>
                    </div>
                </div>
            </div>

            <!-- Back Link -->
            <div class="mt-6 text-center">
                <a href="{{ route('checkout.index', ['plan' => $plan->id]) }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to checkout
                </a>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
            <div class="bg-white rounded-lg p-6 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-700">Processing your payment...</p>
            </div>
        </div>
    </div>

    <script>
        const options = {
            "key": "{{ $razorpay_key }}",
            "amount": "{{ $amount }}",
            "currency": "{{ $payment->currency }}",
            "name": "ReviewBiz",
            "description": "{{ $plan->name }} Subscription",
            "order_id": "{{ $order_id }}",
            "handler": function(response) {
                document.getElementById('loadingOverlay').classList.remove('hidden');
                
                // Redirect to success page with payment details
                const successUrl = "{{ route('payment.success') }}" + 
                    "?razorpay_payment_id=" + response.razorpay_payment_id +
                    "&payment_id={{ $payment->id }}" +
                    "&plan_id={{ $plan->id }}";
                
                window.location.href = successUrl;
            },
            "prefill": {
                "name": "{{ $user->name }}",
                "email": "{{ $user->email }}",
            },
            "theme": {
                "color": "#2563eb"
            },
            "modal": {
                "ondismiss": function() {
                    // Payment was cancelled
                    console.log('Payment cancelled by user');
                }
            }
        };

        const rzp = new Razorpay(options);

        // Handle payment errors
        rzp.on('payment.failed', function(response) {
            alert('Payment failed: ' + response.error.description);
        });

        // Open Razorpay checkout on button click
        document.getElementById('rzp-button').onclick = function(e) {
            e.preventDefault();
            rzp.open();
        };

        // Auto-open payment modal after 1 second
        setTimeout(() => {
            rzp.open();
        }, 1000);
    </script>
</body>
</html>
