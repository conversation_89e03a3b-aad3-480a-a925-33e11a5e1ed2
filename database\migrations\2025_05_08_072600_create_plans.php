<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->string('name', 50);
            $table->string('short_description', 255);
            $table->text('long_description');
            $table->string('currency', 10);
            $table->string('currency_symbol', 3)->default('₹');
            $table->decimal('price', 10, 2);
            $table->decimal('annual_price', 10, 2)->nullable(); // Annual pricing
            $table->integer('annual_discount_percentage')->default(0); // Annual discount %
            $table->decimal('tax_percentage', 5, 2)->nullable(); // Tax %
            $table->integer('duration_days');
            $table->json('features')->nullable(); // Store all features as JSON
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
