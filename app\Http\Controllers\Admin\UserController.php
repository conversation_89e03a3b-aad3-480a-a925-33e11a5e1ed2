<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Subscription;
use App\Models\Payment;
use App\Models\Business;
use App\Models\AssociateBusiness;
use App\Models\TeamMember;
use App\Models\BusinessAccount;
use App\Models\BusinessActivityLog;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * Display users listing with filters
     */
    public function index(Request $request)
    {
        $query = User::with(['subscriptions.plan', 'businessAccounts', 'teamMemberships']);

        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        // Subscription status filter
        if ($request->filled('subscription_status')) {
            $status = $request->subscription_status;
            if ($status === 'active') {
                $query->whereHas('subscriptions', function($q) {
                    $q->where('status', 'ACTIVE')
                      ->where('expiry_date', '>=', now());
                });
            } elseif ($status === 'expired') {
                $query->whereHas('subscriptions', function($q) {
                    $q->where('status', 'EXPIRED')
                      ->orWhere('expiry_date', '<', now());
                });
            } elseif ($status === 'none') {
                $query->whereDoesntHave('subscriptions');
            }
        }

        // Plan filter
        if ($request->filled('plan_id')) {
            $query->whereHas('subscriptions', function($q) use ($request) {
                $q->where('plan_id', $request->plan_id)
                  ->where('status', 'ACTIVE');
            });
        }

        // Registration date filter
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', Carbon::parse($request->date_from));
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', Carbon::parse($request->date_to)->endOfDay());
        }

        // Business connection filter
        if ($request->filled('business_status')) {
            $status = $request->business_status;
            if ($status === 'connected') {
                $query->whereHas('businessAccounts');
            } elseif ($status === 'not_connected') {
                $query->whereDoesntHave('businessAccounts');
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['name', 'email', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $users = $query->paginate(20)->withQueryString();

        // Get filter options
        $plans = Plan::where('is_active', true)->get();
        
        // Get statistics
        $stats = $this->getUserStatistics();

        return view('admin.users.index', compact('users', 'plans', 'stats'));
    }

    /**
     * Show user details
     */
    public function show(User $user)
    {
        $user->load([
            'subscriptions.plan',
            'subscriptions.payment',
            'businessAccounts',
            'teamMemberships.business',
            'teamMemberships.permissions',
            'invitedTeamMembers.business',
            'businessPreferences.business',
            'associateBusinesses'
        ]);

        // Get user's businesses (owned)
        $ownedBusinesses = Business::where('user_id', $user->id)
            ->with(['teamMembers.user', 'setting'])
            ->get();

        // Get associated businesses (shared access)
        $associatedBusinesses = $user->associateBusinesses;

        // Get team memberships (businesses where user is a team member)
        $teamMemberships = $user->teamMemberships()
            ->with(['business.user', 'permissions', 'invitedBy'])
            ->get();

        // Get subscription utilization
        $subscriptionUtilization = $this->getSubscriptionUtilization($user);

        // Get recent activity
        $recentActivity = BusinessActivityLog::where('user_id', $user->id)
            ->with('business')
            ->latest()
            ->limit(10)
            ->get();

        // Get payment history
        $payments = Payment::where('user_id', $user->id)
            ->with('plan')
            ->latest('payment_date')
            ->limit(10)
            ->get();

        return view('admin.users.show', compact(
            'user',
            'ownedBusinesses',
            'associatedBusinesses', 
            'teamMemberships',
            'subscriptionUtilization',
            'recentActivity',
            'payments'
        ));
    }

    /**
     * Get user subscriptions
     */
    public function subscriptions(User $user)
    {
        $subscriptions = $user->subscriptions()
            ->with(['plan', 'payment', 'business'])
            ->latest()
            ->paginate(10);

        return view('admin.users.subscriptions', compact('user', 'subscriptions'));
    }

    /**
     * Get user payments
     */
    public function payments(User $user)
    {
        $payments = Payment::where('user_id', $user->id)
            ->with(['plan'])
            ->latest('payment_date')
            ->paginate(15);

        return view('admin.users.payments', compact('user', 'payments'));
    }

    /**
     * Get user activity
     */
    public function activity(User $user)
    {
        $activities = BusinessActivityLog::where('user_id', $user->id)
            ->with('business')
            ->latest()
            ->paginate(20);

        return view('admin.users.activity', compact('user', 'activities'));
    }

    /**
     * Toggle user status (activate/deactivate)
     */
    public function toggleStatus(User $user)
    {
        // For now, we'll use a custom field or handle via subscriptions
        // Since there's no direct 'is_active' field in users table
        
        $activeSubscription = $user->subscriptions()
            ->where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->first();

        if ($activeSubscription) {
            // Deactivate by cancelling subscription
            $activeSubscription->update(['status' => 'CANCELLED']);
            $message = 'User has been deactivated (subscription cancelled).';
        } else {
            // For reactivation, we'd need to create a new subscription or reactivate existing
            $message = 'User status updated. Note: User needs an active subscription to access services.';
        }

        return back()->with('success', $message);
    }

    /**
     * Reset user password
     */
    public function resetPassword(User $user, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $user->update([
            'password' => Hash::make($request->new_password),
            'password_created_at' => now(),
        ]);

        return back()->with('success', 'Password has been reset successfully.');
    }

    /**
     * Get user statistics for dashboard
     */
    private function getUserStatistics()
    {
        return [
            'total_users' => User::count(),
            'active_users' => User::whereHas('subscriptions', function($q) {
                $q->where('status', 'ACTIVE')
                  ->where('expiry_date', '>=', now());
            })->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'new_users_this_month' => User::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'users_with_businesses' => User::whereHas('businessAccounts')->count(),
            'team_members' => User::whereHas('teamMemberships', function($q) {
                $q->where('status', 'active');
            })->count(),
        ];
    }

    /**
     * Get subscription utilization for user
     */
    private function getSubscriptionUtilization(User $user)
    {
        $activeSubscription = $user->subscriptions()
            ->where('status', 'ACTIVE')
            ->where('expiry_date', '>=', now())
            ->with('plan')
            ->first();

        if (!$activeSubscription || !$activeSubscription->features) {
            return null;
        }

        $features = $activeSubscription->features;
        $utilization = [];

        // Business connections
        if (isset($features['business_connections_limit'])) {
            $connectedBusinesses = Business::where('user_id', $user->id)->count();
            $utilization['business_connections'] = [
                'used' => $connectedBusinesses,
                'limit' => $features['business_connections_limit']['value'] ?? 0,
                'title' => $features['business_connections_limit']['title'] ?? 'Business Connections'
            ];
        }

        // Team members
        if (isset($features['team_members_limit'])) {
            $teamMembers = TeamMember::whereHas('business', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('status', 'active')->count();

            $utilization['team_members'] = [
                'used' => $teamMembers,
                'limit' => $features['team_members_limit']['value'] ?? 0,
                'title' => $features['team_members_limit']['title'] ?? 'Team Members'
            ];
        }

        // Review replies (if tracked)
        if (isset($features['monthly_replies_limit'])) {
            // This would need to be implemented based on your review reply tracking
            $utilization['monthly_replies'] = [
                'used' => 0, // Implement based on your review reply system
                'limit' => $features['monthly_replies_limit']['value'] ?? 0,
                'title' => $features['monthly_replies_limit']['title'] ?? 'Monthly Replies'
            ];
        }

        return $utilization;
    }

    /**
     * Send login link to user
     */
    public function sendLoginLink(User $user)
    {
        // Generate a secure token for login
        $token = Str::random(64);

        // Store token in cache for 1 hour
        Cache::put("admin_login_token_{$token}", $user->id, 3600);

        // Generate login URL
        $loginUrl = route('admin.users.login-as', ['token' => $token]);

        return back()->with('success', "Login link generated: {$loginUrl}");
    }

    /**
     * Login as user (for admin testing)
     */
    public function loginAs(Request $request)
    {
        $token = $request->get('token');
        $userId = Cache::get("admin_login_token_{$token}");

        if (!$userId) {
            return redirect()->route('admin.dashboard')->with('error', 'Invalid or expired login token.');
        }

        $user = User::find($userId);
        if (!$user) {
            return redirect()->route('admin.dashboard')->with('error', 'User not found.');
        }

        // Clear the token
        Cache::forget("admin_login_token_{$token}");

        // Login as the user
        Auth::guard('web')->login($user);

        return redirect()->route('dashboard')->with('success', 'You are now logged in as ' . $user->name);
    }
}
