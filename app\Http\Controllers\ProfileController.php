<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    public function getProfileData()
    {
        return response()->json(Auth::user());
    }

    /**
     * Update user profile information
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Base validation rules
            $rules = [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
                'phone_number' => 'nullable|string|max:20',
                'country_code' => 'nullable|string|max:5',
            ];

            // Add password validation rules based on user type
            if ($request->has('new_password')) {
                $rules['new_password'] = 'required|min:8|confirmed';
                
                // Only require current password for non-Google users
                if (!$user->google_id) {
                    $rules['current_password'] = 'required';
                }
            }

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Verify current password only for non-Google users
            if ($request->new_password && !$user->google_id) {
                if (!Hash::check($request->current_password, $user->password)) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Current password is incorrect'
                    ], 422);
                }
            }

            // Update user information
            $user->name = $request->name;
            $user->email = $request->email;
            $user->country_code = $request->country_code;
            $user->phone_number = $request->phone_number;

            if ($request->new_password) {
                $user->password = Hash::make($request->new_password);
            }

            $user->save();

            return response()->json([
                'status' => true,
                'message' => 'Profile updated successfully',
                'user' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone_number' => $user->phone_number,
                    'country_code' => $user->country_code
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while updating profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
