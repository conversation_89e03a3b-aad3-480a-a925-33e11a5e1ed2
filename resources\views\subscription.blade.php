@extends('layouts.app')

@section('content')
<!-- Pricing Toggle -->
<section class="py-[150px]">


    <div class="container mx-auto px-4">
        <!-- <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"> -->
        <div class="flex justify-center">


            <div class="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow relative transform scale-105">
                @if($plans)
                <div class="absolute top-0 right-0 bg-[#006AFF] text-white px-4 py-1 rounded-tr-2xl rounded-bl-2xl text-sm font-medium">Launch Special</div>
                <h3 class="text-xl font-semibold mb-2">{{ $plans->name }}</h3>
                <p class="text-gray-600 mb-6">{{ $plans->short_description }}</p>

                <div class="text-3xl font-bold mb-2">
                    <span class="monthly-price">₹{{ round($plans->price) }}</span>
                    <span class="yearly-price hidden">₹{{ round($plans->price) }}</span>
                    <span class="text-lg font-normal text-gray-600">/ {{ $plans->duration_days }} Days </span>
                </div>
                <p class="text-green-600 text-sm mb-6">
                    <span class="monthly-save">Limited time offer</span>
                    <span class="yearly-save hidden">Limited time offer</span>
                </p>
                <ul class="space-y-4 mb-8">
                    <!-- <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Up to 10 business locations</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Advanced AI customization</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Full analytics suite*</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Team collaboration tools*</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Priority support</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Valid up to 90 days</li> -->
                    @foreach(json_decode($plans['features']) as $feature)
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> {{ $feature }} </li>
                    @endforeach
                </ul>

                <div class="text-xs mb-6">* New Features Coming soon!</div>
                <div class="bg-gray-100 p-4 mt-4 rounded-lg">
                    <!-- <form method="POST" action="{{ route('submit.couponcode') }}" class="bg-gray-100 p-4 mt-4 rounded-lg">
                    @csrf -->

                    <h3 for=""> Use Coupon </h3>
                    <div class="flex items-center gap-4 relative">
                        <input type="text" name="coupon_code" id="couponCodeVal" value="" class="bg-white border border-gray-300 rounded-lg px-4 py-2 w-full h-12" placeholder="Enter coupon code" required>
                        <!-- <button type="button" class="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold applyCoupon">Apply</button> -->
                         <span class="bg-green-500 rounded-full flex items-center justify-center p-1 absolute right-2 w-[20px] h-[20px] text-xs"><i class="fa-solid fa-check text-white"></i></span>
                         <span class="bg-red-500 rounded-full flex items-center justify-center p-1 absolute right-2 w-[20px] h-[20px] text-xs hidden"><i class="fa-solid fa-xmark text-white"></i></span>
                    </div>
                    <span class="validateCouponCode" style="color:red; display:none">Please Enter Coupon Code</span>
                    <span class="dangerCoupon" style="color:red; display:none">Invalid Coupon Code</span>
                    <span class="successCoupon" style="color:green; display:none">Valid Coupon Code</span>
                    <!-- </form> -->
                </div>

                <form method="POST" action="{{ route('payment.process') }}" class="mt-4">
                    @csrf
                    <input type="hidden" name="plan_id" value="{{ $plans->id }}">
                    <input type="hidden" name="business_id" value="{{-- $businesses[0]->id --}}">
                    <input type="hidden" name="payment_type" value="ONE_TIME_PAYMENT">
                    <input type="hidden" name="gateway" value="STRIPE">
                    <button type="submit" class="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold payNowBtn">Pay Now</button>
                </form>
                <form method="POST" action="{{ route('submit.couponcode') }}" class="bg-gray-100 p-4 mt-4 rounded-lg hidden proceedBtn">
                    @csrf
                    <input type="hidden" name="coupon_code" value="" id="couponCodeValUpdate">
                    <button type="submit" class="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold">Proceed</button>
                </form>
                @else
                <p>Plans Coming Soon</p>
                @endif
                <!-- <button class="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold">Get Started</button> -->
                <hr />


            </div>


        </div>
    </div>
</section>

@endsection