<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SeetingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('settings')->insert([
            'business_id'            => 1,
            'ai_provider'            => 'chatgpt',
            'model_version'          => 'gpt-3.5-turbo',
            'response_style'         => 50,
            'response_length'        => 50,
            'custom_signoff_text'    => null,
            'custom_instruction'     => null,
            'response_language'      => 'same-as-review',
            'auto_check_reviews'     => 1,
            'check_interval_minutes' => 1440,
            'auto_reply'             => 0,
            'auto_reply_settings'    => json_encode([
                ["status" => "off", "template" => "2", "reply_rating" => 1],
                ["status" => "off",  "template" => "3", "reply_rating" => 2],
                ["status" => "off", "template" => "1", "reply_rating" => 3],
                ["status" => "off",  "template" => "2", "reply_rating" => 4],
                ["status" => "off",  "template" => "2", "reply_rating" => 5],
            ]),
            'review_length_filter'   => 'any',
            'reply_timing'           => 'immediate',
            'timezone'               => 'America/New_York',
            'created_at'             => now(),
            'updated_at'             => now(),
        ]);
    }
}
