<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->string('name')->nullable();
            $table->string('google_id')->nullable()->unique();
            $table->string('email')->unique();
            $table->string('country_code', 7)->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->string('image')->nullable();
            $table->rememberToken();
            $table->string('password')->nullable();
            $table->boolean('first_login_popup_shown')->default(false);

            $table->string('stripe_id')->nullable();
            $table->string('stripe_customer_id')->nullable();
            $table->string('card_brand', 50)->nullable();
            $table->string('card_last_four', 4)->nullable();
            $table->text('access_token')->nullable();
            $table->text('refresh_token')->nullable();

            $table->string('razorpay_customer_id')->nullable();
            $table->string('razorpay_payment_method', 50)->nullable();
            $table->string('razorpay_card_last_four', 4)->nullable();

            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('email_verified_at')->nullable();

            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->string('email')->primary();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->string('id')->primary();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
