<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\TeamMember;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_member_id',
        'permission_name',
        'is_granted'
    ];

    protected $casts = [
        'is_granted' => 'boolean',
    ];

    /**
     * Get the team member that owns this permission.
     */
    public function teamMember(): BelongsTo
    {
        return $this->belongsTo(TeamMember::class);
    }

    /**
     * Define available permissions in the system.
     * 
     * @return array
     */
    public static function availablePermissions(): array
    {
        return [
            'manager' => 'Manager',
            'viewer' => 'Viewer',
            // 'view_reviews' => 'View Reviews',
            // 'reply_to_reviews' => 'Reply to Reviews',
            // 'manage_templates' => 'Manage Templates',
            // 'view_analytics' => 'View Analytics',
            // 'manage_settings' => 'Manage Settings',
            // 'manage_team' => 'Manage Team Members'
        ];
    }
}
