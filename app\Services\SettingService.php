<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Carbon;

class SettingService
{
    /**
     * Create default settings for a new business
     *
     * @param int $businessId
     * @return void
     */
    public static function createDefaultSettingsForBusiness($businessId)
    {
        // Get all global settings (without business_id)
        $globalSettings = Setting::where('business_id', $businessId)->first();

        if ($globalSettings) {
            // If global settings exist, return early
            return;
        }
        // Create a copy of each setting for the new business
        Setting::create([
            'business_id' => $businessId,
            'ai_provider' => 'chatgpt',
            'model_version' => 'gpt-3.5-turbo',
            'response_style' => '50',
            'response_length' => '50',
            'custom_signoff_text' => 'Thank you for your feedback!',
            'custom_instruction' => '',
            'response_language' => 'same-as-review',
            'auto_check_reviews' => '1',
            'check_interval_minutes' => '30',
            'auto_reply' => '0',            
            'auto_reply_settings' => json_encode([
                ["status" => "off", "template" => "", "reply_rating" => 1],
                ["status" => "off",  "template" => "", "reply_rating" => 2],
                ["status" => "off", "template" => "", "reply_rating" => 3],
                ["status" => "off",  "template" => "", "reply_rating" => 4],
                ["status" => "off",  "template" => "", "reply_rating" => 5],
            ]),            
            'review_length_filter' => 'any',            
            'reply_timing' => 'immediate',
            'timezone' => 'America/New_York',
        ]);
    }
}
