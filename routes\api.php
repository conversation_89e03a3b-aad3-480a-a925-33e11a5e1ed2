<?php

use App\Http\Controllers\Api\V1\PlanController;
use App\Http\Controllers\Api\V1\IpLocationController;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->group(function () {
    Route::get('plans', [PlanController::class, 'getAllPlans'])->name('plans');
    Route::get('plans/compare', [PlanController::class, 'getComparePlans'])->name('plans.compare');

    // IP Location Detection Routes
    Route::get('location', [IpLocationController::class, 'getLocation'])->name('ip.location');
    Route::get('location/{ipAddress}', [IpLocationController::class, 'getLocationByIp'])->name('ip.location.by.ip');
});
