@extends('admin.layouts.app')

@section('title', 'Site Settings')
@section('page-title', 'Site Settings')

@push('styles')
<style>
    .setting-card {
        transition: all 0.2s ease;
    }
    .setting-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .category-icon {
        @apply w-8 h-8 rounded-lg flex items-center justify-center text-white;
    }
    .category-ai { @apply bg-purple-500; }
    .category-google_api { @apply bg-blue-500; }
    .category-email { @apply bg-green-500; }
    .category-application { @apply bg-orange-500; }
    .category-general { @apply bg-gray-500; }
    .access-badge {
        @apply px-2 py-1 text-xs font-medium rounded;
    }
    .access-super_admin { @apply bg-red-100 text-red-800; }
    .access-admin { @apply bg-blue-100 text-blue-800; }
    .access-moderator { @apply bg-green-100 text-green-800; }
</style>
@endpush

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Site Settings</h1>
            <p class="text-gray-600">Manage global application settings and configuration</p>
        </div>
        <div class="flex items-center space-x-3">
            @if(auth('admin')->user()->hasPermission('site_settings.edit'))
                <form method="POST" action="{{ route('admin.site-settings.clear-cache') }}" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        <i class="fas fa-sync mr-2"></i>Clear Cache
                    </button>
                </form>
                
                <a href="{{ route('admin.site-settings.export') }}" 
                   class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                    <i class="fas fa-download mr-2"></i>Export Settings
                </a>
            @endif
        </div>
    </div>

    <!-- Category Filter -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filter by Category</h3>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-3">
                <a href="{{ route('admin.site-settings.index') }}" 
                   class="px-4 py-2 rounded-md {{ !request('category') ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                    All Categories
                </a>
                @foreach($settings->keys() as $category)
                    <a href="{{ route('admin.site-settings.index', ['category' => $category]) }}" 
                       class="px-4 py-2 rounded-md {{ request('category') == $category ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        {{ ucfirst(str_replace('_', ' ', $category)) }}
                    </a>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Settings by Category -->
    @forelse($settings as $categoryName => $categoryGroups)
        <div class="mb-8">
            <!-- Category Header -->
            <div class="flex items-center mb-4">
                <div class="category-icon category-{{ $categoryName }}">
                    @switch($categoryName)
                        @case('ai')
                            <i class="fas fa-brain"></i>
                            @break
                        @case('google_api')
                            <i class="fab fa-google"></i>
                            @break
                        @case('email')
                            <i class="fas fa-envelope"></i>
                            @break
                        @case('application')
                            <i class="fas fa-cog"></i>
                            @break
                        @default
                            <i class="fas fa-sliders-h"></i>
                    @endswitch
                </div>
                <div class="ml-3">
                    <h2 class="text-xl font-semibold text-gray-900">
                        {{ ucfirst(str_replace('_', ' ', $categoryName)) }} Settings
                    </h2>
                    <p class="text-sm text-gray-600">
                        @switch($categoryName)
                            @case('ai')
                                Configure AI provider settings and parameters
                                @break
                            @case('google_api')
                                Manage Google API credentials and limits
                                @break
                            @case('email')
                                Email configuration and SMTP settings
                                @break
                            @case('application')
                                General application settings and limits
                                @break
                            @default
                                General configuration settings
                        @endswitch
                    </p>
                </div>
                @if(auth('admin')->user()->hasPermission('site_settings.edit'))
                    <div class="ml-auto">
                        <a href="{{ route('admin.site-settings.edit', ['category' => $categoryName]) }}" 
                           class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-edit mr-2"></i>Edit {{ ucfirst($categoryName) }}
                        </a>
                    </div>
                @endif
            </div>

            <!-- Settings Groups -->
            @foreach($categoryGroups as $groupName => $groupSettings)
                <div class="bg-white rounded-lg shadow mb-4">
                    @if($groupName)
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">
                                {{ ucfirst(str_replace('_', ' ', $groupName)) }}
                            </h3>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                            @foreach($groupSettings as $setting)
                                <div class="setting-card border border-gray-200 rounded-lg p-3 sm:p-4">
                                    <div class="flex items-start justify-between mb-2">
                                        <h4 class="font-medium text-gray-900 text-sm">{{ $setting->name }}</h4>
                                        <span class="access-badge access-{{ $setting->access_level }}">
                                            {{ ucfirst(str_replace('_', ' ', $setting->access_level)) }}
                                        </span>
                                    </div>
                                    
                                    @if($setting->description)
                                        <p class="text-xs text-gray-600 mb-3">{{ $setting->description }}</p>
                                    @endif
                                    
                                    <div class="mb-2">
                                        <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                                            Current Value
                                        </label>
                                        <div class="text-sm text-gray-900">
                                            @if($setting->type === 'boolean')
                                                <span class="px-2 py-1 text-xs rounded {{ $setting->getTypedValue() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ $setting->getTypedValue() ? 'Enabled' : 'Disabled' }}
                                                </span>
                                            @elseif($setting->is_encrypted && $setting->value)
                                                <span class="text-gray-500 font-mono">••••••••</span>
                                            @elseif($setting->type === 'json')
                                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">JSON</code>
                                            @else
                                                <span class="font-mono">{{ $setting->getTypedValue() ?: 'Not set' }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <span>{{ ucfirst($setting->type) }}</span>
                                        @if($setting->is_required)
                                            <span class="text-red-600">Required</span>
                                        @endif
                                    </div>
                                    
                                    @if($setting->help_text)
                                        <div class="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
                                            <i class="fas fa-info-circle mr-1"></i>{{ $setting->help_text }}
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @empty
        <div class="text-center py-12">
            <i class="fas fa-cog text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No settings found</h3>
            <p class="text-gray-600">No settings are available for your access level.</p>
        </div>
    @endforelse

    <!-- Quick Stats -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-4 sm:p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Settings Overview</h3>
        </div>
        <div class="p-4 sm:p-6">
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">
                        {{ $settings->flatten()->count() }}
                    </div>
                    <div class="text-sm text-gray-600">Total Settings</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ $settings->flatten()->where('is_required', true)->count() }}
                    </div>
                    <div class="text-sm text-gray-600">Required Settings</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ $settings->flatten()->where('is_encrypted', true)->count() }}
                    </div>
                    <div class="text-sm text-gray-600">Encrypted Settings</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ $settings->count() }}
                    </div>
                    <div class="text-sm text-gray-600">Categories</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
