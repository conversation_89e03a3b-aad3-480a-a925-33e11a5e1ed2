<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\TeamMember;
use Illuminate\Support\Facades\Log;

class TeamMemberStatusCheck
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip this middleware if user is not authenticated
        if (!Auth::check()) {
            return $next($request);
        }

        $userId = Auth::id();
        $isTeamMember = Session::get('is_team_member', false);
        
        // If user is flagged as team member in session, verify their status
        if ($isTeamMember) {
            $businessId = Session::get('businessId');
            
            // Check if team member still exists and is active
            $teamMember = TeamMember::where('user_id', $userId)
                ->where(function($query) use ($businessId) {
                    // Either check by business ID if available
                    if ($businessId) {
                        $query->where('business_id', $businessId);
                    }
                })
                ->first();
                
            // If team member doesn't exist or is removed/inactive
            if (!$teamMember || $teamMember->status !== 'active' || $teamMember->business_status !== 'active') {
                // Log the access attempt for security auditing
                Log::warning('Removed team member attempted to access restricted area', [
                    'user_id' => $userId,
                    'business_id' => $businessId,
                    'team_member_status' => $teamMember ? $teamMember->status : 'not_found'
                ]);
                
                // Clear all session data to prevent redirect loops
                Session::flush();
                
                // Redirect to login with appropriate message
                Auth::logout();
                
                return redirect()->route('login')
                    ->with('error', 'Your access to this business has been removed. Please contact the business owner for assistance.');
            }
        }

        return $next($request);
    }
}
