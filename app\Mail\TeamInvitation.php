<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class TeamInvitation extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The team member instance.
     *
     * @var \App\Models\TeamMember
     */
    public $teamMember;

    /**
     * The business instance.
     *
     * @var \App\Models\Business
     */
    public $business;
    public $token;
    public $user;

    /**
     * Create a new message instance.
     *
     * @param \App\Models\TeamMember $teamMember
     * @param \App\Models\Business $business
     */
    public function __construct(\App\Models\TeamMember $teamMember, \App\Models\Business $business, $token, $user)
    {
        $this->teamMember = $teamMember;
        $this->business = $business;
        $this->token = $token;
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Invitation to Join Business Location',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.team-invitation',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
