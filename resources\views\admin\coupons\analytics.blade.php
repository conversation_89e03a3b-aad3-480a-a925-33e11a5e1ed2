@extends('admin.layouts.app')

@section('title', 'Coupon Analytics')

@section('content')
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Coupon Analytics</h1>
            <p class="text-gray-600 mt-1">Comprehensive analytics and insights for coupon performance</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-0">
            <form method="GET" action="{{ route('admin.coupons.analytics') }}" class="flex gap-2">
                <select name="date_range" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="7" {{ request('date_range') == '7' ? 'selected' : '' }}>Last 7 days</option>
                    <option value="30" {{ request('date_range', '30') == '30' ? 'selected' : '' }}>Last 30 days</option>
                    <option value="90" {{ request('date_range') == '90' ? 'selected' : '' }}>Last 90 days</option>
                    <option value="365" {{ request('date_range') == '365' ? 'selected' : '' }}>Last year</option>
                </select>
            </form>
            <a href="{{ route('admin.coupons.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Coupons
            </a>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-tags text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Coupons</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($analytics['overview']['total_coupons']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Coupons</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($analytics['overview']['active_coupons']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Usage</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($analytics['overview']['total_usage']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Discount</dt>
                        <dd class="text-lg font-medium text-gray-900">₹{{ number_format($analytics['overview']['total_discount'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-calculator text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Average Discount</dt>
                        <dd class="text-lg font-medium text-gray-900">₹{{ number_format($analytics['overview']['average_discount'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-pink-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-percentage text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Conversion Rate</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($analytics['overview']['conversion_rate'], 2) }}%</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Usage Trend Chart -->
        @if($analytics['usage_trend']->isNotEmpty())
        <div class="bg-white rounded-lg shadow">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Trend</h3>
                <div class="h-64">
                    <canvas id="usageTrendChart"></canvas>
                </div>
            </div>
        </div>
        @endif

        <!-- Discount Distribution Chart -->
        @if($analytics['discount_distribution']->isNotEmpty())
        <div class="bg-white rounded-lg shadow">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Discount Distribution by Type</h3>
                <div class="h-64">
                    <canvas id="discountDistributionChart"></canvas>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Top Performing Coupons -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Top Performing Coupons</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="block w-full min-w-[600px] md:table divide-y divide-gray-200>
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Coupon</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type & Value</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage Count</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($analytics['top_coupons'] as $coupon)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $coupon->coupon_code }}</div>
                                    <div class="text-sm text-gray-500">{{ $coupon->name }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $coupon->type === 'percentage' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                    {{ $coupon->discount_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {{ number_format($coupon->usage_logs_count) }}
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $coupon->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $coupon->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium space-x-2">
                                <a href="{{ route('admin.coupons.show', $coupon) }}" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.coupons.edit', $coupon) }}" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-chart-bar text-4xl text-gray-300 mb-4"></i>
                                <p class="text-lg font-medium">No coupon data available</p>
                                <p class="text-sm">Create and activate coupons to see analytics</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@if($analytics['usage_trend']->isNotEmpty() || $analytics['discount_distribution']->isNotEmpty())
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
@if($analytics['usage_trend']->isNotEmpty())
// Usage Trend Chart
const usageTrendCtx = document.getElementById('usageTrendChart').getContext('2d');
const usageTrendChart = new Chart(usageTrendCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($analytics['usage_trend']->pluck('date')) !!},
        datasets: [{
            label: 'Usage Count',
            data: {!! json_encode($analytics['usage_trend']->pluck('usage_count')) !!},
            borderColor: 'rgb(99, 102, 241)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.1,
            yAxisID: 'y'
        }, {
            label: 'Total Discount (₹)',
            data: {!! json_encode($analytics['usage_trend']->pluck('total_discount')) !!},
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Date'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Usage Count'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Total Discount (₹)'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
@endif

@if($analytics['discount_distribution']->isNotEmpty())
// Discount Distribution Chart
const discountDistributionCtx = document.getElementById('discountDistributionChart').getContext('2d');
const discountDistributionChart = new Chart(discountDistributionCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($analytics['discount_distribution']->pluck('type')->map(function($type) { return ucfirst($type); })) !!},
        datasets: [{
            data: {!! json_encode($analytics['discount_distribution']->pluck('usage_count')) !!},
            backgroundColor: [
                'rgba(99, 102, 241, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(239, 68, 68, 0.8)'
            ],
            borderColor: [
                'rgb(99, 102, 241)',
                'rgb(16, 185, 129)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            }
        }
    }
});
@endif
</script>
@endpush
@endif
