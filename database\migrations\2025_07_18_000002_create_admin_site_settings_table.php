<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_site_settings', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            
            // Setting identification
            $table->string('key', 100)->unique(); // Setting key (e.g., 'ai.max_tokens')
            $table->string('name', 150); // Human readable name
            $table->text('description')->nullable(); // Setting description
            
            // Setting value and type
            $table->longText('value')->nullable(); // Setting value (JSON for complex values)
            $table->enum('type', [
                'string', 'integer', 'float', 'boolean', 'json', 
                'text', 'email', 'url', 'select', 'multiselect'
            ])->default('string');
            
            // Setting categorization and organization
            $table->string('category', 50); // Category (e.g., 'ai', 'google_api', 'email', 'general')
            $table->string('group', 50)->nullable(); // Sub-group within category
            $table->integer('sort_order')->default(0); // Display order
            
            // Validation and constraints
            $table->json('validation_rules')->nullable(); // Laravel validation rules
            $table->json('options')->nullable(); // For select/multiselect types
            $table->text('help_text')->nullable(); // Help text for admin interface
            
            // Setting metadata
            $table->boolean('is_public')->default(false); // Can be accessed by frontend
            $table->boolean('is_required')->default(false); // Is this setting required
            $table->boolean('is_encrypted')->default(false); // Should value be encrypted
            $table->text('default_value')->nullable(); // Default value
            
            // Access control
            $table->enum('access_level', ['super_admin', 'admin', 'moderator'])->default('super_admin');
            $table->unsignedBigInteger('updated_by')->nullable(); // Admin who last updated
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['category', 'group', 'sort_order']);
            $table->index(['is_public', 'is_required']);
            $table->index('access_level');
            $table->index('updated_by');
            
            // Foreign keys
            //$table->foreign('updated_by')->references('id')->on('admins')->onDelete('set null');
            
            // Charset
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_site_settings');
    }
};
